<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cw</groupId>
    <artifactId>mallentity</artifactId>
    <version>1.0.1-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <hutool-version>5.7.16</hutool-version>
    </properties>


    <scm>
        <connection>scm:git:http://47.98.165.42:30080/cw/mallentity.git</connection>
        <url>http://47.98.165.42:30080/cw/mallentity.git</url>
        <developerConnection>scm:git:http://47.98.165.42:30080/cw/mallentity.git</developerConnection>
        <tag>HEAD</tag>
    </scm>

    <dependencies>
        <dependency>
            <groupId>com.cw</groupId>
            <artifactId>entity-starter</artifactId>
            <version>1.1.6</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
            <version>2.3.6.RELEASE</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool-version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.scm</groupId>
                        <artifactId>maven-scm-provider-gitexe</artifactId>
                        <version>1.9.4</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <tagNameFormat>v@{project.version}</tagNameFormat>
                    <releaseProfiles>release</releaseProfiles>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!--    <distributionManagement>-->
    <!--        <repository>-->
    <!--            <id>product</id>-->
    <!--            <name>Nexus Release Repository</name>-->
    <!--            <url>http://47.98.165.42:8081/repository/maven-releases/</url>-->
    <!--        </repository>-->
    <!--        <snapshotRepository>-->
    <!--            <id>snapshots</id>-->
    <!--            <name>Nexus Snapshot Repository</name>-->
    <!--            <url>http://47.98.165.42:8081/repository/maven-snapshots/</url>-->
    <!--        </snapshotRepository>-->
    <!--    </distributionManagement>-->

    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <name>Release Repository</name>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <name>Snapshot Repository</name>
        </snapshotRepository>
    </distributionManagement>

</project>