<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.7.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.cw</groupId>
    <artifactId>dsmall</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>dsmall</name>
    <description>dsmall</description>

    <properties>
        <java.version>1.8</java.version>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <!-- Spring Cloud Alibaba versions -->
        <spcloud-aliaba.version>2.2.6.RELEASE</spcloud-aliaba.version>
        <seata.vesion>1.4.1</seata.vesion>
        <nacos-client.vesion>2.0.2</nacos-client.vesion>
        <!-- 第三方中间件 -->
            <lombok.version>1.18.6</lombok.version>
        <redisson.version>3.16.0</redisson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <sa-token.version>1.26.0</sa-token.version>
        <xstream.version>1.4.18</xstream.version>
        <jackson-dataformat-version>2.12.3</jackson-dataformat-version>

        <knife4j.version>3.0.3</knife4j.version>
        <swagger.version>3.0.0</swagger.version>
        <swaggerui.version>${swagger.version}</swaggerui.version>
        <quartz.verison>2.3.0</quartz.verison>

        <wxtools.version>4.3.0</wxtools.version>


        <!--自有私服包-->
        <common-tool.version>1.0.4-SNAPSHOT</common-tool.version>
        <mallentity.version>1.0.1-SNAPSHOT</mallentity.version>

    </properties>

    <scm>
        <connection>scm:git:http://47.98.165.42:30080/cw/dsmall.git</connection>
        <url>http://47.98.165.42:30080/cw/dsmall.git</url>
        <developerConnection>scm:git:http://47.98.165.42:30080/cw/dsmall.git</developerConnection>
        <tag>HEAD</tag>
    </scm>


    <dependencies>
        <!-- SPRING 和 SPRING CLOUD 包 BEGIN-->
        <!-- 阿里巴巴服务发现.与配置中心 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>${spcloud-aliaba.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--2.3以上版本去掉web校验依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>


        <!-- SPRING 和 SPRING CLOUD 包 END-->


        <!--  第三方包 START-->
        <!-- 第三方包集成 -->
        <!--解析SVG图片宽高用到的依赖包-->
        <!--<dependency>-->
        <!--    <groupId>org.apache.xmlgraphics</groupId>-->
        <!--    <artifactId>batik.all</artifactId>-->
        <!--    <version>1.14</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
        <!--    <groupId>org.apache.xmlgraphics</groupId>-->
        <!--    <artifactId>fop</artifactId>-->
        <!--    <version>2.7</version>-->
        <!--</dependency>-->

        <!--//视频截帧-->
        <!--javacv 精简版本 ffmpeg -->
        <!--<dependency>-->
        <!--    <groupId>org.bytedeco</groupId>-->
        <!--    <artifactId>javacv</artifactId>-->
        <!--    <version>1.4.1</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--    <groupId>org.bytedeco</groupId>-->
        <!--    <artifactId>javacpp</artifactId>-->
        <!--    <version>1.4.1</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--    <groupId>org.bytedeco.javacpp-presets</groupId>-->
        <!--    <artifactId>opencv-platform</artifactId>-->
        <!--    <version>3.4.1-1.4.1</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--    <groupId>org.bytedeco.javacpp-presets</groupId>-->
        <!--    <artifactId>ffmpeg-platform</artifactId>-->
        <!--    <version>3.4.2-1.4.1</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>4.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>${sa-token.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-dao-redis-jackson</artifactId>
            <version>${sa-token.version}</version>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>${xstream.version}</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>io.springfox</groupId>-->
        <!--            <artifactId>springfox-swagger2</artifactId>-->
        <!--            <version>${swagger.version}</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>io.springfox</groupId>-->
        <!--            <artifactId>springfox-swagger-ui</artifactId>-->
        <!--            <version>${swaggerui.version}</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>${swagger.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jjwt.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
            <version>5.5.2</version>
        </dependency>

        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>${quartz.verison}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <version>${lombok.version}</version>
        </dependency>
        <!--诺诺发票SDK-->
        <dependency>
            <groupId>com.nuonuo</groupId>
            <artifactId>open-sdk</artifactId>
            <version>1.0.5.2</version>
        </dependency>

        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-jpeg</artifactId>
            <version>3.6.4</version>
        </dependency>

        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-tiff</artifactId>
            <version>3.6.4</version>
        </dependency>

        <!--OSS-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.5.0</version>
        </dependency>

        <!--COS-->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.97</version>
        </dependency>


        <dependency>
        <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.336</version>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sts</artifactId>
            <version>1.11.336</version>
        </dependency>

        <!--图片压缩-->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.9</version>
        </dependency>
        <!--图片压缩-->

        <!-- 微信与支付相关开发-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>${wxtools.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>${wxtools.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
            <version>${wxtools.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
            <version>${jackson-dataformat-version}</version>
        </dependency>

        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.39.60.ALL</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 二维码生成处理-->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.1</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.16.2</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.itextpdf</groupId>-->
        <!--            <artifactId>itextpdf</artifactId>-->
        <!--            <version>********</version>-->
        <!--        </dependency>-->


        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <version>8.0.2</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>font-asian</artifactId>
            <version>8.0.2</version>
        </dependency>


        <!-- 微信相关开发-->

        <!--  第三方包END-->

        <!--  自有私服包 START-->

        <dependency>
            <groupId>com.cw</groupId>
            <artifactId>common-tools</artifactId>
            <version>${common-tool.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cw</groupId>
            <artifactId>mallentity</artifactId>
            <version>${mallentity.version}</version>
        </dependency>
        <!--  自有私服包END-->


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <tagNameFormat>v@{project.version}</tagNameFormat>
                    <releaseProfiles>release</releaseProfiles>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

        </plugins>

        <finalName>dsmall</finalName>

    </build>

    <distributionManagement>
        <repository>
            <id>product</id>
            <name>Nexus Release Repository</name>
            <url>http://47.98.165.42:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://47.98.165.42:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
