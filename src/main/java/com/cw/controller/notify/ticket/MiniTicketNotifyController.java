package com.cw.controller.notify.ticket;

/**
 * @Describe 微票
 * <AUTHOR> <PERSON>
 * @Create on 2024-09-20
 */

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.func.Var;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.pojo.mtticket.pojo.request.*;
import com.cw.outsys.pojo.mtticket.pojo.response.MiniTicketResponse;
import com.cw.outsys.pojo.mtticket.pojo.response.MiniTicketResponseHead;
import com.cw.pojo.notify.sd.SDTicketNotifyData;
import com.cw.pojo.notify.sd.TicketNotifyEventModel;
import com.cw.service.mq.MqNameUtils;
import com.cw.utils.DESUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.ticket.MTicketCodeEnum;
import com.cw.utils.enums.ticket.TicketNotifyEventType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.crypto.IllegalBlockSizeException;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;


@RestController
@RequestMapping(value = "/miniTicket/notify", method = RequestMethod.POST)
public class MiniTicketNotifyController {
    private static final String OKRESULT = "success";
    private static final String FAILRESULT = "failure";

    @Autowired
    RabbitTemplate rabbitTemplate;


    private Logger logger = LoggerFactory.getLogger(this.getClass());


    /**
     * 订单核销通知
     *
     * @param request
     * @param user_id
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/{user_id}", method = RequestMethod.POST)
    public String ticketCheck(@RequestBody String consumeNotice, HttpServletRequest request, @PathVariable String user_id) throws Exception {
        try {
            String consumeNoticeStr = Base64Decoder.decodeStr(consumeNotice, StandardCharsets.UTF_8);
            //防止加密不是标准的json字符串 判断前后是否是{ }符号
            if (!consumeNoticeStr.startsWith("{")) {
                int index = consumeNoticeStr.indexOf("{");
                if (index != -1) {
                    consumeNoticeStr = consumeNoticeStr.substring(index);
                }
            }
            if (!consumeNoticeStr.endsWith("}")) {
                int index = consumeNoticeStr.lastIndexOf("}");
                if (index != -1) {
                    consumeNoticeStr = consumeNoticeStr.substring(0, index + 1);
                }
            }
            MiniTicketRequest requestData = JSON.parseObject(consumeNoticeStr, MiniTicketRequest.class);
            System.out.println(JSON.toJSONString(requestData));
            MiniTicketRequestHead requestHead = requestData.getRequestHead();
            String userId = requestHead.getUser_id();
            String method = requestHead.getMethod();
            long timestamp = requestHead.getTimestamp();
            String version = requestHead.getVersion();
            String postSign = requestHead.getSign();
            String requestBody = requestData.getRequestBody();
            Var<String> var = new Var<>();
            //todo 判断时间戳有效性  防止乱传
            boolean lcheck = checkSign(userId, method, timestamp, version, requestBody, postSign, var);
            if (!lcheck) {
                logger.info("签名验证失败");
                return getBase64Response(MTicketCodeEnum.UNAUTHORIZED);
            }
            String appSecrect = getAppSecrect(user_id);
            //签名通过DES解码获取请求Body
            String bodyStr = DESUtil.decrypt(requestBody, appSecrect);
            //根据method解析
            if (method.equals("ConsumeNotice")) {//核销通知
                MTOrderConsumeNoticeBody body = JSON.parseObject(bodyStr, MTOrderConsumeNoticeBody.class);
                logger.info("核销回调通知：{}", JSON.toJSONString(body));
                String bookingId = body.getOrderSerialId();
                String outId = body.getPartnerOrderId();
                int tickets = body.getTickets();
                if (lcheck && ObjectUtil.isAllNotEmpty(bookingId, outId, tickets) && !var.getValue().isEmpty()) {
                    TicketNotifyEventModel model = new TicketNotifyEventModel();
                    SDTicketNotifyData ticketNotifyData = new SDTicketNotifyData();
                    ticketNotifyData.setOrderid(bookingId);
                    //微票不传suborderid，即本地订单的regno,消费者默认查询orderId
                    //ticketNotifyData.setStatus(status);
                    ticketNotifyData.setChecknum(tickets);//消费数量
                    ticketNotifyData.setType(TicketNotifyEventType.CHECKTICEKT);
                    model.setData(ticketNotifyData);
                    model.setProjectId(var.getValue());

                    String postStr = JSON.toJSONString(model);
                    logger.info("核销{}张 [{} - {}]: {}", tickets, bookingId, outId, postStr);
                    putIntoQueue(postStr);
                } else {
                    //
                    return getBase64Response(MTicketCodeEnum.PARAMBLANK);

                }
            } else if (method.equals("refundNotice")) {
                MTOrderRefundNoticeBody body = JSON.parseObject(bodyStr, MTOrderRefundNoticeBody.class);
                logger.info("微票回调退款通知:{}", JSON.toJSONString(body));
                String bookingId = body.getOrderSerialId();
                String outId = body.getPartnerOrderId();
                int tickets = body.getTickets();//

                if (lcheck && ObjectUtil.isAllNotEmpty(bookingId, outId, tickets) && !var.getValue().isEmpty()) {
                    TicketNotifyEventModel model = new TicketNotifyEventModel();
                    SDTicketNotifyData ticketNotifyData = new SDTicketNotifyData();
                    ticketNotifyData.setOrderid(bookingId);
                    ticketNotifyData.setSuborderid(outId);
                    //ticketNotifyData.setStatus(auditStatus);
                    ticketNotifyData.setReturnnum(tickets);
                    ticketNotifyData.setType(TicketNotifyEventType.CANCEL);
                    model.setData(ticketNotifyData);
                    model.setProjectId(var.getValue());
                    model.setSense(bookingId != null & bookingId.startsWith(ProdType.ACTGROUP.val()) ? 1 : 0);
                    String postStr = JSON.toJSONString(model);
                    logger.info("目前不做线上处理.退票{}张 [{} - {}]: {}", tickets, bookingId, outId, postStr);
                    //putIntoQueue(postStr);
                } else {
                    //
                    return getBase64Response(MTicketCodeEnum.PARAMBLANK);

                }
            } else if (method.equals("RetCodeNotice")) {
                //签名通过DES解码获取请求Body
                MTOrderRetCodeNoticeBody body = JSON.parseObject(bodyStr, MTOrderRetCodeNoticeBody.class);
                logger.info("微票回调反码通知:{}", JSON.toJSONString(body));
                String bookingId = body.getOrderSerialId();
                String outId = body.getPartnerOrderId();
                String partnerCode = body.getPartnerCode();
                String qrCodeAddress = body.getPartnerQRCodeAddress();
                if (StringUtils.isBlank(partnerCode) && StringUtils.isBlank(qrCodeAddress)) {
                    return getBase64Response(MTicketCodeEnum.PARAMBLANK);
                }
                //todo 修改订单 入园辅助码和
                logger.info("入园辅助码：{} ，进园二维码：{}", partnerCode, qrCodeAddress);

            } else if (method.equals("QuerySession")) {
                //分

            } else if (method.equals("ProductChangeNotice")) {

            }

        } catch (IllegalBlockSizeException e) {
            //requestbody 解密请求实体报错
            e.printStackTrace();
            return getBase64Response(MTicketCodeEnum.PARAMBLANK);
        } catch (DefinedException e) {
            e.printStackTrace();
            return getBase64Response(MTicketCodeEnum.PARAMBLANK);
        } catch (Exception e) {
            e.printStackTrace();
            return getBase64Response(MTicketCodeEnum.SYSERR);
        }

        return getBase64Response(MTicketCodeEnum.SUCCESS);
    }


    /**
     * @param userid
     * @param method
     * @param timestamp
     * @param version
     * @param requestBody
     * @param postSignStr
     * @param projectId
     * @return
     */
    public boolean checkSign(String userid, String method, long timestamp, String version, String requestBody, String postSignStr, Var<String> projectId) {
        VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig vendorconfig = vendorConfigCache.getVendorConfigByAppId(userid);
        if (vendorconfig != null) {
            projectId.setValue(vendorconfig.getProjectid());
            String sign = vendorconfig.getAppid() + method + timestamp + version + requestBody + vendorconfig.getAppsecrect();
            String localSign = SecureUtil.md5(sign);
            return localSign.equals(postSignStr);
        } else {
            return false;
        }
    }


    public String getAppSecrect(String userid) throws DefinedException {
        VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig vendorconfig = vendorConfigCache.getVendorConfigByAppId(userid);
        if (vendorconfig != null) {
            return vendorconfig.getAppsecrect();
        } else {
            throw new DefinedException("user_id不存在");
        }
    }


    /**
     * @param codeEnum
     * @return 返回base64
     */
    private String getBase64Response(MTicketCodeEnum codeEnum) {
        MiniTicketResponse response = new MiniTicketResponse();
        MiniTicketResponseHead responseHead = new MiniTicketResponseHead();
        responseHead.setRes_code(codeEnum.getCode());
        responseHead.setRes_msg(codeEnum.getMsg());
        responseHead.setTimestamp(System.currentTimeMillis() / 1000);
        response.setResponseHead(responseHead);

        return Base64Encoder.encode(JSON.toJSONString(response), StandardCharsets.UTF_8);
    }

    private void putIntoQueue(String data) {
        String exchangeName = MqNameUtils.DirectExchange.MALL.name();
        String queueName = MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.TICKETNOTIFY);//.getGroupNotifyQueueName(CrsMqNameUtils.NotifyOp_Group.TICKET);
        rabbitTemplate.convertAndSend(exchangeName, queueName, data);
    }


}
