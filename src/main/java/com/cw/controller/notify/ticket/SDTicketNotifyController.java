package com.cw.controller.notify.ticket;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.func.Var;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.entity.Vendorconfig;
import com.cw.pojo.notify.sd.SDTicketNotifyData;
import com.cw.pojo.notify.sd.TicketNotifyEventModel;
import com.cw.service.mq.MqNameUtils;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.ticket.TicketNotifyEventType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 票务系统通知
 */
@RestController
@RequestMapping(value = "/sdticket/notify", method = RequestMethod.GET)
public class SDTicketNotifyController {
    private static final String OKRESULT = "success";
    private static final String FAILRESULT = "failure";
    private static final String CANCELRESULT = "cancel";

    @Autowired
    RabbitTemplate rabbitTemplate;
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 2.14 核销通知
     *
     * @param request
     * @param appid
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/check/{appid}", method = RequestMethod.GET)
    public String ticketcheck(HttpServletRequest request, @PathVariable String appid) throws Exception {
        String order_no = request.getParameter("order_no");
        String sub_order_no = request.getParameter("sub_order_no");
        String status = request.getParameter("status");
        String checkNum = request.getParameter("checkNum");
        String returnNum = request.getParameter("returnNum");
        String total = request.getParameter("total");
        String sign = request.getParameter("sign");
        Var<String> var = new Var<>();
        //TODO 验签

        boolean lcheck = checkSign(appid, "order_no=", order_no, sign, var);

        if (!lcheck) {
            logger.info(lcheck ? "签名验证成功" : "签名验证失败");
        }
        if (lcheck && ObjectUtil.isAllNotEmpty(order_no, status, checkNum, returnNum, total) && !var.getValue().isEmpty()) {
            TicketNotifyEventModel model = new TicketNotifyEventModel();
            SDTicketNotifyData ticketNotifyData = new SDTicketNotifyData();
            ticketNotifyData.setOrderid(order_no);
            ticketNotifyData.setSuborderid(sub_order_no);
            ticketNotifyData.setStatus(status);
            ticketNotifyData.setChecknum(StringUtils.isNotBlank(checkNum) ? Integer.parseInt(checkNum) : 0);
            ticketNotifyData.setReturnnum(StringUtils.isNotBlank(returnNum) ? Integer.parseInt(returnNum) : 0);
            ticketNotifyData.setTotalnum(StringUtils.isNotBlank(total) ? Integer.parseInt(total) : 0);
            ticketNotifyData.setType(TicketNotifyEventType.CHECKTICEKT);
            model.setData(ticketNotifyData);
            model.setProjectId(var.getValue());

            String postStr = JSON.toJSONString(model);
            logger.info("核销{}张 [{} - {}]: {}", checkNum, order_no, sub_order_no, postStr);
            putIntoQueue(postStr);
        } else {
            //

        }


        return OKRESULT;
    }

    /**
     * 2.9 退票通知
     *
     * @param request
     * @param appid
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/cancel/{appid}", method = RequestMethod.GET)
    public String ticketcancel(HttpServletRequest request, @PathVariable String appid) throws Exception {
        String orderCode = request.getParameter("orderCode");//订单号  本地BookingId
        String subOrderCode = request.getParameter("subOrderCode");//子订单号 本地门票子订单号
        String retreatBatchNo = request.getParameter("retreatBatchNo");//退票批次号.用于查询退票成功或失败
        String auditStatus = request.getParameter("auditStatus");//failure:退票失败,success:退票成功
        String returnNum = request.getParameter("returnNum");//退票数量
        String sign = request.getParameter("sign");

        Var<String> var = new Var<>();
        boolean lcheck = checkSign(appid, "", orderCode, sign, var);
        if (!lcheck) {
            logger.info(lcheck ? "签名验证成功" : "签名验证失败");
        }

        if (lcheck && ObjectUtil.isAllNotEmpty(orderCode, subOrderCode, retreatBatchNo, auditStatus, returnNum) && !var.getValue().isEmpty()) {//处理退票成功的通知
            TicketNotifyEventModel model = new TicketNotifyEventModel();
            SDTicketNotifyData ticketNotifyData = new SDTicketNotifyData();
            ticketNotifyData.setOrderid(orderCode);
            ticketNotifyData.setSuborderid(subOrderCode);
            ticketNotifyData.setStatus(auditStatus);
            ticketNotifyData.setReturnnum(StringUtils.isNotBlank(returnNum) ? Integer.parseInt(returnNum) : 0);
            ticketNotifyData.setType(TicketNotifyEventType.CANCEL);
            model.setData(ticketNotifyData);
            model.setProjectId(var.getValue());

            String postStr = JSON.toJSONString(model);
            logger.info("目前不做线上处理.退票{}张 [{} - {}]: {}", returnNum, orderCode, subOrderCode, postStr);
            if (auditStatus.equals(OKRESULT)) {
//                putIntoQueue(postStr);
            }
        } else {
            return FAILRESULT;
        }


        return OKRESULT;
    }

    /**
     * 2.7 订单完结OR 线下取消时.会有通知
     *
     * @param request
     * @param appid
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/finish/{appid}", method = RequestMethod.GET)
    public String ticketfinish(HttpServletRequest request, @PathVariable String appid) throws Exception {
        String order_code = request.getParameter("order_code");
        String status = request.getParameter("status");//cancel:已取消,success:已完成
        String checkNum = request.getParameter("checkNum");
        String returnNum = request.getParameter("returnNum");
        String total = request.getParameter("total");
        String sign = request.getParameter("sign");
        Var<String> var = new Var<>();

        boolean lcheck = checkSign(appid, "order_code=", order_code, sign, var);
        if (!lcheck) {
            logger.info(lcheck ? "签名验证成功" : "签名验证失败");
        }

        if (lcheck && ObjectUtil.isAllNotEmpty(order_code, status, checkNum, returnNum, total) && !var.getValue().isEmpty()) {
            TicketNotifyEventModel model = new TicketNotifyEventModel();
            SDTicketNotifyData ticketNotifyData = new SDTicketNotifyData();
            ticketNotifyData.setOrderid(order_code);
            ticketNotifyData.setStatus(status);
            ticketNotifyData.setChecknum(StringUtils.isNotBlank(checkNum) ? Integer.parseInt(checkNum) : 0);
            ticketNotifyData.setReturnnum(StringUtils.isNotBlank(returnNum) ? Integer.parseInt(returnNum) : 0);
            ticketNotifyData.setTotalnum(StringUtils.isNotBlank(total) ? Integer.parseInt(total) : 0);
            ticketNotifyData.setType(TicketNotifyEventType.FINISH);
            model.setData(ticketNotifyData);
            model.setProjectId(var.getValue());
            model.setSense(order_code.startsWith(ProdType.ACTGROUP.val()) ? 1 : 0);

            String postStr = JSON.toJSONString(model);
            logger.info("{} [{}]: {}", "收到门票检票完成通知", order_code, postStr);
            if (status.equals(OKRESULT)) {
                putIntoQueue(postStr);
            }
        } else {
            return FAILRESULT;
        }


        return OKRESULT;
    }

    public boolean checkSign(String appid, String prefix, String params, String postSignStr, Var<String> projectId) {
        VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig vendorconfig = vendorConfigCache.getVendorConfigByAppId(appid);
        String privateKey = vendorconfig.getUserpwd();
        if (vendorconfig != null) {
            projectId.setValue(vendorconfig.getProjectid());
            String localSign = SecureUtil.md5(prefix + params + privateKey);
            return localSign.equalsIgnoreCase(postSignStr);
        } else {
            return false;
        }
    }

    private void putIntoQueue(String data) {
        String exchangeName = MqNameUtils.DirectExchange.MALL.name();
        String queueName = MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.TICKETNOTIFY);//.getGroupNotifyQueueName(CrsMqNameUtils.NotifyOp_Group.TICKET);
        rabbitTemplate.convertAndSend(exchangeName, queueName, data);
    }

}
