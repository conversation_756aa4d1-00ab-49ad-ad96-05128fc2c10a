package com.cw.controller.notify.ticket;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.func.Var;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.entity.Vendorconfig;
import com.cw.outsys.pojo.pft.pojo.req.PFTOrderConsumeNoticeBody;
import com.cw.outsys.pojo.pft.pojo.req.PFTOrderRefundNoticeBody;
import com.cw.pojo.notify.sd.SDTicketNotifyData;
import com.cw.pojo.notify.sd.TicketNotifyEventModel;
import com.cw.service.mq.MqNameUtils;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.ticket.MTicketCodeEnum;
import com.cw.utils.enums.ticket.TicketNotifyEventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 票务系统通知
 */
@RestController
@RequestMapping(value = "/pftticket/notify", method = RequestMethod.POST)
public class PftTicketNotifyController {
    private static final String OKRESULT = "200";
    private static final String REFUSE = "拒绝退票:";
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    RabbitTemplate rabbitTemplate;

    @RequestMapping(value = "/ticketcheck/{appid}", method = RequestMethod.POST)
    public String ticketcheck(@RequestBody String consumeNotice, HttpServletRequest request, @PathVariable String appid) throws Exception {
        String consumeNoticeStr = consumeNotice;
        //防止加密不是标准的json字符串 判断前后是否是{ }符号
        if (!consumeNoticeStr.startsWith("{")) {
            int index = consumeNoticeStr.indexOf("{");
            if (index != -1) {
                consumeNoticeStr = consumeNoticeStr.substring(index);
            }
        }
        if (!consumeNoticeStr.endsWith("}")) {
            int index = consumeNoticeStr.lastIndexOf("}");
            if (index != -1) {
                consumeNoticeStr = consumeNoticeStr.substring(0, index + 1);
            }
        }
        PFTOrderConsumeNoticeBody requestData = JSON.parseObject(consumeNoticeStr, PFTOrderConsumeNoticeBody.class);
        String VerifyCode = requestData.getVerifyCode();
        String Order16U = requestData.getOrder16U();
        String OrderCall = requestData.getOrderCall();
        String ActionTime = requestData.getActionTime();
        int Tnumber = requestData.getTnumber();
        int AllCheckNum = requestData.getAllCheckNum();
        int OrderState = requestData.getOrderState();
        int Source = requestData.getSource();//验证渠道
        int Action = requestData.getAction();//操作类型
        String sign = requestData.getSign();
        Var<String> var = new Var<>();
        VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig vendorconfig = vendorConfigCache.getVendorConfigByAppId(appid);
        var.setValue(vendorconfig.getProjectid());
        //TODO 验签
//        boolean lcheck = checkSign(appid, "OrderCall=", OrderCall, sign, var);

//        if (!lcheck) {
//            logger.info("签名验证失败");
//            return String.valueOf(MTicketCodeEnum.UNAUTHORIZED);
//        }

        if (OrderState == 1 || OrderState == 7) {//核销通知
            if (ObjectUtil.isAllNotEmpty(Order16U, OrderCall, Tnumber) && !var.getValue().isEmpty()) {
                TicketNotifyEventModel model = new TicketNotifyEventModel();
                SDTicketNotifyData ticketNotifyData = new SDTicketNotifyData();
                ticketNotifyData.setOrderid(Order16U);
                ticketNotifyData.setOtaorderid(OrderCall);
                ticketNotifyData.setChecknum(Tnumber);
                ticketNotifyData.setTotalnum(AllCheckNum);
                ticketNotifyData.setStatus(String.valueOf(OrderState));
                ticketNotifyData.setType(TicketNotifyEventType.CHECKTICEKT);
                model.setData(ticketNotifyData);
                model.setProjectId(var.getValue());
                model.setSense(Order16U != null & Order16U.startsWith(ProdType.ACTGROUP.val()) ? 1 : 0);
                String postStr = JSON.toJSONString(model);
                logger.info("核销{}张 [{} - {}]: {}", Tnumber, Order16U, OrderCall, postStr);
                putIntoQueue(postStr);
            } else {
                MTicketCodeEnum.PARAMBLANK.getMsg();
            }

        } else if (OrderState == 8) {//退票通知
            PFTOrderRefundNoticeBody body = JSON.parseObject(consumeNoticeStr, PFTOrderRefundNoticeBody.class);
            logger.info("微票回调退款通知:{}", JSON.toJSONString(body));
            String bookingId = body.getOrderCall();
            String outId = body.getOrder16U();
            int tnumber = body.getTnumber();
            int refundtype = body.getRefundtype();
            int allCheckNum = body.getAllCheckNum();
            String explain = body.getExplain();
            if (ObjectUtil.isAllNotEmpty(bookingId, outId, tnumber) && !var.getValue().isEmpty()) {
                if (refundtype == 1) {
                    TicketNotifyEventModel model = new TicketNotifyEventModel();
                    SDTicketNotifyData ticketNotifyData = new SDTicketNotifyData();
                    ticketNotifyData.setOrderid(bookingId);
                    ticketNotifyData.setSuborderid(outId);
                    //
                    ticketNotifyData.setReturnnum(tnumber);
                    ticketNotifyData.setExplain(explain);
                    ticketNotifyData.setTotalnum(allCheckNum);
                    ticketNotifyData.setType(TicketNotifyEventType.CANCEL);
                    model.setData(ticketNotifyData);
                    model.setProjectId(var.getValue());
                    model.setSense(bookingId != null & bookingId.startsWith(ProdType.ACTGROUP.val()) ? 1 : 0);
                    String postStr = JSON.toJSONString(model);
                    logger.info("目前不做线上处理.退票{}张 [{} - {}]: {}", tnumber, bookingId, outId, postStr);
                    putIntoQueue(postStr);
                } else if (refundtype == 2) {
                    TicketNotifyEventModel model = new TicketNotifyEventModel();
                    SDTicketNotifyData ticketNotifyData = new SDTicketNotifyData();
                    ticketNotifyData.setExplain(explain);
                    ticketNotifyData.setType(TicketNotifyEventType.CANCEL);
                    model.setProjectId(var.getValue());
                    model.setSense(Order16U != null & Order16U.startsWith(ProdType.ACTGROUP.val()) ? 1 : 0);
                    model.setData(ticketNotifyData);
                    String postStr = JSON.toJSONString(model);
                    putIntoQueue(postStr);
                    return REFUSE+ticketNotifyData.getExplain();


                }
            } else {
                //
                return String.valueOf(MTicketCodeEnum.PARAMBLANK);

            }
        }

        return OKRESULT;

    }


    private void putIntoQueue(String data) {
        String exchangeName = MqNameUtils.DirectExchange.MALL.name();
        String queueName = MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.TICKETNOTIFY);//.getGroupNotifyQueueName(CrsMqNameUtils.NotifyOp_Group.TICKET);
        rabbitTemplate.convertAndSend(exchangeName, queueName, data);
    }


    @RequestMapping(value = "/pft/get/service", method = RequestMethod.GET)
    public String ptfGetService() {
        return "success";
    }

    public boolean checkSign(String appid, String prefix, String params, String postSignStr, Var<String> projectId) {
        VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig vendorconfig = vendorConfigCache.getVendorConfigByAppId(appid);
        String privateKey = vendorconfig.getUserpwd();
        projectId.setValue(vendorconfig.getProjectid());
        String localSign = SecureUtil.md5(prefix + params + privateKey);
        return localSign.equalsIgnoreCase(postSignStr);
    }

}
