package com.cw.controller.notify.invoice;

import com.alibaba.fastjson.JSON;
import com.cw.pojo.common.ResultJson;
import com.cw.service.order.InvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-13
 */
@Slf4j
@RestController
@RequestMapping(value = "/invoice", method = RequestMethod.POST)
public class InvoiceNotifyController {

    @Autowired
    InvoiceService invoiceService;

    /**
     * N诺诺发票申请开具成功回调 通知更新发票开票成功
     *
     * @param request
     * @return
     */
    @PostMapping("/callback/{projectId}")
    public ResultJson invoiceCallback(@PathVariable String projectId, HttpServletRequest request) {
        String content = request.getParameter("content");
        Map map = JSON.parseObject(content, Map.class);
        String errMsg = "";
        //发票流水号
        String serialNum = (String) map.get("c_fpqqlsh");
        //商户税号
        String saleTaxNum = (String) map.get("c_saletaxnum");
        String status = (String) map.get("c_status");//发票状态 1标识成功开票
        String data = JSON.toJSONString(map);
        log.info("项目：" + projectId + "回调发票信息：" + data);
        invoiceService.invoiceCallback(serialNum, saleTaxNum, projectId, status, data);
        return ResultJson.ok();
    }

    /**
     * 诺诺发票红字确认单申请接口回调
     *
     * @param request
     * @return
     */
    @PostMapping("redConfirm/callback/{projectId}")
    public ResultJson invoiceRedConfirmCallback(@PathVariable String projectId, HttpServletRequest request) {
        String content = request.getParameter("content");
        Map map = JSON.parseObject(content, Map.class);
        String data = JSON.toJSONString(map);
        log.info("项目：" + projectId + "回调红字确认单信息/自动开红票：" + data);
        invoiceService.invoiceRedConfirmCallback(projectId, data);
        return ResultJson.ok();
    }

    @PostMapping("/jlCallback/{projectId}")
    public ResultJson jlInvoiceCallback(@PathVariable String projectId, @RequestBody String message) {
        System.out.println("JL发票响应");
        System.out.println(message);
        //String errMsg = "";
        ////发票流水号
        //String serialNum = (String) map.get("c_fpqqlsh");
        ////商户税号
        //String saleTaxNum = (String) map.get("c_saletaxnum");
        //String status = (String) map.get("c_status");//发票状态 1标识成功开票
        //String data = JSON.toJSONString(map);
        //log.info("项目：" + projectId + "回调发票信息：" + data);
        //invoiceService.invoiceCallback(serialNum, saleTaxNum, projectId, status, data);
        return ResultJson.ok();
    }


}
