package com.cw.controller.notify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.notify.zj.*;
import com.cw.service.notify.NotifyHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/18 16:40
 **/
@RestController
@RequestMapping(value = "/vendor", method = RequestMethod.POST)
public class VendorNotifyController {

    @Autowired
    private NotifyHandlerService notifyHandlerService;

    /**
     * @param msg
     * @return
     */
    @PostMapping("/zjnotify/price/{appid}")
    public ResultJson reciveZJprice(@RequestParam String msg, @PathVariable String appid) {
        ZjPlatformNotifyMsg<RateNotifyData> notifyMsg = JSON.parseObject(msg, new TypeReference<ZjPlatformNotifyMsg<RateNotifyData>>() {
        });
        return notifyHandlerService.handlerZjPriceNotify(appid, notifyMsg, msg);
    }

    /**
     * @param msg
     * @return
     */
    @PostMapping("/zjnotify/resource/{appid}")
    public ResultJson reciveZJresource(@RequestParam String msg, @PathVariable String appid) {
        ZjPlatformNotifyMsg<GridNotifyData> notifyMsg = JSON.parseObject(msg, new TypeReference<ZjPlatformNotifyMsg<GridNotifyData>>() {
        });
        return notifyHandlerService.handlerZjSkuNotify(appid, notifyMsg, msg);
    }

    /**
     * @param msg
     * @return
     */
    @PostMapping("/zjnotify/product/{appid}")
    public ResultJson reciveZJProduct(@RequestParam String msg, @PathVariable String appid) {
        ZjPlatformNotifyMsg<ProductNotifyData> notifyMsg = JSON.parseObject(msg, new TypeReference<ZjPlatformNotifyMsg<ProductNotifyData>>() {
        });
        return notifyHandlerService.handlerZjProductNotify(appid, notifyMsg, msg);
    }

    /**
     * @param msg
     * @return
     */
    @PostMapping("/zjnotify/order/{appid}")
    public ResultJson reciveZJorder(@RequestParam String msg, @PathVariable String appid) {

        ZjPlatformNotifyMsg<ColOrderNotifyData> notifyMsg = JSON.parseObject(msg, new TypeReference<ZjPlatformNotifyMsg<ColOrderNotifyData>>() {
        });
        return notifyHandlerService.handlerZjOrderNotify(appid, notifyMsg, msg);
    }

    @PostMapping("/zjnotify/ticket/{appid}")
    public ResultJson reciveZJticket(@RequestParam String msg, @PathVariable String appid) {

        ZjPlatformNotifyMsg<ColTicketNotifyData> notifyMsg = JSON.parseObject(msg, new TypeReference<ZjPlatformNotifyMsg<ColTicketNotifyData>>() {
        });
        return notifyHandlerService.handlerZjTicketNotify(appid, notifyMsg, msg);
    }

    @GetMapping("/pftnotify/ticket/{appid}")
    public String recivePftNotify(@PathVariable String appid) {

        return "success";
    }


}
