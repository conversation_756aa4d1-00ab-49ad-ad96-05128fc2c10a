package com.cw.controller.notify;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.config.confyaml.VendorYaml;
import com.cw.config.confyaml.node.Conf_Xms;
import com.cw.outsys.pojo.xms.RateMapping;
import com.cw.outsys.pojo.xms.nofity.XmsRTNotifyRequest;
import com.cw.outsys.pojo.xms.nofity.XmsRoomAvlNotify;
import com.cw.outsys.pojo.xms.nofity.XmsRoomBlockNotify;
import com.cw.outsys.pojo.xms.nofity.XmsRoomRateNotify;
import com.cw.pojo.common.ResultJson;
import com.cw.service.notify.XmsNotifyHandlerService;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.VendorType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe 接收XMS消息推送房价 库存 订单等消息
 * <AUTHOR> Tony Leung
 * @Create on 2024-10-18
 */
@RestController
@RequestMapping(value = "/xms/notify", method = RequestMethod.POST)
public class XMSNotifyController {


    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    XmsNotifyHandlerService xmsNotifyHandlerService;


    private Logger logger = LoggerFactory.getLogger(this.getClass());
    private Logger slowlog = LoggerFactory.getLogger("slowlog");


    @RequestMapping(value = "/order/{appid}", method = RequestMethod.POST)
    public ResultJson orderNotify(@RequestBody String bodyStr, HttpServletRequest request, @PathVariable String appid) {
        return xmsNotifyHandlerService.handlerXmsOrderNotify(appid, bodyStr, request);

    }


    /**
     * 订单核销通知
     *
     * @param request
     * @param appid
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/resources/room/{appid}", method = RequestMethod.POST)
    public ResultJson RoomNotify(@RequestBody String bodyStr, HttpServletRequest request, @PathVariable String appid) {
        try {
            XmsRTNotifyRequest requestBody = JSON.parseObject(bodyStr, XmsRTNotifyRequest.class);
            String reqType = requestBody.getReqtype();
            Object content = requestBody.getContent();
            if (ObjectUtil.isEmpty(content)) {
                return ResultJson.ok();
            }

            VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
            VendorYaml vendorYaml = vendorConfigCache.getVendorYaml(VendorType.XMSPMS);
            if (vendorYaml == null) {
                logger.info("xmsvendor yaml未配置");
                slowlog.info("xmsvendor yaml未配置");
                return ResultJson.ok();
            }
            Conf_Xms xmsConf = vendorYaml.getXms();
            if ("RTAV".equals(reqType)) {
                if (StringUtils.isNotBlank(xmsConf.getBlockcode())) {
                    //logger.info("xms配置blockcode，不处理普通房量消息");
                    //slowlog.info("xms配置blockcode，不处理普通房量消息");
                    return ResultJson.ok();
                }
                XmsRoomAvlNotify roomAvlNotify = JSON.parseObject(content.toString(), XmsRoomAvlNotify.class);
                return xmsNotifyHandlerService.handlerXmsSkuNotify(appid, roomAvlNotify, request, bodyStr);
            } else if ("RATE".equals(reqType)) {
                XmsRoomRateNotify roomRateNotify = JSON.parseObject(content.toString(), XmsRoomRateNotify.class);
                String rateCode = roomRateNotify.getRatecode();

                List<RateMapping> rateMappings = xmsConf.getRpmapping();
                //如果推送消息
                if (CollectionUtil.isNotEmpty(rateMappings)) {
                    List<String> rateCodes = rateMappings.stream().map(RateMapping::getRatecode).collect(Collectors.toList());
                    if (!rateCodes.contains(rateCode)) {
                        //logger.info("xms推送房价信息的房价码不在配置范围，推送房价码：{}", rateCodes);
                        //slowlog.info("xms推送房价信息的房价码不在配置范围，推送房价码：{}", rateCodes);
                        return ResultJson.ok();
                    }
                }
                return xmsNotifyHandlerService.handlerXmsPriceNotify(appid, roomRateNotify, request, bodyStr);

            } else if ("BLOCK".equals(reqType)) {
                //签名通过DES解码获取请求Body
                XmsRoomBlockNotify roomBlockNotify = JSON.parseObject(content.toString(), XmsRoomBlockNotify.class);
                String blockCode = roomBlockNotify.getBlockcode();
                //判断锁房代码是否配置 不是配置则直接返回
                if (StringUtils.isNotBlank(blockCode)) {
                    if (!blockCode.equals(xmsConf.getBlockcode())) {
                        //logger.info("xms推送锁房房量信息blockCode:{}与配置锁房代码不相等，不处理", blockCode);
                        //slowlog.info("xms推送锁房房量信息blockCode:{}与配置锁房代码不相等，不处理", blockCode);
                        return ResultJson.ok();
                    }
                }
                return xmsNotifyHandlerService.handlerXmsBlockNotify(appid, roomBlockNotify, request, bodyStr);

            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return ResultJson.ok();
    }


}
