package com.cw.controller;

import com.alibaba.fastjson.JSON;
import com.cw.core.report.base.RpInputCompent;
import com.cw.core.report.proc.RpProc_Sample;
import com.cw.entity.Rptemplate;
import com.cw.mapper.RptemplateMapper;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.statistic.req.RpPostForm;
import com.cw.service.report.ReportService;
import com.cw.utils.SpringUtil;
import com.cw.utils.rp.RpType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/3 11:49
 **/
@Slf4j
@RestController
@RequestMapping(value = "/report")
public class TestReportController {

    @Autowired
    ReportService reportService;


    @RequestMapping(value = "/loadReportForm", method = RequestMethod.GET)
    public ResultJson<List<RpInputCompent>> loadReportForm() {
        List<RpInputCompent> compents = reportService.getReportForm(getTestRpPostForm("1"));//   reportService.getReportForm(rpPostForm);


        //try {
        //    Thread.sleep(3000L);
        //    //模拟测试
        //} catch (InterruptedException e) {
        //    throw new RuntimeException(e);
        //}
        return ResultJson.ok().data(compents);
    }

    @RequestMapping(value = "/testReport", method = RequestMethod.POST)
    public ResultJson testReport(@RequestBody RpPostForm rpPostForm) {
        RpProc_Sample procSample = new RpProc_Sample();
        Rptemplate rptemplate = new Rptemplate();
        rptemplate.setRpid("1");
        rptemplate.setProjectid("001");
        rptemplate.setDescription("测试SQL第一张");
        rptemplate.setSqlstr("select userid,mobileno from  app_user ");
        rptemplate.setInputform(JSON.toJSONString(procSample.getinitQueryForm()));
        rptemplate.setColumn(JSON.toJSONString(procSample.getInitPrintColumn()));
        rptemplate.setType(RpType.SQL.name());

        RptemplateMapper rptemplateMapper = SpringUtil.getBean(RptemplateMapper.class);
        //Rptemplate rpt= rptemplateMapper.save(rptemplate);
        //log.info("sqlid {}", rpt.getSqlid());

        return ResultJson.ok();
    }


    @RequestMapping(value = "/testProduceReport", method = RequestMethod.GET)
    public ResultJson testProduceReport() {

        ResultJson resultJson = reportService.excuteProduceReport(getTestRpPostForm("2"));

        return resultJson;
    }

    @RequestMapping(value = "/testSqlReport", method = RequestMethod.GET)
    public ResultJson testSqlReport() {
        ResultJson resultJson = reportService.executeSqlReport(getTestRpPostForm("1"));
        return resultJson;
    }

    private RpPostForm getTestRpPostForm(String rpid) {

        RpPostForm rpPostForm = new RpPostForm();
        rpPostForm.setRpId(rpid);
        return rpPostForm;
    }


}
