package com.cw.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.SpelFomulaFactory;
import com.cw.arithmetic.events.SysPushEvent;
import com.cw.arithmetic.func.Var;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.cache.impl.ShopsiteCache;
import com.cw.cache.impl.TicketCache;
import com.cw.core.CoreAvl;
import com.cw.core.CoreCache;
import com.cw.core.CorePrice;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.CrmVendorSwitcher;
import com.cw.core.orderhandler.OrderVendorSwitcher;
import com.cw.core.orderhandler.ParkingVendorSwitcher;
import com.cw.core.platform.wechat.WxGlobalProperties;
import com.cw.core.platform.wechat.WxMaConfiguration;
import com.cw.core.platform.wechat.WxMpConfiguration;
import com.cw.core.vendor.crm.ZjCrmVendor;
import com.cw.core.vendor.order.ticket.PftTicketVendor;
import com.cw.core.vendor.parking.ParkingVendorHandler;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.BookingrsMapper;
import com.cw.mapper.Op_userMapper;
import com.cw.mapper.RratedetMapper;
import com.cw.mapper.TratedetMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.request.crm.StdAddCustomerRequest;
import com.cw.outsys.stdop.request.crm.StdUpdCustomerRequest;
import com.cw.outsys.stdop.response.*;
import com.cw.outsys.stdop.response.crm.StdAddCustomerResponse;
import com.cw.outsys.stdop.response.crm.StdUpdCustomerResponse;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.common.core.CachePrice;
import com.cw.pojo.dto.common.res.Common_Pic_Res;
import com.cw.pojo.dto.conf.req.roomtype.RmtypeResourcePriceSaveReq;
import com.cw.service.config.room.RoomTypeService;
import com.cw.service.config.sys.ManagerToolService;
import com.cw.service.mq.DelayQueueService;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.bussiness.AppNotifyMsg;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushTicketMsg;
import com.cw.service.mq.msgmodel.bussiness.Bussness_QrCodeWebSocketMsg;
import com.cw.service.sync.impl.FetchSyncServiceImpl;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.datetime.DateStyle;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.menus.ProductQrcodePathFactory;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.cw.utils.pay.PayUtil;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/3 11:49
 **/

@RestController
@Slf4j
@RequestMapping(value = "/test")
public class TestController {

    @Autowired
    WxGlobalProperties wxGlobalProperties;

    @Autowired
    RoomTypeService roomTypeService;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    ManagerToolService managerToolService;

    @Autowired
    RratedetMapper rratedetMapper;

    @Autowired
    TratedetMapper tratedetMapper;

    @Autowired
    DelayQueueService delayQueueService;


    @Autowired
    private DaoLocal<T> daoLocal;
    @Autowired
    private ParkingVendorSwitcher parkingVendorSwitcher;

    @RequestMapping("/getMaxTestId")
    public ResultJson TEST() {
        Common_Pic_Res pic_res = managerToolService.generateQrList("001", 4, "WXAPP", "TESTTICKET2", ProductQrcodePathFactory.QRSIZE_ONLINE);
        return ResultJson.ok().data(pic_res).msg("权限测试结果");
    }

    @RequestMapping("/testParking")
    public ResultJson gensales() {

        ParkingVendorHandler handler = parkingVendorSwitcher.getVendorHandler(VendorType.DSPARKING);
        StdQueryParkingStatusRequest request = new StdQueryParkingStatusRequest();
        request.setProjectId("001");


        StdParkingStatusResponse response = handler.queryParkingStatus(request);

        return ResultJson.ok().data(response);
    }

    @RequestMapping(value = "/printMemory", method = RequestMethod.GET)
    public ResultJson TEST2() {
        long freeMemory = Runtime.getRuntime().freeMemory() / 1024 / 1024;
        long maxMemory = Runtime.getRuntime().maxMemory() / 1024 / 1024;
        long totalMemory = Runtime.getRuntime().totalMemory() / 1024 / 1024;

        String memoryInfo = StrUtil.format("freeMemory:{}M  totalMemory:{}M maxMemory:{}M", freeMemory, totalMemory, maxMemory);
        Op_userMapper opUserMapper = SpringUtil.getBean(Op_userMapper.class);
        List<Op_user> ops = opUserMapper.getOp_usersByProjectidAndRoleid("001", "supervisor");
        log.info("ops:{}", ops.size());

        Luggage_rs rs = new Luggage_rs();
        rs.setProjectid("001");
        rs.setOrderid("XL241230800004");
        rs.setGuestname("xxx");
        rs.setTarget_site("CS");
        Sub_usermsg subUserMsg = daoLocal.find(Sub_usermsg.class, 11L);
        if (subUserMsg != null) {//查看这个托运单是否有订阅消息
            AppNotifyMsg msg = new AppNotifyMsg();
            msg.setProjectId(rs.getProjectid());
            msg.setKeyId(rs.getOrderid());
            msg.setWxOpenId(subUserMsg.getTargetid());
            msg.setTemplateId(subUserMsg.getTemplateid());
            msg.setTriggerType(subUserMsg.getTriggertype());

            ShopsiteCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SHOPSITE);
            Shopsite site = cache.getRecord(rs.getProjectid(), rs.getTarget_site());

            msg.setP1(rs.getOrderid());//订单号
            msg.setP2(rs.getGuestname());//托运人
            msg.setP3("已送达");//状态
            msg.setP4(site == null ? rs.getTarget_site() : site.getDescription());//目的地
            msg.setP5(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm"));//送达时间

            delayQueueService.addDelayNotify(msg, 1, TimeUnit.SECONDS);

            log.info("开始推送行李送达消息通知");
        }

        return ResultJson.ok().msg("内存测试结果" + memoryInfo);
    }

    @RequestMapping(value = "/time", method = RequestMethod.GET)
    public ResultJson mqexpire(@RequestParam String outtradeNo) {
  /*      String bookingExp = "'您已预订:'+#pdesc('Z',#booking_rs.product,#booking_rs.projectid)+'套餐'+#booking_rs.anz+'套,金额'+#booking_rs.amount+'元,预订号:'+#booking_rs.bookingid+' .套餐最晚取消需提前一天23:59前操作订单，逾期不可取消，请您确认入住时间并如期入住，感谢您的支持与理解！'";
        //String bookingExp2="'您已预订'+#gdesc('Z',#booking_rs.product,#booking_rs.projectid,false)+'入住'+#pdesc('R',#booking_rs.product,#booking_rs.projectid)+',￥' +#n2s('ROOM',#booking_rs.arrdate,#booking_rs.product,#booking_rs.projectid)+'元/间/晚，总房费：'+#booking_rs.amount+'元，预订号：'+#booking_rs.bookingid+'。客房保留至到店当天16:00。'";

        String booleanExp = "#booking_rs.ptype==' Z '";


        Booking_rs bookingRs = daoLocal.getObject("from Booking_rs where bookingid=?1", outtradeNo);
        if (bookingRs == null) {
            return ResultJson.ok();
        }
        *//*new Booking_rs();
        rs.setProjectid("001");
        rs.setAmount(BigDecimal.ONE);
        rs.setPtype(ProdType.TAOCAN.val());
        rs.setProduct("1321");
        rs.setArrdate(CalculateDate.stringToDate("2024-05-20"));
        rs.setDeptdate(CalculateDate.stringToDate("2024-05-21"));
        rs.setBookingid("22200022312314");
        rs.setAnz(1);*//*

        SysPushEvent sysPushEvent = new SysPushEvent(bookingRs, MsgTriggerEnum.ORDERCONFIRM, bookingRs.getTel(), "", bookingRs.getBookingid(), bookingRs.getProjectid());
        SpringUtil.getApplicationContext().publishEvent(sysPushEvent);

        boolean lsend = SpelFomulaFactory.getBooleanFomulaResult(booleanExp, bookingRs);
        String output1 = SpelFomulaFactory.getStringFomulaResult(bookingExp, bookingRs);

        log.info("是否发送短信:{}", lsend);
        log.info("短信内容1:{}", output1);

        //SysPushEvent pushEvent=new SysPushEvent();
        //pushEvent.setData();
*/

        WxMpTemplateData mpTemplateData1 = new WxMpTemplateData("character_string1", "58757645");//订单号
        WxMpTemplateData mpTemplateData2 = new WxMpTemplateData("thing2", "牛啊老弟");//服务名称
        WxMpTemplateData mpTemplateData3 = new WxMpTemplateData("amount4", new BigDecimal("555.00").toPlainString());//支付金额
        WxMpTemplateData mpTemplateData4 = new WxMpTemplateData("time5", CalculateDate.dateFormat(new Date(), DateStyle.YYYY_MM_DD_HH_MM_SS.getValue()) + "");//支付时间

        List<WxMpTemplateData> data = Lists.newArrayList(mpTemplateData1, mpTemplateData2, mpTemplateData3, mpTemplateData4);

        WxMpTemplateMessage.MiniProgram miniProgram = new WxMpTemplateMessage.MiniProgram();
        miniProgram.setAppid("wx477b242e9aea4815");
        miniProgram.setPagePath("pagea/room/list");

        WxMpTemplateMessage mpTemplateMessage = new WxMpTemplateMessage();
        mpTemplateMessage.setTemplateId("aOi4OhHy6paaaOBqThUB8cGlxPxkJabD77lxG13HqEw");
        mpTemplateMessage.setToUser("oknQu589ZO3vOPYPFE9SAe6eWPAs");
        mpTemplateMessage.setMiniProgram(miniProgram);
        mpTemplateMessage.setData(data);


        WxMpService wxMpService = WxMpConfiguration.getMpService("wxa4031c49bd52973e");
        try {
            String msg = wxMpService.getTemplateMsgService().sendTemplateMsg(mpTemplateMessage);

            System.out.println(msg);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }


        return ResultJson.ok().msg(StrUtil.format("{},{},{}", "msg", "1", "2"));
    }

    @RequestMapping(value = "/tuikuan", method = RequestMethod.GET)
    public ResultJson refund(String orderid, String pwd) {
//        20220121655700006
        return ResultJson.ok();


//        WxPayService wxPayService = WxPayConfiguration.getProjectPayService(projectid);
//        WxPayUnifiedOrderV3Request v3PayRequest = new WxPayUnifiedOrderV3Request();
//        v3PayRequest.setAppid(appid);//APPid
//        v3PayRequest.setMchid(wxGlobalProperties.getWxPayConfig(appid).getMchId());//直连商户号


    }

    @RequestMapping(value = "/initRoomtype", method = RequestMethod.GET)
    public ResultJson initRoom() {
        //RoomTypeCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
        //List<Roomtype> roomtypeList = cache.getDataList("001");
        //if (CollectionUtil.isNotEmpty(roomtypeList)) {
        //    RratedetDao rratedetDao = SpringUtil.getBean(RratedetDao.class);
        //    RroomsDao rroomsDao = SpringUtil.getBean(RroomsDao.class);
        //    for (Roomtype roomtype : roomtypeList) {
        //        //异步处理
        //        rratedetDao.batchInsertRratedet(roomtype);
        //        rroomsDao.batchInsertRrooms(roomtype);
        //    }c
        //}
        return ResultJson.ok();
    }

    @RequestMapping(value = "/initTicket", method = RequestMethod.GET)
    public ResultJson initPrice() {
        //TicketCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
        //List<Ticket> ticketList = cache.getDataList("001");
        //if (CollectionUtil.isNotEmpty(ticketList)) {
        //    TratedetDao tratedetDao = SpringUtil.getBean(TratedetDao.class);
        //    RticketsDao rticketsDao = SpringUtil.getBean(RticketsDao.class);
        //    for (Ticket ticket : ticketList) {
        //        //异步处理
        //        tratedetDao.batchInsertTratedet(ticket);
        //        rticketsDao.batchInsertRtickets(ticket);
        //    }
        //}
        return ResultJson.ok();
    }


    @RequestMapping(value = "/init", method = RequestMethod.GET)
    public ResultJson initCache() {
        CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
        String projectId = "001";
        RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
        List<Roomtype> roomtypeList = roomTypeCache.getDataList("001");
        Date startDate = CalculateDate.stringToDate("2022-01-19");
        Date endDate = CalculateDate.stringToDate("2022-05-30");
        for (Roomtype roomtype : roomtypeList) {
            corePrice.calc2Cache(projectId, CorePrice.getRateCode(projectId, ""), ProdType.ROOM, roomtype.getCode(), startDate, endDate);
        }

        TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
        List<Ticket> ticketList = ticketCache.getDataList("001");
        for (Ticket ticket : ticketList) {
            corePrice.calc2Cache(projectId, CorePrice.getRateCode(projectId, ""), ProdType.TICKET, ticket.getCode(), startDate, endDate);
        }
        return ResultJson.ok();
    }

    @RequestMapping(value = "/initsku", method = RequestMethod.GET)
    public ResultJson initSkuCache() {
        CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
        String projectId = "001";
        RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
        List<Roomtype> roomtypeList = roomTypeCache.getDataList("001");
        Date startDate = CalculateDate.stringToDate("2022-01-19");
        Date endDate = CalculateDate.stringToDate("2022-05-30");
        for (Roomtype roomtype : roomtypeList) {
//            corePrice.calc2Cache(projectId,CorePrice.getRateCode(projectId,""),ProdType.ROOM,roomtype.getCode(),startDate,endDate);
            coreAvl.calc2Cache(projectId, roomtype.getCode(), startDate, endDate, ProdType.ROOM, "");
        }
        TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
        List<Ticket> ticketList = ticketCache.getDataList("001");
        for (Ticket ticket : ticketList) {
            coreAvl.calc2Cache(projectId, ticket.getCode(), startDate, endDate, ProdType.TICKET, "");
        }
        return ResultJson.ok();
    }

    @RequestMapping(value = "/testpricecache", method = RequestMethod.GET)
    public ResultJson TESTcache() {
        RmtypeResourcePriceSaveReq req = new RmtypeResourcePriceSaveReq();
        req.setRmtype("SZ12");
        req.setPrice(BigDecimal.valueOf(2000L));
        req.setStartDate(CalculateDate.stringToDate("2022-01-04"));
        req.setEndDate(CalculateDate.stringToDate("2022-01-30"));
        roomTypeService.batchRoomtype(req);
        return ResultJson.ok();
    }

    @RequestMapping(value = "/testroomcache", method = RequestMethod.GET)
    public ResultJson TESTRoomcache() {
        RmtypeResourcePriceSaveReq req = new RmtypeResourcePriceSaveReq();
        req.setRmtype("SZ12");
        req.setAvl(10);
        req.setStartDate(CalculateDate.stringToDate("2022-01-04"));
        req.setEndDate(CalculateDate.stringToDate("2022-01-30"));
        roomTypeService.batchRoomtype(req);
        return ResultJson.ok();
    }

    @RequestMapping(value = "/testcache", method = RequestMethod.GET)
    public ResultJson Testcache() {
        RmtypeResourcePriceSaveReq req = new RmtypeResourcePriceSaveReq();
        req.setRmtype("SZ12");
        req.setAvl(10);
        req.setStartDate(CalculateDate.stringToDate("2022-01-04"));
        req.setEndDate(CalculateDate.stringToDate("2022-01-30"));
        roomTypeService.batchRoomtype(req);
        return ResultJson.ok();
    }

    @RequestMapping(value = "/testcacheget", method = RequestMethod.GET)
    public ResultJson testGet() {
        CoreCache coreCache = SpringUtil.getBean(CoreCache.class);
        Stopwatch stopwatch = Stopwatch.createStarted();
        for (int i = 0; i < 10; i++) {
            List<CachePrice> cachePrices = coreCache.calcPrice_cache("001", "SKJ", ProdType.ROOM.val(), "SZ12",
                    CalculateDate.stringToDate("2022-01-04"), CalculateDate.stringToDate("2022-01-04"), "");
//            for (CachePrice cachePrice : cachePrices) {
//                System.out.println(cachePrice.getDate()+"  "+cachePrice.getPrice());
//            }
        }
        System.out.println("平均耗时结果：" + stopwatch.stop() + "ms");
//        coreCache.calcPrice_cache("SZ12", CalculateDate.stringToDate("2021-01-04"), CalculateDate.stringToDate("2021-01-30"));
//        CorePrice.getRateCode(projectId, roomType);
        return ResultJson.ok();
    }


    @RequestMapping(value = "/testcrm", method = RequestMethod.GET)
    public ResultJson testcrm() throws DefinedException {
        CrmVendorSwitcher switcher = SpringUtil.getBean(CrmVendorSwitcher.class);
        ZjCrmVendor crmVendor = (ZjCrmVendor) switcher.getVendorHandler(VendorType.ZJ_CRM);
        //获取模板
        StdAddCustomerRequest request = new StdAddCustomerRequest();
        request.setProjectId("001");
        request.setUnionid("ZAAA");
        request.setPhone("19907791122");
        request.setCustomer_name("陈某人");

        StdAddCustomerResponse response = crmVendor.addCrmMember(request);
        String crmno = response.getCrmno();
        System.out.println(response.getCrmno());


        StdUpdCustomerRequest request1 = new StdUpdCustomerRequest();
        request1.setProjectId("001");
        request1.setCustomer_name("张XX");
        request1.setBirthday(LocalDate.now());
        request1.setCustomer_no(crmno);

        StdUpdCustomerResponse response1 = crmVendor.updCrmMember(request1);

        return ResultJson.ok().data(response.getCrmno());
    }

    @RequestMapping(value = "/testcrmpost", method = RequestMethod.GET)
    public ResultJson testcrmqq() throws DefinedException {

        Var<String> specStr = new Var<String>();
        String msg = ContentCacheTool.getGifRsProductDesc("G000001:trr2vv:lq9do5", "001", specStr);

        log.info("{} {}", msg, specStr.getValue());


        return ResultJson.ok().msg(msg);
    }

    @GetMapping(value = "testwxnofity")
    public ResultJson testwxnofity() throws DefinedException {
        WxMaSubscribeMessage subscribeMessage = new WxMaSubscribeMessage();
        subscribeMessage.setPage("pages/home/<USER>");
        subscribeMessage.setTemplateId("DHn1zZ98Nrk72kFw0PgUfso1ZDOmimwQ0P_-RZE2JgU");
        subscribeMessage.setToUser("o0QQg4yudKpCVIttj_Db4v7C4EFY");

        WxMaSubscribeMessage.MsgData msgData = new WxMaSubscribeMessage.MsgData();
        msgData.setName("thing1");
        msgData.setValue("问题" + RandomUtil.randomString(10));
        subscribeMessage.addData(msgData);

        WxMaSubscribeMessage.MsgData msgData1 = new WxMaSubscribeMessage.MsgData();
        msgData1.setName("thing2");
        msgData1.setValue("答复" + RandomUtil.randomString(10));
        subscribeMessage.addData(msgData1);

        WxMaService wxMaService = WxMaConfiguration.getMaService("wx477b242e9aea4815");

        try {
            wxMaService.getMsgService().sendSubscribeMsg(subscribeMessage);
            log.info("发送订阅成功");
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
//        subscribeMessage.addData()
        return ResultJson.ok();
    }

    @GetMapping(value = "testsmssend", produces = "application/json;charset=UTF-8")
    public ResultJson 短信接口() throws DefinedException {
        BookingrsMapper mapper = SpringUtil.getBean(BookingrsMapper.class);
        Booking_rs rs = mapper.findBooking_rsByBookingid("20220617208100002");
        SysPushEvent sysPushEvent = new SysPushEvent(rs, MsgTriggerEnum.REFUNDREFUSE, rs.getTel(), "", rs.getBookingid(), rs.getProjectid());
        SpringUtil.getApplicationContext().publishEvent(sysPushEvent);
        System.out.println(" 发起短信 context");
        return ResultJson.ok();

    }

    @GetMapping(value = "testqr", produces = "application/json;charset=UTF-8")
    public ResultJson 发码测() throws DefinedException {
        String bookingId = "Y20250325234800002";

        Booking_rs rs = new Booking_rs();
        rs.setBookingid(bookingId);
        rs.setProjectid("001");


        Bussness_PushTicketMsg ticketMsg = new Bussness_PushTicketMsg();
        ticketMsg.setBookingId(rs.getBookingid());
        ticketMsg.setProjectId(rs.getProjectid());
        ticketMsg.setScene(PayUtil.ACT_SCENE);
        rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.SENDTICKETQR),
                JSON.toJSONString(ticketMsg));

        //核销检票消息
        //Bussness_QrCodeWebSocketMsg msg = new Bussness_QrCodeWebSocketMsg();
        //msg.setQrcode(bookingId);
        //msg.setProjectid("001");
        ////发送队列消息
        //rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.QRCODEWEB),
        //        JSON.toJSONString(msg));


        return ResultJson.ok();
    }




    @GetMapping(value = "extendRratedet")
    public ResultJson extendRratedet(String roomtype) throws DefinedException {
        FetchSyncServiceImpl fetchSyncService = SpringUtil.getBean(FetchSyncServiceImpl.class);
        //String projectId = GlobalContext.getCurrentProjectId();
        String projectId = "001";
        String rateCode = CorePrice.getRateCode(projectId, "");//价格码
        Date startDate = DateUtil.beginOfDay(new Date());
        Date endDate = SpringUtil.getBean(CoreAvl.class).getRroomsMaxDate(projectId);//房量最大日期
        Rratedet endRratedet = SpringUtil.getBean(RratedetMapper.class).findRratedetByLastDatum(roomtype, projectId);
        if (endRratedet == null) { //如果没有房价设定历史
            endRratedet = new Rratedet();
            endRratedet.setProjectid(projectId);
            endRratedet.setDatum(DateUtil.offsetDay(startDate, -1));
            endRratedet.setCode(rateCode);
            endRratedet.setProductcode(roomtype);
            endRratedet.setRate(BigDecimal.valueOf(999L));
        }
        int dasy = (int) DateUtil.between(startDate, endDate, DateUnit.DAY);
        List<Rratedet> rmPriceList = new ArrayList<>();
        //todo 周末价和非周末价赋值
        for (int i = 0; i < dasy; i++) {
            Date datum = DateUtil.offsetDay(startDate, i);
            Rratedet rratedet = new Rratedet();
            rratedet.setCode(rateCode);
            rratedet.setProjectid(projectId);
            rratedet.setRate(endRratedet.getRate());
            rratedet.setDatum(datum);
            rratedet.setProductcode(endRratedet.getProductcode());
            rmPriceList.add(rratedet);

        }
        updateRmPriceList(rateCode, projectId, roomtype, startDate, endDate, rmPriceList);
        System.out.println("更新房价：" + JSON.toJSON(rmPriceList));
        fetchSyncService.syncRoomTypePrice(rmPriceList);
        //更新房型价格缓存
        CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
        for (Rratedet rratedet : rmPriceList) {
            corePrice.calc2Cache(projectId, rateCode, ProdType.ROOM, rratedet.getProductcode(), startDate, endDate);
        }

        return ResultJson.ok();

    }

    private void updateRmPriceList(String rateCode, String projectId, String roomtype, Date startDate, Date endDate, List<Rratedet> rmPriceList) {
        List<Rratedet> dbRratedetList = rratedetMapper.queryRmtypeRateList(projectId, rateCode, roomtype, startDate, endDate);
        if (CollectionUtil.isNotEmpty(dbRratedetList)) {
            for (Rratedet dbitem : dbRratedetList) {
                for (Rratedet item : rmPriceList) {
                    //判断code projectid productcode datum都相等 则更新数据，否则默认sqlid为0查询数据
                    if (item.getCode().equals(dbitem.getCode()) && item.getProjectid().equals(dbitem.getProjectid()) &&
                            item.getProductcode().equals(dbitem.getProductcode()) && item.getDatum().equals(dbitem.getDatum())) {
                        item.setSqlid(dbitem.getSqlid());
                    }
                }
            }

        }
    }

    @GetMapping(value = "extendTratedet")
    public ResultJson extendTratedet(String ticket) throws DefinedException {
        FetchSyncServiceImpl fetchSyncService = SpringUtil.getBean(FetchSyncServiceImpl.class);
        //String projectId = GlobalContext.getCurrentProjectId();
        String projectId = "001";
        String rateCode = CorePrice.getRateCode(projectId, "");//价格码
        Date startDate = DateUtil.beginOfDay(new Date());
        Date endDate = SpringUtil.getBean(CoreAvl.class).getRroomsMaxDate(projectId);//房量最大日期
        Tratedet endTratedet = SpringUtil.getBean(TratedetMapper.class).findTratedetByLastDatum(ticket, projectId);
        if (endTratedet == null) { //如果没有票价设定历史
            endTratedet = new Tratedet();
            endTratedet.setProjectid(projectId);
            endTratedet.setDatum(DateUtil.offsetDay(startDate, -1));
            endTratedet.setCode(rateCode);
            endTratedet.setProductcode(ticket);
            endTratedet.setRate(BigDecimal.valueOf(999L));
        }
        int dasy = (int) DateUtil.between(startDate, endDate, DateUnit.DAY);
        List<Tratedet> ticketPriceList = new ArrayList<>();
        //todo 周末价和非周末价赋值
        for (int i = 0; i < dasy; i++) {
            Date datum = DateUtil.offsetDay(startDate, i);
            Tratedet tratedet = new Tratedet();
            tratedet.setCode(rateCode);
            tratedet.setProjectid(projectId);
            tratedet.setRate(endTratedet.getRate());
            tratedet.setDatum(datum);
            tratedet.setProductcode(endTratedet.getProductcode());
            ticketPriceList.add(tratedet);

        }
        if (CollectionUtil.isNotEmpty(ticketPriceList)) {
            updateTicketPriceList(rateCode, projectId, ticket, startDate, endDate, ticketPriceList);
            System.out.println("更新票价：" + JSON.toJSON(ticketPriceList));
            fetchSyncService.syncTicketPrice(ticketPriceList);
            //更新票型价格缓存
            CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
            for (Tratedet tratedet : ticketPriceList) {
                corePrice.calc2Cache(projectId, rateCode, ProdType.TICKET, tratedet.getProductcode(), startDate, endDate);
            }
        }

        return ResultJson.ok();

    }

    private void updateTicketPriceList(String rateCode, String projectId, String ticket, Date startDate, Date endDate, List<Tratedet> ticketPriceList) {
        List<Tratedet> dbTratedetList = tratedetMapper.findTicketPriceList(rateCode, projectId, ticket, startDate, endDate);
        if (CollectionUtil.isNotEmpty(dbTratedetList)) {
            for (Tratedet dbitem : dbTratedetList) {
                for (Tratedet item : ticketPriceList) {
                    //判断code project productcode datum 则更新数据，否则默认sqlid为0查询数据
                    if (item.getCode().equals(dbitem.getCode()) && item.getProjectid().equals(dbitem.getProjectid())
                            && item.getProductcode().equals(dbitem.getProductcode()) && item.getDatum().equals(dbitem.getDatum())) {
                        item.setSqlid(dbitem.getSqlid());
                    }
                }
            }

        }
    }


    //23041310303902203521
    //@GetMapping(value = "/nnInvoice_query")
    //public ResultJson nnInvoice_query(@RequestParam("invoiceId") String invoiceId, @RequestParam("orderId") String orderId,
    //                                  @RequestParam("projectId") String projectId) throws DefinedException {
    //    InvoiceHandler invoiceHandler = SpringUtil.getBean(InvoiceHandler.class);
    //    InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);
    //    StdInvoiceQueryRequest request = new StdInvoiceQueryRequest();
    //    request.setProjectId(projectId);
    //    if (StringUtils.isNotBlank(invoiceId)) {
    //        List<String> list = new ArrayList<>();
    //        list.add(invoiceId);//流水号：23041216504202179577 23041310303902203521
    //        request.setInvoiceids(list);
    //    }
    //    if (StringUtils.isNotBlank(orderId)) {
    //        List<String> list = new ArrayList<>();
    //        list.add(orderId);//订单号：23041216504202179577 23041310303902203521
    //        request.setOrderNos(list);
    //    }
    //
    //    //request.setOrderNos(list);
    //    request.setIsOfferDetail("1");
    //    StdInvoiceQueryResponse response = invoiceVendorHandler.InvoiceQuery(request);//todo 诺诺发票开票失败重开接口问题
    //    if (response != null) {
    //        return ResultJson.ok().data(response);
    //    } else {
    //        return ResultJson.failure(ResultCode.SERVER_ERROR);
    //    }
    //}

    //@GetMapping(value = "/nnInvoice_queryRedConfirm")
    //public ResultJson nnInvoice_queryRedConfrim(@RequestParam("invoiceNo") String invoiceNo, @RequestParam("redConfirm") String redConfirm,
    //                                            @RequestParam("projectId") String projectId) throws DefinedException {
    //    InvoiceHandler invoiceHandler = SpringUtil.getBean(InvoiceHandler.class);
    //    InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler(projectId);
    //    StdInvoiceQueryRedConfirmRequest request = new StdInvoiceQueryRedConfirmRequest();
    //    request.setProjectId(projectId);
    //    if (StringUtils.isNotBlank(invoiceNo)) {
    //        request.setInvoiceNo(invoiceNo);
    //    }
    //    if (StringUtils.isNotBlank(redConfirm)) {
    //
    //        request.setRedConfirm(redConfirm);
    //    }
    //
    //    StdInvoiceQueryRedConfirmResponse response = invoiceVendorHandler.NNInvoiceQueryRedCreateConfirm(request);//todo 诺诺发票开票失败重开接口问题
    //    if (response != null) {
    //        return ResultJson.ok().data(response);
    //    } else {
    //        return ResultJson.failure(ResultCode.SERVER_ERROR);
    //    }
    //}

    //@GetMapping(value = "/nnInvoice_reCreate")
    //public ResultJson nnInvoice_ReCreate(@RequestParam("invoiceId") String invoiceId) throws DefinedException {
    //    InvoiceHandler invoiceHandler = SpringUtil.getBean(InvoiceHandler.class);
    //    InvoiceVendorHandler invoiceVendorHandler = invoiceHandler.getVendorHandler("001");
    //    StdInvoiceReCreateRequest request = new StdInvoiceReCreateRequest();
    //    request.setProjectId("001");
    //    request.setInvoiceId(invoiceId);//23041310303902203521
    //    StdInvoiceReCreateResponse response = invoiceVendorHandler.InvoiceReCreate(request);//todo 诺诺发票开票失败重开接口问题
    //    if (response != null) {
    //        return ResultJson.ok().data(response);
    //    } else {
    //        return ResultJson.failure(ResultCode.SERVER_ERROR);
    //    }
    //}

    @PostMapping(value = "/MultipartUpload")
    public ResultJson imgMainHex() throws Exception {

        Act_rs act_rs = new Act_rs();
        act_rs.setSitecode("AAA");
        act_rs.setProjectid("001");
        act_rs.setUsedate(CalculateDate.stringToDate("2025-01-01"));
        String el = "'收到新的预约'+ #gdesc('Y',#act_rs.sitecode,#act_rs.projectid,false)+' 产品'+#pdesc('Y',#act_rs.sitecode,#act_rs.projectid)+'请在管理后台进行查看'+#d2s(#act_rs.usedate)";

        String sendContent = SpelFomulaFactory.getStringFomulaResult(el, act_rs);
        log.info("测试活动预约发送内容:{}", sendContent);

        return ResultJson.ok();
    }


    @PostMapping(value = "/qrcode_socket")
    public ResultJson testsocket(@RequestParam("qrcode") String qrcode) throws Exception {
        Bussness_QrCodeWebSocketMsg msg = new Bussness_QrCodeWebSocketMsg();
        msg.setQrcode(qrcode);
        msg.setProjectid("001");
        //发送队列消息
        rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.QRCODEWEB),
                JSON.toJSONString(msg));
        return ResultJson.ok();
    }


    //@Autowired
    //SmsHandler smsHandler;
    //
    //@GetMapping(value = "/Gsms_test")
    //public ResultJson Gsms_test() throws Exception {
    //    BookingrsMapper mapper = SpringUtil.getBean(BookingrsMapper.class);
    //    Booking_rs rs = mapper.findBooking_rsByBookingid("20220629054200003");
    //    SysPushEvent sysPushEvent = new SysPushEvent(rs, MsgTriggerEnum.REFUNDREFUSE, "15278917894", "", rs.getBookingid(), rs.getProjectid());
    //    SpringUtil.getApplicationContext().publishEvent(sysPushEvent);
    //    System.out.println(" 发起短信 context");
    //    return ResultJson.ok();
    //}

    //@PostMapping(value = "/qrcode_socket")
    //public ResultJson testsocket(@RequestParam("qrcode") String qrcode) throws Exception {
    //    Bussness_QrCodeWebSocketMsg msg = new Bussness_QrCodeWebSocketMsg();
    //    msg.setQrcode(qrcode);
    //    msg.setProjectid("001");
    //    //发送队列消息
    //    rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.QRCODEWEB),
    //            JSON.toJSONString(msg));
    //    return ResultJson.ok();
    //}


    //@Resource(name = "${oss.type}")
    //OSSService ossService;
    //@Autowired
    //OSSConfig ossConfig;
    //
    //@PostMapping(value = "/osscopy")
    //public ResultJson ossCopy(@RequestBody OssCopyRequest request) throws DefinedException {
    //    List<String> mslide = new ArrayList<>();
    //    List<String> slide = new ArrayList<>();
    //    if (StringUtils.isNotBlank(request.getMslidepics())) {
    //        mslide = addList(request.getMslidepics().replaceAll(" ", ""));
    //    }
    //    if (StringUtils.isNotBlank(request.getSlidepics())) {
    //        slide = addList(request.getSlidepics().replaceAll(" ", ""));
    //    }
    //    if (CollectionUtil.isNotEmpty(slide)) {
    //        mslide.addAll(slide);
    //    }
    //    if (CollectionUtil.isNotEmpty(mslide)) {
    //        mslide = mslide.stream().distinct().collect(Collectors.toList());
    //        for (String item : mslide) {
    //            ossService.copyFile(item.trim(), request.getOlddir(), request.getNewdir());
    //        }
    //    }
    //    return ResultJson.ok();
    //}


    //@Data
    //public static class OssCopyRequest {
    //    @ApiModelProperty(value = "移动端图片，逗号分割")
    //    String mslidepics;
    //    @ApiModelProperty(value = "电脑图片，逗号分割")
    //    String slidepics;
    //    @ApiModelProperty(value = "旧产品分类目录")
    //    String olddir;
    //    @ApiModelProperty(value = "新产品分类目录")
    //    String newdir;
    //}
    //
    //public List<String> addList(String str) {
    //    str = str.replace("\n", ",");
    //    str = str.replace("\r", "");
    //    List<String> urls = Arrays.asList(str.split(","));
    //    urls = urls.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    //    return urls;
    //
    //}
    String token;
    PftTicketVendor pftTicketVendor;
    OrderVendorSwitcher switcher;
    String projectId = "001";
    Date queryStartDate = new Date();
    Date queryEndDate = new Date();
    String bookingid = "";
    String regno = "";
    @Autowired
    private WebApplicationContext context;
    @RequestMapping(value = "/TESTPFT", method = RequestMethod.GET)
    public ResultJson TESTPFT() throws DefinedException {

        OrderVendorSwitcher switcher = SpringUtil.getBean(OrderVendorSwitcher.class);

//        PftTicketVendor pftTicketVendor = (PftTicketVendor) switcher.getVendorHandler(VendorType.PFT_TICKET);
//        mvc = MockMvcBuilders.webAppContextSetup(context).build();
        switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        pftTicketVendor = (PftTicketVendor) switcher.getVendorHandler(VendorType.PFT_TICKET);
        queryStartDate = new Date();
        queryEndDate = DateUtil.offsetDay(queryStartDate, 30);
        bookingid = RandomUtil.randomString("1234567890", 10);
        regno = RandomUtil.randomString("1234567890", 10);

        //获取模板
        StdOrderData stdOrderData3 = getAtestData(5,5,5);
        StdOrderResponse stdOrderResponse3 = pftTicketVendor.createOrder(stdOrderData3);
        log.info("订单号:{}", JSON.toJSONString(stdOrderResponse3.getStdIdResult()));

        return ResultJson.ok().data(stdOrderResponse3);
    }
    public StdOrderData getAtestData(int passlen, int passportlen, int forlen) {
        String baseStr = RandomUtil.BASE_NUMBER.toUpperCase();
        String passport = RandomUtil.randomString(baseStr, passlen);
        String pass = RandomUtil.randomString(baseStr, passportlen);
        String forno = RandomUtil.randomString(baseStr, forlen);

        String testidinfo = "[{\"guestname\":\"测试\",\"idno\":\"450521199402270059\",\"idtype\":\"1\",\"mobile\":\"15278917894\"}]";

        StdOrderData stdOrderData = new StdOrderData();
        Booking_rs rs = new Booking_rs();
        rs.setBookingid("202409244304000040941");
        rs.setGuestname("测试");
        rs.setTel("15278917894");
        rs.setAmount(0.01);
        rs.setPtype(ProdType.TICKET.val());
        rs.setProjectid(projectId);
        stdOrderData.setBookingRs(rs);

        Ticket_rs ticket_rs = new Ticket_rs();
        ticket_rs.setBookingid(bookingid);
        ticket_rs.setProjectid(projectId);
        ticket_rs.setUsedate(CalculateDate.reckonDay(new Date(), 5, 0));
        ticket_rs.setTcode("CRP");//
        ticket_rs.setAmount(0.01);
        ticket_rs.setPrice(0.01);
        ticket_rs.setAnz(1);
        ticket_rs.setIdinfo(testidinfo);
        ticket_rs.setRegno(regno);
        stdOrderData.getTickets().add(ticket_rs);
        return stdOrderData;
    }
    @RequestMapping(value = "/QUERYPFT", method = RequestMethod.GET)
    public ResultJson QUERYPFT() throws DefinedException{
        switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        pftTicketVendor = (PftTicketVendor) switcher.getVendorHandler(VendorType.PFT_TICKET);
        queryStartDate = new Date();
        queryEndDate = DateUtil.offsetDay(queryStartDate, 30);
        bookingid = RandomUtil.randomString("1234567890", 10);
        regno = RandomUtil.randomString("1234567890", 10);
        StdTicketQueryRequest request = new StdTicketQueryRequest();
        request.setColno("20240925175600002");
        request.setOutId("74305764416677");
        request.setProjectId(projectId);
        StdTicketQueryResponse response = pftTicketVendor.queryTicket(request);
//        output_withOutJsonFormat(response, response.getStd_data());
        return ResultJson.ok().data(response);
    }
    @RequestMapping(value = "/CANCELPFT", method = RequestMethod.GET)
    public ResultJson CANCELPFT() throws DefinedException{
        switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        pftTicketVendor = (PftTicketVendor) switcher.getVendorHandler(VendorType.PFT_TICKET);
        queryStartDate = new Date();
        queryEndDate = DateUtil.offsetDay(queryStartDate, 30);
        bookingid = RandomUtil.randomString("1234567890", 10);
        regno = RandomUtil.randomString("1234567890", 10);
        StdCancelOrderRequest request = new StdCancelOrderRequest();
        request.setOtaorderid("74356574911431");
        request.setOutid("74356574911431");
        request.setProjectId(projectId);

        String testidinfo = "[{\"guestname\":\"测试\",\"idno\":\"450521199402270059\",\"idtype\":\"1\",\"mobile\":\"15278917894\"}]";

        StdOrderData stdOrderData = new StdOrderData();
        Booking_rs rs = new Booking_rs();
        rs.setBookingid(bookingid);
        rs.setGuestname("测试");
        rs.setTel("15278917894");
        rs.setAmount(0.01);
        rs.setPtype(ProdType.TICKET.val());
        rs.setProjectid(projectId);
        rs.setOutid("74356574911431");
        rs.setAnz(1);
        stdOrderData.setBookingRs(rs);

        Ticket_rs ticket_rs = new Ticket_rs();
        ticket_rs.setBookingid("74356574911431");
        ticket_rs.setProjectid(projectId);
        ticket_rs.setUsedate(CalculateDate.reckonDay(new Date(), 5, 5));
        ticket_rs.setTcode("CRP");
        ticket_rs.setAmount(1);
        ticket_rs.setAnz(1);
        ticket_rs.setIdinfo(testidinfo);
        ticket_rs.setRegno("74356574911431");
        ticket_rs.setReturnnum("1");
        stdOrderData.getTickets().add(ticket_rs);

        request.setOrderDataContext(stdOrderData);

        StdCancelOrderResponse response = pftTicketVendor.cancelOrder(request);
        return ResultJson.ok().data(response);
    }
    @RequestMapping(value = "/queryTicketStatus", method = RequestMethod.GET)
    public ResultJson queryTicketStatus() throws Exception {
        switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        pftTicketVendor = (PftTicketVendor) switcher.getVendorHandler(VendorType.PFT_TICKET);
        queryStartDate = new Date();
        queryEndDate = DateUtil.offsetDay(queryStartDate, 30);
        bookingid = RandomUtil.randomString("1234567890", 10);
        regno = RandomUtil.randomString("1234567890", 10);
        StdTicketQueryStatusRequest request = new StdTicketQueryStatusRequest();
        request.setOutId("74356574911431");
        request.setColno(bookingid);
        request.setProjectId(projectId);
        StdTicketQueryStatusResponse response = pftTicketVendor.queryTicketStatus(request);
return ResultJson.ok().data(response);
    }
    @RequestMapping(value = "/sendTicketMsg", method = RequestMethod.GET)
    public ResultJson sendTicketMsg() throws Exception {
        switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        pftTicketVendor = (PftTicketVendor) switcher.getVendorHandler(VendorType.PFT_TICKET);
        queryStartDate = new Date();
        queryEndDate = DateUtil.offsetDay(queryStartDate, 30);
        bookingid = RandomUtil.randomString("1234567890", 10);
        regno = RandomUtil.randomString("1234567890", 10);
        StdTicketSendMsgRequest request = new StdTicketSendMsgRequest();
        request.setColno(bookingid);
        request.setProjectId(projectId);
        request.setColno("20240925175600002");
        request.setTemplate("30227954");
        request.setOutid("74356574911431");
        request.setMobileno("15278917894");
        StdTicketSendMsgResponse response = pftTicketVendor.sendTicketMsg(request);
        return ResultJson.ok().data(response);
    }
    @RequestMapping(value = "/queryCanleTicketStatus", method = RequestMethod.GET)
    public ResultJson queryTicketCacnelStatus() throws Exception {
        switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        pftTicketVendor = (PftTicketVendor) switcher.getVendorHandler(VendorType.PFT_TICKET);
        queryStartDate = new Date();
        queryEndDate = DateUtil.offsetDay(queryStartDate, 30);
        bookingid = RandomUtil.randomString("1234567890", 10);
        regno = RandomUtil.randomString("1234567890", 10);
        StdTicketQueryCanelStatusRequest request = new StdTicketQueryCanelStatusRequest();
        request.setOutId("74477138516168");
        request.setColno("20250416448700007");
        request.setProjectId(projectId);
//        StdTicketQueryStatusResponse response = pftTicketVendor.queryTicketStatus(request);
        StdTicketQueryCancelStatusResponse stdTicketQueryCancelStatusResponse = pftTicketVendor.queryTicketCacnelStatus(request);
        return ResultJson.ok().data(stdTicketQueryCancelStatusResponse);
    }
    @RequestMapping(value = "/changeTicketDate", method = RequestMethod.GET)
    public ResultJson changeTicketDate() throws Exception {
        switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        pftTicketVendor = (PftTicketVendor) switcher.getVendorHandler(VendorType.PFT_TICKET);
        queryStartDate = new Date();
        queryEndDate = DateUtil.offsetDay(queryStartDate, 30);
        bookingid = RandomUtil.randomString("1234567890", 10);
        regno = RandomUtil.randomString("1234567890", 10);
        StdTicketChangeDateRequest request = new StdTicketChangeDateRequest();
        request.setBookingid(bookingid);
        request.setProjectId(projectId);
        StdTicketChangeDateResponse response = pftTicketVendor.changeTicketDate(request);
        return ResultJson.ok().data(response);
    }
    @RequestMapping(value = "/returnTicketNum", method = RequestMethod.GET)
    public ResultJson returnTicketNum() throws Exception {
        switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        pftTicketVendor = (PftTicketVendor) switcher.getVendorHandler(VendorType.PFT_TICKET);
        queryStartDate = new Date();
        queryEndDate = DateUtil.offsetDay(queryStartDate, 30);
        bookingid = RandomUtil.randomString("1234567890", 10);
        regno = RandomUtil.randomString("1234567890", 10);
        StdTicketReturnNumRequest request = new StdTicketReturnNumRequest();
        request.setOutId("74477138516168");
        request.setReturnNum("0");
        request.setBookingId("20250416448700007");
        request.setProjectId(projectId);
        StdTicketReturnNumResponse response = pftTicketVendor.returnTicketNum(request);
        return ResultJson.ok().data(response);
    }

}
