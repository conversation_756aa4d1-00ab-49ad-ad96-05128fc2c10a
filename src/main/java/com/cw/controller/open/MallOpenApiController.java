package com.cw.controller.open;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.open.req.OpenApiActReportReq;
import com.cw.pojo.dto.open.req.OpenApiActrsReportReq;
import com.cw.pojo.dto.open.req.OpenApiResourceReq;
import com.cw.pojo.dto.open.req.OpenApiTokenReq;
import com.cw.pojo.dto.open.res.OpenApiTokenRes;
import com.cw.pojo.dto.open.res.node.OpenApiActReportNode;
import com.cw.pojo.dto.open.res.node.OpenApiActrsNode;
import com.cw.pojo.dto.open.res.node.OpenApiResourceNode;
import com.cw.service.config.sys.impl.SysConfServiceImpl;
import com.cw.service.open.OpenApiService;
import com.cw.utils.SpringUtil;
import com.cw.utils.SwaggerUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


/**
 * 开放平台API
 */
@Slf4j
@RestController
@Api(tags = SwaggerUtil.MainIndex.OPENAPICONFIG, position = SwaggerUtil.MainIndex.OPENAPICONFIG_INDEX)
@RequestMapping(value = "/v1/openapi/")
public class MallOpenApiController {
    @Autowired
    OpenApiService openApiService;


    @ApiOperation(value = "开放接口 - 获取访问token", notes = "获取访问token")
    @RequestMapping(value = "/getToken", method = RequestMethod.POST)
    public ResultJson<OpenApiTokenRes> getToken(@Valid @RequestBody OpenApiTokenReq openApiTokenReq) {
        OpenApiTokenRes openApiTokenRes = openApiService.getToken(openApiTokenReq);
        return ResultJson.ok().data(openApiTokenRes);
    }

    @ApiOperation(value = "开放接口 - 查询活动预约汇总", notes = "查询活动预约汇总")
    @RequestMapping(value = "/actReport", method = RequestMethod.POST)
    public ResultJson<List<OpenApiActReportNode>> getActReport(@RequestBody OpenApiActReportReq openApiTokenReq) {
        List<OpenApiActReportNode> nodes = openApiService.getActReport(openApiTokenReq);
        return ResultJson.ok().data(nodes);
    }

    @ApiOperation(value = "开放接口 - 查询活动预约明细", notes = "查询活动预约明细")
    @RequestMapping(value = "/actRsReport", method = RequestMethod.POST)
    public ResultJson<List<OpenApiActrsNode>> getActRsReport(@RequestBody OpenApiActrsReportReq req) {
        List<OpenApiActrsNode> nodes = openApiService.getActRsReport(req);
        return ResultJson.ok().data(nodes);
    }

    @ApiOperation(value = "开放接口 - 查询静态资源配置列表", notes = "查询资源配置列表")
    @RequestMapping(value = "/queryResource", method = RequestMethod.POST)
    public ResultJson<List<OpenApiResourceNode>> getActRsReport(@Valid @RequestBody OpenApiResourceReq req) {
        List<OpenApiResourceNode> nodes = openApiService.getResource(req);
        return ResultJson.ok().data(nodes);
    }


    @RequestMapping(value = "/refreshConf", method = RequestMethod.POST)
    public ResultJson<List<OpenApiActReportNode>> getActReport(@RequestBody Common_Load_Req req) {
        SysConfServiceImpl sysConfService = SpringUtil.getBean(SysConfServiceImpl.class);
        sysConfService.fixNewAgent(req.getGroupid());
        return ResultJson.ok().data(null);
    }


}
