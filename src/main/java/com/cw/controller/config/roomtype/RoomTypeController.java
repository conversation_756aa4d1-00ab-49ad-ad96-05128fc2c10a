package com.cw.controller.config.roomtype;

import com.cw.entity.Roomtype;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.roomtype.RmtypeQueryReq;
import com.cw.pojo.dto.conf.req.roomtype.RmtypeResourcePriceQueryReq;
import com.cw.pojo.dto.conf.req.roomtype.RmtypeResourcePriceSaveReq;
import com.cw.pojo.dto.conf.res.factor.FactorHeaderAndChildData;
import com.cw.pojo.dto.conf.res.roomtype.RoomtypeListRes;
import com.cw.pojo.dto.conf.res.roomtype.RoomtypeResourcePriceListRes;
import com.cw.pojo.entity.Roomtype_Entity;
import com.cw.service.config.room.RoomTypeService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/17 0017
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGHOTEL, position = SwaggerUtil.MainIndex.CONFIGHOTEL_INDEX)
@RestController
@RequestMapping(value = "/api/rmtype", method = RequestMethod.POST)
public class RoomTypeController {
    @Autowired
    RoomTypeService roomTypeService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.ROOMTYPE_SETUP_LIST_INDEX)
    @ApiOperation(value = "房型设置 - 获取房型列表数据", notes = "获取房型设置列表数据")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_RMTYPE_LIST)
    public ResultJson<RoomtypeListRes> fetchlist(@RequestBody RmtypeQueryReq req) {
        return ResultJson.ok().data(roomTypeService.queryTableData(req));
    }

    @ApiOperationSupport(order=SwaggerUtil.SubIndex.ConfigHotel.ROOMTYPE_SETUP_GET_INDEX)
    @ApiOperation(value = "房型设置 - 获取房型对象", notes = "根据唯一id获取房型对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_RMTYPE_LOAD)
    public ResultJson<Roomtype_Entity> load(@RequestBody Common_Load_Req req) {
        Roomtype roomtype = null;
        if (req.getSqlid() > 0) {
            roomtype = roomTypeService.loadRoomType(req.getSqlid());
        } else {
            roomtype = new Roomtype();
        }
        Roomtype_Entity entity = new Roomtype_Entity();
        BeanUtils.copyProperties(roomtype, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.ROOMTYPE_SETUP_SAVE_INDEX)
    @ApiOperation(value = "房型设置 - 保存房型", notes = "保存房型")
    @RequestMapping(value = "/save")//add和upd合在一起
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_RMTYPE_SAVE)
    public ResultJson<Roomtype_Entity> save(@RequestBody Roomtype_Entity req) {
        Roomtype_Entity entity = roomTypeService.saveRoomType(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order=SwaggerUtil.SubIndex.ConfigHotel.ROOMTYPE_SETUP_DELETE_INDEX)
    @ApiOperation(value = "房型设置 - 删除房型", notes = "删除房型")
    @RequestMapping(value = "/del")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_RMTYPE_DELETE)
    public ResultJson del(@RequestBody Common_Del_Req req) {
        roomTypeService.deleteRoomType(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.ROOMTYPE_SETUP_RESOURCE_PRICE_INDEX)
    @ApiOperation(value = "房型设置 - 获取房型库存和价格", notes = "获取房型库存和价格")
    @RequestMapping(value = "/load_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_RMTYPE_RESOURCE_LOAD)
    public ResultJson<RoomtypeResourcePriceListRes> load_resources_price(@Validated @RequestBody RmtypeResourcePriceQueryReq req) {
        return ResultJson.ok().data(roomTypeService.queryRoomTypeResourceList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.ROOMTYPE_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "房型设置 - 更新房型开关状态.对当前状态取反", notes = "更新房型状态")
    @RequestMapping(value = "/update_roomtype_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_RMTYPE_STATUS)
    public ResultJson updRoomTypeStatus(@RequestBody Common_Switch_Req req) {
        roomTypeService.updRoomTypeStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.ROOMTYPE_SETUP_KIT_LIST_INDEX)
    @ApiOperation(value = "房型设置 - 获取房型配套", notes = "获取房型配套")
    @RequestMapping(value = "/rmkit")
    public ResultJson<FactorHeaderAndChildData> updRoomTypeStatus(@RequestBody Common_Load_Req req) {
        return ResultJson.ok().data(roomTypeService.getRoomTypeKit(req.getSqlid()));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.ROOMTYPE_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "房型设置 - 设置房型库存和价格", notes = "设置房型库存和价格")
    @RequestMapping(value = "/save_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_RMTYPE_RESOURCE_SAVE)
    public ResultJson save_resources_price(@Validated @RequestBody RmtypeResourcePriceSaveReq req) {
        roomTypeService.batchRoomtype(req);
        return ResultJson.ok();
    }
}
