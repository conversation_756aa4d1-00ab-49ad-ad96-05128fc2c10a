package com.cw.controller.config.custom;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Msg_Req;
import com.cw.pojo.dto.common.res.Common_Msg_Res;
import com.cw.pojo.log.SendMsgData;
import com.cw.pojo.log.req.Template_BatchSend_Req;
import com.cw.pojo.log.req.Template_Content_Req;
import com.cw.pojo.log.res.Template_Content_Res;
import com.cw.service.log.MsgService;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 常用的下拉框填充数据.调用这里
 * 后期可以考虑登陆后.存放在全局的 vue ex 中
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2019-07-24 16:30
 **/
@Api(tags = SwaggerUtil.MainIndex.MSG, position = SwaggerUtil.MainIndex.MSG_INDEX)
@RestController
@RequestMapping(value = "api/msg", method = RequestMethod.POST)
public class MsgController {

    private MsgService msgService;

    @Autowired
    public MsgController(MsgService msgService) {
        this.msgService = msgService;
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Msg.MSG_LOGLIST_INDEX)
    @ApiOperation(value = "短信 - 短信日志列表", notes = "获取短信日志列表")
    @RequestMapping(value = "/log_list")
    public ResultJson<Common_Msg_Res> msglog_list(@RequestBody Common_Msg_Req common_msg_req) {
        return ResultJson.ok().data(msgService.queryList(common_msg_req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Msg.MSG_RESOLVING_INDEX)
    @ApiOperation(value = "短信 - 获取解析过的模板内容", notes = "获取解析过的模板内容")
    @RequestMapping(value = "/load_content")
    public ResultJson<Template_Content_Res> load_content(@RequestBody Template_Content_Req template_content_req) {
        List<String> content = msgService.loadContent(template_content_req);
        Template_Content_Res res = new Template_Content_Res();
        res.setContent(content);
        return ResultJson.ok().data(res);
    }

    //@ApiOperationSupport(order = SwaggerUtil.SubIndex.Msg.MSG_SEND_INDEX)
    //@ApiOperation(value = "短信 - 发送短信", notes = "发送短信")
    //@RequestMapping(value = "/send")
    //public ResultJson<MsgResult> send_msg(@RequestBody Template_Send_Req template_send_req) throws DefinedException {
    //    MsgResult msgResult = msgService.sendMessage(template_send_req);
    //    return ResultJson.ok().data(msgResult);
    //}

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Msg.MSG_BATCH_SEND_INDEX)
    @ApiOperation(value = "短信 - 批量发送短信", notes = "批量发送短信")
    @RequestMapping(value = "/batch_send")
    public ResultJson<List<SendMsgData>> batch_send_msg(@RequestBody Template_BatchSend_Req template_batchSend_req) throws DefinedException {
        List<SendMsgData> sendDatas = msgService.batchSendMessage(template_batchSend_req);
        return ResultJson.ok().data(sendDatas);
    }
}
