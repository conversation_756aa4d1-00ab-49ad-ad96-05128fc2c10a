package com.cw.controller.config.custom;

import com.cw.entity.Factor;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Seq_Req;
import com.cw.pojo.dto.conf.req.factor.FactorHeaderReq;
import com.cw.pojo.dto.conf.req.factor.FactorListReq;
import com.cw.pojo.dto.conf.res.factor.FactorListRes;
import com.cw.pojo.entity.FactorData_Entiry;
import com.cw.pojo.entity.Factor_Entity;
import com.cw.service.config.factor.FactorService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/23 0023
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGCOMMON, position = SwaggerUtil.MainIndex.CONFIGCOMMON_INDEX)
@RestController
@RequestMapping(value = "api/factor", method = RequestMethod.POST)
public class FactorSetupController {

    @Autowired
    private FactorService factorService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCommon.FACTOR_SETUP_LIST_INDEX)
    @ApiOperation(value = "公共数据设置 - 公共数据列表", notes = "获取公共数据列表数据")
    @RequestMapping(value = "/factor_list")
    @RequireOpRight(opRight = OpRightCodes.OP_RESOURCE_DATA_LIST)
    public ResultJson<FactorListRes> factor_list(@RequestBody FactorListReq req) {
        return ResultJson.ok().data(factorService.queryFactorList(req));
    }

    @ApiOperationSupport(order= SwaggerUtil.SubIndex.ConfigCommon.FACTOR_SETUP_GET_INDEX)
    @ApiOperation(value = "公共数据设置 - 获取公共数据对象", notes = "根据唯一id获取公共数据对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_factor")
    @RequireOpRight(opRight = OpRightCodes.OP_RESOURCE_DATA_LOAD)
    public ResultJson<Factor_Entity> load_factor(@RequestBody Common_Load_Req req) {
        Factor factor = null;
        if(req.getSqlid()!=null && req.getSqlid()>0){
            factor = factorService.loadFactor(req.getSqlid());
        }else{
            factor = new Factor();
        }
        Factor_Entity entity = new Factor_Entity();
        BeanUtils.copyProperties(factor, entity);
        //entity.setHeader(factorService.tansToHeaderName(entity.getHeader(), entity.getType()));//转中文
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCommon.FACTOR_SETUP_SAVE_INDEX)
    @ApiOperation(value = "公共数据设置 - 保存公共数据", notes = "保存公共数据")
    @RequestMapping(value = "/save_factor")
    @RequireOpRight(opRight = OpRightCodes.OP_RESOURCE_DATA_SAVE)
    public ResultJson<Factor_Entity> save_factor(@RequestBody Factor_Entity req) {
        Factor_Entity entity = factorService.saveFactor(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order=SwaggerUtil.SubIndex.ConfigCommon.FACTOR_SETUP_DELETE_INDEX)
    @ApiOperation(value = "公共数据设置 - 删除公共数据", notes = "删除公共数据")
    @RequestMapping(value = "/delete_factor")
    @RequireOpRight(opRight = OpRightCodes.OP_RESOURCE_DATA_DELETE)
    public ResultJson delete_factor(@RequestBody Common_Del_Req req) {
        factorService.deleteFactor(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCommon.FACTOR_SETUP_STATUS_INDEX)
    @ApiOperation(value = "公共数据设置 - 更新当前设置状态，当前状态取反", notes = "更新状态")
    @RequestMapping(value = "/change_status")
    @RequireOpRight(opRight = OpRightCodes.OP_RESOURCE_DATA_STATUS)
    public ResultJson change_status(@RequestBody Common_Load_Req req) {
        factorService.updateStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCommon.FACTOR_SETUP_SEQ_INDEX)
    @ApiOperation(value = "公共数据设置 - 更新当前排序", notes = "更新排序")
    @RequestMapping(value = "/change_seq")
    @RequireOpRight(opRight = OpRightCodes.OP_RESOURCE_DATA_SEQ)
    public ResultJson change_seq(@RequestBody Common_Seq_Req req) {
        factorService.updateSeq(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCommon.FACTOR_SETUP_GET_HEADER_AND_CHILD_LIST_INDEX)
    @ApiOperation(value = "公共数据设置 - 获取指定Header及其子项所有数据", notes = "获取指定Header及其子项所有数据")
    @RequestMapping(value = "/header_list")
    @RequireOpRight(opRight = OpRightCodes.OP_RESOURCE_DATA_CHILDREN)
    public ResultJson<List<FactorData_Entiry>> header_list(@Validated @RequestBody FactorHeaderReq req) {
        return ResultJson.ok().data(factorService.getFactorAndChildListData(req.getSearchkey()));
    }


}
