package com.cw.controller.config.custom;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Select_Req;
import com.cw.pojo.dto.common.res.Common_Select_Res;
import com.cw.service.config.common.CustomDataService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/23 0023
 */
@Api(tags= SwaggerUtil.MainIndex.CONFIGCOMMON,position = SwaggerUtil.MainIndex.CONFIGCOMMON_INDEX)
@RestController
@RequestMapping(value = "api/custom", method = RequestMethod.POST)
public class CustomDataController {
    @Autowired
    CustomDataService customDataService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCommon.SELECT_VALUE_INDEX)
    @ApiOperation(value = "公共数据-获取下拉框数据", notes = "获取下拉框数据")
    @RequestMapping(value = "/select_data")
    public ResultJson<List<Common_Select_Res>> select_data(@RequestBody Common_Select_Req req) {
        return ResultJson.ok().data(customDataService.getSelectData(req, GlobalContext.getCurrentProjectId()));
    }

}
