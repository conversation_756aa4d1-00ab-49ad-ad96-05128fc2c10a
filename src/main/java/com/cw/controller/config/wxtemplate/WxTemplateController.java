package com.cw.controller.config.wxtemplate;

import com.cw.entity.Wxtemplate;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.conf.req.template.TemplateStatusReq;
import com.cw.pojo.dto.conf.req.wxtemplate.WxTemplateListReq;
import com.cw.pojo.dto.conf.res.wxtemlate.WxTemplateListRes;
import com.cw.pojo.entity.Wxtemplate_Entity;
import com.cw.service.config.wxtemplate.WxTemplateService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/12/24 0024
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGTEMPLATE, position = SwaggerUtil.MainIndex.CONFIGTEMPLATE_INDEX)
@RestController
@RequestMapping(value = "api/wxtemplate", method = RequestMethod.POST)
public class WxTemplateController {
    private WxTemplateService wxTemplateService;

    @Autowired
    public WxTemplateController(WxTemplateService wxTemplateService) {
        this.wxTemplateService = wxTemplateService;
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.WXTEMPLATE_LIST_INDEX)
    @ApiOperation(value = "小程序通知设置 - 小程序通知列表", notes = "获取小程序通知列表列表数据")
    @RequestMapping(value = "/wxtemplate_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_WXTEMPLATE_LIST)
    public ResultJson<WxTemplateListRes> template_list(@RequestBody WxTemplateListReq req) {
        return ResultJson.ok().data(wxTemplateService.queryList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.WXTEMPLATE_GET_INDEX)
    @ApiOperation(value = "小程序通知设置 - 获取小程序通知对象", notes = "根据唯一id获取模板对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_wxtemplate")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_WXTEMPLATE_LOAD)
    public ResultJson<Wxtemplate_Entity> load_template(@RequestBody Common_Load_Req req) {
        Wxtemplate template = wxTemplateService.loadWxTemplate(req.getSqlid());
        Wxtemplate_Entity entity = new Wxtemplate_Entity();
        BeanUtils.copyProperties(template, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.WXTEMPLATE_SAVE_INDEX)
    @ApiOperation(value = "小程序通知设置 - 保存小程序通知", notes = "保存小程序通知")
    @RequestMapping(value = "/save_wxtemplate")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_WXTEMPLATE_SAVE)
    public ResultJson<Wxtemplate_Entity> save_template(@RequestBody Wxtemplate_Entity req) {
        Wxtemplate_Entity entity = wxTemplateService.saveTemplate(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.WXTEMPLATE_STATUS_INDEX)
    @ApiOperation(value = "小程序通知设置 - 启用/停用小程序通知", notes = "启用/停用小程序通知")
    @RequestMapping(value = "/use_wxtemplate")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_WXTEMPLATE_STATUS)
    public ResultJson use_template(@RequestBody TemplateStatusReq req) {
        wxTemplateService.WxTemplateStatus(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.WXTEMPLATE_DELETE_INDEX)
    @ApiOperation(value = "小程序通知设置 - 删除小程序通知", notes = "删除短信模板")
    @RequestMapping(value = "/delete_wxtemplate")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_WXTEMPLATE_DELETE)
    public ResultJson delete_template(@RequestBody Common_Del_Req req) {
        wxTemplateService.deleteWxTemplate(req.getSqlid());
        return ResultJson.ok();
    }
}
