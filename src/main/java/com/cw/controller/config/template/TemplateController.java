package com.cw.controller.config.template;

import com.cw.entity.Template;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.conf.req.template.TemplateListReq;
import com.cw.pojo.dto.conf.req.template.TemplateStatusReq;
import com.cw.pojo.dto.conf.res.template.TemplateListRes;
import com.cw.pojo.entity.Template_Entity;
import com.cw.service.template.TemplateService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/12/24 0024
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGTEMPLATE, position = SwaggerUtil.MainIndex.CONFIGTEMPLATE_INDEX)
@RestController
@RequestMapping(value = "api/template", method = RequestMethod.POST)
public class TemplateController {
    private TemplateService templateService;

    @Autowired
    public TemplateController(TemplateService templateService) {
        this.templateService = templateService;
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.TEMPLATE_LIST_INDEX)
    @ApiOperation(value = "短信模板 - 短信模板列表", notes = "获取短信模板列表数据")
    @RequestMapping(value = "/template_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TEMPLATE_LIST)
    public ResultJson<TemplateListRes> template_list(@RequestBody TemplateListReq req) {
        return ResultJson.ok().data(templateService.queryList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.TEMPLATE_GET_INDEX)
    @ApiOperation(value = "短信模板 - 获取短信模板对象", notes = "根据唯一id获取模板对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_template")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TEMPLATE_LOAD)
    public ResultJson<Template_Entity> load_template(@RequestBody Common_Load_Req req) {
        Template template = templateService.loadTemplate(req.getSqlid());
        Template_Entity entity = new Template_Entity();
        BeanUtils.copyProperties(template, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.TEMPLATE_SAVE_INDEX)
    @ApiOperation(value = "短信模板 - 保存短信模板", notes = "保存短信模板")
    @RequestMapping(value = "/save_template")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TEMPLATE_SAVE)
    public ResultJson<Template_Entity> save_template(@RequestBody Template_Entity req) {
        Template_Entity entity = templateService.saveTemplate(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.TEMPLATE_STATUS_INDEX)
    @ApiOperation(value = "短信模板 - 启用/停用短信模板", notes = "启用/停用短信模板")
    @RequestMapping(value = "/use_template")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TEMPLATE_STATUS)
    public ResultJson use_template(@RequestBody TemplateStatusReq req) {
        templateService.templateStatus(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTemplate.TEMPLATE_DELETE_INDEX)
    @ApiOperation(value = "短信模板 - 删除短信模板", notes = "删除短信模板")
    @RequestMapping(value = "/delete_template")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TEMPLATE_DELETE)
    public ResultJson delete_template(@RequestBody Common_Del_Req req) {
        templateService.deleteTemplate(req.getSqlid());
        return ResultJson.ok();
    }
}
