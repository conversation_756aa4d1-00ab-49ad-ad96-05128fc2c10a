package com.cw.controller.config.template;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.conf.req.pagetemplate.PagetemplateListReq;
import com.cw.pojo.dto.conf.res.pagetemplate.PagetemplateListRes;
import com.cw.pojo.entity.Pagetemplate_Entity;
import com.cw.service.config.template.PagetemplateService;
import com.cw.utils.SwaggerUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = SwaggerUtil.MainIndex.CONFIGTEMPLATE, position = SwaggerUtil.MainIndex.CONFIGTEMPLATE_INDEX)
@RestController
@RequestMapping(value = "api/pagetemplate", method = RequestMethod.POST)
public class PagetemplateController {

    private PagetemplateService pagetemplateService;

    @Autowired
    public PagetemplateController(PagetemplateService pagetemplateService) {
        this.pagetemplateService = pagetemplateService;
    }

    @ApiOperation(value = "页面模板 - 页面模板列表", notes = "获取页面模板列表数据")
    @RequestMapping(value = "/pagetemplate_list")
    public ResultJson<PagetemplateListRes> template_list(@RequestBody PagetemplateListReq req) {
        return ResultJson.ok().data(pagetemplateService.queryList(req));
    }

    @ApiOperation(value = "页面模板 - 获取页面模板对象", notes = "根据唯一id获取模板对象")
    @RequestMapping(value = "/load_pagetemplate")
    public ResultJson<Pagetemplate_Entity> load_template(@RequestBody Common_Load_Req req) {
        Pagetemplate_Entity template = pagetemplateService.loadPagetemplate(req.getSqlid());
        return ResultJson.ok().data(template);
    }

    @ApiOperation(value = "页面模板 - 保存页面模板", notes = "保存页面模板")
    @RequestMapping(value = "/save_pagetemplate")
    public ResultJson<Pagetemplate_Entity> save_template(@RequestBody Pagetemplate_Entity req) {
        Pagetemplate_Entity entity = pagetemplateService.saveTemplate(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "页面模板 - 删除页面模板", notes = "删除页面模板")
    @RequestMapping(value = "/delete_pagetemplate")
    public ResultJson delete_template(@RequestBody Common_Del_Req req) {
        pagetemplateService.deletePagetemplate(req.getSqlid());
        return ResultJson.ok();
    }
} 