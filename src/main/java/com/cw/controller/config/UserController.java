package com.cw.controller.config;

import cn.dev33.satoken.stp.StpUtil;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.conf.req.users.Usersign_Req;
import com.cw.pojo.dto.conf.res.users.UserInfo;
import com.cw.pojo.dto.conf.res.users.UserResult;
import com.cw.service.config.users.UserService;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = SwaggerUtil.MainIndex.LOGINPUB, position = SwaggerUtil.MainIndex.LOGIN_PUBINDEX)
@RestController
@RequestMapping(value = "/api/users", method = RequestMethod.POST)
public class UserController {

    @Autowired
    UserService opuserService;

    /**
     * 登陆
     *
     * @param req
     * @return
     */
    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "后台用户登录", notes = "登录系统")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public ResultJson<UserResult> login(@RequestBody Usersign_Req req) {
        UserResult result = opuserService.login(req);
        return ResultJson.ok().data(result);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.USER_INFO_INDEX)
    @ApiOperation(value = "登陆后获取用户信息", notes = "获取登录用户信息")
    @RequestMapping(value = "/info")
    public ResultJson<UserInfo> getinfo() {
        UserInfo userInfo = opuserService.getInfo(StpUtil.getTokenValue());
        return ResultJson.ok().data(userInfo);
    }

    /**
     * 校验是否登录有效
     *
     * @return
     */
    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGOUT_INDEX)
    @ApiOperation(value = "校验是否登录有效", notes = "校验是否登录有效")
    @RequestMapping(value = "/checkToken")
    public ResultJson checkToken() {
        //opuserService.logout(StpUtil.getTokenValue());
        return ResultJson.ok();
    }


    /**
     * 登出
     *
     * @return
     */
    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGOUT_INDEX)
    @ApiOperation(value = "用户注销退出", notes = "退出登录")
    @RequestMapping(value = "/logout")
    public ResultJson logOut() {
        opuserService.logout(StpUtil.getTokenValue());
        return ResultJson.ok();
    }


}
