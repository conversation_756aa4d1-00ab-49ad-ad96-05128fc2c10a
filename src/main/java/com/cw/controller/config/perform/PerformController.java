package com.cw.controller.config.perform;

import com.alibaba.fastjson.JSONArray;
import com.cw.entity.Perform;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.perform.PerformListReq;
import com.cw.pojo.dto.conf.res.perform.PerformListRes;
import com.cw.pojo.dto.conf.res.perform.PerformSchedule;
import com.cw.pojo.entity.Perform_Entity;
import com.cw.service.config.perform.PerformService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2024-07-03
 */

@Api(tags = SwaggerUtil.MainIndex.CONFIGPERFORM, position = SwaggerUtil.MainIndex.CONFIGPERFORM_INDEX)
@RestController
@RequestMapping(value = "api/perform", method = RequestMethod.POST)
public class PerformController {
    @Autowired
    PerformService performService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPerform.PERFORM_SETUP_LIST_INDEX)
    @ApiOperation(value = "演出节目设置 - 演出节目列表", notes = "获取演出节目列表数据")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PERFORM_LIST)
    public ResultJson<PerformListRes> group_list(@RequestBody PerformListReq req) {
        return ResultJson.ok().data(performService.queryPerformList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPerform.PERFORM_SETUP_GET_INDEX)
    @ApiOperation(value = "演出管理设置 - 获取演出节目对象", notes = "根据唯一id获取演出节目对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PERFORM_LOAD)
    public ResultJson<Perform_Entity> load(@RequestBody Common_Load_Req req) {
        Perform perform = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            perform = performService.loadPerform(req.getSqlid());
        } else {
            perform = new Perform();
        }
        Perform_Entity entity = new Perform_Entity();
        BeanUtils.copyProperties(perform, entity);
        entity.setSchedule(JSONArray.parseArray(perform.getSchedule(), PerformSchedule.class));
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPerform.PERFORM_SETUP_SAVE_INDEX)
    @ApiOperation(value = "演出管理设置 - 保存演出节目", notes = "保存演出节目")
    @RequestMapping(value = "/save")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PERFORM_SAVE)
    public ResultJson<Perform_Entity> save(@RequestBody @Valid Perform_Entity req) {
        Perform_Entity entity = performService.savePerform(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPerform.PERFORM_SETUP_DELETE_INDEX)
    @ApiOperation(value = "演出管理设置 - 删除演出节目", notes = "删除演出节目")
    @RequestMapping(value = "/delete")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PERFORM_DELETE)
    public ResultJson delete(@RequestBody Common_Del_Req req) {
        performService.deletePerform(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPerform.PERFORM_SETUP_STATUS_INDEX)
    @ApiOperation(value = "演出管理设置 - 更新演出节目开关状态.对当前状态取反", notes = "更新演出节目开关状态")
    @RequestMapping(value = "/update_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PERFORM_STATUS)
    public ResultJson update_status(@Valid @RequestBody Common_Switch_Req req) {
        performService.updPerformStatus(req.getSqlid());
        return ResultJson.ok();
    }
}
