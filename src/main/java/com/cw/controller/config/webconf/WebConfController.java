package com.cw.controller.config.webconf;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.entity.WebConf_Entity;
import com.cw.service.config.webconf.WebConfService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-02-23
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGWEB, position = SwaggerUtil.MainIndex.CONFIGWEB_INDEX)
@RestController
@RequestMapping(value = "/api/webconf", method = RequestMethod.POST)
public class WebConfController {
    @Autowired
    WebConfService webConfService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigWeb.LOADSYSCONF)
    @ApiOperation(value = "加载网站页脚设置")
    @RequireOpRight(opRight = OpRightCodes.OP_WEBCONF_LOAD)
    @RequestMapping(value = "/loadconf")
    public ResultJson<WebConf_Entity> loadConf(@RequestBody Common_Load_Req req) {
        WebConf_Entity result = webConfService.loadConf(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigWeb.SAVESYSCONF)
    @ApiOperation(value = "保存网站页脚设置")
    @RequireOpRight(opRight = OpRightCodes.OP_WEBCONF_SAVE)
    @RequestMapping(value = "/saveconf", method = RequestMethod.POST)
    public ResultJson saveConfig(@RequestBody WebConf_Entity req) {
        webConfService.save(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigWeb.DELFILE)
    @ApiOperation(value = "删除网站页脚设置")
    @RequireOpRight(opRight = OpRightCodes.OP_WEBCONF_DELETE)
    @RequestMapping(value = "/deleteconf", method = RequestMethod.POST)
    public ResultJson deleteConfig(@RequestBody Common_Del_Req req) {
        webConfService.delete(req.getSqlid()); //.login(req);
        return ResultJson.ok();
    }
}
