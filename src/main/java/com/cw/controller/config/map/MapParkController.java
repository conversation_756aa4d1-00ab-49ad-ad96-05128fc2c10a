package com.cw.controller.config.map;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ParkBussinessCache;
import com.cw.entity.Park;
import com.cw.entity.Parkbusiness;
import com.cw.entity.Parksite;
import com.cw.entity.Traveltip;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.area.TipsNode;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.req.cms.MapParkListReq;
import com.cw.pojo.dto.conf.req.cms.MapParkQuickImportReq;
import com.cw.pojo.dto.conf.req.cms.MapParksiteListReq;
import com.cw.pojo.dto.conf.req.cms.TraveltipListReq;
import com.cw.pojo.dto.conf.res.cms.MapParkListRes;
import com.cw.pojo.dto.conf.res.cms.MapParksiteFeature;
import com.cw.pojo.dto.conf.res.cms.MapParksiteListRes;
import com.cw.pojo.dto.conf.res.cms.TraveltipListRes;
import com.cw.pojo.dto.conf.res.factor.FactorHeaderAndChildData;
import com.cw.pojo.entity.Park_Entity;
import com.cw.pojo.entity.Parksite_Entity;
import com.cw.pojo.entity.Traveltip_Entity;
import com.cw.service.config.cms.MapService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2023-04-06
 */
@Api(tags = SwaggerUtil.MainIndex.PARKMAPCONFIG, position = SwaggerUtil.MainIndex.PARKMAPCONFIG_INDEX)
@RestController
@RequestMapping(value = "/api/parkMap", method = RequestMethod.POST)
public class MapParkController {

    @Autowired
    MapService mapService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARK_SETUP_LIST_INDEX)
    @ApiOperation(value = "地图业态管理 - 获取园区列表", notes = "获取园区列表")
    @RequestMapping(value = "/park_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAK_LIST)
    public ResultJson<MapParkListRes> park_list(@Validated @RequestBody MapParkListReq req) {
        return ResultJson.ok().data(mapService.queryParkList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARK_SETUP_LOAD_INDEX)
    @ApiOperation(value = "地图业态管理 -获取园区数据", notes = "获取园区数据")
    @RequestMapping(value = "/load_park")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAK_LOAD)
    public ResultJson<Park_Entity> load_park(@RequestBody Common_Load_Req req) {
        Park park = mapService.loadPark(req.getSqlid());
        Park_Entity entity = new Park_Entity();
        BeanUtils.copyProperties(park, entity);
        ParkBussinessCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PARKBUSS);
        List<Parkbusiness> pbList = cache.getDataListWithCondition(park.getProjectid(), pb -> pb.getGroupid().equals(park.getCode()));
        if (CollectionUtil.isNotEmpty(pbList)) {
            pbList.sort(Comparator.comparing(Parkbusiness::getSeq));
            List<String> businessTypes = pbList.stream().map(Parkbusiness::getBusinesstype).collect(Collectors.toList());
            entity.setBusinesstype(StringUtils.join(businessTypes, ","));
        }
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARK_SETUP_SAVE_INDEX)
    @ApiOperation(value = "地图业态管理 -添加园区", notes = "添加园区")
    @RequestMapping(value = "/save_park")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAK_SAVE)
    public ResultJson<Park_Entity> save_park(@Valid @RequestBody Park_Entity req) {
        Park_Entity entity = mapService.savePark(req);
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARK_SETUP_DELETE_INDEX)
    @ApiOperation(value = "地图业态管理 -删除园区", notes = "删除园区")
    @RequestMapping(value = "/delete_park")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAK_DELETE)
    public ResultJson<Common_response> delete_park(@Valid @RequestBody Common_Del_Req postform) {
        mapService.deletePark(postform.getSqlid());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_LIST_INDEX)
    @ApiOperation(value = "地图业态管理 - 获取业态规划标注列表", notes = "获取业态标注列表")
    @RequestMapping(value = "/parksite_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAKSITE_LIST)
    public ResultJson<MapParksiteListRes> parksite_list(@Validated @RequestBody MapParksiteListReq req) {
        return ResultJson.ok().data(mapService.queryParksiteList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_LOAD_INDEX)
    @ApiOperation(value = "地图业态管理 -获取业态规划标注", notes = "获取业态标注")
    @RequestMapping(value = "/load_parksite")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAKSITE_LOAD)
    public ResultJson<Parksite_Entity> load_parksite(@RequestBody Common_Load_Req req) {
        Parksite parksite = mapService.loadParksite(req.getSqlid());
        Parksite_Entity entity = new Parksite_Entity();
        BeanUtils.copyProperties(parksite, entity);
        //视频封面图片
        if (StringUtils.isNotBlank(entity.getVideourl())) {
            entity.setPoster(ContentCacheTool.getOSSVideoPicUrl(entity.getVideourl(), entity.getSlidepics()));
        }
        if (StringUtils.isNotBlank(parksite.getFeaturemanage())) {
            entity.setManage(JSON.parseObject(parksite.getFeaturemanage(), new TypeReference<List<MapParksiteFeature>>() {
            }));
        }
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_SAVE_INDEX)
    @ApiOperation(value = "地图业态管理 -添加业态规划标注", notes = "添加业态标注")
    @RequestMapping(value = "/save_parksite")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAKSITE_SAVE)
    public ResultJson<Parksite_Entity> save_parksite(@Valid @RequestBody Parksite_Entity req) {
        Parksite_Entity entity = mapService.saveParksite(req);
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_DELETE_INDEX)
    @ApiOperation(value = "地图业态管理 -删除业态规划标注", notes = "删除业态标注")
    @RequestMapping(value = "/delete_parksite")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAKSITE_DELETE)
    public ResultJson<Common_response> delete_parksite(@Valid @RequestBody Common_Del_Req postform) {
        mapService.deleteParksite(postform.getSqlid());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_STATUS_INDEX)
    @ApiOperation(value = "地图业态管理 -更新业态规划标注开关状态.对当前状态取反", notes = "更新业态标注开关状态")
    @RequestMapping(value = "/update_Parksite_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAKSITE_STATUS)
    public ResultJson<Common_response> update_Parksite_status(@Valid @RequestBody Common_Load_Req req) {
        mapService.updParksiteStatus(req.getSqlid());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_DELETE_INDEX)
    @ApiOperation(value = "地图业态管理 -获取业态标注服务设施", notes = "获取业态标注服务设施")
    @RequestMapping(value = "/parksitekit")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAKSITEKIT_LOAD)
    public ResultJson<FactorHeaderAndChildData> parksitekit(@Valid @RequestBody Common_Load_Req postform) {
        return ResultJson.ok().data(mapService.getParksiteKit(postform.getSqlid()));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_STATUS_INDEX)
    @ApiOperation(value = "地图业态管理 -业态规划快速导入(暂时不用)", notes = "业态规划快速导入")
    @RequestMapping(value = "/quick_import")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_PRAKSITE_INPORT)
    public ResultJson<Common_response> quick_import(@Valid @RequestBody MapParkQuickImportReq req) {
        mapService.quickImport(req.getParkid(), GlobalContext.getCurrentProjectId());
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_LIST_INDEX)
    @ApiOperation(value = "地图路线规划 - 获取路线规划列表", notes = "获取路线规划列表")
    @RequestMapping(value = "/travelTip_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_TRAVELTIP_LIST)
    public ResultJson<TraveltipListRes> travelTip_list(@Validated @RequestBody TraveltipListReq req) {
        return ResultJson.ok().data(mapService.queryTravelTip(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_LOAD_INDEX)
    @ApiOperation(value = "地图路线规划 -获取路线", notes = "获取路线")
    @RequestMapping(value = "/load_travelTip")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_TRAVELTIP_LOAD)
    public ResultJson<Traveltip_Entity> load_travelTip(@RequestBody Common_Load_Req req) {
        Traveltip traveltip = mapService.loadTravelTip(req.getSqlid());
        Traveltip_Entity entity = new Traveltip_Entity();
        BeanUtils.copyProperties(traveltip, entity);
        if (StringUtils.isNotBlank(traveltip.getTipinfo())) {
            entity.setTips(JSONArray.parseArray(traveltip.getTipinfo(), TipsNode.class));//JSON转化数组
        }
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_SAVE_INDEX)
    @ApiOperation(value = "地图路线规划 -添加路线", notes = "添加路线")
    @RequestMapping(value = "/save_travelTip")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_TRAVELTIP_SAVE)
    public ResultJson<Traveltip_Entity> save_travelTip(@Valid @RequestBody Traveltip_Entity req) {
        Traveltip_Entity entity = mapService.saveTraveltip(req);
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_DELETE_INDEX)
    @ApiOperation(value = "地图路线规划 -删除路线", notes = "删除路线")
    @RequestMapping(value = "/delete_travelTip")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_TRAVELTIP_DELETE)
    public ResultJson<Common_response> delete_travelTip(@Valid @RequestBody Common_Del_Req postform) {
        mapService.deleteTraveltip(postform.getSqlid());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigParkMap.PARKSITE_SETUP_STATUS_INDEX)
    @ApiOperation(value = "地图路线规划 -更新业路线开关状态.对当前状态取反", notes = "更新业路线开关状态")
    @RequestMapping(value = "/update_travelTip_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MAP_TRAVELTIP_STATUS)
    public ResultJson<Common_response> update_travelTip_status(@Valid @RequestBody Common_Load_Req req) {
        mapService.updTraveltipStatus(req.getSqlid());
        return ResultJson.ok().data(new Common_response());
    }
}
