package com.cw.controller.config.rules;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.rules.RuleProductQueryReq;
import com.cw.pojo.dto.conf.req.rules.RuleQueryReq;
import com.cw.pojo.dto.conf.req.rules.RuleSetupLoadReq;
import com.cw.pojo.dto.conf.req.rules.RulespolicyQueryReq;
import com.cw.pojo.dto.conf.res.rules.RuleProductListQueryRes;
import com.cw.pojo.dto.conf.res.rules.RuleSetupListRes;
import com.cw.pojo.dto.conf.res.rules.RulespolicyListRes;
import com.cw.pojo.entity.Rules_Entity;
import com.cw.pojo.entity.Rulespolicy_Entity;
import com.cw.service.config.rules.RulesService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/16 0016
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGRULES, position = SwaggerUtil.MainIndex.CONFIGRULES_INDEX)
@RestController
@RequestMapping(value = "api/salerules", method = RequestMethod.POST)
public class RulesController {

    @Autowired
    RulesService rulesService;

    @ApiOperation(value = "销售规则 - 获取规则列表数据", notes = "获取规则列表数据")
    @RequestMapping(value = "/rules_list")
    @RequireOpRight(opRight = OpRightCodes.OP_RULE_LIST)
    public ResultJson<RuleSetupListRes> rules_buy_ticket_list(@RequestBody RuleQueryReq req) {
        return ResultJson.ok().data(rulesService.queryTableData(req));
    }

    @ApiOperation(value = "销售规则 - 获取规则对象", notes = "获取规则对象")
    @RequestMapping(value = "/load_rule")
    @RequireOpRight(opRight = OpRightCodes.OP_RULE_LOAD)
    public ResultJson<Rules_Entity> rules_load_buy_ticket(@RequestBody RuleSetupLoadReq req) {
        return ResultJson.ok().data(rulesService.loadRule(req.getSqlid()));
    }

    @ApiOperation(value = "销售规则 - 保存规则", notes = "保存规则")
    @RequestMapping(value = "/save_rule")
    @RequireOpRight(opRight = OpRightCodes.OP_RULE_SAVE)
    public ResultJson<Rules_Entity> rules_save_buy_ticket(@Validated @RequestBody Rules_Entity req) {
        return ResultJson.ok().data(rulesService.saveRule(req));
    }

    @ApiOperation(value = "销售规则 - 删除规则", notes = "删除规则")
    @RequestMapping(value = "/delete_rule")
    @RequireOpRight(opRight = OpRightCodes.OP_RULE_DELETE)
    public ResultJson rules_delete_buy_ticket(@RequestBody Common_Del_Req req) {
        rulesService.deleteRule(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperation(value = "销售规则 - 使用对象查询", notes = "使用对象查询")
    @RequestMapping(value = "/rule_product_list")
    @RequireOpRight(opRight = OpRightCodes.OP_RULE_USING)
    public ResultJson<RuleProductListQueryRes> rule_product_list(@Validated @RequestBody RuleProductQueryReq req) {
        return ResultJson.ok().data(rulesService.getProductList(req));
    }

    @ApiOperation(value = "销售规则 - 更新规则开关状态.对当前状态取反", notes = "更新规则状态")
    @RequestMapping(value = "/update_rule_status")
    @RequireOpRight(opRight = OpRightCodes.OP_RULE_STATUS)
    public ResultJson updRuleStatus(@RequestBody Common_Switch_Req req) {
        rulesService.updRuleStatus(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperation(value = "销售策略 - 获取策略列表数据", notes = "获取策略列表数据")
    @RequestMapping(value = "/rulespolicy_list")
    @RequireOpRight(opRight = OpRightCodes.OP_RULESPOLICY_LIST)
    public ResultJson<RulespolicyListRes> rulespolicy_list(@RequestBody RulespolicyQueryReq req) {
        return ResultJson.ok().data(rulesService.queryRulespolicyList(req));
    }

    @ApiOperation(value = "销售策略 - 获取策略对象", notes = "获取策略对象")
    @RequestMapping(value = "/load_rulespolicy")
    @RequireOpRight(opRight = OpRightCodes.OP_RULESPOLICY_LOAD)
    public ResultJson<Rulespolicy_Entity> load_rulespolicy(@RequestBody Common_Load_Req req) {
        return ResultJson.ok().data(rulesService.loadRulespolicy(req.getSqlid()));
    }


    @ApiOperation(value = "销售策略 - 保存策略", notes = "保存策略")
    @RequestMapping(value = "/save_rulespolicy")
    @RequireOpRight(opRight = OpRightCodes.OP_RULESPOLICY_save)
    public ResultJson<Rulespolicy_Entity> save_rulespolicy(@Validated @RequestBody Rulespolicy_Entity req) {
        return ResultJson.ok().data(rulesService.saveRulespolicy(req));
    }

    @ApiOperation(value = "销售策略 - 删除策略", notes = "删除策略")
    @RequestMapping(value = "/delete_rulespolicy")
    @RequireOpRight(opRight = OpRightCodes.OP_RULESPOLICY_DELETE)
    public ResultJson delete_rulespolicy(@RequestBody Common_Del_Req req) {
        rulesService.deleteRulespolicy(req.getSqlid());
        return ResultJson.ok();
    }


}
