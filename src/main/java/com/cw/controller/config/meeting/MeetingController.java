package com.cw.controller.config.meeting;

import com.cw.cache.GlobalCache;
import com.cw.entity.Meeting;
import com.cw.entity.Meetinggroup;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.meeting.MeetingGroupListReq;
import com.cw.pojo.dto.conf.req.meeting.MeetingListReq;
import com.cw.pojo.dto.conf.req.meeting.MeetingLoadReq;
import com.cw.pojo.dto.conf.res.factor.FactorHeaderAndChildData;
import com.cw.pojo.dto.conf.res.meeting.MeetingGroupListRes;
import com.cw.pojo.dto.conf.res.meeting.MeetingListRes;
import com.cw.pojo.entity.MeetingGroup_Entity;
import com.cw.pojo.entity.Meeting_Entity;
import com.cw.service.config.meeting.MeetingService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/10/25 0025
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGMEETING, position = SwaggerUtil.MainIndex.CONFIGMEETING_INDEX)
@RestController
@RequestMapping(value = "api/meetingsetup", method = RequestMethod.POST)
public class MeetingController {
    @Autowired
    MeetingService meetingService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_GROUP_SETUP_LIST_INDEX)
    @ApiOperation(value = "会务管理设置 - 会场大类列表", notes = "获取会场大类列表数据")
    @RequestMapping(value = "/group_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGGROUP_LIST)
    public ResultJson<MeetingGroupListRes> group_list(@RequestBody MeetingGroupListReq req) {
        return ResultJson.ok().data(meetingService.queryMeetingGroupList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_GROUP_SETUP_GET_INDEX)
    @ApiOperation(value = "会务管理设置 - 获取会场大类对象", notes = "根据唯一id获取会场大类对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGGROUP_LOAD)
    public ResultJson<MeetingGroup_Entity> load_group(@RequestBody Common_Load_Req req) {
        Meetinggroup meetingGroup = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            meetingGroup = meetingService.loadMeetingGroup(req.getSqlid());
            if (meetingGroup == null) {
                meetingGroup = new Meetinggroup();
            }
        } else {
            meetingGroup = new Meetinggroup();
        }
        MeetingGroup_Entity entity = new MeetingGroup_Entity();
        BeanUtils.copyProperties(meetingGroup, entity);
        //查找会议室
        entity.setMeetingCode(meetingService.getMeetingGroupData(entity.getCode(), meetingGroup.getProjectid()));

        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_GROUP_SETUP_SAVE_INDEX)
    @ApiOperation(value = "会务管理设置 - 保存会场大类", notes = "保存会场大类")
    @RequestMapping(value = "/save_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGGROUP_SAVE)
    public ResultJson<MeetingGroup_Entity> save_group(@RequestBody MeetingGroup_Entity req) {
        MeetingGroup_Entity entity = meetingService.saveMeetingGroup(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_GROUP_SETUP_DELETE_INDEX)
    @ApiOperation(value = "会务管理设置 - 删除会场大类", notes = "删除会场大类")
    @RequestMapping(value = "/delete_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGGROUP_DELETE)
    public ResultJson delete_group(@RequestBody Common_Del_Req req) {
        meetingService.deleteMeetingGroup(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_SETUP_LIST_INDEX)
    @ApiOperation(value = "会务管理设置 - 会议室列表", notes = "获取会议室列表数据")
    @RequestMapping(value = "/meeting_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGROOM_LIST)
    public ResultJson<MeetingListRes> meeting_list(@RequestBody MeetingListReq req) {
        return ResultJson.ok().data(meetingService.queryMeetingList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_GROUP_SETUP_GET_INDEX)
    @ApiOperation(value = "会务设置 - 获取会议室对象", notes = "根据唯一id获取会议室对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_meeting")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGROOM_LOAD)
    public ResultJson<Meeting_Entity> load_meeting(@RequestBody MeetingLoadReq req) {
        Meeting meeting = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            meeting = meetingService.loadMeeting(req.getSqlid());
            if (meeting == null) {
                meeting = new Meeting();
            }
        } else {
            meeting = new Meeting();
            Meetinggroup meetingGroup = (Meetinggroup) GlobalCache.getDataStructure()
                    .getCache(SystemUtil.GlobalDataType.MEETINGGROUP).getRecord(GlobalContext.getCurrentProjectId()
                            , req.getGroupip());
            if (meetingGroup != null) {
                meeting.setGroupid(meetingGroup.getCode());
            }
        }
        Meeting_Entity entity = new Meeting_Entity();
        BeanUtils.copyProperties(meeting, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_SETUP_SAVE_INDEX)
    @ApiOperation(value = "会务设置 - 保存会议室", notes = "保存会议室")
    @RequestMapping(value = "/save_meeting")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGROOM_SAVE)
    public ResultJson<Meeting_Entity> save_meeting(@RequestBody Meeting_Entity req) {
        Meeting_Entity entity = meetingService.saveMeeting(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_SETUP_DELETE_INDEX)
    @ApiOperation(value = "会务设置 - 删除会议室", notes = "删除会议室")
    @RequestMapping(value = "/delete_meeting")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGROOM_DELETE)
    public ResultJson delete_meeting(@RequestBody Common_Del_Req req) {
        meetingService.deleteMeeting(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "会务设置 - 更新会议室开关状态.对当前状态取反", notes = "更新会议室状态")
    @RequestMapping(value = "/update_meeting_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGROOM_STATUS)
    public ResultJson updMeetingStatus(@Valid @RequestBody Common_Load_Req req) {
        meetingService.updMeetingStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_GROUP_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "会务设置 - 更新会务大类开关状态.对当前状态取反", notes = "更新会务大类状态")
    @RequestMapping(value = "/update_meetingGroup_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_MEETINGGROUP_STATUS)
    public ResultJson updMeetingGroupStatus(@Valid @RequestBody Common_Switch_Req req) {
        meetingService.updMeetingGroupStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_GROUP_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "会务设置 - 获取会场配套", notes = "获取会场配套")
    @RequestMapping(value = "/mgkit_list")
    public ResultJson<FactorHeaderAndChildData> mgkit_list(@Valid @RequestBody Common_Load_Req req) {
        return ResultJson.ok().data(meetingService.getMeetingGroupKit(req.getSqlid()));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMeeting.MEETING_SETUP_KIT_LIST_INDEX)
    @ApiOperation(value = "会务设置 - 获取会议室配套", notes = "获取会议室配套")
    @RequestMapping(value = "/meetkit_list")
    public ResultJson<FactorHeaderAndChildData> meetkit_list(@Valid @RequestBody Common_Load_Req req) {
        return ResultJson.ok().data(meetingService.getMeetingKit(req.getSqlid()));
    }
}
