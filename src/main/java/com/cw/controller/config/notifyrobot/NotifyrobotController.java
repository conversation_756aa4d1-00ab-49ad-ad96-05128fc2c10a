package com.cw.controller.config.notifyrobot;

import com.cw.entity.Notifyrobot;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.conf.req.notifyrobot.NotifyRobot_ChagneStatus_Req;
import com.cw.pojo.dto.conf.req.notifyrobot.NotifyRobot_Delete_Req;
import com.cw.pojo.dto.conf.req.notifyrobot.NotifyRobot_List_Req;
import com.cw.pojo.dto.conf.req.notifyrobot.NotifyRobot_Load_Req;
import com.cw.pojo.dto.conf.res.notifyrobot.NotifyRobot_List_Resp;
import com.cw.pojo.entity.Notifyrobot_Entity;
import com.cw.service.config.notifyrobot.NotifyRobotService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = SwaggerUtil.MainIndex.CONFIGNOTIFYROBOT, position = SwaggerUtil.MainIndex.CONFIGNOTIFYROBOT_INDEX)
@RestController
@RequestMapping(value = "api/notifyrobot", method = RequestMethod.POST)
public class NotifyrobotController {

    @Autowired
    private NotifyRobotService notifyRobotService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNotifyRobot.ROBOT_LIST_INDEX)
    @ApiOperation(value = "机器人设置 - 获取机器人列表", notes = "获取机器人列表")
    @RequestMapping(value = "list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ROBOT_LIST)
    public ResultJson<NotifyRobot_List_Resp> query_robotList(@RequestBody NotifyRobot_List_Req req) {
        return ResultJson.ok().data(notifyRobotService.findList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNotifyRobot.ROBOT_GET_INDEX)
    @ApiOperation(value = "机器人设置 - 获取机器人对象", notes = "获取机器人对象")
    @RequestMapping(value = "load")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ROBOT_LOAD)
    public ResultJson<Notifyrobot_Entity> queryOne(@RequestBody NotifyRobot_Load_Req req) {
        Notifyrobot notifyrobot = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            notifyrobot = notifyRobotService.loadRobot(req.getSqlid());
        } else {
            notifyrobot = new Notifyrobot();
            if (req.getCopyid() != null && req.getCopyid() > 0) {
                Notifyrobot copyRobot = notifyRobotService.loadRobot(req.getCopyid());
                if (copyRobot != null) {
                    BeanUtils.copyProperties(copyRobot, notifyrobot, new String[]{"sqlid", "code", "robotstatus", "group"});
                }
            }
        }
        Notifyrobot_Entity entity = new Notifyrobot_Entity();
        BeanUtils.copyProperties(notifyrobot, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNotifyRobot.ROBOT_SAVE_INDEX)
    @ApiOperation(value = "机器人设置 - 保存机器人设置", notes = "保存机器人设置")
    @RequestMapping(value = "/save")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ROBOT_SAVE)
    public ResultJson<Notifyrobot_Entity> save_robot(@RequestBody Notifyrobot_Entity entity) {
        return ResultJson.ok().data(notifyRobotService.save(entity));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNotifyRobot.ROBOT_DELETE_INDEX)
    @ApiOperation(value = "机器人设置 - 删除通知机器人", notes = "删除通知机器人")
    @RequestMapping(value = "/delete")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ROBOT_DELETE)
    public ResultJson delete_robot(@RequestBody NotifyRobot_Delete_Req req) {
        notifyRobotService.delete(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNotifyRobot.ROBOT_TEST_INDEX)
    @ApiOperation(value = "机器人设置 - 测试机器人通知", notes = "测试机器人通知")
    @RequestMapping(value = "/test")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ROBOT_TEST)
    public ResultJson<String> test_notify(@RequestBody Common_Load_Req req) {
        String msg = notifyRobotService.test_notify(req);
        return ResultJson.ok().data(msg);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNotifyRobot.ROBOT_STATUS_INDEX)
    @ApiOperation(value = "机器人设置 - 机器人状态取反", notes = "机器人状态取反")
    @RequestMapping(value = "/change_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ROBOT_STATUS)
    public ResultJson<Notifyrobot_Entity> change_status(@RequestBody NotifyRobot_ChagneStatus_Req req) {
        return ResultJson.ok().data(notifyRobotService.changeStatus(req.getSqlid()));
    }

}
