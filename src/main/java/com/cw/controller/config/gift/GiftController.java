package com.cw.controller.config.gift;

import com.cw.cache.GlobalCache;
import com.cw.entity.Gift;
import com.cw.entity.Giftitem;
import com.cw.entity.Postage;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Query_Page_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.gift.*;
import com.cw.pojo.dto.conf.res.gift.GiftListRes;
import com.cw.pojo.dto.conf.res.gift.GiftitemListRes;
import com.cw.pojo.dto.conf.res.gift.GiftitemResourcePriceListRes;
import com.cw.pojo.dto.conf.res.gift.PostageListRes;
import com.cw.pojo.entity.Gift_Entity;
import com.cw.pojo.entity.Giftitem_Entity;
import com.cw.pojo.entity.Postage_Entity;
import com.cw.service.config.gift.GiftSetupService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2022-02-24
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGGIFT, position = SwaggerUtil.MainIndex.CONFIGGIFT_INDEX)
@RestController
@RequestMapping(value = "api/gift", method = RequestMethod.POST)
public class GiftController {

    @Autowired
    GiftSetupService giftSetupService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFT_SETUP_LIST_INDEX)
    @ApiOperation(value = "伴手礼管理 - 类目模板列表", notes = "获取类目模板列表数据")
    @RequestMapping(value = "/gift_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFT_LIST)
    public ResultJson<GiftListRes> Gift_list(@Validated @RequestBody GiftListReq req) {
        return ResultJson.ok().data(giftSetupService.queryGiftList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFT_SETUP_GET_INDEX)
    @ApiOperation(value = "伴手礼管理 - 获取类目模板", notes = "根据唯一id获取类目模板对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_gift")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFT_LOAD)
    public ResultJson<Gift_Entity> load_Gift(@Validated @RequestBody GiftLoadReq req) {
        Gift gift = new Gift();
        if (StringUtils.isNotBlank(req.getSearchkey())) {
            gift = giftSetupService.loadGift(req.getSearchkey());
        } else {
            gift = giftSetupService.loadGift(req.getSqlid());
        }
        Gift_Entity entity = new Gift_Entity();
        BeanUtils.copyProperties(gift, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFT_SETUP_SAVE_INDEX)
    @ApiOperation(value = "伴手礼管理 - 保存类目模板", notes = "保存类目模板")
    @RequestMapping(value = "/save_gift")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFT_SAVE)
    public ResultJson<Gift_Entity> save_Gift(@RequestBody Gift_Entity req) {
        return ResultJson.ok().data(giftSetupService.saveGift(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFT_SETUP_DELETE_INDEX)
    @ApiOperation(value = "伴手礼管理 - 删除类目模板", notes = "删除类目模板")
    @RequestMapping(value = "/delete_gift")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFT_DELETE)
    public ResultJson delete_Gift(@RequestBody Common_Del_Req req) {
        giftSetupService.deleteGift(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFT_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "伴手礼管理 - 更新类目模板开关状态.对当前状态取反", notes = "更新类目模板状态")
    @RequestMapping(value = "/update_Gift_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFT_STATUS)
    public ResultJson updGiftStatus(@Valid @RequestBody Common_Load_Req req) {
        giftSetupService.updGiftStatus(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFTITEM_SETUP_LIST_INDEX)
    @ApiOperation(value = "伴手礼管理 - 商品列表", notes = "商品列表数据")
    @RequestMapping(value = "/giftitem_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFTITEM_LIST)
    public ResultJson<GiftitemListRes> giftitem_list(@RequestBody GiftitemListReq req) {
        return ResultJson.ok().data(giftSetupService.queryGiftitemList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFTITEM_SETUP_GET_INDEX)
    @ApiOperation(value = "伴手礼管理 - 获取商品对象", notes = "根据唯一id获取产品详情对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_giftitem")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFTITEM_LOAD)
    public ResultJson<Giftitem_Entity> load_giftitem(@RequestBody Common_Load_Req req) {
        Giftitem giftitem = giftSetupService.loadGiftitem(req.getSqlid());
        Giftitem_Entity entity = new Giftitem_Entity();
        BeanUtils.copyProperties(giftitem, entity);
        Gift gift = (Gift) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT).getRecord(giftitem.getProjectid(), giftitem.getGroupid());
        if (gift != null) {
            entity.setParamkey(gift.getTags());
        }
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFTITEM_SETUP_SAVE_INDEX)
    @ApiOperation(value = "伴手礼管理 - 保存商品", notes = "保存商品")
    @RequestMapping(value = "/save_giftitem")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFTITEM_SAVE)
    public ResultJson<Giftitem_Entity> save_giftitem(@RequestBody Giftitem_Entity req) {
        Giftitem_Entity entity = giftSetupService.saveGiftitem(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFTITEM_SETUP_DELETE_INDEX)
    @ApiOperation(value = "伴手礼管理 - 删除商品", notes = "删除商品")
    @RequestMapping(value = "/delete_giftitem")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFTITEM_DELETE)
    public ResultJson delete_giftitem(@RequestBody Common_Del_Req req) {
        giftSetupService.deleteGiftitem(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFTITEM_SETUP_RESOURCE_PRICE_INDEX)
    @ApiOperation(value = "伴手礼管理 - 获取商品库存&价格", notes = "获取商品库存&库存")
    @RequestMapping(value = "/load_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFTITEM_RESOURCE_INDEX)
    public ResultJson<List<GiftitemResourcePriceListRes>> load_resources_price(@Validated @RequestBody GiftitemResourceQueryReq req) {
        return ResultJson.ok().data(giftSetupService.queryGiftitemResourceList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigProduct.PRODUCT_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "伴手礼管理 - 设置商品库存&价格", notes = "设置商品库存&价格")
    @RequestMapping(value = "/save_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFTITEM_RESOURCE_SAVE)
    public ResultJson save_resources_price(@Validated @RequestBody GiftitemResourcePriceSaveReq req) {
        giftSetupService.batchGiftitem(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.GIFTITEM_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "伴手礼管理 - 更新产品开关状态.对当前状态取反", notes = "更新产品状态")
    @RequestMapping(value = "/update_giftitem_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_GIFTITEM_STATUS)
    public ResultJson updGiftitemStatus(@Valid @RequestBody Common_Switch_Req req) {
        giftSetupService.updGiftitemStatus(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.POSTAGE_SETUP_LIST_INDEX)
    @ApiOperation(value = "运费设置 - 区域自定义邮费列表数据", notes = "区域自定义邮费列表数据")
    @RequestMapping(value = "/postage_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CUSTOM_POSTAGE_LIST)
    public ResultJson<PostageListRes> postage_list(@RequestBody Common_Query_Page_Req req) {
        return ResultJson.ok().data(giftSetupService.queryPostageList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.POSTAGE_SYS_SETUP_GET_INDEX)
    @ApiOperation(value = "运费设置 - 获取系统运费配置", notes = "获取运费配置")
    @RequestMapping(value = "/postage_sys")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_SYSTEM_POSTAGE_DELETE)
    public ResultJson<Postage_Entity> postage_sys() {
        return ResultJson.ok().data(giftSetupService.getSysPostage(GlobalContext.getCurrentProjectId()));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.POSTAGE_SETUP_GET_INDEX)
    @ApiOperation(value = "运费设置 - 获取自定义运费设置详情", notes = "根据唯一id获取邮费对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_postage")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CUSTOM_POSTAGE_LOAD)
    public ResultJson<Postage_Entity> load_postage(@RequestBody Common_Load_Req req) {
        Postage postage = giftSetupService.loadPostage(req.getSqlid());
        Postage_Entity entity = new Postage_Entity();
        BeanUtils.copyProperties(postage, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.POSTAGE_SETUP_SAVE_INDEX)
    @ApiOperation(value = "运费设置 - 保存运费设置", notes = "保存运费设置")
    @RequestMapping(value = "/save_postage")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CUSTOM_POSTAGE_SAVE)
    public ResultJson<Postage_Entity> save_postage(@RequestBody Postage_Entity req) {
        Postage_Entity entity = giftSetupService.savePostage(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigGift.POSTAGE_SETUP_DELETE_INDEX)
    @ApiOperation(value = "运费设置 - 删除运费设置", notes = "删除运费设置")
    @RequestMapping(value = "/delete_postage")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CUSTOM_POSTAGE_DELETE)
    public ResultJson delete_postage(@RequestBody Common_Del_Req req) {
        giftSetupService.deletePostage(req.getSqlid());
        return ResultJson.ok();
    }
}
