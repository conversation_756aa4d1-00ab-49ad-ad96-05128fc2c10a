package com.cw.controller.config.hotel;

import com.cw.entity.Hotel;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Query_Page_Req;
import com.cw.pojo.dto.conf.res.factor.FactorHeaderAndChildData;
import com.cw.pojo.dto.conf.res.hotel.HotelListRes;
import com.cw.pojo.entity.Hotel_Entity;
import com.cw.service.config.hotel.HotelService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/17 0017
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGHOTEL, position = SwaggerUtil.MainIndex.CONFIGHOTEL_INDEX)
@RestController
@RequestMapping(value = "api/hotel", method = RequestMethod.POST)
public class HotelController {

    @Autowired
    HotelService hotelService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.HOTEL_SETUP_LIST_INDEX)
    @ApiOperation(value = "酒店设置 - 获取酒店列表数据", notes = "获取酒店设置列表数据")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_HOTEL_LIST)
    public ResultJson<HotelListRes> fetchlist(@RequestBody Common_Query_Page_Req req) {
        return ResultJson.ok().data(hotelService.queryTableData(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.HOTEL_SETUP_GET_INDEX)
    @ApiOperation(value = "酒店设置 - 获取酒店对象", notes = "根据唯一id获取酒店对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_HOTEL_LOAD)
    public ResultJson<Hotel_Entity> load(@RequestBody Common_Load_Req req) {
        Hotel hotel = null;
        if (req.getSqlid() > 0) {
            hotel = hotelService.loadHotel(req.getSqlid());
        } else {
            hotel = new Hotel();
        }
        Hotel_Entity entity = new Hotel_Entity();
        BeanUtils.copyProperties(hotel, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.HOTEL_SETUP_SAVE_INDEX)
    @ApiOperation(value = "酒店设置 - 保存酒店", notes = "保存酒店")
    @RequestMapping(value = "/save")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_HOTEL_SAVE)
    public ResultJson<Hotel_Entity> save(@RequestBody Hotel_Entity req) {
        Hotel_Entity entity = hotelService.saveHotel(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.HOTEL_SETUP_DELETE_INDEX)
    @ApiOperation(value = "酒店设置 - 删除酒店", notes = "删除酒店")
    @RequestMapping(value = "/delete")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_HOTEL_DELETE)
    public ResultJson delete(@RequestBody Common_Del_Req req) {
        hotelService.deleteHotel(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.HOTEL_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "酒店设置 - 更新酒店开关状态.对当前状态取反", notes = "更新酒店状态")
    @RequestMapping(value = "/update_hotel_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_HOTEL_STATUS)
    public ResultJson updHotelStatus(@Valid @RequestBody Common_Load_Req req) {
        hotelService.updHotelStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigHotel.HOTEL_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "酒店设置 - 获取酒店配套", notes = "获取酒店配套")
    @RequestMapping(value = "/hotelkit_list")
    public ResultJson<FactorHeaderAndChildData> hotelKit_list(@Valid @RequestBody Common_Load_Req req) {
        return ResultJson.ok().data(hotelService.getHotelKit(req.getSqlid()));
    }
}
