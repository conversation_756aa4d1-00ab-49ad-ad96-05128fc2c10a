package com.cw.controller.config.coupon;

import com.cw.entity.Coupon;
import com.cw.entity.Coupongroup;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.req.coupon.*;
import com.cw.pojo.dto.conf.res.coupon.*;
import com.cw.pojo.entity.Coupon_Entity;
import com.cw.pojo.entity.Coupongroup_Entity;
import com.cw.service.config.coupon.CouponService;
import com.cw.utils.CalculateDate;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2024-01-21
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGCOUPON, position = SwaggerUtil.MainIndex.CONFIGCOUPON_INDEX)
@RestController
@RequestMapping(value = "api/coupon", method = RequestMethod.POST)
public class CouponController {
    @Autowired
    CouponService couponService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPONGROUP_SETUP_LIST_INDEX)
    @ApiOperation(value = "优惠券设置 - 优惠券大类列表", notes = "获取优惠券大类列表数据")
    @RequestMapping(value = "/group_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPONGROUP_LIST)
    public ResultJson<CoupongroupListRes> group_list(@Validated @RequestBody CoupongroupListReq req) {
        return ResultJson.ok().data(couponService.queryCoupongroupList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPONGROUP_SETUP_GET_INDEX)
    @ApiOperation(value = "优惠券设置 - 获取优惠券大类对象", notes = "根据唯一id获取优惠券大类对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_coupongroup")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPONGROUP_LOAD)
    public ResultJson<Coupongroup_Entity> load_coupongroup(@Validated @RequestBody Common_Load_Req req) {
        Coupongroup coupongroup = couponService.loadCoupongroup(req.getSqlid());
        Coupongroup_Entity entity = new Coupongroup_Entity();
        BeanUtils.copyProperties(coupongroup, entity);
        if (CalculateDate.emptyDate(coupongroup.getStartdate())) {
            entity.setStartdate(null);
            entity.setEnddate(null);
        }
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPONGROUP_SETUP_SAVE_INDEX)
    @ApiOperation(value = "优惠券设置 - 保存优惠券大类", notes = "保存优惠券大类")
    @RequestMapping(value = "/save_coupongroup")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPONGROUP_SAVE)
    public ResultJson<Coupongroup_Entity> save_coupongroup(@RequestBody Coupongroup_Entity req) {
        return ResultJson.ok().data(couponService.saveCoupongroup(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPONGROUP_SETUP_DELETE_INDEX)
    @ApiOperation(value = "优惠券设置 - 删除优惠券大类", notes = "删除优惠券大类")
    @RequestMapping(value = "/delete_coupongroup")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPONGROUP_DELETE)
    public ResultJson delete_coupongroup(@RequestBody Common_Del_Req req) {
        couponService.deleteCoupongroup(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPONGROUP_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "优惠券设置 - 更新优惠券大类开关状态.对当前状态取反", notes = "更新优惠券大类状态")
    @RequestMapping(value = "/update_coupongroup_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPONGROUP_STATUS)
    public ResultJson update_coupongroup_status(@Valid @RequestBody Common_Load_Req req) {
        couponService.updCoupongroupStatus(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPON_SETUP_LIST_INDEX)
    @ApiOperation(value = "优惠券设置 - 优惠券列表", notes = "获取优惠券列表数据")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPON_LIST)
    public ResultJson<CouponListRes> list(@Validated @RequestBody CouponListReq req) {
        return ResultJson.ok().data(couponService.queryCouponList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPON_SETUP_GET_INDEX)
    @ApiOperation(value = "优惠券设置 - 获取优惠券对象", notes = "根据唯一id获取优惠券对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_coupon")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPON_LOAD)
    public ResultJson<Coupon_Entity> load_coupon(@Validated @RequestBody Common_Load_Req req) {
        Coupon coupon = couponService.loadCoupon(req.getSqlid());
        Coupon_Entity entity = new Coupon_Entity();
        BeanUtils.copyProperties(coupon, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPON_SETUP_SAVE_INDEX)
    @ApiOperation(value = "优惠券设置 - 保存优惠券", notes = "保存优惠券")
    @RequestMapping(value = "/save_coupon")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPON_SAVE)
    public ResultJson<Coupon_Entity> save_group(@RequestBody Coupon_Entity req) {
        return ResultJson.ok().data(couponService.saveCoupon(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPON_SETUP_DELETE_INDEX)
    @ApiOperation(value = "优惠券设置 - 删除优惠券", notes = "删除优惠券")
    @RequestMapping(value = "/delete_coupon")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPON_DELETE)
    public ResultJson delete_group(@RequestBody Common_Del_Req req) {
        couponService.deleteCoupon(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPON_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "优惠券设置 - 更新优惠券开关状态.对当前状态取反", notes = "更新优惠券大类状态")
    @RequestMapping(value = "/update_coupon_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPON_STATUS)
    public ResultJson updGroupStatus(@Valid @RequestBody Common_Load_Req req) {
        couponService.updCouponStatus(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPON_SETUP_RESOURCE_PRICE_INDEX)
    @ApiOperation(value = "优惠券设置 - 获取优惠券库存", notes = "获取优惠券库存")
    @RequestMapping(value = "/load_coupon_avl")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPON_RESOURCE_INDEX)
    public ResultJson<CouponResourceRes> load_coupon_avl(@Validated @RequestBody CouponResourceQueryReq req) {
        return ResultJson.ok().data(couponService.queryCouponResourceList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.COUPON_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "优惠券设置 - 设置优惠券库存", notes = "设置优惠券库存")
    @RequestMapping(value = "/save_coupon_avl")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPON_RESOURCE_SAVE)
    public ResultJson save_coupon_avl(@Validated @RequestBody RCouponSaveReq req) {
        couponService.batchProduct(req);
        return ResultJson.ok();
    }

    /**
     * 用户优惠券
     */
    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.USERCOUPON_SETUP_LIST_INDEX)
    @ApiOperation(value = "用户优惠券设置 - 产品核销列表", notes = "产品核列表")
    @RequestMapping(value = "/product_list")
    //@RequireOpRight(opRight = OpRightCodes.OP_CONFIG_USERCOUPON_LIST)
    public ResultJson<ProductCheckListRes> product_list(@Valid @RequestBody ProductChecktListReq req) {
        return ResultJson.ok().data(couponService.queryProductCheckList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.USERCOUPON_SETUP_LIST_INDEX)
    @ApiOperation(value = "用户优惠券设置 - 产品核销统计", notes = "核销统计")
    @RequestMapping(value = "/product_static")
    //@RequireOpRight(opRight = OpRightCodes.OP_CONFIG_USERCOUPON_LIST)
    public ResultJson<ProductCheckStaticRes> product_static(@Valid @RequestBody ProductCheckStaticReq req) {
        return ResultJson.ok().data(couponService.staticProductCheck(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.USERCOUPON_SETUP_LIST_INDEX + 1)
    @ApiOperation(value = "用户优惠券设置 - 产品统计明细", notes = "获取产品统计明细数据")
    @RequestMapping(value = "/product_static_detail")
    //@RequireOpRight(opRight = OpRightCodes.OP_CONFIG_USERCOUPON_LIST)
    public ResultJson<ProductStaticDetailListRes> product_static_detail(@Valid @RequestBody ProductStaticDetailReq req) {
        return ResultJson.ok().data(couponService.getProductStaticDetail(req));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.USERCOUPON_SETUP_GET_INDEX)
    @ApiOperation(value = "用户优惠券设置 - 获取产品对象", notes = "根据唯一id获取产品详情对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_product")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_USERCOUPON_LOAD)
    public ResultJson<ProductDetail> load_product(@Valid @RequestBody Usercoupon_Query_Req req) {
        ProductDetail entity = couponService.loadProductQr(req.getCouponid());
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.USERCOUPON_SETUP_CHECK_INDEX)
    @ApiOperation(value = "用户优惠券设置 - 核销产品二维码", notes = "产品二维码核销")
    @RequestMapping(value = "/check_qrcode")
    //@RequireOpRight(opRight = OpRightCodes.OP_CONFIG_USERCOUPON_CHECK)  2023.2.8 暂时去掉.员工端核销有问题
    public ResultJson<Common_response> save_product(@RequestBody ProductCheckReq req) {
        String desc = couponService.productQRCodeCheck(req);
        Common_response response = new Common_response();
        response.setMsg(desc);
        return ResultJson.ok().data(response);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigCoupon.USERCOUPON_SETUP_CHECK_INDEX)
    @ApiOperation(value = "优惠券设置 - 获取优惠券大类使用统计", notes = "获取优惠券大类使用统计")
    @RequestMapping(value = "/get_coupongroup_useData")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_COUPONGROUP_STATI)
    public ResultJson<CouponGroupUseData> get_coupongroup_useData(@RequestBody CoupongroupStatiReq req) {
        CouponGroupUseData entity = couponService.statisticsByCoupongroup(req);
        return ResultJson.ok().data(entity);
    }

}
