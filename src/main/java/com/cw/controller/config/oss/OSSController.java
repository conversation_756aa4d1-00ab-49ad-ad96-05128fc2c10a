package com.cw.controller.config.oss;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cw.entity.Ossdir;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.conf.req.oss.*;
import com.cw.pojo.dto.conf.res.oss.OSSObjectInfo;
import com.cw.pojo.dto.conf.res.oss.OSSObjectList;
import com.cw.pojo.dto.conf.res.oss.OSSUploadFileRes;
import com.cw.pojo.entity.Ossdir_Entity;
import com.cw.service.context.GlobalContext;
import com.cw.service.oss.OSSService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.pagedata.SelectDataNode;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/23 0023
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGOSS, position = SwaggerUtil.MainIndex.CONFIGOSS_INDEX)
@RestController
@RequestMapping(value = "api/oss", method = RequestMethod.POST)
public class OSSController {


    @Resource(name = "${oss.type}")
    OSSService ossService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOss.OSS_UPLOAD_FILE_INDEX)
    @ApiOperation(value = "OSS设置 - 上传文件", notes = "OSS上传文件")
    @RequestMapping(value = "/upload_file", method = RequestMethod.POST)
    @ApiImplicitParam(name = "file", value = "上传文件", required = true, dataType = "__File")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_UPLOADFILE)
    public ResultJson<OSSUploadFileRes> upload_file(@RequestPart MultipartFile file, OSSUploadFileReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        Map<String, String> userMetadataMap = null;
        if (StringUtils.isNotBlank(req.getUserMetadata())) {
            userMetadataMap = JSON.parseObject(req.getUserMetadata(), new TypeReference<HashMap<String, String>>() {
            });
        }
        return ResultJson.ok().data(ossService.upLoadFile(file, req.getFileType(), req.getCompress(), projectId, userMetadataMap));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOss.OSS_DELETE_FILE_INDEX)
    @ApiOperation(value = "OSS设置 - 删除文件", notes = "OSS删除文件")
    @RequestMapping(value = "/delete_file", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_DELFILE)
    public ResultJson delete_file(OSSRemoveFileReq req) {
        return ResultJson.ok().data(ossService.removeFileByUrl(req.getOssFileUrl()));
    }




    @ApiOperation(value = "OSS设置 - 下载项目所有文件到本地目录", notes = "下载项目所有文件到本地目录")
    @RequestMapping(value = "/download", method = RequestMethod.POST)
    public ResultJson<OSSObjectList> download() throws IOException {
        String projectId = GlobalContext.getCurrentProjectId();
        ossService.downloadProjectFile(projectId);
        return ResultJson.ok();
    }


    @ApiOperation(value = "OSS设置 - 获取OSS文件自定义信息", notes = "获取OSS文件自定义信息")
    @RequestMapping(value = "/fileInfo", method = RequestMethod.POST)
    public ResultJson<OSSObjectInfo> fileInfo(@RequestBody OSSFileInfoReq req) throws IOException {
        OSSObjectInfo info = ossService.getOSSObjectInfo(req.getFileUrl());
        return ResultJson.ok().data(info);
    }

    @ApiOperation(value = "OSS设置 - 获取素材中心所有目录", notes = "获取素材中心所有目录")
    @RequestMapping(value = "/getall_dir", method = RequestMethod.POST)
    public ResultJson<List<SelectDataNode>> getalldir(String type) {
        String projectId = GlobalContext.getCurrentProjectId();
        List<SelectDataNode> list = ossService.getAllDir(projectId, type);
        return ResultJson.ok().data(list);
    }

    @ApiOperation(value = "OSS设置 - 获取当前目录下所有文件夹和文件", notes = "获取当前目录下所有文件夹和文件")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResultJson<OSSObjectList> fileList(OSSQueryReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        return ResultJson.ok().data(ossService.getAllFileUrl(req, projectId));
    }

    @ApiOperation(value = "OSS设置 - 新建目录", notes = "新建目录")
    @RequestMapping(value = "/mkdir", method = RequestMethod.POST)
    public ResultJson<Ossdir_Entity> mkdir(@RequestBody OSSMkdirReq req) throws IOException {
        Ossdir ossdir = ossService.mkdir(req);
        Ossdir_Entity entity = new Ossdir_Entity();
        BeanUtil.copyProperties(ossdir, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "OSS设置 - 重命名目录", notes = "重命名目录")
    @RequestMapping(value = "/rename_dir", method = RequestMethod.POST)
    public ResultJson<Ossdir_Entity> rename_dir(@RequestBody OSSRenameDirReq req) throws IOException {
        Ossdir ossdir = ossService.renameDir(req.getPath(), req.getNewName());
        Ossdir_Entity entity = new Ossdir_Entity();
        BeanUtil.copyProperties(ossdir, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperation(value = "OSS设置 - 删除目录及其文件", notes = "删除目录及其文件")
    @RequestMapping(value = "/deletedir", method = RequestMethod.POST)
    public ResultJson deletedir(@RequestBody OSSDeleteDirAndFilesReq req) throws IOException {
        ossService.deleteDirAndFile(req.getPath());
        return ResultJson.ok();
    }


    @ApiOperation(value = "OSS设置 - 文件重命名", notes = "文件重命名")
    @RequestMapping(value = "/rename_file", method = RequestMethod.POST)
    public ResultJson rename_file(@RequestBody OSSRenameFileReq req) throws IOException {
        ossService.renameFile(req.getOldUrl(), req.getNewName());
        return ResultJson.ok();
    }

}
