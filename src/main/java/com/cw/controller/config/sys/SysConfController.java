package com.cw.controller.config.sys;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Query_Req;
import com.cw.pojo.dto.conf.req.sys.Sysconf_Entity;
import com.cw.service.config.sys.SysConfService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGSYS, position = SwaggerUtil.MainIndex.CONFIGSYS_INDEX)
@RestController
@RequestMapping(value = "/api/sysconf", method = RequestMethod.POST)
public class SysConfController {

    @Autowired
    SysConfService sysConfService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigSys.LOADSYSCONF)
    @ApiOperation(value = "加载系统设置")
    @RequestMapping(value = "/loadconf")
    public ResultJson<Sysconf_Entity> loadConf(@RequestBody Common_Load_Req req) {
        Sysconf_Entity result = sysConfService.loadSysConf(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigSys.SAVESYSCONF)
    @ApiOperation(value = "保存系统设置")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_SYSCONF_BASE_SAVE)
    @RequestMapping(value = "/saveconf", method = RequestMethod.POST)
    public ResultJson saveConfig(@RequestBody Sysconf_Entity req) {
        sysConfService.updateSysConf(req); //.login(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigSys.SAVESYSCONF)
    @ApiOperation(value = "刷新系统设置")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_SYSCONF_BASE_SAVE)
    @RequestMapping(value = "/refreshconf", method = RequestMethod.POST)
    public ResultJson refresh(@RequestBody Common_Query_Req req) {
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigSys.CHANGESEQ)
    @ApiOperation(value = "更新表头排序")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_CHANGEORDER)
    @RequestMapping(value = "/updateTableSeq", method = RequestMethod.POST)
    public ResultJson updSeq(@RequestBody Sysconf_Entity req) {
        sysConfService.updateSysConf(req); //.login(req);
        return ResultJson.ok();
    }


}
