package com.cw.controller.config.sys;

import com.alibaba.fastjson.JSON;
import com.cw.pojo.common.EncodeAesData;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.conf.req.sys.Vendorconf_Entity;
import com.cw.service.config.sys.VendorConfService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGVENDOR, position = SwaggerUtil.MainIndex.CONFIGVENDOR_INDEX)
@RestController
@RequestMapping(value = "/api/vendorconf", method = RequestMethod.POST)
public class VendorConfController {

    @Autowired
    VendorConfService vendorConfService;

    @ApiOperation(value = "加载厂商设置")
    @RequireOpRight(opRight = OpRightCodes.OP_VENDOR_LOAD)
    @RequestMapping(value = "/load")
    public ResultJson<EncodeAesData<Vendorconf_Entity>> loadConf(@RequestBody Common_Load_Req req) {
        Vendorconf_Entity result = vendorConfService.loadVendorConf(req);
        EncodeAesData<Vendorconf_Entity> encodeAesData = EncodeAesData.builder();
        encodeAesData.setEncodeStr(JSON.toJSONString(result), GlobalContext.getCurrentProjectId());
        return ResultJson.ok().data(encodeAesData);
    }

    @ApiOperation(value = "保存厂商设置")
    @RequireOpRight(opRight = OpRightCodes.OP_VENDOR_SAVE)
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultJson saveConfig(@RequestBody EncodeAesData<Vendorconf_Entity> req) {
        Vendorconf_Entity postEntity = req.getDecodeData(Vendorconf_Entity.class, GlobalContext.getCurrentProjectId());
        vendorConfService.updateVendorConf(postEntity); //.login(req);
        return ResultJson.ok();
    }


    @ApiOperation(value = "应用厂商配置")
    @RequireOpRight(opRight = OpRightCodes.OP_VENDOR_DELETE)
    @RequestMapping(value = "/apply", method = RequestMethod.POST)
    public ResultJson apply(@RequestBody Common_Load_Req req) {
        vendorConfService.applyVendorConf(req); //.login(req);
        return ResultJson.ok();
    }


}
