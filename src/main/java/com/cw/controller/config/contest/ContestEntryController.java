package com.cw.controller.config.contest;

import com.cw.entity.Contestentry;
import com.cw.entity.Onlinecontest;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.conf.req.contestentry.ContestentryListReq;
import com.cw.pojo.dto.conf.req.contestentry.ContestentryRefuseReq;
import com.cw.pojo.dto.conf.req.contestentry.OnlinecontestListReq;
import com.cw.pojo.dto.conf.res.contestentry.ContestentryListRes;
import com.cw.pojo.dto.conf.res.contestentry.OnlinecontestListRes;
import com.cw.pojo.entity.Contestentry_Entity;
import com.cw.pojo.entity.Onlinecontest_Entity;
import com.cw.service.config.contest.ContestEntryService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2024-07-22
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGCONTEST, position = SwaggerUtil.MainIndex.CONFIGCONTEST_INDEX)
@RestController
@RequestMapping(value = "api/contest", method = RequestMethod.POST)
public class ContestEntryController {
    @Autowired
    ContestEntryService contestEntryService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONFIGCONTEST_SETUP_LIST_INDEX)
    @ApiOperation(value = "投稿作品设置 - 大赛列表", notes = "获取大赛列表数据")
    @RequestMapping(value = "/onlinecontest_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ONLINECONTEST_LIST)
    public ResultJson<OnlinecontestListRes> group_list(@Validated @RequestBody OnlinecontestListReq req) {
        return ResultJson.ok().data(contestEntryService.queryOnlineContestList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONFIGCONTEST_SETUP_GET_INDEX)
    @ApiOperation(value = "投稿作品设置 - 获取大赛对象", notes = "根据唯一id获取大赛对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_onlinecontest")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ONLINECONTEST_LOAD)
    public ResultJson<Onlinecontest_Entity> load_coupongroup(@Validated @RequestBody Common_Load_Req req) {
        Onlinecontest onlinecontest = contestEntryService.loadOnlineContest(req.getSqlid());
        Onlinecontest_Entity entity = new Onlinecontest_Entity();
        BeanUtils.copyProperties(onlinecontest, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONFIGCONTEST_SETUP_SAVE_INDEX)
    @ApiOperation(value = "投稿作品设置 - 保存大赛", notes = "保存大赛")
    @RequestMapping(value = "/save_onlinecontest")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ONLINECONTEST_SAVE)
    public ResultJson<Onlinecontest_Entity> save_coupongroup(@RequestBody Onlinecontest_Entity req) {
        return ResultJson.ok().data(contestEntryService.saveOnlineContest(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONFIGCONTEST_SETUP_DELETE_INDEX)
    @ApiOperation(value = "投稿作品设置 - 删除大赛", notes = "删除大赛")
    @RequestMapping(value = "/delete_onlinecontest")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ONLINECONTEST_DELETE)
    public ResultJson delete_coupongroup(@RequestBody Common_Del_Req req) {
        contestEntryService.deleteOnlineContest(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONFIGCONTEST_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "投稿作品设置 - 更新大赛开关状态.对当前状态取反", notes = "更新大赛状态")
    @RequestMapping(value = "/update_onlinecontest_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ONLINECONTEST_STATUS)
    public ResultJson update_coupongroup_status(@Valid @RequestBody Common_Load_Req req) {
        contestEntryService.updOnlineContestStatus(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONTEST_SETUP_LIST_INDEX)
    @ApiOperation(value = "投稿作品设置 - 投稿作品列表", notes = "获取投稿作品列表数据")
    @RequestMapping(value = "/contest_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CONTESTENTRY_LIST)
    public ResultJson<ContestentryListRes> list(@Validated @RequestBody ContestentryListReq req) {
        return ResultJson.ok().data(contestEntryService.queryContestentryList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONTEST_SETUP_GET_INDEX)
    @ApiOperation(value = "投稿作品设置 - 获取投稿作品对象", notes = "根据唯一id获取投稿作品对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_contest")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CONTESTENTRY_LOAD)
    public ResultJson<Contestentry_Entity> load_coupon(@Validated @RequestBody Common_Load_Req req) {
        Contestentry contestentry = contestEntryService.loadContestentry(req.getSqlid());
        Contestentry_Entity entity = new Contestentry_Entity();
        BeanUtils.copyProperties(contestentry, entity);
        return ResultJson.ok().data(entity);
    }

    //@ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONTEST_SETUP_SAVE_INDEX)
    //@ApiOperation(value = "投稿作品设置 - 保存投稿作品", notes = "保存投稿作品")
    //@RequestMapping(value = "/save_contest")
    //@RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CONTESTENTRY_SAVE)
    //public ResultJson<Contestentry_Entity> save_group(@RequestBody Contestentry_Entity req) {
    //    return ResultJson.ok().data(contestEntryService.saveOnlineContest(req));
    //}

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONTEST_SETUP_DELETE_INDEX)
    @ApiOperation(value = "投稿作品设置 - 删除投稿作品", notes = "删除投稿作品")
    @RequestMapping(value = "/delete_contest")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CONTESTENTRY_DELETE)
    public ResultJson delete_contest(@RequestBody Common_Del_Req req) {
        contestEntryService.deleteContestentry(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONTEST_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "投稿作品设置 - 投稿作品审核通过", notes = "投稿作品审核通过")
    @RequestMapping(value = "/confirm")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CONTESTENTRY_CONFIRM)
    public ResultJson confirm(@Valid @RequestBody Common_Load_Req req) {
        contestEntryService.confirmContestEntry(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigContest.CONTEST_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "投稿作品设置 - 投稿作品审核拒绝", notes = "投稿作品审核拒绝")
    @RequestMapping(value = "/refuse")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_CONTESTENTRY_REFUSE)
    public ResultJson refuse(@Valid @RequestBody ContestentryRefuseReq req) {
        contestEntryService.refuseContestEntry(req);
        return ResultJson.ok();
    }
}
