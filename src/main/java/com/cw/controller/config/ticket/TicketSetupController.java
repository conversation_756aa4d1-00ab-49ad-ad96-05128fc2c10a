package com.cw.controller.config.ticket;

import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.entity.Ticket;
import com.cw.entity.Ticketgroup;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.ticket.TicektResourceSaveReq;
import com.cw.pojo.dto.conf.req.ticket.TicketResourceQueryReq;
import com.cw.pojo.dto.conf.req.ticket.TicketSetupListReq;
import com.cw.pojo.dto.conf.req.ticket.TicketSetupLoadReq;
import com.cw.pojo.dto.conf.req.ticket.group.TicketSetupGroupListReq;
import com.cw.pojo.dto.conf.res.ticket.TicketResourcePriceListRes;
import com.cw.pojo.dto.conf.res.ticket.TicketSetup_GroupList_Res;
import com.cw.pojo.dto.conf.res.ticket.TicketSetup_List_Res;
import com.cw.pojo.entity.Ticket_Entity;
import com.cw.pojo.entity.Ticketgroup_Entity;
import com.cw.service.config.ticket.TicketSetupService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/14 0014
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGTICKET, position = SwaggerUtil.MainIndex.CONFIGTICKET_INDEX)
@RestController
@RequestMapping(value = "api/ticketsetup", method = RequestMethod.POST)
public class TicketSetupController {

    @Autowired
    TicketSetupService ticketSetupService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTicket.TICKETGROUP_SETUP_LIST_INDEX)
    @ApiOperation(value = "票务管理设置 - 票务大类列表", notes = "获取票务大类列表数据")
    @RequestMapping(value = "/group_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKETGROUP_LIST)
    public ResultJson<TicketSetup_GroupList_Res> group_list(@RequestBody TicketSetupGroupListReq req) {
        return ResultJson.ok().data(ticketSetupService.queryTicketgroupList(req));
    }

    @ApiOperationSupport(order=SwaggerUtil.SubIndex.ConfigTicket.TICKETGROUP_SETUP_GET_INDEX)
    @ApiOperation(value = "票务管理设置 - 获取票务大类对象", notes = "根据唯一id获取票务大类对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKETGROUP_LOAD)
    public ResultJson<Ticketgroup_Entity> load_group(@RequestBody Common_Load_Req req) {
        Ticketgroup ticketgroup = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            ticketgroup = ticketSetupService.loadTicketgroup(req.getSqlid());
        } else {
            ticketgroup = new Ticketgroup();
        }
        Ticketgroup_Entity entity = new Ticketgroup_Entity();
        BeanUtils.copyProperties(ticketgroup, entity);
        if (CalculateDate.emptyDate(entity.getStartdate())) {//非必填字段
            entity.setStartdate(null);
            entity.setEnddate(null);
        }
        if (CalculateDate.emptyDate(entity.getStartsell())) {//非必填字段
            entity.setStartsell(null);
            entity.setEndsell(null);
        }
        //获取票务绑定票型数据
        entity.setTicketCode(ticketSetupService.getTicketGroupData(entity.getCode()));
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTicket.TICKETGROUP_SETUP_SAVE_INDEX)
    @ApiOperation(value = "票务管理设置 - 保存票务大类", notes = "保存票务大类")
    @RequestMapping(value = "/save_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKETGROUP_SAVE)
    public ResultJson<Ticketgroup_Entity> save_group(@RequestBody Ticketgroup_Entity req) {
        Ticketgroup_Entity entity = ticketSetupService.saveTicketgroup(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order=SwaggerUtil.SubIndex.ConfigTicket.TICKETGROUP_SETUP_DELETE_INDEX)
    @ApiOperation(value = "票务管理设置 - 删除票务大类", notes = "删除票务大类")
    @RequestMapping(value = "/delete_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKETGROUP_DELETE)
    public ResultJson delete_group(@RequestBody Common_Del_Req req) {
        ticketSetupService.deleteTicketgroup(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTicket.TICKET_SETUP_LIST_INDEX)
    @ApiOperation(value = "票务管理设置 - 票型列表", notes = "获取票型列表数据")
    @RequestMapping(value = "/ticket_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKET_LIST)
    public ResultJson<TicketSetup_List_Res> ticket_list(@RequestBody TicketSetupListReq ticketSetup_list_req) {
        return ResultJson.ok().data(ticketSetupService.queryTicketList(ticketSetup_list_req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTicket.TICKET_SETUP_GET_INDEX)
    @ApiOperation(value = "票务设置 - 获取票型对象", notes = "根据唯一id获取票型对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_ticket")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKET_LOAD)
    public ResultJson<Ticket_Entity> load_ticket(@RequestBody TicketSetupLoadReq ticketSetup_load_req) {
        Ticket ticket = null;
        if (ticketSetup_load_req.getSqlid() != null && ticketSetup_load_req.getSqlid() > 0) {
            ticket = ticketSetupService.loadTicket(ticketSetup_load_req.getSqlid());
        } else {
            ticket = new Ticket();
            Ticketgroup ticketgroup = (Ticketgroup) GlobalCache.getDataStructure()
                    .getCache(SystemUtil.GlobalDataType.TICKETGROUP).getRecord(GlobalContext.getCurrentProjectId()
                            , ticketSetup_load_req.getGroup());
            if (ticketgroup != null) {
                ticket.setGroupid(ticketgroup.getCode());
            }
            //ticket.setGroupid("D");//默认日票
        }
        Ticket_Entity entity = new Ticket_Entity();
        BeanUtils.copyProperties(ticket, entity);
        entity.setReasondesc(CustomData.getDesc(ticket.getProjectid(), entity.getReason(), SystemUtil.CustomDataKey.closereason));
        entity.setOverreasondesc(CustomData.getDesc(ticket.getProjectid(), entity.getOverreason(), SystemUtil.CustomDataKey.closereason));
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order=SwaggerUtil.SubIndex.ConfigTicket.TICKET_SETUP_SAVE_INDEX)
    @ApiOperation(value = "票务设置 - 保存票型", notes = "保存票型")
    @RequestMapping(value = "/save_ticket")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKET_SAVE)
    public ResultJson<Ticket_Entity> save_ticket(@RequestBody Ticket_Entity req) {
        Ticket_Entity entity = ticketSetupService.saveTicket(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTicket.TICKET_SETUP_DELETE_INDEX)
    @ApiOperation(value = "票务设置 - 删除票型 解绑票型大类", notes = "删除票型 解绑票型大类")
    @RequestMapping(value = "/delete_ticket")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKET_DELETE)
    public ResultJson delete_ticket(@RequestBody Common_Del_Req req) {
        ticketSetupService.deleteTicket(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTicket.TICKET_SETUP_RESOURCE_PRICE_INDEX)
    @ApiOperation(value = "票务设置 - 获取票型代码库存&价格", notes = "获取票型代码库存&价格")
    @RequestMapping(value = "/load_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKET_RESOURCE_LOAD)
    public ResultJson<List<TicketResourcePriceListRes>> load_resources_price(@Validated @RequestBody TicketResourceQueryReq req) {
        return ResultJson.ok().data(ticketSetupService.queryTicketResourceList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTicket.TICKET_SETUP_UPDATE_TICKET_STATUS_INDEX)
    @ApiOperation(value = "票务设置 - 更新票型开关状态.对当前状态取反", notes = "更新票型状态")
    @RequestMapping(value = "/update_ticket_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKET_STATUS)
    public ResultJson updTicketStatus(@Valid @RequestBody Common_Load_Req req) {
        ticketSetupService.updTicketStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTicket.TICKET_SETUP_UPDATE_TICKETGROUP_STATUS_INDEX)
    @ApiOperation(value = "票务设置 - 更新票务大类开关状态.对当前状态取反", notes = "更新票务大类状态")
    @RequestMapping(value = "/update_ticketGroup_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKETGROUP_STATUS)
    public ResultJson updTicketGroupStatus(@Valid @RequestBody Common_Switch_Req req) {
        ticketSetupService.updTicketGroupStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTicket.TICKET_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "票务设置 - 设置票型代码库存&价格", notes = "设置票型价格&库存")
    @RequestMapping(value = "/save_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_TICKET_RESOURCE_SAVE)
    public ResultJson save_resources_price(@Validated @RequestBody TicektResourceSaveReq req) {
        ticketSetupService.batchTicket(req);
        return ResultJson.ok();
    }


}
