package com.cw.controller.config.shopsite;

import com.cw.entity.Shopsite;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.shopsite.ShopsiteQueryReq;
import com.cw.pojo.dto.conf.res.shopsite.ShopsiteSetupListRes;
import com.cw.pojo.entity.Shopsite_Entity;
import com.cw.service.config.shopsite.ShopsiteService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2024-10-31
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGSHOPSITE, position = SwaggerUtil.MainIndex.CONFIGSHOPSITE_INDEX)
@RestController
@RequestMapping(value = "api/shopsite", method = RequestMethod.POST)
public class ShopsiteController {
    @Autowired
    ShopsiteService shopsiteService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Shopsite.SHOPSITE_LIST_INDEX)
    @ApiOperation(value = "站点管理 - 获取站点列表数据", notes = "获取站点列表数据")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_SHOPSITE_LIST)
    public ResultJson<ShopsiteSetupListRes> getShopsiteList(@RequestBody ShopsiteQueryReq req) {
        return ResultJson.ok().data(shopsiteService.queryList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Shopsite.SHOPSITE_LOAD_INDEX)
    @ApiOperation(value = "站点管理 - 获取站点对象", notes = "获取站点对象")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_SHOPSITE_LOAD)
    public ResultJson<Shopsite_Entity> loadShopsite(@RequestBody Common_Load_Req req) {
        Shopsite shopsite = shopsiteService.loadShopsite(req.getSqlid());
        Shopsite_Entity entity = new Shopsite_Entity();
        BeanUtils.copyProperties(shopsite, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Shopsite.SHOPSITE_SAVE_INDEX)
    @ApiOperation(value = "站点管理 - 保存站点", notes = "保存站点")
    @RequestMapping(value = "/save")
    @RequireOpRight(opRight = OpRightCodes.OP_SHOPSITE_SAVE)
    public ResultJson<Shopsite_Entity> Shopsites_save(@Validated @RequestBody Shopsite_Entity req) {
        return ResultJson.ok().data(shopsiteService.saveShopsite(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Shopsite.SHOPSITE_DELETE_INDEX)
    @ApiOperation(value = "站点管理 - 删除站点", notes = "删除站点")
    @RequestMapping(value = "/delete")
    @RequireOpRight(opRight = OpRightCodes.OP_SHOPSITE_DELETE)
    public ResultJson Shopsite_delete(@RequestBody Common_Del_Req req) {
        shopsiteService.deleteShopsite(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Shopsite.SHOPSITE_STATUS_INDEX)
    @ApiOperation(value = "站点管理 - 更新站点开关状态.对当前状态取反", notes = "更新站点状态")
    @RequestMapping(value = "/update_Shopsite_status")
    @RequireOpRight(opRight = OpRightCodes.OP_SHOPSITE_STATUS)
    public ResultJson updShopsiteStatus(@RequestBody Common_Switch_Req req) {
        shopsiteService.updShopsiteStatus(req.getSqlid());
        return ResultJson.ok();
    }
}
