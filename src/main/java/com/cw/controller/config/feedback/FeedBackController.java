package com.cw.controller.config.feedback;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.entity.Feedback;
import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.conf.req.feedback.FeedBackListReq;
import com.cw.pojo.dto.conf.res.feedback.FeedBackListRes;
import com.cw.pojo.entity.FeedBack_Entity;
import com.cw.service.config.feedback.FeedbackService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.FeedbackType;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/11/25 0025
 */
@RestController
@Api(tags = SwaggerUtil.MainIndex.CONFIGORDER, position = SwaggerUtil.MainIndex.CONFIGORDER_INDEX)
@RequestMapping(value = "api/feedback", method = RequestMethod.POST)
public class FeedBackController {

    @Autowired
    FeedbackService feedbackService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.FEEDBACK_LIST_INDEX)
    @ApiOperation(value = "意见反馈 - 意见反馈列表", notes = "意见反馈列表")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_FEEDBACK_LIST)
    public ResultJson<FeedBackListRes> refundList(@RequestBody FeedBackListReq req) {
        return ResultJson.ok().data(feedbackService.queryList(req));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.FEEDBACK_GET_INDEX)
    @ApiOperation(value = "意见反馈 - 获取详情", notes = "获取详情")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_FEEDBACK_LOAD)
    public ResultJson<FeedBack_Entity> load(@RequestBody Common_Load_Req req) {
        Feedback feedback = feedbackService.load(req.getSqlid());
        FeedBack_Entity entity = new FeedBack_Entity();
        BeanUtil.copyProperties(feedback, entity, "handletime");
        if (feedback.getHandletime() != null && DateUtil.parse(SystemUtil.DEFAULT_LOCALTIME).isBefore(feedback.getHandletime())) {
            entity.setHandletime(feedback.getHandletime());
        }
        if (StringUtils.isNotBlank(entity.getType())) {
            entity.setTypedesc(FeedbackType.getFeedbackTypeDesc(entity.getType()));
        } else {
            entity.setTypedesc(FeedbackType.ADVICE.getDesc());
        }
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.FEEDBACK_SAVE_INDEX)
    @ApiOperation(value = "意见反馈 - 意见反馈跟进", notes = "意见反馈跟进")
    @RequestMapping(value = "/comfirm")
    @RequireOpRight(opRight = OpRightCodes.OP_FEEDBACK_CONFIRM)
    public ResultJson<FeedBack_Entity> save(@RequestBody FeedBack_Entity req) {
        return ResultJson.ok().data(feedbackService.save(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.FEEDBACK_DELETE_INDEX)
    @ApiOperation(value = "意见反馈 - 删除意见反馈", notes = "删除意见反馈")
    @RequestMapping(value = "/delete")
    @RequireOpRight(opRight = OpRightCodes.OP_FEEDBACK_DELETE)
    public ResultJson<FeedBack_Entity> delete(@RequestBody Common_Del_Req req) {
        feedbackService.deleteFeedback(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "意见反馈-反馈记录导出excel", notes = "反馈记录导出excel")
    @RequestMapping(value = "/output_excel")
    @RequireOpRight(opRight = OpRightCodes.OP_FEEDBACK_EXCEL_OUTPUT)
    public void outputExcel(@Validated @RequestBody FeedBackListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        feedbackService.outPutExcel(req, httpServletResponse);
    }
}
