package com.cw.controller.config;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Query_Page_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.users.UserUpdPwd_Req;
import com.cw.pojo.dto.conf.res.sys.OpRole_List_Res;
import com.cw.pojo.dto.conf.res.sys.OpUser_List_Res;
import com.cw.pojo.entity.OpRole_Entity;
import com.cw.pojo.entity.OpUser_Entity;
import com.cw.service.config.users.UserService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/26 18:17
 **/
@Api(tags = SwaggerUtil.MainIndex.CONFIGUSER, position = SwaggerUtil.MainIndex.CONFIGUSER_INDEX)
@RestController
@RequestMapping(value = "api/userconf", method = RequestMethod.POST)
public class UserConfigController {

    @Autowired
    UserService userService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.USERS_SETUP_SAVE_INDEX)
    @ApiOperation(value = "用户设置 -保存用户", notes = "保存用户")
    @RequestMapping(value = "/saveuser")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_USER_SAVE)
    public ResultJson<OpUser_Entity> saveUser(@RequestBody OpUser_Entity entity) {
        entity = userService.saveUser(entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.USERS_SETUP_PWD_INDEX)
    @RequestMapping(value = "/updpwd")
    @ApiOperation(value = "用户设置 - 更改密码", notes = "更改密码")
    public ResultJson updateUserPwd(@RequestBody UserUpdPwd_Req req) {
        userService.updPassword(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.USERS_SETUP_CHSTATUS)
    @RequestMapping(value = "/chstatus")
    @ApiOperation(value = "用户设置 - 启用/关闭用户", notes = " 更改用户状态")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_USER_STAUS)
    public ResultJson chStatus(@RequestBody Common_Switch_Req req) {
        userService.updStatus(req);
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.USERS_SETUP_LIST_INDEX)
    @RequestMapping(value = "/deluser")
    @ApiOperation(value = "用户设置 - 删除用户", notes = "删除用户")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_USER_DELETE)
    public ResultJson deleteUser(@RequestBody Common_Del_Req req) {
        userService.deleteUser(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.USERS_SETUP_LIST_INDEX)
    @ApiOperation(value = "用户设置 - 获取用户对象", notes = "根据唯一id获取用户对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/loaduser")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_USER_LOAD)
    public ResultJson<OpUser_Entity> loadUser(@RequestBody Common_Load_Req req) {
        OpUser_Entity opUserEntity = userService.loadUser(req.getSqlid());
        return ResultJson.ok().data(opUserEntity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.ROLES_SETUP_SAVE_INDEX)
    @ApiOperation(value = "用户设置 - 保存角色", notes = "保存角色")
    @RequestMapping(value = "/saverole")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_ROLE_SAVE)
    public ResultJson<OpRole_Entity> saveRole(@RequestBody OpRole_Entity entity) {
        OpRole_Entity opRole = userService.saveRole(entity);
        return ResultJson.ok().data(opRole);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.ROLES_SETUP_DELETE_INDEX)
    @ApiOperation(value = "用户设置 - 删除角色", notes = "删除角色")
    @RequestMapping(value = "/delrole")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_ROLE_DELETE)
    public ResultJson deleteRole(@RequestBody Common_Del_Req req) {
        userService.deleteReole(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.ROLES_SETUP_GET_INDEX)
    @ApiOperation(value = "用户设置 - 获取角色对象", notes = "根据唯一id获取角色对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/loadrole")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_ROLE_LOAD)
    public ResultJson<OpRole_Entity> loadRole(@RequestBody Common_Load_Req req) {
        OpRole_Entity opRole = userService.loadRole(req.getSqlid());
//        OpRole_Entity entity = new OpRole_Entity();
//        BeanUtils.copyProperties(opRole, entity);
        return ResultJson.ok().data(opRole);
    }

//    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.ROLES_RIGHTS_LIST_INDEX)
//    @ApiOperation(value = "用户设置 - 权限列表", notes = "获取权限列表")
//    @RequestMapping(value = "/loadrights")
//    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_RIGHT_LIST)
//    public ResultJson<CrudRoleRight_Response> loadRolesRight(@RequestBody CrudRoleRight_Req req) {
//        CrudRoleRight_Response roleRightResponse = userService.getRolesRight(req);
//        return ResultJson.ok().data(roleRightResponse);
//    }

//    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.ROLES_RIGHTS_SAVE_INDEX)
//    @ApiOperation(value = "用户设置 - 保存角色权限", notes = "保存权限")
//    @RequestMapping(value = "/saverights")
//    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_RIGHT_SAVE)
//    public ResultJson<CrudRoleRight_Response> save(@RequestBody CrudRoleRight_Req req) {
//        CrudRoleRight_Response roleRightResponse = userService.saveRoleRight(req.getRoleid(),req.getMrights(), req.getOprights());
//        return ResultJson.ok().data(roleRightResponse);
//    }

//    @RequestMapping(value = "/loadright")
//    public ResultJson<CrudRoleRight_Response> loadright(@RequestBody CrudRoleRight_Req req) {
//        CrudRoleRight_Response response = userService.getRolesRight(req);
//        return ResultJson.ok().data(response);
//    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.USERS_SETUP_LIST_INDEX)
    @ApiOperation(value = "用户设置 - 用户列表", notes = "获取用户列表数据")
    @RequestMapping(value = "/userlist")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_USER_LIST)
    public ResultJson<OpUser_List_Res> fetchUserlist(@RequestBody Common_Query_Page_Req req) {
        return ResultJson.ok().data(userService.queryTableData(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigUser.ROLES_SETUP_LIST_INDEX)
    @RequestMapping(value = "/rolelist")
    @ApiOperation(value = "用户设置 - 角色列表", notes = "获取角色列表数据")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_ROLE_LIST)
    public ResultJson<OpRole_List_Res> fetchRolelist(@RequestBody Common_Query_Page_Req req) {
        return ResultJson.ok().data(userService.queryRoleTableData(req));
    }


}
