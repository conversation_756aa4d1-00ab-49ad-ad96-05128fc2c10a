package com.cw.controller.config.cms;

import com.cw.arithmetic.SysFuncLibTool;
import com.cw.entity.Menus;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.MenuContentPageQueryReq;
import com.cw.pojo.dto.common.req.*;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.req.cms.*;
import com.cw.pojo.dto.conf.req.sys.BaseSysconfReq;
import com.cw.pojo.dto.conf.req.sys.MenuBaseConfigReq;
import com.cw.pojo.dto.conf.req.sys.SysconfReq;
import com.cw.pojo.dto.conf.res.cms.MenuBaseConfigRes;
import com.cw.pojo.dto.conf.res.cms.MenuConfigRes;
import com.cw.pojo.dto.conf.res.cms.MenuContent_List_Res;
import com.cw.pojo.dto.conf.res.cms.Menu_List_Res;
import com.cw.service.config.cms.MenuService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/14 10:54
 **/
@Api(tags = SwaggerUtil.MainIndex.CONFIGMENU, position = SwaggerUtil.MainIndex.CONFIGMENU_INDEX)
@RestController
@RequestMapping(value = "/api/menus", method = RequestMethod.POST)
public class MenuController {

    @Autowired
    MenuService menuService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.LOADMENU)
    @ApiOperation(value = "菜单设置 -加载菜单栏目", notes = "加载栏目菜单")
    @RequestMapping(value = "/loadMenu")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_LOAD)
    public ResultJson<MenuEntity> loadMenu(@RequestBody Common_Load_Req req) {
        Menus menus = menuService.loadMenu(req.getSqlid());
        MenuEntity entity = new MenuEntity();
        BeanUtils.copyProperties(menus, entity, "udf");
        entity.setUdf(SysFuncLibTool.converString2Json(menus.getUdf()));
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.ADDMENU)
    @ApiOperation(value = "菜单设置 -添加菜单栏目", notes = "添加栏目菜单")
    @RequestMapping(value = "/addMenu")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_SAVE)
    public ResultJson<MenuEntity> addMenu(@Valid @RequestBody MenuEntity req) {
        MenuEntity entity = menuService.addMenu(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.UPDMENU)
    @ApiOperation(value = "菜单设置 -更新菜单栏目", notes = "更新栏目菜单", position = SwaggerUtil.SubIndex.ConfigMenu.UPDMENU)
    @RequestMapping(value = "/updateMenu")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_UPDATE)
    public ResultJson<MenuEntity> updateMenu(@Valid @RequestBody MenuEntity postform) {
        MenuEntity entity = menuService.updMenu(postform);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.DELMENU)
    @ApiOperation(value = "菜单设置 -删除菜单栏目", notes = "删除栏目菜单")
    @RequestMapping(value = "/deleteMenu")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_DELETE)
    public ResultJson<Common_response> deleteMenu(@Valid @RequestBody Common_Del_Req postform) {
        menuService.deleteMenu(postform);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.MENULIST)
    @ApiOperation(value = "菜单设置 -加载菜单栏目列表", notes = "加载菜单栏目列表")
    @RequestMapping(value = "/listMenu")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_LIST)
    public ResultJson<Menu_List_Res> listMenu(@RequestBody MenuQueryListReq req) {
        Menu_List_Res res = menuService.listConfigMenu(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.MENUSEQCHANGE)
    @ApiOperation(value = "菜单设置 -更改菜单栏目同级排序", notes = "更改菜单栏目同级排序")
    @RequestMapping(value = "/changeMenuSeq")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_SEQ)
    public ResultJson<Common_response> changeMenuSeq(@RequestBody Common_Seq_Req req) {
        menuService.updateMenuSeq(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.MENUSUPDATESTATUS)
    @ApiOperation(value = "菜单设置 -更新菜单栏目状态，取反操作", notes = "更新菜单栏目状态，取反操作")
    @RequestMapping(value = "/updateStatus")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_STATUS)
    public ResultJson updateStatus(@RequestBody Common_Switch_Req req) {
        menuService.updateStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.LOADMENUCONTENT)
    @ApiOperation(value = "菜单设置 -加载卡片内容", notes = "加载卡片内容")
    @RequestMapping(value = "/loadContent")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_CONTENT_LOAD)
    public ResultJson<MenuContentEntity> loadMenuContent(@Valid @RequestBody Common_Load_Req req) {
        MenuContentEntity entity = menuService.loadContentEntity(req);
        //转换
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.ADDMENUCONTENT)
    @ApiOperation(value = "菜单设置 -添加卡片内容", notes = "添加卡片内容")
    @RequestMapping(value = "/addContent")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_CONTENT_SAVE)
    public ResultJson<MenuContentEntity> addMenuContent(@Valid @RequestBody MenuContentEntity postform) {
        MenuContentEntity entity = menuService.addMenuContent(postform);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.UPDMENUCONTENT)
    @ApiOperation(value = "菜单设置 -更新卡片内容", notes = "更新卡片内容")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_CONTENT_UPDATE)
    @RequestMapping(value = "/updateContent")
    public ResultJson<MenuContentEntity> updateMenuContent(@Valid @RequestBody MenuContentEntity postform) {
        MenuContentEntity entity = menuService.updMenuContent(postform);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.DELMENUCONTENT)
    @ApiOperation(value = "菜单设置 -删除卡片内容", notes = "删除卡片内容")
    @RequestMapping(value = "/delContent")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_CONTENT_DELETE)
    public ResultJson<Common_response> updMenuContent(@Valid @RequestBody Common_List_Req req) {
        menuService.deleteMenuContent(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.CONTENTLIST)
    @ApiOperation(value = "菜单设置 -卡片内容列表", notes = "卡片内容列表")
    @RequestMapping(value = "/listContent")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_CONTENT_LIST)
    public ResultJson<MenuContent_List_Res> fetchMenuContent(@RequestBody MenuContentPageQueryReq postform) {
        MenuContent_List_Res entity = menuService.listContentPageList(postform);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.CONTENTSEQCHANGE)
    @ApiOperation(value = "菜单设置 -更改内容同级排序", notes = "更改内容同级排序")
    @RequestMapping(value = "/changeContentSeq")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_CONTENT_SEQ)
    public ResultJson<Common_response> changeContentSeq(@RequestBody Common_Seq_Req req) {
        menuService.updateMenuContentSeq(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.CONTENTTOP)
    @ApiOperation(value = "菜单设置 -卡牌内容置顶取反", notes = "卡牌内容置顶取反")
    @RequestMapping(value = "/contentTop")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_CONTENT_STATUS)
    public ResultJson<Common_response> contentTop(@RequestBody Common_Load_Req req) {
        menuService.updateMenuContentTop(req);
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.CONTENTSTATUSLIST)
    @ApiOperation(value = "菜单设置 -卡牌内容批量审核", notes = "卡牌内容批量审核")
    @RequestMapping(value = "/change_contentStatus")
    @RequireOpRight(opRight = OpRightCodes.OP_MENU_CONTENT_AUDIT)
    public ResultJson<Common_response> changeContentStatus(@Valid @RequestBody MenuContentStatusListReq req) {
        menuService.changeContentStatus(req);
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.BASE_COLOR_INDEX)
    @ApiOperation(value = "页面管理 -获取基础信息", notes = "获取基础信息")
    @RequestMapping(value = "/color_style", method = RequestMethod.POST)
    public ResultJson<MenuConfigRes> color_style(@RequestBody SysconfReq req) {
        MenuConfigRes entity = menuService.getSysconf(req.getType(), GlobalContext.getCurrentProjectId());
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.BASE_COLOR_SAVE)
    @ApiOperation(value = "页面管理 -保存基础信息", notes = "保存基础信息")
    @RequestMapping(value = "/save_color_style")
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_SYSCONF_SAVE)
    public ResultJson<MenuConfigRes> save_color_style(@Valid @RequestBody MenuConfigReq req) {
        MenuConfigRes entity = menuService.saveMenuConfig(req);
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.BASE_COLOR_INDEX)
    @ApiOperation(value = "页面管理 -获取基础信息(修改版)", notes = "获取基础信息")
    @RequestMapping(value = "/getbaseinfo", method = RequestMethod.POST)
    public ResultJson<MenuBaseConfigRes> getbaseinfo(@RequestBody BaseSysconfReq req) {
        MenuBaseConfigRes entity = menuService.getBasePageConf(req.getType(), req.getTab(), GlobalContext.getCurrentProjectId());
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigMenu.BASE_COLOR_SAVE)
    @ApiOperation(value = "页面管理 -保存基础信息(修改版)", notes = "保存基础信息")
    @RequestMapping(value = "/save_baseinfo")
    public ResultJson<MenuBaseConfigRes> save_baseinfo(@Valid @RequestBody MenuBaseConfigReq req) {
        MenuBaseConfigRes entity = menuService.saveBasePageConf(req);
        return ResultJson.ok().data(entity);
    }


}
