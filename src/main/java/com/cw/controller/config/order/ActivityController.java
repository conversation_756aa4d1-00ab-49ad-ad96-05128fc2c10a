package com.cw.controller.config.order;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.order.req.ActOrderCancelReq;
import com.cw.pojo.dto.order.req.ActOrderListReq;
import com.cw.pojo.dto.order.req.ActrsLoadReq;
import com.cw.pojo.dto.order.req.OrderConfirmReq;
import com.cw.pojo.dto.order.res.ActOrderListRes;
import com.cw.pojo.entity.Act_rs_Entity;
import com.cw.service.order.ActOrderService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@Api(tags = SwaggerUtil.MainIndex.CONFIGORDER, position = SwaggerUtil.MainIndex.CONFIGORDER_INDEX)
@RequestMapping(value = "api/order/activity", method = RequestMethod.POST)
public class ActivityController {

    @Autowired
    ActOrderService actOrderService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ACTIVITY_ORDER_LIST_INDEX)
    @ApiOperation(value = "预约记录 - 综合预约记录列表", notes = "综合预约记录列表")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_ACTORDER_LIST)
    public ResultJson<ActOrderListRes> actOrderList(@RequestBody ActOrderListReq req) {
        return ResultJson.ok().data(actOrderService.queryList(req));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ACTIVITY_ORDER_GET_INDEX)
    @ApiOperation(value = "预约记录 - 获取综合预约详情", notes = "获取综合预约详情")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_ACTORDER_DETAIL)
    public ResultJson<Act_rs_Entity> load(@Validated @RequestBody ActrsLoadReq req) {
        Act_rs_Entity data = actOrderService.load(req);
        return ResultJson.ok().data(data);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ACTIVITY_ORDER_CANCEL_INDEX)
    @ApiOperation(value = "预约记录 - 取消综合预约", notes = "取消综合预约")
    @RequestMapping(value = "/cancel")
    @RequireOpRight(opRight = OpRightCodes.OP_ACTORDER_CANCEL)
    public ResultJson cancel(@Validated @RequestBody ActOrderCancelReq req) {
        actOrderService.cancel(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ACTIVITY_ORDER_CANCEL_INDEX)
    @ApiOperation(value = "预约记录 -完成免费预约", notes = "完结免费预约")
    @RequestMapping(value = "/finish")
    public ResultJson finish(@Validated @RequestBody ActrsLoadReq req) {
        actOrderService.finish(req);
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ACTIVITY_ORDER_EXCEL_OUTPUT_INDEX)
    @ApiOperation(value = "预约记录 - 综合预约订单记录导出excel", notes = "综合预约订单记录导出excel")
    @RequestMapping(value = "/output_excel")
    @RequireOpRight(opRight = OpRightCodes.OP_ACTORDER_EXCEL_OUTPUT)
    public void outputExcel(@Validated @RequestBody ActOrderListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        actOrderService.outPutExcel(req, httpServletResponse);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ACTIVITY_ORDER_HIS_GET_INDEX)
    @ApiOperation(value = "预约记录 - 获取综合预约历史详情", notes = "获取综合预约历史详情")
    @RequestMapping(value = "/load_his")
    //@RequireOpRight(opRight = OpRightCodes.OP_ACTORDERHIS_DETAIL)
    public ResultJson<Act_rs_Entity> load_his(@Validated @RequestBody ActrsLoadReq req) {
        Act_rs_Entity data = actOrderService.loadHis(req.getBookingid());
        return ResultJson.ok().data(data);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ACTIVITY_ORDER_EXCEL_OUTPUT_INDEX)
    @ApiOperation(value = "预约记录 - 重推二维码", notes = "重推二维码")
    @RequestMapping(value = "/resendAct")
    public ResultJson<Common_response> resend(@Validated @RequestBody OrderConfirmReq req) throws DefinedException {
        actOrderService.resend(req);
        return ResultJson.ok().data(new Common_response());
    }


}
