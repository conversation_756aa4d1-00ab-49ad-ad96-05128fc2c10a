package com.cw.controller.config.order;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.order.req.*;
import com.cw.pojo.dto.order.res.*;
import com.cw.service.context.GlobalContext;
import com.cw.service.order.GiftOrderService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2022-02-25
 */
@Api(tags = SwaggerUtil.MainIndex.GIFTORDER, position = SwaggerUtil.MainIndex.GIFTORDER_INDEX)
@RestController
@RequestMapping(value = "api/order/giftitem", method = RequestMethod.POST)
public class GiftOrderController {
    @Autowired
    GiftOrderService giftOrderService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_ORDER_LIST_INDEX)
    @ApiOperation(value = "发货管理 - 查看伴手礼订单列表", notes = "查看伴手礼订单列表")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_ORDER_LIST)
    public ResultJson<GiftOrderListRes> order(@RequestBody GiftOrderListReq req) {
        return ResultJson.ok().data(giftOrderService.queryList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_ORDER_LIST_INDEX)
    @ApiOperation(value = "发货管理 - 查看伴手礼订单详情", notes = "查看伴手礼订单详情")
    @RequestMapping(value = "/getDetail")
    //@RequireOpRight(opRight = OpRightCodes.OP_GIFT_ORDER_LIST)
    public ResultJson<GiftOrderDetailRes> getDetail(@RequestBody GiftOrderProductReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        return ResultJson.ok().data(giftOrderService.loadGiftOrder(req.getBookingid(), projectId));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_ORDER_SAVE)
    @ApiOperation(value = "发货管理 - 发货流程", notes = "发货流程")
    @RequestMapping(value = "/save")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_ORDER_HANDLE_SAVE)
    public ResultJson save(@RequestBody GiftOrderHandlerReq req) {
        giftOrderService.handleOrder(req);
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_ORDER_LOAD_GIFT)
    @ApiOperation(value = "发货管理 - 获取发货流程伴手礼信息", notes = "获取发货流程伴手礼信息")
    @RequestMapping(value = "/load_product")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_ORDER_DETAIL)
    public ResultJson<GiftOrderSendProductInfo> loadOrderProduct(@Validated @RequestBody GiftOrderProductReq req) {
        return ResultJson.ok().data(giftOrderService.queryGiftitem(req));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_ORDER_EXCEL_OUTPUT_INDEX)
    @ApiOperation(value = "发货管理 - 发货管理记录导出excel", notes = "发货管理记录导出excel")
    @RequestMapping(value = "/output_excel")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_ORDER_OUTPUT)
    public void outputExcel(@Validated @RequestBody GiftOrderListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        giftOrderService.outPutExcel(req, httpServletResponse);
    }


    //退货管理
    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_AUDITING_ORDER_LIST_INDEX)
    @ApiOperation(value = "退货申请 - 查看退货申请订单列表", notes = "查看退货申请订单列表")
    @RequestMapping(value = "/auditing_list")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_AUDIT_ORDER_LIST)
    public ResultJson<GiftAuditingOrderListRes> auditing_list(@RequestBody GiftAuditingOrderListReq req) {
        return ResultJson.ok().data(giftOrderService.queryAuditingList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_AUDITING_ORDER_DETAIL)
    @ApiOperation(value = "退货申请 - 获取退货申请详情", notes = "获取退货申请详情")
    @RequestMapping(value = "/auditing_detail")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_AUDIT_ORDER_DETAIL)
    public ResultJson<GiftAuditingOrderReasonRes> auditing_detail(@Validated @RequestBody GiftOrderProductReq req) {
        String projectId = GlobalContext.getCurrentProjectId();
        return ResultJson.ok().data(giftOrderService.queryAuditDetail(req.getBookingid(), projectId));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_AUDITING_ORDER_CONFIRM_INDEX)
    @ApiOperation(value = "退货申请 - 同意退货", notes = "同意退货")
    @RequestMapping(value = "/auditing_confirm")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_AUDIT_ORDER_CONFIRM)
    public ResultJson auditing_confirm(@Validated @RequestBody GiftOrderProductReq req) throws DefinedException {
        giftOrderService.comfirm(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_AUDITING_ORDER_RECEIVE_INDEX)
    @ApiOperation(value = "退货申请 - 接收退货", notes = "接收退货")
    @RequestMapping(value = "/auditing_receive")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_AUDIT_ORDER_RECIEVE)
    public ResultJson auditing_receive(@Validated @RequestBody GiftOrderBackReq req) throws DefinedException {
        giftOrderService.receiveBack(req);
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_AUDITING_ORDER_REFUSE_INDEX)
    @ApiOperation(value = "退货申请 - 拒绝退货", notes = "拒绝退货")
    @RequestMapping(value = "/auditing_refuse")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_AUDIT_ORDER_REFUSE)
    public ResultJson auditing_refuse(@Validated @RequestBody GiftOrderBackReq req) throws DefinedException {
        giftOrderService.refuse(req);
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_AUDITING_ORDER_EXCEL_OUTPUT_INDEX)
    @ApiOperation(value = "退货申请 - 退货申请记录导出excel", notes = "退货申请记录导出excel")
    @RequestMapping(value = "/auditing_output_excel")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_AUDIT_ORDER_OUTPUT)
    public void auditing_output_excel(@Validated @RequestBody GiftAuditingOrderListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        giftOrderService.auditing_output_excel(req, httpServletResponse);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.GIFT_AUDITING_ORDER_POSTAGE_INFO_INDEX)
    @ApiOperation(value = "退货申请 - 获取退货物流信息", notes = "获取退货物流信息")
    @RequestMapping(value = "/auditing_postageInfo")
    @RequireOpRight(opRight = OpRightCodes.OP_GIFT_AUDIT_ORDER_POSTAGEINFO)
    public ResultJson<GiftAuditingOrderPostageInfo> auditing_postageInfo(@Validated @RequestBody GiftOrderProductReq req) throws DefinedException {
        return ResultJson.ok().data(giftOrderService.getAudPostageInfo(req));
    }

}
