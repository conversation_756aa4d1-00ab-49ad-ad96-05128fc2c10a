package com.cw.controller.config.order;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.order.req.PassOrderListReq;
import com.cw.pojo.dto.order.req.PassOrderLoadReq;
import com.cw.pojo.dto.order.req.PassOrderStatusReq;
import com.cw.pojo.dto.order.res.PassOrderListRes;
import com.cw.pojo.entity.Pass_rs_Entity;
import com.cw.service.order.PassOrderService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2022-04-21
 */
@RestController
@Api(tags = SwaggerUtil.MainIndex.CONFIGORDER, position = SwaggerUtil.MainIndex.CONFIGORDER_INDEX)
@RequestMapping(value = "api/passorder", method = RequestMethod.POST)
public class PassOrderController {

    @Autowired
    PassOrderService passOrderService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.PASS_ORDER_LIST_INDEX)
    @ApiOperation(value = "线下订单记录 - 线下订单列表", notes = "线下订单列表")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_PASS_ORDER_LIST)
    public ResultJson<PassOrderListRes> order(@RequestBody PassOrderListReq req) {
        return ResultJson.ok().data(passOrderService.queryList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.PASS_ORDER_LOAD)
    @ApiOperation(value = "线下订单记录 - 线下订单详情", notes = "线下订单详情")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_PASS_ORDER_DETAIL)
    public ResultJson<Pass_rs_Entity> loadOrder(@RequestBody PassOrderLoadReq req) {
        Pass_rs_Entity entity = passOrderService.load(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.PASS_ORDER_LOAD)
    @ApiOperation(value = "线下订单记录 - 更新线下订单状态", notes = "更新线下订单状态")
    @RequestMapping(value = "/updMainStatus")
    @RequireOpRight(opRight = OpRightCodes.OP_UPDATE_PASS_ORDER_STATUS)
    public ResultJson<Pass_rs_Entity> updMainStatus(@RequestBody PassOrderStatusReq req) {
        passOrderService.changeStatus(req);
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.PASS_ORDER_LOAD)
    @ApiOperation(value = "线下订单记录 - 线下订单退款", notes = "线下订单退款")
    @RequestMapping(value = "/refund")
    @RequireOpRight(opRight = OpRightCodes.OP_REFUND_PASS_ORDER)
    public ResultJson<Pass_rs_Entity> refund(@RequestBody PassOrderLoadReq req) {
        passOrderService.refund(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "线下订单 - 线下订单记录导出excel", notes = "线下订单记录导出excel")
    @RequestMapping(value = "/output_excel")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_EXCEL_OUTPUT)
    public void outputExcel(@Validated @RequestBody PassOrderListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        passOrderService.outPutExcel(req, httpServletResponse);
    }


}
