package com.cw.controller.config.order;

import com.cw.arithmetic.func.Var;
import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Query_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.res.products.SpuListNode;
import com.cw.pojo.dto.order.req.*;
import com.cw.pojo.dto.order.res.OrderListRes;
import com.cw.pojo.dto.order.res.OrderProductRes;
import com.cw.pojo.entity.Booking_rs_Entity;
import com.cw.service.order.OrderService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Describe 订单记录
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/22 0022
 */
@RestController
@Api(tags = SwaggerUtil.MainIndex.CONFIGORDER, position = SwaggerUtil.MainIndex.CONFIGORDER_INDEX)
@RequestMapping(value = "api/order", method = RequestMethod.POST)
public class OrderController {
    @Autowired
    OrderService orderService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_LIST_INDEX)
    @ApiOperation(value = "订单记录 - 订单记录列表", notes = "订单记录列表")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_LIST)
    public ResultJson<OrderListRes> order(@RequestBody OrderListReq req) {
        return ResultJson.ok().data(orderService.queryList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_LIST_HIS_INDEX)
    @ApiOperation(value = "订单记录 - 订单历史记录列表", notes = "订单历史记录列表")
    @RequestMapping(value = "/list_his")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_HIS_LIST)
    public ResultJson<OrderListRes> list_his(@RequestBody OrderHisListReq req) {
        return ResultJson.ok().data(orderService.queryHisList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_GET_INDEX)
    @ApiOperation(value = "订单记录 - 订单重推送", notes = "订单重新推送")
    @RequestMapping(value = "/resend")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_DETAIL)
    public ResultJson<Booking_rs_Entity> reSend(@RequestBody OrderConfirmReq req) throws DefinedException {
        Booking_rs_Entity entity = orderService.resend(req);
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_GET_INDEX)
    @ApiOperation(value = "订单记录 - 订单记录详情", notes = "订单记录详情")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_DETAIL)
    public ResultJson<Booking_rs_Entity> loadOrder(@RequestBody OrderLoadReq req) {
        Booking_rs_Entity entity = orderService.load(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_GET_INDEX)
    @ApiOperation(value = "订单记录 - 订单历史记录详情", notes = "订单历史记录详情")
    @RequestMapping(value = "/loadhis")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_HIS_DETAIL)
    public ResultJson<Booking_rs_Entity> loadHisOrder(@RequestBody OrderHisLoadReq req) {
        Booking_rs_Entity entity = orderService.loadHis(req.getBookingid());
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_PRODUCT_INDEX)
    @ApiOperation(value = "订单记录 - 订单商品详情", notes = "订单商品详情")
    @RequestMapping(value = "/load_product")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_PRODUCT_DETAIL)
    public ResultJson<OrderProductRes> loadOrderProduct(@Validated @RequestBody OrderProductReq req) {
        return ResultJson.ok().data(orderService.queryProductDetail(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 用户端订单隐藏/显示状态取反", notes = "用户端订单隐藏/显示状态取反")
    @RequestMapping(value = "/change_hide")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_CONFIRM)
    public ResultJson change_hide(@Validated @RequestBody OrderHideReq req) throws DefinedException {
        orderService.changeHide(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 确定预定", notes = "确定预定")
    @RequestMapping(value = "/confirm")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_CONFIRM)
    public ResultJson confirm(@Validated @RequestBody OrderConfirmReq req) throws DefinedException {
        orderService.confirm(req.getBookingid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 核销预定", notes = "核销预定")
    @RequestMapping(value = "/finish")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_CONFIRM)
    public ResultJson finish(@Validated @RequestBody OrderQrFinishReq req) throws DefinedException {
        orderService.finish(req.getQrcontent());
        return ResultJson.ok();
    }

    @ApiOperation(value = "订单记录 - 手动核销完成预定", notes = "手动核销完成预定")
    @RequestMapping(value = "/manualFinish")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_CONFIRM)
    public ResultJson manualFinish(@RequestBody OrderLoadReq req) throws DefinedException {
        orderService.manualFinish(req);
        return ResultJson.ok();
    }


    @ApiOperation(value = "订单记录 - 完结订单退款", notes = "完结订单退款")
    @RequestMapping(value = "/refund_finishorder")
    @RequireOpRight(opRight = OpRightCodes.OP_FINISH_REFUND)
    public ResultJson<Common_response> refundFinish(@RequestBody RefundReq req) throws DefinedException {
        Common_response response=orderService.refundFinishOrder(req);
        return ResultJson.ok().data(response);
    }



    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 拒绝预定", notes = "拒绝预定")
    @RequestMapping(value = "/refuse")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_REFUSE)
    public ResultJson refuse(@Validated @RequestBody OrderRefuseReq req) throws DefinedException {
        orderService.refuse(req.getBookingid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 拒绝预定(待确认)", notes = "拒绝预定(待确认)")
    @RequestMapping(value = "/refuse_waitOrder")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_REFUSE_WAIT)
    public ResultJson refuseWaite(@Validated @RequestBody OrderRefuseReq req) throws DefinedException {
        orderService.refuseWait(req.getBookingid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 订单记录导出excel", notes = "订单记录导出excel")
    @RequestMapping(value = "/output_excel")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_EXCEL_OUTPUT)
    public void outputExcel(@Validated @RequestBody OrderListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        orderService.outPutExcel(req, httpServletResponse);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 订单历史记录导出excel", notes = "订单历史记录导出excel")
    @RequestMapping(value = "/outputhis_excel")
    public void outputExcel(@Validated @RequestBody OrderHisListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        orderService.outPutHisExcel(req, httpServletResponse);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 景区卡券商品核销", notes = "景区卡券商品核销")
    @RequestMapping(value = "/write_off_card")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_WRITEOFF_CARD)
    public ResultJson write_off_card(@Validated @RequestBody OrderRefuseReq req) throws DefinedException {
        orderService.writeOffCard(req.getBookingid(), new Var<String>());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 发送景区产品卡券", notes = "发送景区卡券")
    @RequestMapping(value = "/sendSpukitCoupon")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_SEND_KITCOUPON)
    public ResultJson<Common_response> send_KitCoupon(@Validated @RequestBody OrderSpukitReq req) throws DefinedException {
        Common_response response = orderService.sendSpuKitCoupon(req);
        return ResultJson.ok().data(response);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "订单记录 - 查询可发景区卡券", notes = "查询可发景区卡券")
    @RequestMapping(value = "/queryAvailSpukit")
    @RequireOpRight(opRight = OpRightCodes.OP_ORDER_SEND_KITCOUPON)
    public ResultJson<List<SpuListNode>> queryAvailKitCoupon(@Validated @RequestBody Common_Query_Req req) throws DefinedException {
        List<SpuListNode> list = orderService.queryAvailKitCounpons();
        return ResultJson.ok().data(list);
    }




}
