package com.cw.controller.config.order;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.order.req.RefundHisListReq;
import com.cw.pojo.dto.order.req.RefundListReq;
import com.cw.pojo.dto.order.req.RefundLoadReq;
import com.cw.pojo.dto.order.req.RefundReq;
import com.cw.pojo.dto.order.res.RefundHisListRes;
import com.cw.pojo.dto.order.res.RefundListRes;
import com.cw.pojo.dto.order.res.RefundLoadRes;
import com.cw.service.order.RefundService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Describe 订单管理-退款管理审核
 * <AUTHOR> Tony Leung
 * @Create on 2021/11/12 0012
 */
@RestController
@Api(tags = SwaggerUtil.MainIndex.CONFIGORDER, position = SwaggerUtil.MainIndex.CONFIGORDER_INDEX)
@RequestMapping(value = "api/order/refund", method = RequestMethod.POST)
public class RefundController {

    @Autowired
    RefundService refundService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.REFUND_LIST_INDEX)
    @ApiOperation(value = "退款管理 - 退款申请列表", notes = "退款申请列表")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_LIST)
    public ResultJson<RefundListRes> refundList(@RequestBody RefundListReq req) {
        return ResultJson.ok().data(refundService.queryList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.REFUND_HIS_LIST_INDEX)
    @ApiOperation(value = "退款管理 - 退款历史列表", notes = "退款历史列表")
    @RequestMapping(value = "/his_list")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_HIS_LIST)
    public ResultJson<RefundHisListRes> refundHisList(@RequestBody RefundHisListReq req) {
        return ResultJson.ok().data(refundService.queryHisList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.REFUND_CONFIRM_INDEX)
    @ApiOperation(value = "退款管理 - 确认退款", notes = "确认退款")
    @RequestMapping(value = "/comfirmrefund")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_PASS)
    public ResultJson comfirmRefund(@Validated @RequestBody RefundReq req) throws DefinedException {
        refundService.comfirm(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.REFUND_CONFIRM_INDEX)
    @ApiOperation(value = "退款管理 -强制审核完成（走线下）", notes = "管理强制审核完成")
    @RequestMapping(value = "/forceComplete")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_FORCE_PASS)
    public ResultJson forceComplete(@Validated @RequestBody RefundReq req) throws DefinedException {
        refundService.forceComplete(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.REFUND_INFO_INDEX)
    @ApiOperation(value = "退款管理 - 获取退款信息", notes = "获取退款信息")
    @RequestMapping(value = "/getrefundinfo")
    public ResultJson<Common_response> getRefundInfo(@Validated @RequestBody RefundReq req) throws DefinedException {
        String msg = refundService.getRefundInfo(req);
        Common_response commonResponse = new Common_response();
        commonResponse.setMsg(msg);
        return ResultJson.ok().data(commonResponse).msg(msg);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.REFUND_REFUSE_INDEX)
    @ApiOperation(value = "退款管理 - 拒绝退款", notes = "拒绝退款")
    @RequestMapping(value = "/refuserefund")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_REFUSE)
    public ResultJson refuse(@Validated @RequestBody RefundReq req) throws DefinedException {
        refundService.refuse(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.REFUND_FORCEREFUND_INDEX)
    @ApiOperation(value = "退款管理 - 强制退款", notes = "强制退款")
    @RequestMapping(value = "/forcerefund")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_PASS_ENFORCE)
    public ResultJson mandatoryRefund(@Validated @RequestBody RefundReq req) throws DefinedException {
        refundService.mandatoryRefund(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "退款管理 - 退款申请记录导出excel", notes = "退款申请记录导出excel")
    @RequestMapping(value = "/output_excel")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_EXCEL_OUTPUT)
    public void outputExcel(@Validated @RequestBody RefundListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        refundService.outPutExcel(req, httpServletResponse);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.REFUND_GET_DETAIL_INDEX)
    @ApiOperation(value = "退款管理 - 退款申请记录详情", notes = "退款申请记录详情")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_DETAIL)
    public ResultJson<RefundLoadRes> loadOrder(@RequestBody RefundLoadReq req) {
        RefundLoadRes entity = refundService.load(req.getBookingid(), req.getPtype());
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.REFUND_GET_HIS_DETAIL_INDEX)
    @ApiOperation(value = "退款管理 - 历史退款记录详情", notes = "历史退款记录详情")
    @RequestMapping(value = "/loadhis")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_HIS_DETAIL)
    public ResultJson<RefundLoadRes> loadHisOrder(@RequestBody RefundLoadReq req) {
        RefundLoadRes entity = refundService.loadHis(req.getBookingid(), req.getPtype());
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.ORDER_SETUP_AUDITING_INDEX)
    @ApiOperation(value = "退款管理 - 历史退款申请记录导出excel", notes = "历史退款申请记录导出excel")
    @RequestMapping(value = "/his_output_excel")
    @RequireOpRight(opRight = OpRightCodes.OP_AUDITING_EXCEL_OUTPUT)
    public void outputExcel(@Validated @RequestBody RefundHisListReq req, HttpServletResponse httpServletResponse) throws DefinedException {
        refundService.hisoutPutExcel(req, httpServletResponse);
    }
}
