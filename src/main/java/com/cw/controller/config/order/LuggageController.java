package com.cw.controller.config.order;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/12/6 15:45
 **/

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.res.AppSingleOrderResult;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Query_Page_Req;
import com.cw.pojo.dto.common.req.Common_Query_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.res.luggage.PetCageListRes;
import com.cw.pojo.dto.order.req.LuggageListQueryReq;
import com.cw.pojo.dto.order.req.LuggageOpReq;
import com.cw.pojo.dto.order.res.LuggageOrderListRes;
import com.cw.pojo.dto.order.res.PetAvlCageRes;
import com.cw.pojo.dto.order.res.PetOrderListRes;
import com.cw.pojo.dto.order.res.PetStatusRes;
import com.cw.pojo.entity.Luggage_rs_Entity;
import com.cw.pojo.entity.Petcage_Entity;
import com.cw.pojo.entity.Petstorage_rs_Entity;
import com.cw.service.order.LuggageService;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Describe 行李管理
 * <AUTHOR> Just
 * @Create on 2021/12/13 0013
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGORDER, position = SwaggerUtil.MainIndex.CONFIGORDER_INDEX)
@RestController
@RequestMapping(value = "api/order/luggage", method = RequestMethod.POST)
public class LuggageController {

    @Autowired
    private LuggageService luggageService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "新增O修改行李订单", notes = "取消托运单或寄存单")
    @RequestMapping(value = "/updLuggageOrder")
    public ResultJson<AppSingleOrderResult> updLuggageOrder(@RequestBody Luggage_rs_Entity req) throws Exception {
        AppSingleOrderResult result = luggageService.updLuggageOrder(req);
        return ResultJson.ok().data(result);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "查看行李订单详情", notes = "查看行李订单详情")
    @RequestMapping(value = "/loadLuggage")
    public ResultJson<Luggage_rs_Entity> loadLuggage(@RequestBody Common_Load_Req req) {
        Luggage_rs_Entity entity = luggageService.loadLuggage(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "查看宠物寄存单详情", notes = "查看宠物寄存单详情")
    @RequestMapping(value = "/loadPetStorage")
    public ResultJson<Petstorage_rs_Entity> loadPetStorage(@RequestBody Common_Load_Req req) {
        Petstorage_rs_Entity entity = luggageService.loadPetStorage(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "确认接收行李订单", notes = "确认接收行李订单")
    @RequestMapping(value = "/confirmLuggageOrder")
    public ResultJson<Common_response> confirmLuggageOrder(@RequestBody LuggageOpReq req) throws Exception {
        luggageService.confirmLuggageOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @RequestMapping(value = "/cancelLuggageOrder")
    @ApiOperation(value = "取消寄存单", notes = "取消寄存单")
    public ResultJson<Common_response> cancelLuggageOrder(@RequestBody LuggageOpReq req) throws Exception {
        luggageService.cancelLuggageOrder(req);
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "寄存转托运", notes = "将寄存单转为托运单")
    @RequestMapping(value = "/storeToTransport")
    public ResultJson<Common_response> storeToTransport(@RequestBody LuggageOpReq req) throws Exception {
        boolean result = luggageService.store2TransPort(req);
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "扫码送达行李单", notes = "扫码送达行李单")
    @RequestMapping(value = "/deliverLuggageOrder")
    public ResultJson<Common_response> deliverLuggageOrder(@RequestBody LuggageOpReq req) throws Exception {
        luggageService.deliverLuggageOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "领取行李订单", notes = "领取托运单/行李")
    @RequestMapping(value = "/pickLuggageOrder")
    public ResultJson<Common_response> pickLuggageOrder(@RequestBody LuggageOpReq req) throws Exception {
        luggageService.pickLuggageOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "手动完结行李订单", notes = "手动完结行李订单")
    @RequestMapping(value = "/finishLuggageOrder")
    public ResultJson<Common_response> finishLuggageOrder(@RequestBody LuggageOpReq req) throws Exception {
        luggageService.finishLuggageOrder(req);
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "查询行李订单列表", notes = "查询行李订单列表")
    @RequestMapping(value = "/getUserLuggages")
    public ResultJson<LuggageOrderListRes> getUserLuggages(@RequestBody LuggageListQueryReq req) {
        LuggageOrderListRes res = luggageService.getLuggages(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @RequestMapping(value = "/outPutLuggageExcel")
    @ApiOperation(value = "导出寄存单EXCEL", notes = "导出寄存单EXCEL")
    public void outPutLuggageExcel(@RequestBody LuggageListQueryReq req, HttpServletResponse response) {
        luggageService.outPutExcel(req, response);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "获取宠物寄存状态", notes = "查询当前宠物寄存状态")
    @RequestMapping(value = "/getPetStatus")
    public ResultJson<PetStatusRes> getPetStatus(@RequestBody Common_Query_Req req) {
        PetStatusRes petStatus = luggageService.getPetStatus();
        return ResultJson.ok().data(petStatus);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "获取空闲宠物笼", notes = "获取空闲宠物笼")
    @RequestMapping(value = "/getPetAvlCage")
    public ResultJson<PetAvlCageRes> getPetAvlCage(@RequestBody Common_Query_Req req) {
        PetAvlCageRes petAvlRes = luggageService.getPetAvlCage();
        return ResultJson.ok().data(petAvlRes);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "新增OR修改宠物寄存订单", notes = "新增OR修改宠物寄存订单")
    @RequestMapping(value = "/updPetOrder")
    public ResultJson<AppSingleOrderResult> updPetOrder(@RequestBody Petstorage_rs_Entity req) throws Exception {
        AppSingleOrderResult result = luggageService.updPetOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "确认接收宠物订单", notes = "领取托运单/行李")
    @RequestMapping(value = "/confirmPetOrder")
    public ResultJson<Common_response> confirmPetOrder(@RequestBody LuggageOpReq req) throws Exception {
        luggageService.confirmPetOrder(req);
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @RequestMapping(value = "/cancelPetOrder")
    @ApiOperation(value = "取消宠物寄存单", notes = "取消宠物寄存单")
    public ResultJson<Common_response> cancelPetOrder(@RequestBody LuggageOpReq req) throws Exception {
        luggageService.cancelPetOrder(req);
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "完结宠物寄存", notes = "完结宠物寄存")
    @RequestMapping(value = "/finishPetOrder")
    public ResultJson<Common_response> finishPetOrder(@RequestBody LuggageOpReq req) throws Exception {
        luggageService.finishPetOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "获取宠物寄存列表", notes = "获取宠物寄存列表")
    @RequestMapping(value = "/getPetOrderList")
    public ResultJson<PetOrderListRes> getPetOrderList(@RequestBody LuggageListQueryReq req) {
        PetOrderListRes res = luggageService.getPetOrderList(req);
        return ResultJson.ok().data(res);
    }


    //@ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    //@ApiOperation(value = "批量创建宠物寄存柜", notes = "批量创建宠物寄存柜")
    //@RequestMapping(value = "/batchCreateCage")
    //public ResultJson<Common_response> batchCreateCage(@RequestBody PetCageCreateReq req) {
    //    luggageService.batchCreateCage(req);
    //    return ResultJson.ok().data(new Common_response());
    //}


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "更新宠物寄存柜OR批量新增宠物寄存柜", notes = "更新宠物寄存柜OR批量新增宠物寄存柜")
    @RequestMapping(value = "/updCage")
    public ResultJson<Common_response> updCage(@RequestBody Petcage_Entity req) {
        luggageService.updCage(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "获取宠物寄存柜详情", notes = "获取宠物寄存柜详情")
    @RequestMapping(value = "/loadCage")
    public ResultJson<Petcage_Entity> loadCage(@RequestBody Common_Load_Req req) {
        Petcage_Entity entity = luggageService.loadCageById(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "删除宠物寄存柜", notes = "删除宠物寄存柜")
    @RequestMapping(value = "/delCage")
    public ResultJson<Common_response> delCage(@RequestBody Common_Del_Req req) {
        luggageService.delCage(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "查询宠物寄存柜列表", notes = "查询宠物寄存柜列表")
    @RequestMapping(value = "/queryCageList")
    public ResultJson<PetCageListRes> fetchCagelist(@RequestBody Common_Query_Page_Req req) {
        PetCageListRes res = luggageService.queryCageList(req);
        return ResultJson.ok().data(res);
    }





}
