package com.cw.controller.config.order;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.order.req.AppointConfirmReq;
import com.cw.pojo.dto.order.req.AppointListReq;
import com.cw.pojo.dto.order.req.AppointLoadReq;
import com.cw.pojo.dto.order.res.AppointListRes;
import com.cw.pojo.dto.order.res.StdAppointData;
import com.cw.service.order.AppointService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe 订单预约记录
 * <AUTHOR> Tony Leung
 * @Create on 2021/11/26 0026
 */
@RestController
@Api(tags = SwaggerUtil.MainIndex.CONFIGORDER, position = SwaggerUtil.MainIndex.CONFIGORDER_INDEX)
@RequestMapping(value = "api/order/appoint", method = RequestMethod.POST)
public class AppointController {
    @Autowired
    AppointService appointService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.APPOINT_LIST_INDEX)
    @ApiOperation(value = "预约记录 - 会务预约记录列表", notes = "会务预约记录列表")
    @RequestMapping(value = "/list")
    @RequireOpRight(opRight = OpRightCodes.OP_APPOINT_LIST)
    public ResultJson<AppointListRes> refundList(@RequestBody AppointListReq req) {
        return ResultJson.ok().data(appointService.queryList(req));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.APPOINT_GET_INDEX)
    @ApiOperation(value = "预约记录 - 会务获取预约详情", notes = "会务获取预约详情")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_APPOINT_LOAD)
    public ResultJson<StdAppointData> load(@Validated @RequestBody AppointLoadReq req) {
        StdAppointData data = appointService.load(req);
        return ResultJson.ok().data(data);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigOrder.APPOINT_SAVE_INDEX)
    @ApiOperation(value = "预约记录 - 会务确定预约", notes = "会务确定预约")
    @RequestMapping(value = "/confirm")
    @RequireOpRight(opRight = OpRightCodes.OP_APPOINT_CONFIRM)
    public ResultJson<StdAppointData> save(@Validated @RequestBody AppointConfirmReq req) {
        return ResultJson.ok().data(appointService.save(req));
    }


}
