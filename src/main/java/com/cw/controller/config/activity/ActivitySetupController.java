package com.cw.controller.config.activity;


import com.cw.cache.GlobalCache;
import com.cw.entity.Actgroup;
import com.cw.entity.Actperiod;
import com.cw.entity.Actqr;
import com.cw.entity.Actsite;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.activity.*;
import com.cw.pojo.dto.conf.res.activity.*;
import com.cw.pojo.entity.Actgroup_Entity;
import com.cw.pojo.entity.Actperiod_Entity;
import com.cw.pojo.entity.Actqr_Entity;
import com.cw.pojo.entity.Actsite_Entity;
import com.cw.service.config.activity.ActivitySetupService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Api(tags = SwaggerUtil.MainIndex.ACTIVITYCONFIG, position = SwaggerUtil.MainIndex.ACTIVITYCONFIG_INDEX)
@RestController
@RequestMapping(value = "api/activitysetup", method = RequestMethod.POST)
public class ActivitySetupController {

    @Autowired
    ActivitySetupService activitySetupService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTGROUP_SETUP_LIST_INDEX)
    @ApiOperation(value = "预约管理设置 - 预约项目列表", notes = "获取预约项目列表数据")
    @RequestMapping(value = "/group_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTGROUP_LIST)
    public ResultJson<ActivitySetupListRes> group_list(@RequestBody ActivitySetupListReq req) {
        return ResultJson.ok().data(activitySetupService.queryActivitygroupList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTGROUP_SETUP_GET_INDEX)
    @ApiOperation(value = "预约管理设置 - 获取预约项目对象", notes = "根据唯一id获取预约项目对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTGROUP_LOAD)
    public ResultJson<Actgroup_Entity> load_group(@RequestBody Common_Load_Req req) {
        Actgroup actgroup = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            actgroup = activitySetupService.loadActgroup(req.getSqlid());
        } else {
            actgroup = new Actgroup();
        }
        Actgroup_Entity entity = new Actgroup_Entity();
        BeanUtils.copyProperties(actgroup, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTGROUP_SETUP_SAVE_INDEX)
    @ApiOperation(value = "预约管理设置 - 保存预约项目", notes = "保存预约项目")
    @RequestMapping(value = "/save_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTGROUP_SAVE)
    public ResultJson<Actgroup_Entity> save_group(@RequestBody Actgroup_Entity req) {
        Actgroup_Entity entity = activitySetupService.saveActgroup(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTGROUP_SETUP_DELETE_INDEX)
    @ApiOperation(value = "预约管理设置 - 删除预约项目", notes = "删除预约项目")
    @RequestMapping(value = "/delete_group")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTGROUP_DELETE)
    public ResultJson delete_group(@RequestBody Common_Del_Req req) {
        activitySetupService.deleteActgroup(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_LIST_INDEX)
    @ApiOperation(value = "预约管理设置 - 预约场所列表", notes = "获取预约场所列表数据")
    @RequestMapping(value = "/actsite_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTSITE_LIST)
    public ResultJson<Actsite_List_Res> actsite_list(@RequestBody ActsiteListReq req) {
        return ResultJson.ok().data(activitySetupService.queryActsiteList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_GET_INDEX)
    @ApiOperation(value = "预约设置 - 获取预约场所对象", notes = "根据唯一id获取预约场所对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_actsite")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTSITE_LOAD)
    public ResultJson<Actsite_Entity> load_actsite(@RequestBody ActsiteLoadReq req) {
        Actsite actsite = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            actsite = activitySetupService.loadActsite(req.getSqlid());
        } else {
            actsite = new Actsite();
            Actgroup actgroup = (Actgroup) GlobalCache.getDataStructure()
                    .getCache(SystemUtil.GlobalDataType.ACTGROUP).getRecord(GlobalContext.getCurrentProjectId()
                            , req.getGroup());
            if (actgroup != null) {
                actsite.setGroupid(actgroup.getCode());
            }
        }
        Actsite_Entity entity = new Actsite_Entity();
        BeanUtils.copyProperties(actsite, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_SAVE_INDEX)
    @ApiOperation(value = "预约设置 - 保存预约场所", notes = "保存预约场所")
    @RequestMapping(value = "/save_actsite")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTSITE_SAVE)
    public ResultJson<Actsite_Entity> save_actsite(@RequestBody Actsite_Entity req) {
        Actsite_Entity entity = activitySetupService.saveActsite(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_DELETE_INDEX)
    @ApiOperation(value = "预约设置 - 删除预约场所", notes = "删除预约场所")
    @RequestMapping(value = "/delete_actsite")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTSITE_DELETE)
    public ResultJson delete_actsite(@RequestBody Common_Del_Req req) {
        activitySetupService.deleteActsite(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_RESOURCE_PRICE_INDEX)
    @ApiOperation(value = "预约设置 - 获取预约场所库存&价格", notes = "获取预约场所库存&价格")
    @RequestMapping(value = "/load_actsite_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTSITE_RESOURCE_LOAD)
    public ResultJson<List<ActsiteResourceQueryRes>> load_actsite_resources_price(@Validated @RequestBody ActsiteResourceQueryReq req) {
        return ResultJson.ok().data(activitySetupService.queryActsiteResourceList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "预约设置 - 更新预约场所开关状态.对当前状态取反", notes = "更新预约场所开关状态")
    @RequestMapping(value = "/update_actsite_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTSITE_STATUS)
    public ResultJson updActsiteStatus(@Valid @RequestBody Common_Switch_Req req) {
        activitySetupService.updActsiteStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTGROUP_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "预约设置 - 更新预约项目开关状态.对当前状态取反", notes = "更新预约项目开关状态")
    @RequestMapping(value = "/update_actgroup_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTGROUP_STATUS)
    public ResultJson updActgroupStatus(@Valid @RequestBody Common_Switch_Req req) {
        activitySetupService.updActgroupStatus(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_RESOURCE_PRICE_UPDATE)
    @ApiOperation(value = "预约设置 - 设置预约场所库存&价格", notes = "设置预约场所价格&库存")
    @RequestMapping(value = "/save_actsite_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTSITE_RESOURCE_SAVE)
    public ResultJson save_actsite_resources_price(@Validated @RequestBody ActsiteResourceSaveReq req) {
        activitySetupService.batchActsite(req);
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_LIST_INDEX)
    @ApiOperation(value = "预约管理设置 - 预约时段列表", notes = "获取预约场时段表数据")
    @RequestMapping(value = "/actperiod_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTPERIOD_LIST)
    public ResultJson<Actperiod_List_Res> actperiod_list(@RequestBody ActperiodListReq req) {
        return ResultJson.ok().data(activitySetupService.queryActperiodList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_GET_INDEX)
    @ApiOperation(value = "预约设置 - 获取预约时段对象", notes = "根据唯一id获取预约时段对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_actperiod")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTPERIOD_LOAD)
    public ResultJson<Actperiod_Entity> load_actperiod(@RequestBody ActperiodLoadReq req) {
        Actperiod actperiod = activitySetupService.loadActperiod(req.getSqlid());
        Actperiod_Entity entity = new Actperiod_Entity();
        BeanUtils.copyProperties(actperiod, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_SAVE_INDEX)
    @ApiOperation(value = "预约设置 - 保存预约时段", notes = "保存预约时段")
    @RequestMapping(value = "/save_actperiod")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTPERIOD_SAVE)
    public ResultJson<Actperiod_Entity> save_actperiod(@RequestBody Actperiod_Entity req) {
        Actperiod_Entity entity = activitySetupService.saveActperiod(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTSITE_SETUP_DELETE_INDEX)
    @ApiOperation(value = "预约设置 - 删除预约时段", notes = "删除预约时段")
    @RequestMapping(value = "/delete_actperiod")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTPERIOD_DELETE)
    public ResultJson delete_actperiod(@RequestBody Common_Del_Req req) {
        activitySetupService.deleteActperiod(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTQR_SETUP_LIST_INDEX)
    @ApiOperation(value = "预约管理设置 - 组合项目列表", notes = "组合项目列表")
    @RequestMapping(value = "/actqr_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTQR_LIST)
    public ResultJson<Actqr_List_Res> actqr_list(@RequestBody ActqrListReq req) {
        return ResultJson.ok().data(activitySetupService.queryActqrList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTQR_SETUP_GET_INDEX)
    @ApiOperation(value = "预约设置 - 获取组合项目对象", notes = "根据唯一id获取组合项目对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_actrq")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTQR_LOAD)
    public ResultJson<Actqr_Entity> load_actrq(@RequestBody Common_Load_Req req) {
        Actqr actqr = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            actqr = activitySetupService.loadActqr(req.getSqlid());
        }
        Actqr_Entity entity = new Actqr_Entity();
        BeanUtils.copyProperties(actqr, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTQR_SETUP_SAVE_INDEX)
    @ApiOperation(value = "预约设置 - 保存组合项目", notes = "保存组合项目")
    @RequestMapping(value = "/save_actqr")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTQR_SAVE)
    public ResultJson<Actqr_Entity> save_actqr(@RequestBody Actqr_Entity req) {
        Actqr_Entity entity = activitySetupService.saveActqr(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigACT.ACTQR_SETUP_DELETE_INDEX)
    @ApiOperation(value = "预约设置 - 删除组合项目", notes = "删除组合项目")
    @RequestMapping(value = "/delete_actqr")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_ACTSQR_DELETE)
    public ResultJson delete_actqr(@RequestBody Common_Del_Req req) {
        activitySetupService.deleteActqr(req.getSqlid());
        return ResultJson.ok();
    }
}
