package com.cw.controller.config;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.log.req.UserLogListReq;
import com.cw.pojo.log.res.UserLogListRes;
import com.cw.service.log.UserLogService;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/21 0021
 */
@Api(tags = SwaggerUtil.MainIndex.LOG, position = SwaggerUtil.MainIndex.LOG_INDEX)
@RestController
@RequestMapping(value = "api/log", method = RequestMethod.POST)
public class UserLogController {

    @Autowired
    private UserLogService userLogService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Log.USERLOG_LIST_INDEX)
    @ApiOperation(value = "用户日志 - 用户日志列表", notes = "获取用户日志列表")
    @RequestMapping(value = "/userlog_list")
    public ResultJson<UserLogListRes> userlog_list(@RequestBody UserLogListReq _userLog_List_req) {
        return ResultJson.ok().data(userLogService.queryList(_userLog_List_req));
    }

    //@ApiOperationSupport(order=SwaggerUtil.SubIndex.Log.GRIDLOG_LIST_INDEX)
    //@ApiOperation(value = "用户日志 - 房表日志列表", notes = "获取房表日志列表")
    //@RequestMapping(value = "/gridlog_list")
    //public ResultJson<Common_UserLog_Res> gridlog_list(@RequestBody Common_GridLog_Req common_gridLog_req){
    //    return ResultJson.ok().data(userLogService.queryGridLogList(common_gridLog_req));
    //}
}
