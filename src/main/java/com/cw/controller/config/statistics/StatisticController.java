package com.cw.controller.config.statistics;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.statistic.req.Statistic_IndexData_Req;
import com.cw.pojo.dto.statistic.res.Statistic_IndexData_Res;
import com.cw.service.context.GlobalContext;
import com.cw.service.statistic.StatisticService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe 统计
 * <AUTHOR> <PERSON>
 * @Create on 2022/1/17 0017
 */

@Api(tags = SwaggerUtil.MainIndex.STATISTIC, position = SwaggerUtil.MainIndex.STATISTIC_INDEX)
@RestController
@RequestMapping(value = "api/statistic", method = RequestMethod.POST)
public class StatisticController {
    @Autowired
    private StatisticService statisticService;
    //@ApiOperationSupport(order = SwaggerUtil.SubIndex.Statistic.STATISTIC_ORDER_AUDITING_INDEX_)
    //@ApiOperation(value = "统计数据 - 统计等待审核的订单数量", notes = "统计等待审核的订单数量")
    //@RequestMapping(value = "/order_auditing")
    //public ResultJson<WaitAndAuditingOrderRes> index_data() {
    //    WaitAndAuditingOrderRes res = statisticService.getWaitAndAuditingOrder(GlobalContext.getCurrentProjectId());
    //    return ResultJson.ok().data(res);
    //}


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Statistic.STATISTIC_ORDER_AUDITING_INDEX_)
    @ApiOperation(value = "统计数据 - 每日统计数据", notes = "每日统计数据")
    @RequestMapping(value = "/dailystat_index")
    @RequireOpRight(opRight = OpRightCodes.OP_INDEX_DATA)
    public ResultJson<Statistic_IndexData_Res> dailystat(@RequestBody Statistic_IndexData_Req req) {
        Statistic_IndexData_Res res = statisticService.getIndexStatisticData(req, GlobalContext.getCurrentProjectId());
        return ResultJson.ok().data(res);
    }




}
