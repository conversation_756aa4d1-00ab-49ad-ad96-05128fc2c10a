package com.cw.controller.config.sync;

import com.cw.arithmetic.lang.R;
import com.cw.cache.GlobalCache;
import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Op_Req;
import com.cw.pojo.dto.common.res.Common_Select_Res;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.sync.req.SyncResourceReq;
import com.cw.service.context.GlobalContext;
import com.cw.service.sync.SyncDataService;
import com.cw.utils.SpringUtil;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Describe 同步数据接口
 * <AUTHOR> Tony Leung
 * @Create on 2021/11/29 0029
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGSYNCDATA, position = SwaggerUtil.MainIndex.CONFIGSYNCDATA_INDEX)
@RestController
@RequestMapping(value = "/api/syncconf", method = RequestMethod.POST)
public class SyncConfigController {
    @Autowired
    SyncDataService syncDataService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigSyncData.SYNC_DATA_INDEX)
    @ApiOperation(value = "数据同步 - 同步PMS数据", notes = "同步PMS数据")
    @RequestMapping(value = "/sync", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYNC_PMS)
    public ResultJson<Common_response> syncdata(@RequestBody Common_Op_Req req) {
        Common_response customResponse = syncDataService.syncPmsData(req);
        return ResultJson.ok().data(customResponse);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigSyncData.SYNC_REFRESH_CACHE)
    @ApiOperation(value = "公共数据-刷新缓存", notes = "刷新缓存")
    @RequestMapping(value = "/refresh_cache")
    public ResultJson refresh_cache() {
        GlobalCache globalCache = SpringUtil.getBean(GlobalCache.class);
        String projectId = GlobalContext.getCurrentProjectId();
        globalCache.refreshAndNotify(SystemUtil.GlobalDataType.ALL, projectId);
        /*****先临时这么用*****/
        globalCache.refreshAndNotify(SystemUtil.GlobalDataType.RLANG, projectId);
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        R.init();
        /*****先临时这么用*****/
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigSyncData.SYNC_RATE_AND_AVL)
    @ApiOperation(value = "数据同步产品库存&价格", notes = "同步产品库存&价格")
    @RequestMapping(value = "/sync_product_resource")
    public ResultJson<List<Common_Select_Res>> refresh_rate_cache(@RequestBody SyncResourceReq req) throws DefinedException, InterruptedException {
        Common_response customResponse = syncDataService.syncProdAvlAndRate(req);
        return ResultJson.ok().data(customResponse);
    }


}
