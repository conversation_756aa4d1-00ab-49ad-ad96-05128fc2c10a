package com.cw.controller.config.na;

import com.cw.entity.Naparam;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.na.req.NaConfig_Req;
import com.cw.pojo.dto.na.req.NaConfig_Save_Req;
import com.cw.pojo.dto.na.req.NaDailyLog_Req;
import com.cw.pojo.dto.na.res.NaConfig_Res;
import com.cw.pojo.dto.na.res.NaDailyLog_Res;
import com.cw.pojo.dto.na.res.NaParamName_Res;
import com.cw.pojo.entity.NaConfig_Entity;
import com.cw.pojo.entity.NaRunInfo_Entity;
import com.cw.pojo.entity.Naparam_Entity;
import com.cw.service.na.NaManager;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.na.NaParams;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/12/29 0029
 */
@Api(tags = SwaggerUtil.MainIndex.CONFIGNA, position = SwaggerUtil.MainIndex.CONFIGNA_INDEX)
@RestController
@RequestMapping(value = "api/schedule")
public class NaConfigController {
    @Autowired
    NaManager naManager;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNa.NA_PARAM_LIST_INDEX)
    @ApiOperation(value = "夜审设置 - 获取夜审配置和参数列表", notes = "获取夜审配置和参数列表")
    @RequestMapping(value = "/naParam_list", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_NA_LIST)
    public ResultJson<NaConfig_Res> naParam_list(@RequestBody NaConfig_Req req) {
        return ResultJson.ok().data(naManager.queryParamList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNa.NA_CONFIG_SAVE_INDEX)
    @ApiOperation(value = "夜审设置 - 保存夜审配置", notes = "保存夜审配置")
    @RequestMapping(value = "/save_naConfig", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_NA_CONFIG_SAVE)
    public ResultJson<NaConfig_Entity> save_naConfig(@RequestBody NaConfig_Save_Req req) {
        return ResultJson.ok().data(naManager.saveConfig(req.getRuntime()));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNa.NA_PARAM_LOAD_INDEX)
    @ApiOperation(value = "夜审设置 - 获取夜审参数对象", notes = "获取夜审参数对象")
    @RequestMapping(value = "/load_naParam", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_NA_PARAM_LOAD)
    public ResultJson<Naparam_Entity> load_naParam(@RequestBody Common_Load_Req req) {
        Naparam naparam = null;
        if (req.getSqlid() > 0L) {
            naparam = naManager.loadNaParam(req.getSqlid());
        } else {
            naparam = new Naparam();
        }
        Naparam_Entity entity = new Naparam_Entity();
        BeanUtils.copyProperties(naparam, entity);
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNa.NA_PARAM_SAVE_INDEX)
    @ApiOperation(value = "夜审设置 - 保存夜审参数", notes = "保存夜审参数")
    @RequestMapping(value = "/save_naParam", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_NA_PARAM_SAVE)
    public ResultJson<Naparam_Entity> save_naParam(@RequestBody Naparam_Entity entity) {
        return ResultJson.ok().data(naManager.saveParams(entity));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNa.NA_PARAM_DELETE_INDEX)
    @ApiOperation(value = "夜审设置 - 删除夜审参数", notes = "删除审参数")
    @RequestMapping(value = "/delete_naParam", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_NA_PARAM_DELETE)
    public ResultJson delete_naParam(@RequestBody Common_Del_Req req) {
        naManager.deleteNaParam(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNa.NA_PARAM_NAME_INDEX)
    @ApiOperation(value = "夜审设置 - 获取参数名列表", notes = "获取参数名列表")
    @RequestMapping(value = "/paramName_list", method = RequestMethod.POST)
    public ResultJson<NaParamName_Res> paramName_list() {
        NaParamName_Res res = new NaParamName_Res();
        List<NaParamName_Res.NaParamName> names = new ArrayList<>();
        NaParamName_Res.NaParamName name;
        NaParams[] naParams = NaParams.values();
        for (NaParams naParam : naParams) {
            name = new NaParamName_Res.NaParamName();
            name.setParamname(naParam.name());
            name.setParamdesc(naParam.getDesc());
            names.add(name);
        }
        res.setNames(names);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNa.NA_GET_STATUS_INDEX)
    @ApiOperation(value = "夜审设置 - 获取夜审状态", notes = "获取夜审状态")
    @RequestMapping(value = "/load_naRunInfo", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_NA_INFO)
    public ResultJson<NaRunInfo_Entity> load_naRunInfo() {
        NaRunInfo_Entity entity = naManager.getNaRunInfo();
        return ResultJson.ok().data(entity);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNa.NA_RESTART_STATUS_INDEX)
    @ApiOperation(value = "夜审设置 - 重设夜审", notes = "重设夜审")
    @RequestMapping(value = "/refresh_naStatus", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_NA_RESET)
    public ResultJson refresh_naStatus() {
        naManager.resetDailyTask();
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigNa.NA_GET_LOG_INDEX)
    @ApiOperation(value = "夜审设置 - 获取夜审日志", notes = "获取夜审日志")
    @RequestMapping(value = "/load_naLog", method = RequestMethod.POST)
    @RequireOpRight(opRight = OpRightCodes.OP_SYSCONFIG_NA_LOG)
    public ResultJson<NaDailyLog_Res> load_naLog(@RequestBody NaDailyLog_Req req) {
        NaDailyLog_Res naLog = naManager.queryNaLog(req);
        return ResultJson.ok().data(naLog);
    }
}
