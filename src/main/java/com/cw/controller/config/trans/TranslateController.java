package com.cw.controller.config.trans;

import com.cw.entity.Rlang;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.req.translate.RlangQueryReq;
import com.cw.pojo.dto.conf.req.translate.RlangSyncReq;
import com.cw.pojo.dto.conf.res.traslate.RlangListRes;
import com.cw.pojo.entity.Rlang_Entity;
import com.cw.service.config.translate.TranslateService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/17 0017
 */
@Api(tags = SwaggerUtil.MainIndex.TRANSCONFIG, position = SwaggerUtil.MainIndex.TRANSCONFIG_INDEX)
@RestController
@RequestMapping(value = "/api/trans", method = RequestMethod.POST)
public class TranslateController {
    @Autowired
    TranslateService translateService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTranslate.CONFIGTRANSLATE_LIST)
    @ApiOperation(value = "多语言翻译 - 获取列表数据", notes = "获取列表数据")
    @RequestMapping(value = "/list")
    public ResultJson<RlangListRes> fetchlist(@RequestBody RlangQueryReq req) {
        return ResultJson.ok().data(translateService.queryListData(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTranslate.CONFIGTRANSLATE_LOAD)
    @ApiOperation(value = "多语言翻译 - 获取对象", notes = "根据唯一id获取房型对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_RMTYPE_LOAD)
    public ResultJson<Rlang_Entity> load(@RequestBody Common_Load_Req req) {
        Rlang rlang = null;
        if (req.getSqlid() > 0) {
            rlang = translateService.loadRlang(req.getSqlid());
        } else {
            rlang = new Rlang();
        }
        Rlang_Entity entity = new Rlang_Entity();
        BeanUtils.copyProperties(rlang, entity);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTranslate.CONFIGTRANSLATE_SAVE)
    @ApiOperation(value = "多语言翻译 - 保存翻译", notes = "保存翻译")
    @RequestMapping(value = "/save")
    public ResultJson<Rlang_Entity> save(@RequestBody Rlang_Entity req) {
        Rlang_Entity entity = translateService.saveRlang(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigTranslate.CONFIGTRANSLATE_SYNC)
    @ApiOperation(value = "多语言翻译 - 同步(留空)", notes = "同步")
    @RequestMapping(value = "/sync")
    public ResultJson<Common_response> del(@RequestBody RlangSyncReq req) {
        Common_response response = translateService.syncRlang(req.getLang());
        return ResultJson.ok().data(response);
    }


}
