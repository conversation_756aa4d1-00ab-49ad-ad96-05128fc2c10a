package com.cw.controller.config.productkit;

import com.cw.entity.Kitgroup;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Switch_Req;
import com.cw.pojo.dto.conf.req.productkit.KitItemListReq;
import com.cw.pojo.dto.conf.req.productkit.ProductKitListReq;
import com.cw.pojo.dto.conf.req.productkit.ProductKitResourceQueryReq;
import com.cw.pojo.dto.conf.req.productkit.group.KitGroupListReq;
import com.cw.pojo.dto.conf.req.productkit.group.KitGroupSaveReq;
import com.cw.pojo.dto.conf.res.productkit.KitItemListRes;
import com.cw.pojo.dto.conf.res.productkit.ProductKitListRes;
import com.cw.pojo.dto.conf.res.productkit.ProductKitResourcePriceListRes;
import com.cw.pojo.dto.conf.res.productkit.group.KitGroupListRes;
import com.cw.pojo.entity.Kitgroup_Entity;
import com.cw.pojo.entity.ProductKit_Entity;
import com.cw.service.config.productkit.ProductKitService;
import com.cw.utils.CalculateDate;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * @Describe 套餐设置
 * <AUTHOR> Tony Leung
 * @Create on 2021/9/16 0016
 */

@Api(tags = SwaggerUtil.MainIndex.CONFIGPACKAGE, position = SwaggerUtil.MainIndex.CONFIGPACKAGE_INDEX)
@RestController
@RequestMapping(value = "api/productkit", method = RequestMethod.POST)
public class ProductKitController {

    @Autowired
    ProductKitService productKitService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.KITGROUP_SETUP_LIST_INDEX)
    @ApiOperation(value = "套餐设置 - 套餐大类列表", notes = "获取套餐大类列表数据")
    @RequestMapping(value = "/kitgroup_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_KITGROUP_LIST)
    public ResultJson<KitGroupListRes> kitgroup_list(@Validated @RequestBody KitGroupListReq req) {
        return ResultJson.ok().data(productKitService.queryKitGroupList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.KITGROUP_SETUP_GET_INDEX)
    @ApiOperation(value = "套餐设置 - 获取套大类餐对象", notes = "根据唯一id获取套餐大类对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_kitgroup")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_KITGROUP_LOAD)
    public ResultJson<Kitgroup_Entity> load_kitgroup(@Validated @RequestBody Common_Load_Req req) {
        Kitgroup kitgroup = null;
        if (req.getSqlid() != null && req.getSqlid() > 0) {
            kitgroup = productKitService.loadKitGroup(req.getSqlid());
        } else {
            kitgroup = new Kitgroup();
            kitgroup.setStartdate(new Date());
            kitgroup.setEnddate(CalculateDate.reckonDay(kitgroup.getStartdate(), 5, 7));//默认7天
        }
        Kitgroup_Entity entity = new Kitgroup_Entity();
        BeanUtils.copyProperties(kitgroup, entity);
        //获取票务绑定票型数据
        entity.setKitCodeList(productKitService.getKitGroupData(entity.getCode()));
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.KITGROUP_SETUP_SAVE_INDEX)
    @ApiOperation(value = "套餐设置 - 保存套餐大类", notes = "保存套餐大类")
    @RequestMapping(value = "/save_kitgroup")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_KITGROUP_SAVE)
    public ResultJson<Kitgroup_Entity> save_kitgroup(@RequestBody KitGroupSaveReq req) {
        return ResultJson.ok().data(productKitService.saveKitGroup(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.KITGROUP_SETUP_DELETE_INDEX)
    @ApiOperation(value = "套餐设置 - 删除套餐大类", notes = "删除套餐大类")
    @RequestMapping(value = "/delete_kitgroup")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_KITGROUP_DELETE)
    public ResultJson delete_kitgroup(@RequestBody Common_Del_Req req) {
        productKitService.deleteKitGroup(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.KITGROUP_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "套餐设置 - 更新套餐大类开关状态.对当前状态取反", notes = "更新套餐大类状态")
    @RequestMapping(value = "/update_kitGroup_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_KITGROUP_STATUS)
    public ResultJson updKitGroupStatus(@Valid @RequestBody Common_Load_Req req) {
        productKitService.updKitGroupStatus(req.getSqlid());
        return ResultJson.ok();
    }

    /**
     * 套餐小类
     */
    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.PRODUCTKIT_SETUP_LIST_INDEX)
    @ApiOperation(value = "套餐设置 - 套餐小类列表", notes = "获取套餐小类列表数据")
    @RequestMapping(value = "/subclass_list")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCTKIT_LIST)
    public ResultJson<ProductKitListRes> subclass_list(@RequestBody ProductKitListReq req) {
        return ResultJson.ok().data(productKitService.queryProductKitList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.PRODUCTKIT_SETUP_GET_INDEX)
    @ApiOperation(value = "套餐设置 - 获取套餐小类对象", notes = "根据唯一id获取套餐详情对象,id=0返回带默认值未保存对象")
    @RequestMapping(value = "/load_subclass")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCTKIT_LOAD)
    public ResultJson<ProductKit_Entity> load_subclass(@RequestBody Common_Load_Req req) {
        return ResultJson.ok().data(productKitService.loadProductKit(req.getSqlid()));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.PRODUCTKIT_SETUP_SAVE_INDEX)
    @ApiOperation(value = "套餐设置 - 保存套餐小类", notes = "保存套餐小类")
    @RequestMapping(value = "/save_subclass")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCTKIT_SAVE)
    public ResultJson<ProductKit_Entity> save_subclass(@RequestBody ProductKit_Entity req) {
        ProductKit_Entity entity = productKitService.saveProductKit(req);
        return ResultJson.ok().data(entity);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.PRODUCTKIT_SETUP_DELETE_INDEX)
    @ApiOperation(value = "套餐设置 - 删除套餐小类 解绑套餐大类", notes = "删除套餐小类 解绑套餐大类")
    @RequestMapping(value = "/delete_subclass")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCTKIT_DELETE)
    public ResultJson delete_subclass(@RequestBody Common_Del_Req req) {
        productKitService.deleteProductKit(req.getSqlid());
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.PRODUCTKIT_SETUP_RESOURCE_PRICE_INDEX)
    @ApiOperation(value = "套餐设置 - 获取套餐代码库存&价格", notes = "获取套餐代码库存&库存")
    @RequestMapping(value = "/load_resources_price")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCTKIT_RESOURCE)
    public ResultJson<List<ProductKitResourcePriceListRes>> load_resources_price(@Validated @RequestBody ProductKitResourceQueryReq req) {
        return ResultJson.ok().data(productKitService.queryKitCodeResourceList(req));
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.PRODUCTKIT_SETUP_UPDATE_STATUS_INDEX)
    @ApiOperation(value = "套餐设置 - 更新套餐开关状态.对当前状态取反", notes = "更新套餐状态")
    @RequestMapping(value = "/update_productkit_status")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCTKIT_STATUS)
    public ResultJson updProductKitStatus(@Valid @RequestBody Common_Switch_Req req) {
        productKitService.updProductKitStatus(req.getSqlid());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.PRODUCTKIT_DETAIL_SETUP_LIST_INDEX)
    @ApiOperation(value = "套餐设置 - 获取套餐小类详情列表", notes = "获取套餐小类详情列表")
    @RequestMapping(value = "/load_subclass_detail")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_KITITEM_LIST)
    public ResultJson<KitItemListRes> load_subclass_detail(@RequestBody KitItemListReq req) {
        return ResultJson.ok().data(productKitService.load_subclass_detail(req));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.ConfigPackage.PRODUCTKIT_DETAIL_SETUP_LIST_INDEX)
    @ApiOperation(value = "套餐设置 - 删除套餐小类", notes = "删除套餐小类")
    @RequestMapping(value = "/delete_productkit")
    @RequireOpRight(opRight = OpRightCodes.OP_CONFIG_PRODUCTKIT_DELETE)
    public ResultJson<KitItemListRes> delete_productkit(@RequestBody Common_Del_Req req) {
        productKitService.delete_productkit(req.getSqlid());
        return ResultJson.ok();
    }
}
