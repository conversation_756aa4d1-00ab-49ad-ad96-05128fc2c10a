package com.cw.controller.pub;

import com.cw.arithmetic.SysFuncLibTool;
import com.cw.cache.RedisTool;
import com.cw.utils.RedisKey;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 提供给微信登陆校验用的
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/4/8 16:23
 **/
@Slf4j
@RestController
public class AppMpValidateController {


    /**
     * 给微信公众号做校验用的方法
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/MP_verify_*.txt", method = RequestMethod.GET)
    public String wxPrivateKey2(HttpServletRequest request) {
        String result = SysFuncLibTool.getSubUtilSimple(request.getRequestURI(), "MP_verify_(.*?).txt");
        return result;
    }

    /**
     * 配合nginx 给微信公众号做校验用的方法
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/*.txt", method = RequestMethod.GET)
    public String ttpvalidate(HttpServletRequest request) {
        String key = SysFuncLibTool.getSubUtilSimple(request.getRequestURI(), "/(.*?).txt");
        RMap<String, String> map = RedisTool.getRedissonClient().getMap(RedisKey.MPCHECKINFO);
        String val = map.getOrDefault(key, "");
        if (!val.isEmpty()) {
            return val;
        } else {
            return "no exist4009d5d804eaa63f4d7060ed363f844d";
        }
    }




}
