package com.cw.controller.app;

import com.cw.arithmetic.func.MockActPerson;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.AppOrderPayQueryReq;
import com.cw.pojo.dto.app.req.AppPassPayReq;
import com.cw.pojo.dto.app.req.AppSubscribeNotifyMsgReq;
import com.cw.pojo.dto.app.res.AppCommonPayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;
import com.cw.pojo.dto.common.req.Common_Query_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.service.app.AppPayService;
import com.cw.service.app.AppSupService;
import com.cw.utils.RedisKey;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDateTime;

/**
 * 微信免登录功能接口
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/4/8 16:23
 **/
@Slf4j
@Api(tags = SwaggerUtil.MainIndex.WXPUB, position = SwaggerUtil.MainIndex.APPPUB_INDEX)
@RestController
@RequestMapping(value = "/webapi/pub")
public class AppPubController {


    @Autowired
    AppPayService userPayService; //支付服务

    @Autowired
    AppSupService appSupService;

    @Autowired
    RedisTemplate redisTemplate;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "发起线下支付单支付", notes = "线下支付单支付")
    @RequestMapping(value = "/passpay", method = RequestMethod.POST)
    public ResultJson<AppCommonPayRes> callback(@Valid @RequestBody AppPassPayReq appPassPayReq) throws Exception {
        AppCommonPayRes res = userPayService.createPassOrderPay(appPassPayReq);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "支付查询", notes = "支付交易查询")
    @RequestMapping(value = "/querypay", method = RequestMethod.POST)
    public ResultJson<AppQueryPayRes> queryPay(@Valid @RequestBody AppOrderPayQueryReq appPassPayReq) throws Exception {
        AppQueryPayRes res = userPayService.queryOrderPayStatus(appPassPayReq);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "微信小程序消息订阅", notes = "微信小程序消息订阅")
    @RequestMapping(value = "/wxmsgsub", method = RequestMethod.POST)
    public ResultJson<Common_response> callback(@Valid @RequestBody AppSubscribeNotifyMsgReq req) throws Exception {
        Common_response res = appSupService.createWxSubMsg(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "参与人数查询", notes = "参与人数查询")
    @RequestMapping(value = "/queryperson", method = RequestMethod.POST)
    public ResultJson<Common_response> queryPerson(@Valid @RequestBody Common_Query_Req req) throws Exception {
        Common_response res = new Common_response();
        res.setNum(MockActPerson.calculateParticipants(LocalDateTime.now()));
        String peopleNumStr = (String) redisTemplate.opsForValue().get(RedisKey.auto_increase_people_num);
        if (peopleNumStr != null) {
            res.setNum(Integer.valueOf(peopleNumStr));
        }
        return ResultJson.ok().data(res);
    }


}
