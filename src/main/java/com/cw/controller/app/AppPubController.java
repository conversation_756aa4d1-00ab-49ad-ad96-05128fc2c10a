package com.cw.controller.app;

import com.cw.arithmetic.func.MockActPerson;
import com.cw.core.CoreAizhu;
import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.AppCheckinReq;
import com.cw.pojo.dto.app.req.AppOrderOpReq;
import com.cw.pojo.dto.app.req.AppOrderPayQueryReq;
import com.cw.pojo.dto.app.req.AppPassPayReq;
import com.cw.pojo.dto.app.req.AppQueryCheckinOrderReq;
import com.cw.pojo.dto.app.req.AppQueryHotelInfoReq;
import com.cw.pojo.dto.app.req.AppSubscribeNotifyMsgReq;
import com.cw.pojo.dto.app.res.AppCheckinRes;
import com.cw.pojo.dto.app.res.AppCommonPayRes;
import com.cw.pojo.dto.app.res.AppOrderDetailResult;
import com.cw.pojo.dto.app.res.AppQueryCheckinOrderRes;
import com.cw.pojo.dto.app.res.AppQueryHotelInfoRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;
import com.cw.pojo.dto.common.req.Common_Query_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.service.app.AppPayService;
import com.cw.service.app.AppSupService;
import com.cw.utils.RedisKey;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDateTime;

/**
 * 微信免登录功能接口
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/4/8 16:23
 **/
@Slf4j
@Api(tags = SwaggerUtil.MainIndex.WXPUB, position = SwaggerUtil.MainIndex.APPPUB_INDEX)
@RestController
@RequestMapping(value = "/webapi/pub")
public class AppPubController {


    @Autowired
    AppPayService userPayService; //支付服务

    @Autowired
    AppSupService appSupService;

    @Autowired
    CoreAizhu coreAizhu; //爱住核心服务

    @Autowired
    RedisTemplate redisTemplate;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "发起线下支付单支付", notes = "线下支付单支付")
    @RequestMapping(value = "/passpay", method = RequestMethod.POST)
    public ResultJson<AppCommonPayRes> callback(@Valid @RequestBody AppPassPayReq appPassPayReq) throws Exception {
        AppCommonPayRes res = userPayService.createPassOrderPay(appPassPayReq);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "支付查询", notes = "支付交易查询")
    @RequestMapping(value = "/querypay", method = RequestMethod.POST)
    public ResultJson<AppQueryPayRes> queryPay(@Valid @RequestBody AppOrderPayQueryReq appPassPayReq) throws Exception {
        AppQueryPayRes res = userPayService.queryOrderPayStatus(appPassPayReq);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "微信小程序消息订阅", notes = "微信小程序消息订阅")
    @RequestMapping(value = "/wxmsgsub", method = RequestMethod.POST)
    public ResultJson<Common_response> callback(@Valid @RequestBody AppSubscribeNotifyMsgReq req) throws Exception {
        Common_response res = appSupService.createWxSubMsg(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "参与人数查询", notes = "参与人数查询")
    @RequestMapping(value = "/queryperson", method = RequestMethod.POST)
    public ResultJson<Common_response> queryPerson(@Valid @RequestBody Common_Query_Req req) throws Exception {
        Common_response res = new Common_response();
        res.setNum(MockActPerson.calculateParticipants(LocalDateTime.now()));
        String peopleNumStr = (String) redisTemplate.opsForValue().get(RedisKey.auto_increase_people_num);
        if (peopleNumStr != null) {
            res.setNum(Integer.valueOf(peopleNumStr));
        }
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "开放接口-客房订单查询", notes = "客房订单查询")
    @RequestMapping(value = "/queryRoom", method = RequestMethod.POST)
    public ResultJson<AppOrderDetailResult> queryRoom(@Valid @RequestBody AppOrderOpReq req) throws DefinedException {
        AppOrderDetailResult result = appSupService.queryRoomOrder(req);
        return ResultJson.ok().data(result);
    }

    // ==================== OTA 相关接口 ====================

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "OTA入住办理", notes = "通过SDK调用dspms工程的入住办理接口")
    @RequestMapping(value = "/otacheckin", method = RequestMethod.POST)
    public ResultJson<AppCheckinRes> otaCheckin(@Valid @RequestBody AppCheckinReq req) throws DefinedException {
            AppCheckinRes result = coreAizhu.checkIn(req);
            return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "OTA查询入住订单", notes = "通过SDK调用dspms工程的查询入住订单接口")
    @RequestMapping(value = "/otaqueryCheckinOrder", method = RequestMethod.POST)
    public ResultJson<AppQueryCheckinOrderRes> otaQueryCheckinOrder(@Valid @RequestBody AppQueryCheckinOrderReq req) throws DefinedException {
            AppQueryCheckinOrderRes result = coreAizhu.queryCheckinOrder(req);
            return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "OTA查询酒店信息", notes = "通过SDK调用dspms工程的查询酒店信息接口")
    @RequestMapping(value = "/otaqueryHotelInfo", method = RequestMethod.POST)
    public ResultJson<AppQueryHotelInfoRes> otaQueryHotelInfo(@Valid @RequestBody AppQueryHotelInfoReq req) throws DefinedException {
            AppQueryHotelInfoRes result = coreAizhu.queryHotelInfo(req);
            return ResultJson.ok().data(result);
    }

}
