package com.cw.controller.app;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.AppOrderQueryListReq;
import com.cw.pojo.dto.app.res.AppSingleOrderResult;
import com.cw.pojo.dto.app.res.AppUserLuggageListRes;
import com.cw.pojo.dto.app.res.AppUserPetOrderListRes;
import com.cw.pojo.dto.order.req.LuggageOpReq;
import com.cw.pojo.entity.Luggage_rs_Entity;
import com.cw.pojo.entity.Petstorage_rs_Entity;
import com.cw.service.order.LuggageService;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = SwaggerUtil.MainIndex.APPUSERACTION, position = SwaggerUtil.MainIndex.APPUSER_INDEX)
@RestController
@RequestMapping(value = "/webapi/userAction", method = RequestMethod.POST)
public class AppUserAction2Controller {


    @Autowired
    private LuggageService appLuggageService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户创建行李寄存订单", notes = "用户创建行李寄存订单")
    @RequestMapping(value = "/saveLuggageOrder")
    public ResultJson<AppSingleOrderResult> saveLuggageOrder(@RequestBody Luggage_rs_Entity luggageEntity) throws DefinedException {
        AppSingleOrderResult result = appLuggageService.appSaveLuggageOrder(luggageEntity);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户取消行李订单", notes = "取消托运单或寄存单")
    @RequestMapping(value = "/cancelLuggageOrder")
    public ResultJson<Void> cancelLuggageOrder(@RequestBody LuggageOpReq req) throws Exception {
        appLuggageService.appCancelLuggageOrder(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户领取行李订单", notes = "用户领取托运单")
    @RequestMapping(value = "/pickLuggageOrder")
    public ResultJson<Void> pickLuggageOrder(@RequestBody LuggageOpReq req) throws Exception {
        appLuggageService.appPickLuggageOrder(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户查询用户行李订单", notes = "根据用户ID查询行李寄存订单")
    @RequestMapping(value = "/getUserLuggageList")
    public ResultJson<AppUserLuggageListRes> getUserLuggages(@RequestBody AppOrderQueryListReq req) {
        AppUserLuggageListRes luggages = appLuggageService.getAppUserLuggages(req);
        return ResultJson.ok().data(luggages);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户查看行李订单详情", notes = "通过订单号查看托运寄存单")
    @RequestMapping(value = "/loadLuggageOrder")
    public ResultJson<Luggage_rs_Entity> loadLuggageOrder(@RequestBody LuggageOpReq req) {
        Luggage_rs_Entity luggage = appLuggageService.appLoadLuggageByOrderNo(req);
        return ResultJson.ok().data(luggage);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户创建宠物寄存订单", notes = "用户创建宠物寄存订单")
    @RequestMapping(value = "/savePetOrder")
    public ResultJson<AppSingleOrderResult> savePetOrder(@RequestBody Petstorage_rs_Entity entity) throws DefinedException {
        AppSingleOrderResult result = appLuggageService.appSavePetOrder(entity);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户取消宠物寄存订单", notes = "取消宠物寄存订单")
    @RequestMapping(value = "/cancelPetOrder")
    public ResultJson<Void> cancelPetOrder(@RequestBody LuggageOpReq req) throws Exception {
        appLuggageService.appCancelPetOrder(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户领取宠物寄存订单", notes = "用户领取宠物寄存订单")
    @RequestMapping(value = "/pickPetOrder")
    public ResultJson<Void> pickPetOrder(@RequestBody LuggageOpReq req) throws Exception {
        appLuggageService.appPickPetOrder(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户查询宠物订单列表", notes = "用户查询宠物订单列表")
    @RequestMapping(value = "/getUserPetOrders")
    public ResultJson<AppUserPetOrderListRes> getUserPetOrders(@RequestBody AppOrderQueryListReq req) {
        AppUserPetOrderListRes petOrders = appLuggageService.appQueryUserPetOrders(req);
        return ResultJson.ok().data(petOrders);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "用户查看宠物订单详情", notes = "用户查看宠物订单详情")
    @RequestMapping(value = "/loadPetOrder")
    public ResultJson<Petstorage_rs_Entity> loadPetOrder(@RequestBody LuggageOpReq req) {
        Petstorage_rs_Entity petOrder = appLuggageService.appLoadPetOrderByOrderNo(req);
        return ResultJson.ok().data(petOrder);
    }


}
