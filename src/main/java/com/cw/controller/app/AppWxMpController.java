package com.cw.controller.app;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.config.Cwconfig;
import com.cw.core.platform.wechat.WxMpConfiguration;
import com.cw.entity.Vendorconfig;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.login.OauthLoginCommonReq;
import com.cw.pojo.dto.app.res.AppUrlRes;
import com.cw.pojo.dto.app.res.WxLoginRes;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.service.app.AppOauthService;
import com.cw.service.app.AppUserService;
import com.cw.utils.SwaggerUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.VendorType;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.service.WxOAuth2Service;
import me.chanjar.weixin.mp.api.WxMpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 微信公众号登陆接口
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/4/8 16:23
 **/
@Slf4j
@Api(tags = SwaggerUtil.MainIndex.WXOP, position = SwaggerUtil.MainIndex.APPMP_INDEX)
@RestController
@RequestMapping(value = "/webapi/wxop")
public class AppWxMpController {

//    @Autowired
//    WxMpConfiguration wxMpConfiguration;

    public static final String H5_AUTHORIZE_URL = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=%s&scope=%s&state=%s#wechat_redirect";
    @Autowired
    AppUserService loginService;

    @Autowired
    AppOauthService oauthService;


    @Autowired
    Cwconfig cwconfig;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "公众号移动端-构建授权链接", notes = "构造授权链接")
    @RequestMapping(value = "/getMpAuthUrl", method = RequestMethod.GET)
    public ResultJson<AppUrlRes> auth(@ApiParam(value = "公众号APPID", required = true) @RequestParam("mpappid") String mpappid,
                                      @ApiParam(value = "返回原路径", required = true) @RequestParam("returnurl") String returnurl) {
        WxMpService wxMpService = WxMpConfiguration.getMpService(mpappid);
        VendorConfigCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig mpconfig = cache.getVendorConfigByAppId(mpappid);
        String projectId = mpconfig != null ? mpconfig.getProjectid() : "";
        String oauthState = SysFuncLibTool.generateOauthRandomStateData(mpappid, returnurl, projectId, VendorType.WX_MP.toString());
        if (wxMpService != null) {
            WxOAuth2Service oAuth2Service = wxMpService.getOAuth2Service();
            AppUrlRes urlRes = new AppUrlRes();
            String url = oAuth2Service.buildAuthorizationUrl(cwconfig.getDomain() + "/webapi/wxop/mpCallBack",
                    WxConsts.OAuth2Scope.SNSAPI_USERINFO, oauthState);
            urlRes.setUrl(url);
            return ResultJson.ok().data(urlRes);
        } else {
            return ResultJson.ok().data(new AppUrlRes());
        }
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "公众号移动端-构建静默授权openid链接", notes = "构造静默授权链接")
    @RequestMapping(value = "/getMpAuthSlientUrl", method = RequestMethod.GET)
    public ResultJson<AppUrlRes> silentauth(@ApiParam(value = "公众号APPID", required = true) @RequestParam("mpappid") String mpappid,
                                            @ApiParam(value = "返回原路径", required = true) @RequestParam("returnurl") String returnurl) {
        WxMpService wxMpService = WxMpConfiguration.getMpService(mpappid);
        VendorConfigCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig mpconfig = cache.getVendorConfigByAppId(mpappid);
        String projectId = mpconfig != null ? mpconfig.getProjectid() : "";
        String oauthState = SysFuncLibTool.generateOauthStaticStateData(mpappid, returnurl, projectId, VendorType.WX_MP.toString());
        if (wxMpService != null) {
            WxOAuth2Service oAuth2Service = wxMpService.getOAuth2Service();
            AppUrlRes urlRes = new AppUrlRes();
            String url = oAuth2Service.buildAuthorizationUrl(cwconfig.getDomain() + "/webapi/wxop/mpslientCallBack",
                    WxConsts.OAuth2Scope.SNSAPI_BASE, oauthState);
            urlRes.setUrl(url);
            return ResultJson.ok().data(urlRes);
        } else {
            return ResultJson.ok().data(new AppUrlRes());
        }
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "公众号移动端-获取ticket", notes = "获取jsapiticket")
    @RequestMapping(value = "/jsapiticket", method = RequestMethod.GET)
    public ResultJson<WxJsapiSignature> getticket(@ApiParam(value = "公众号APPID", required = true) @RequestParam("mpappid") String mpappid,
                                                  @RequestParam("signurl") String signurl) throws Exception {
        WxMpService wxMpService = WxMpConfiguration.getMpService(mpappid);

        VendorConfigCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig mpconfig = cache.getVendorConfigByAppId(mpappid);
        if (wxMpService != null && mpconfig != null) {
//            signurl=mpconfig.getUrl()+signurl;
//            signurl= URLDecoder.decode(signurl,"UTF-8");

            signurl = SysFuncLibTool.decodeAesContent(signurl, "");//仅仅是测试.不涉及projectid

            WxJsapiSignature wxJsapiSignature = wxMpService.createJsapiSignature(signurl);

            return ResultJson.ok().data(wxJsapiSignature);
        }
        return ResultJson.failure(ResultCode.BAD_REQUEST);

    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "公众号移动端-获取openid", notes = "获取openid")
    @RequestMapping(value = "/getopenid", method = RequestMethod.GET)
    public ResultJson<Common_response> getOpenid(
            @ApiParam(value = "公众号APPID", required = true) @RequestParam("mpappid") String mpappid,
            @ApiParam(value = "微信回调code", required = true) @RequestParam("code") String code) throws Exception {
        WxMpService wxMpService = WxMpConfiguration.getMpService(mpappid);

        WxOAuth2AccessToken wxOAuth2AccessToken = wxMpService.getOAuth2Service().getAccessToken(code);

        String openid = wxOAuth2AccessToken.getOpenId();


        return ResultJson.ok().data(openid);

    }


    @ApiIgnore
    @RequestMapping(value = "/mpCallBack", method = RequestMethod.GET)
    public WxLoginRes callback(HttpServletRequest request, HttpServletResponse response) throws Exception {
        loginService.wxMpOauth(request, response);
        return null;
    }

    //    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
//    @ApiOperation(value = "公众号移动端-静默获取openid响应回调", notes = "静默获取openid响应回调")
    @ApiIgnore
    @RequestMapping(value = "/mpslientCallBack", method = RequestMethod.GET)
    public WxLoginRes silentcallback(HttpServletRequest request, HttpServletResponse response) throws Exception {
        loginService.wxMpOpenidSlientOauth(request, response);
        return null;
    }

    /**
     * @param appid
     * @param signature
     * @param timestamp
     * @param nonce
     * @param echostr
     * @param request
     * @return
     * @throws WxPayException
     */
    @ApiIgnore
    @RequestMapping(value = "/validate/{appid}", method = RequestMethod.GET)
    public String wxopenvalidate(@PathVariable String appid, String signature, String timestamp, String nonce, String echostr, HttpServletRequest request) throws WxPayException {
//        final WxScanPayNotifyResult result = this.wxService.parseScanPayNotifyResult(xmlData);
        WxMpService wxMpService = WxMpConfiguration.getMpService(appid);
        if (wxMpService.checkSignature(timestamp, nonce, signature)) {
            return echostr;
        }
        Logger logger = LoggerFactory.getLogger(this.getClass());
        logger.info("MP接收微信公众号验证:{},{},{},{},{}", appid, signature, timestamp, nonce, echostr);
        return echostr;
    }


    /**
     * 给微信公众号做校验用的方法
     *
     * @param request
     * @return
     */
    @ApiIgnore
    @RequestMapping(value = "/MP_verify_*.txt", method = RequestMethod.GET)
    public String wxPrivateKey2(HttpServletRequest request) {
        String result = SysFuncLibTool.getSubUtilSimple(request.getRequestURI(), "MP_verify_(.*?).txt");
        return result;
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "公众号移动端-构建支付宝授权链接", notes = "构造支付宝授权链接")
    @RequestMapping(value = "/getAliAuthUrl", method = RequestMethod.GET)
    public ResultJson<AppUrlRes> aliauth(@ApiParam(value = "支付宝APPID", required = true) @RequestParam("aliappid") String aliappid,
                                         @ApiParam(value = "返回原路径", required = true) @RequestParam("returnurl") String returnurl) {

        VendorConfigCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig aliconfig = cache.getVendorConfigByAppId(aliappid);
        String projectId = aliconfig != null ? aliconfig.getProjectid() : "";
        String oauthState = SysFuncLibTool.generateOauthRandomStateData(aliappid, returnurl, projectId, VendorType.ALI_PAY.toString());
        if (aliconfig != null) {
            AppUrlRes urlRes = new AppUrlRes();
            String url = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id={}&state={}&scope=auth_user" +
                    "&redirect_uri={}"; //scope=auth_base 静默授权 ,auth_user 弹窗询问用户信息
            url = StrUtil.format(url, aliappid, oauthState, URLUtil.encode(returnurl));
            urlRes.setUrl(url);
            return ResultJson.ok().data(urlRes);
        } else {
            return ResultJson.ok().data(new AppUrlRes());
        }
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "PC端-支付宝授权回调", notes = "构造支付宝授权链接")
    @RequestMapping(value = "/aliCallBack", method = RequestMethod.GET)
    public WxLoginRes aliCallback(HttpServletRequest request, HttpServletResponse response) throws Exception {
        loginService.aliMpOauth(request, response);
        return null;
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "移动端-支付宝解析用户信息自动注册并登陆", notes = "支付宝解析用户信息自动注册并登陆")
    @RequestMapping(value = "/aliAuthLogin", method = RequestMethod.POST)
    public WxLoginRes aliAuthLogin(@Valid @RequestBody OauthLoginCommonReq req) throws Exception {
        WxLoginRes loginRes = oauthService.aliH5UserInfoAuthLogin(req);
        return loginRes;
    }


}
