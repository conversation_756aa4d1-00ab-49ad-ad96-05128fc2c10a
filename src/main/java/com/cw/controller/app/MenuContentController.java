package com.cw.controller.app;

import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.*;
import com.cw.pojo.dto.app.res.*;
import com.cw.pojo.dto.app.res.node.AppParkItem;
import com.cw.pojo.dto.app.res.node.AppParksiteDetItem;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Query_Req;
import com.cw.pojo.entity.Pagetemplate_Entity;
import com.cw.service.config.cms.MapService;
import com.cw.service.config.cms.MenuService;
import com.cw.service.config.template.PagetemplateService;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/26 18:17
 **/
@Api(tags = SwaggerUtil.MainIndex.APP, position = SwaggerUtil.MainIndex.APP_INDEX)
@RestController
@RequestMapping(value = "/webapi/content", method = RequestMethod.POST)
public class MenuContentController {

    @Autowired
    MenuService menuService;

    @Autowired
    MapService mapService;

    @Autowired
    PagetemplateService pagetemplateService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.GETMENU)
    @ApiOperation(value = "内容拉取 - 拉取页面&卡片", notes = "拉取指定页面内栏目卡片")
    @RequestMapping(value = "/getPageMenus")
    public ResultJson<List<MenuRes>> getPagesMenus(@RequestBody MenuQueryReq req) {
        List<MenuRes> menus = menuService.fetchList(req);
        return ResultJson.ok().data(menus);

    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.GETMENUCONTENT)
    @ApiOperation(value = "内容拉取 - 拉取栏目内所有内容", notes = "拉取栏目内容")
    @RequestMapping(value = "/getCardContents")
    public ResultJson<List<MenuContentRes>> getCardContent(@RequestBody MenuContentQueryReq req) {
        List<MenuContentRes> menus = menuService.fetchContentList(req);
        return ResultJson.ok().data(menus);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.GETTABBAR)
    @ApiOperation(value = "内容拉取 - 拉取主题配色与相关配置", notes = "拉取底部tabbar")
    @RequestMapping(value = "/getTabbar")
    public ResultJson<AppThemeRes> getTabbar(@RequestBody AppThemeQueryReq req) {

        AppThemeRes res = menuService.getAppTheme(req);
        return ResultJson.ok().data(res);

    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.GETMYACCOUNT)
    @ApiOperation(value = "内容拉取 - 拉取个人中心功能", notes = "获取个人中心内容与按钮栏")
    @RequestMapping(value = "/getMyaccountFunc")
    public ResultJson<List<MyAccountQueryRes>> getPagesMenus(@RequestBody MyAccountFuncQueryReq req) {
        List<MyAccountQueryRes> menus = menuService.getMyAppFunc(req);
        return ResultJson.ok().data(menus);
    }

//    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.GETMYACCOUNT)
//    @ApiOperation(value = "内容拉取 - 拉取预订须知", notes = "获取个人中心内容与按钮栏")
//    @RequestMapping(value = "/getOrderNotice")
//    public ResultJson<MenuContentEntity> getOrderNotice(@RequestBody AppOrderNoticeReq req) {
//        MenuContentEntity content = menuService.getOrderNotice(req);
//        return ResultJson.ok().data(content);
//    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.GETMYACCOUNT)
    @ApiOperation(value = "内容拉取 - 拉取指定栏目下列表", notes = "拉取指定栏目列表-通常用在二级栏目下景点介绍")
    @RequestMapping(value = "/getContentList")
    public ResultJson<MenuContentListRes> getContentList(@RequestBody MenuContentQueryReq queryReq) {
//        MenuContentEntity content = menuService.getOrderNotice(req);
        MenuContentListRes contentLIst = menuService.getContentList(queryReq);
        return ResultJson.ok().data(contentLIst);
    }


    @ApiOperation(value = "内容拉取 - 拉取指定栏目指定内容", notes = "通常用在公告.新闻展示")
    @RequestMapping(value = "/loadContent")
    public ResultJson<MenuContentRes> loadMenuContent(@RequestBody AppMenuContentLoadReq req) {
        MenuContentRes content = menuService.appLoadMenuContent(req);
        return ResultJson.ok().data(content);
    }

    @ApiOperation(value = "内容拉取 - 拉取栏目第一条内容", notes = "拉取栏目第一条内容.通常用在首页拉取隐私协议.用户协议富文本")
    @RequestMapping(value = "/loadMenuFirstContent")
    public ResultJson<MenuContentRes> loadMenuFirstContent(@RequestBody MenuContentQueryReq req) {
        MenuContentRes content = menuService.getMenuFirstContent(req);
        return ResultJson.ok().data(content);
    }


    @ApiOperation(value = "地图发现-拉取地图初始化内容", notes = "发现地图页首页内容")
    @RequestMapping(value = "/loadParkMap")
    public ResultJson<List<AppParkItem>> loadParkMap(@RequestBody Common_Query_Req req) {
        List<AppParkItem> items = mapService.fetchMapInitData();
        return ResultJson.ok().data(items);
    }

    @ApiOperation(value = "地图发现-拉取园区指定业态点位详情", notes = "拉取园区指定业态点位详情")
    @RequestMapping(value = "/getParkbussdet")
    public ResultJson<List<AppParksiteDetItem>> loadParkBussDet(@RequestBody AppMapSiteSearchReq req) {
        List<AppParksiteDetItem> items = mapService.fetchParkBussniessDetail(req);
        return ResultJson.ok().data(items);
    }


    @ApiOperation(value = "地图发现-坐标分类页", notes = "坐标分类页")
    @RequestMapping(value = "/getParksiteList")
    public ResultJson<List<AppParksiteListRes>> getParksiteList(@RequestBody AppMapSiteListSearchReq req) {
        List<AppParksiteListRes> items = mapService.getParksiteList(req);
        return ResultJson.ok().data(items);
    }


    @ApiOperation(value = "地图发现-获取坐标详情页", notes = "获取坐标详情页")
    @RequestMapping(value = "/loadParksite")
    public ResultJson<AppParksiteDetItem> loadParksite(@RequestBody AppMapSiteLoadReq req) {
        AppParksiteDetItem appParksiteDetItem = mapService.loadParksiteByCode(req);
        return ResultJson.ok().data(appParksiteDetItem);
    }

    @ApiOperation(value = "模板拉取 - 拉取模板", notes = "拉取模板")
    @RequestMapping(value = "/loadTemplate")
    public ResultJson<Pagetemplate_Entity> loadTemplate(@RequestBody Common_Load_Req req) {
        Pagetemplate_Entity template = pagetemplateService.loadPagetemplateByCode(req.getUniqueid(), WebAppGlobalContext.getCurrentAppProjectId());
        return ResultJson.ok().data(template);
    }


}
