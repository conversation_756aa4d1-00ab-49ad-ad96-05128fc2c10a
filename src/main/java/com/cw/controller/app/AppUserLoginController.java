package com.cw.controller.app;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.login.*;
import com.cw.pojo.dto.app.res.SmsLoginRes;
import com.cw.pojo.dto.app.res.WxLoginRes;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.service.app.AppUserService;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Api(tags = SwaggerUtil.MainIndex.APPREGLOGIN, position = SwaggerUtil.MainIndex.APPUSER_INDEX)
@RestController
@RequestMapping(value = "/webapi/user", method = RequestMethod.POST)
public class AppUserLoginController {

    @Autowired
    AppUserService loginService;

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "小程序-微信小程序用户登录", notes = "微信小程序登陆")
    @RequestMapping(value = "/wxlogin")
    public ResultJson<WxLoginRes> login(@Valid @RequestBody WxAppLoginReq req) {
        WxLoginRes wxLoginRes = loginService.wxAppLogin(req);
        return ResultJson.ok().data(wxLoginRes);
    }

    /**
     * @param req
     * @return
     */
    @Deprecated
    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "小程序- 微信用户头像昵称信息授权注册", notes = "如果已经注册.会更新用户信息")
    @RequestMapping(value = "/saveWxUserInfo")
    public ResultJson<WxLoginRes> updUserInfoAndLogin(@Valid @RequestBody WxNickNameAvatarRegReq req) {
        WxLoginRes wxLoginRes = loginService.wxRegByUserInfo(req);
        return ResultJson.ok().data(wxLoginRes);
    }

   /* @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "小程序- 微信用户OPENID注册", notes = "如果该小程序不要求手机号注册,则使用该接口注册")
    @RequestMapping(value = "/saveWxUserId")
    public ResultJson<WxLoginRes> updUserWxId(@Valid @RequestBody WxEncryptReq req) {
        WxLoginRes wxLoginRes = loginService.wxRegByWxId(req);
        return ResultJson.ok().data(wxLoginRes);
    }*/


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "小程序- 微信用户手机号授权注册", notes = "如果已经注册.会更新用户信息")
    @RequestMapping(value = "/saveWxUserPhone")
    public ResultJson<WxLoginRes> updUserPhone(@Valid @RequestBody WxEncryptReq req) {
        WxLoginRes wxLoginRes = loginService.wxRegByPhone(req);
        return ResultJson.ok().data(wxLoginRes);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端-微信扫码登陆", notes = "H5微信扫码登陆")
    @RequestMapping(value = "/qrlogin")
    public ResultJson<WxLoginRes> qrlogin(@Valid @RequestBody QrLoginReq req) throws DefinedException {
        WxLoginRes appUserInfo = loginService.h5QrLogin(req);
        return ResultJson.ok().data(appUserInfo);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-短信验证登陆", notes = "短信验证登陆")
    @RequestMapping(value = "/smsLogin")
    public ResultJson<WxLoginRes> smslogin(@Valid @RequestBody SmsLoginReq req) throws DefinedException {
        WxLoginRes appUserInfo = loginService.smsLogin(req);
        return ResultJson.ok().data(appUserInfo);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-短信登陆.未注册自动注册", notes = "短信登陆.未注册自动注册")
    @RequestMapping(value = "/smsUserLogin", method = RequestMethod.POST)
    public ResultJson<WxLoginRes> autoLogin(@Valid @RequestBody RegisterReq req) throws DefinedException {
        WxLoginRes appUserInfo = loginService.registerLogin(req);
        return ResultJson.ok().data(appUserInfo);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-发送短信验证码", notes = "发送短信验证码")
    @RequestMapping(value = "/sendLoginSmsCode")
    public ResultJson<SmsLoginRes> sendLoginSmsCode(@Valid @RequestBody SmsSendReq req) throws DefinedException {
        SmsLoginRes res = loginService.sendSmsCode(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-密码登陆", notes = "密码登陆")
    @RequestMapping(value = "/pwdLogin", method = RequestMethod.POST)
    public ResultJson<WxLoginRes> pwdLogin(@Valid @RequestBody PasswordLoginReq req) throws DefinedException {
        WxLoginRes appUserInfo = loginService.pwdLogin(req);
        return ResultJson.ok().data(appUserInfo);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-用户注册", notes = "用户注册")
    @RequestMapping(value = "/h5Reg", method = RequestMethod.POST)
    public ResultJson<WxLoginRes> register(@Valid @RequestBody RegisterReq req) throws DefinedException {
        WxLoginRes appUserInfo = loginService.register(req);
        return ResultJson.ok().data(appUserInfo);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-找回密码", notes = "用户注册")
    @RequestMapping(value = "/findPwd", method = RequestMethod.POST)
    public ResultJson<Common_response> findpwd(@Valid @RequestBody FindPwdReq req) throws DefinedException {
        loginService.findPwd(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-校验短信验证码", notes = "校验用户输入验证码")
    @RequestMapping(value = "/checkcode", method = RequestMethod.POST)
    public ResultJson<Common_response> checkValidCode(@RequestBody QrcodeValidReq req) throws DefinedException {
        loginService.checkValidCode(req);
        return ResultJson.ok().data(new Common_response());
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "PC端-微信开放平台扫码登陆跳转", notes = "获取PC端微信登陆二维码")
    @RequestMapping(value = "/h5WxQrcode", method = RequestMethod.POST)
    public ResultJson<WxLoginRes> h5WxQrcode(@Valid @RequestBody RegisterReq req) throws DefinedException {
        WxLoginRes appUserInfo = loginService.register(req);
        return ResultJson.ok().data(appUserInfo);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.Login.LOGIN_INDEX)
    @ApiOperation(value = "PC端-微信登陆跳转", notes = "PC端微信授权跳转")
    @RequestMapping(value = "/wxQrcallback", method = RequestMethod.GET)
    public ResultJson<WxLoginRes> wxQrcallback(HttpServletRequest request, HttpServletResponse response) throws DefinedException {

        loginService.wxOauth(request, response);

        return ResultJson.ok().data(null);
    }


}
