package com.cw.controller.app;

import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.*;
import com.cw.pojo.dto.app.req.login.PasswordLoginReq;
import com.cw.pojo.dto.app.req.login.ResetMobileReq;
import com.cw.pojo.dto.app.res.*;
import com.cw.pojo.dto.common.req.Common_Del_Req;
import com.cw.pojo.dto.common.req.Common_Query_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.conf.res.coupon.AppCouponVoucherRes;
import com.cw.pojo.dto.conf.res.coupon.AppUsercouonGetRes;
import com.cw.pojo.dto.conf.res.oss.OSSUploadFileRes;
import com.cw.service.app.AppActService;
import com.cw.service.app.AppPayService;
import com.cw.service.app.AppUserActionService;
import com.cw.service.app.AppUserService;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.service.order.ContestService;
import com.cw.service.order.GiftOrderService;
import com.cw.utils.SwaggerUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = SwaggerUtil.MainIndex.APPUSERACTION, position = SwaggerUtil.MainIndex.APPUSER_INDEX)
@RestController
@RequestMapping(value = "/webapi/userAction", method = RequestMethod.POST)
public class AppUserActionController {

    @Autowired
    AppUserActionService userActionService; //订单操作

    @Autowired
    AppPayService userPayService; //外部交互

    @Autowired
    AppUserService userService;

    @Autowired
    AppActService appActService;//活动预约

    @Autowired
    ContestService contestService;//演出活动

    @Autowired
    GiftOrderService giftOrderService;


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-购物车内容更新指定项目", notes = "更新购物车内容")
    @RequestMapping(value = "/upditem")
    public ResultJson<Common_response> updItem(@Valid @RequestBody AppUpdShopItemReq req) {
        Integer num = userActionService.updItem(req);
        Common_response common_response = new Common_response();
        common_response.setNum(num);
        return ResultJson.ok().data(common_response);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-购物车内容删除", notes = "删除购物车内容")
    @RequestMapping(value = "/delitem")
    public ResultJson<Common_response> delItem(@Valid @RequestBody AppDelShopItemReq req) {
        userActionService.delItem(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-购物车列表", notes = "加载购物车列表")
    @RequestMapping(value = "/itemList")
    public ResultJson<List<AppShopItemListNode>> getItemList() {
        List<AppShopItemListNode> list = userActionService.itemList();
        return ResultJson.ok().data(list);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-购物车类型分组列表", notes = "加载购物车类型分组列表")
    @RequestMapping(value = "/itemgroupList")
    public ResultJson<List<AppShopItemGroupNode>> getItemGroupList() {
        List<AppShopItemGroupNode> list = userActionService.itemGroupList();
        return ResultJson.ok().data(list);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单创建", notes = "创建订单")
    @RequestMapping(value = "/createOrder")
    public ResultJson<AppSingleOrderResult> createOrder(@Valid @RequestBody AppOrderReq req) throws DefinedException {
        AppSingleOrderResult result = userActionService.createOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单创建-购物车批量提交", notes = "创建购物车订单")
    @RequestMapping(value = "/items2Order")
    public ResultJson<AppBatchItem2OrderResult> itemBatchOrder(@Valid @RequestBody AppBatchItem2OrderReq req) throws DefinedException {
        AppBatchItem2OrderResult result = userActionService.itemBatchOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单取消", notes = "取消订单")
    @RequestMapping(value = "/cancelOrder")
    public ResultJson<Common_response> cancelOrder(@Valid @RequestBody AppWxCancelOrderReq req) throws DefinedException {
        userActionService.cancelOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    //@ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    //@ApiOperation(value = "PC和移动端-删除订单", notes = "删除订单(隐藏订单前端不可见)")
    //@RequestMapping(value = "/hideOrder")
    //public ResultJson<Common_response> hideOrder(@Valid @RequestBody AppOrderOpReq req) throws DefinedException {
    //    userActionService.hideOrder(req);
    //    return ResultJson.ok().data(new Common_response());
    //}

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-伴手礼订单申请售后", notes = "伴手礼订单申请售后")
    @RequestMapping(value = "/GiftOrderAMS")
    public ResultJson<Common_response> GiftOrderAMS(@Valid @RequestBody AppWxCancelGiftOrderReq req) throws DefinedException {
        userActionService.GiftOrderAMS(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取伴手礼可退款金额", notes = "获取伴手礼可退款金额")
    @RequestMapping(value = "/getGiftOrderCanRefund")
    public ResultJson<AppGiftOrderCanRefund> getGiftOrderCanRefund(@Valid @RequestBody AppWxGiftOrderRefundReq req) throws DefinedException {
        AppGiftOrderCanRefund canRefund = userActionService.getGiftOrderRefund(req);
        return ResultJson.ok().data(canRefund);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单支付接口查询", notes = "订单是否支付接口查询")
    @RequestMapping(value = "/queryPayStaus")
    public ResultJson<AppOrderStatusResult> queryOrderPay(@RequestBody AppOrderOpReq req) throws DefinedException {
        AppOrderStatusResult appOrderStatusResult = userActionService.queryOrderPayStatus(req);
        return ResultJson.ok().data(appOrderStatusResult);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单详情查看", notes = "订单详情查看")
    @RequestMapping(value = "/loadOrder")
    public ResultJson<AppOrderDetailResult> loadOrder(@Valid @RequestBody AppOrderOpReq req) throws DefinedException {
        AppOrderDetailResult result = userActionService.loadOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单取消预检查", notes = "订单取消预检查")
    @RequestMapping(value = "/validateCancel")
    public ResultJson<AppCancelValidateRes> validateCancel(@Valid @RequestBody AppOrderOpReq req) throws DefinedException {
        AppCancelValidateRes result = userActionService.cancelValidate(req);
        return ResultJson.ok().data(result);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单详情批量查看", notes = "订单详情批量查看")
    @RequestMapping(value = "/batchLoadOrder")
    public ResultJson<AppBatchLoadOrderRes> batchLoadOrder(@Valid @RequestBody AppBatchLoadOrderReq req) throws DefinedException {
        AppBatchLoadOrderRes result = userActionService.batchLoadOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单完结评分", notes = "订单评分")
    @RequestMapping(value = "/commentOrder")
    public ResultJson<Common_response> commonOrder(@Valid @RequestBody AppCommentOpReq req) throws DefinedException {
        userActionService.commentOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-意见反馈", notes = "意见反馈")
    @RequestMapping(value = "/advice")
    public ResultJson<Common_response> adviceOrder(@Valid @RequestBody AppAdviceOpReq req) throws DefinedException {
        Common_response response = userActionService.createAdvice(req);
        return ResultJson.ok().data(response);
    }

//    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
//    @ApiOperation(value = "小程序-微信支付发起", notes = "请求微信支付")
//    @RequestMapping(value = "/ wxPay")
//    public ResultJson<AppWxJsApiPayRes> wxpay(@Valid @RequestBody AppWxJsApiPayReq req) throws DefinedException {
//        AppWxJsApiPayRes wxJsApiPayRes = userPayService.createWxJsApiOrder(req);
//        return ResultJson.ok().data(wxJsApiPayRes);
//
//    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单可卖校验", notes = "产品库存价格校验")
    @RequestMapping(value = "/validateOrder")
    public ResultJson<Common_response> validateOrder(@Valid @RequestBody AppOrderReq req) {
        userActionService.validateOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "移动端-微信支付-JSAPI小程序公众号", notes = " 小程序微信 JSAPI发起支付")
    @RequestMapping(value = "/wxJsApiPayOrder")
    public ResultJson<AppWxJsApiPayRes> wxpayOrder(@Valid @RequestBody AppOnlinePayReq req) throws DefinedException {
        AppWxJsApiPayRes result = userPayService.createWxJsApiOrder(req);//.payOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = " PC端-微信支付-二维码", notes = "PC端扫码支付")
    @RequestMapping(value = "/wxNativePayOrder")
    public ResultJson<AppQrcodePayRes> wxNativePay(@Valid @RequestBody AppOnlinePayReq req) throws DefinedException {
        AppQrcodePayRes result = userPayService.createWxNativeOrder(req);//.payOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = " 移动端-微信支付-H5支付", notes = "移动端H5支付")
    @RequestMapping(value = "/wxH5PayOrder")
    public ResultJson<AppH5payRes> wxH5Pay(@Valid @RequestBody AppOnlinePayReq req) throws DefinedException {
        AppH5payRes result = userPayService.createWxH5Order(req);//.payOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = " PC端-PC 端请求支付宝二维码支付", notes = "PC端支付宝扫码支付")
    @RequestMapping(value = "/aliQrPayOrder")
    public ResultJson<AppQrcodePayRes> alQrPay(@Valid @RequestBody AppOnlinePayReq req) throws DefinedException {
        String qrcode = userPayService.createAliQrcodePayOrder(req);//.payOrder(req);
        AppQrcodePayRes result = new AppQrcodePayRes();
        result.setQrcode(qrcode);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = " 移动端-移动端H5支付宝支付", notes = "移动端唤醒支付宝支付")
    @RequestMapping(value = "/aliWapPayOrder")
    public ResultJson<AppH5payRes> aliWapPay(@Valid @RequestBody AppOnlinePayReq req, HttpServletRequest request, HttpServletResponse response) throws DefinedException {
        String form = userPayService.createAliH5PayOrder(req);//.payOrder(req);

//        try {
//            response.setContentType("text/html;charset=UTF_8");
//            response.getWriter().write(form);//直接将完整的表单html输出到页面
//            response.getWriter().flush();
//            response.getWriter().close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }


        AppH5payRes result = new AppH5payRes();
        result.setPayform(form);
//        result.setQrcode(qrcode);
        return ResultJson.ok().data(result);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单列表", notes = "订单列表")
    @RequestMapping(value = "/orderList")
    public ResultJson<AppUserOrderListRes> getOrderList(@Valid @RequestBody AppOrderQueryListReq req) {
        AppUserOrderListRes res = userActionService.getMyOrderList(req);
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-会务预约", notes = "创建会议需求")
    @RequestMapping(value = "/createMeetingOrder")
    public ResultJson<Common_response> createMeetingOrder(@Valid @RequestBody AppMeetingOrderReq req) throws DefinedException {
        userActionService.createMeetingOrder(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-个人信息更新", notes = "个人信息更新")
    @RequestMapping(value = "/updUserInfo")
    public ResultJson<Common_response> updUserInfo(@Valid @RequestBody AppUpdUserInfoReq req) {
        userService.updUserInfo(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取个人信息", notes = "个人中心获取个人信息")
    @RequestMapping(value = "/getUserInfo")
    public ResultJson<AppUserInfo> getUserInfo(@RequestBody Common_Query_Req req) {
        AppUserInfo info = userService.loadUserInfo();
        return ResultJson.ok().data(info);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-刷新TOKEN", notes = "延长TOKEN有效期")
    @RequestMapping(value = "/freshToken")
    public ResultJson<Common_response> refreshToken(@RequestBody Common_Query_Req req) {
        //空方法. 仅仅为了刷新用户 token 有效期
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-个人密码修改", notes = "个人中心个人密码修改")
    @RequestMapping(value = "/chPwd")
    public ResultJson<Common_response> chPwd(@RequestBody PasswordLoginReq req) {
        userService.updPwd(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-修改绑定手机", notes = "个人中心手机换绑")
    @RequestMapping(value = "/chMobileNo")
    public ResultJson<Common_response> chPwd(@RequestBody ResetMobileReq req) throws DefinedException {
        userService.updMobileNo(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-个人登陆状态登出", notes = "退出登陆")
    @RequestMapping(value = "/logOut")
    public ResultJson<Common_response> logOut() {
        userService.logOut();
        return ResultJson.ok().data(new Common_response());
    }


//    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
//    @ApiOperation(value = "小程序-用户密码修改", notes = "用户密码修改")
//    @RequestMapping(value = "/chPwd")
//    public ResultJson<Common_response> changePwd() {
//        return ResultJson.ok().data(new Common_response());
//    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端和移动端-意见反馈上传OSS图片音频", notes = "意见反馈上传OSS图片音频")
    @RequestMapping(value = "/upload_oss")
    public ResultJson<OSSUploadFileRes> upload_oss(@RequestPart MultipartFile file, String dirName) {
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        OSSUploadFileRes res = userActionService.uploadFile(projectid, file, dirName);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端和移动端-上传OSS头像图片", notes = "上传OSS头像图片")
    @RequestMapping(value = "/upload_oss_pfp")
    public ResultJson<OSSUploadFileRes> upload_oss_pfp(@RequestPart MultipartFile file) {
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        OSSUploadFileRes res = userActionService.uploadOSSProfilePicture(projectid, file);
        return ResultJson.ok().data(res);
    }



    @ApiOperation(value = "上传OSS图片需要加密，一个签名标识一个文件上传", notes = "上传OSS图片需要加密")
    @RequestMapping(value = "/public/upload_oss_open")
    public ResultJson<OSSUploadFileRes> newUpload_oss(
            @RequestPart MultipartFile file,
            String sign,
            long timestamp,
            String nonce,
            String hotelId) {
        if (StringUtils.isBlank(hotelId)) {
            return ResultJson.ok();
        }
        OSSUploadFileRes res = userActionService.uploadOSSProfilePictureOpen(hotelId, file,sign,timestamp, nonce);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC端和移动端-获取意见反馈", notes = "获取意见反馈")
    @RequestMapping(value = "/getFeedback")
    public ResultJson<AppFeedbackRes> getFeedback(@RequestBody AppFeedbackReq req) throws DefinedException {
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();
        AppFeedbackRes res = userActionService.getFeedback(req.getRegno(), projectid);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-活动预约订单创建", notes = "预约订单创建")
    @RequestMapping(value = "/createAct")
    public ResultJson<AppSingleOrderResult> createActOrder(@Valid @RequestBody AppCreateActReq req) throws DefinedException {
        AppSingleOrderResult result = appActService.createActOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-活动预约订单取消", notes = "预约订单取消")
    @RequestMapping(value = "/cancelAct")
    public ResultJson<Common_response> cancelAct(@Valid @RequestBody AppWxCancelOrderReq req) throws DefinedException {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        String bookingId = req.getOrderid();
        appActService.cancelOrder(bookingId, projectId, WebAppGlobalContext.getCurrentAppUserId(), req.getReason());
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-活动预约订单支付查询", notes = "预约订单支付查询")
    @RequestMapping(value = "/queryActPay")
    public ResultJson<AppOrderStatusResult> queryOrderPayStatus(@Valid @RequestBody AppOrderOpReq req) throws DefinedException {
        AppOrderStatusResult result = appActService.queryOrderPayStatus(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-加载活动预约订单", notes = "加载活动预约订单")
    @RequestMapping(value = "/loadActOrder")
    public ResultJson<AppActDetailResult> loadActOrder(@Valid @RequestBody AppOrderOpReq req) throws DefinedException {
        AppActDetailResult result = appActService.loadActOrder(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-个人活动预约订单列表", notes = "个人活动预约订单列表")
    @RequestMapping(value = "/queryActlist")
    public ResultJson<AppUserActListRes> getMyActList(@Valid @RequestBody AppOrderQueryListReq req) throws DefinedException {
        AppUserActListRes result = appActService.getMyOrderList(req);
        return ResultJson.ok().data(result);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-伴手礼确定收货", notes = "伴手礼确定收货")
    @RequestMapping(value = "/confrimReceived")
    public ResultJson confrimReceived(@Valid @RequestBody AppOrderOpReq req) throws DefinedException {
        giftOrderService.receivedGift(req.getOrderid(), WebAppGlobalContext.getCurrentAppProjectId());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-填写退货物流信息", notes = "填写退货物流信息")
    @RequestMapping(value = "/savePostageInfo")
    public ResultJson savePostageInfo(@Valid @RequestBody AppOrderPostageReq req) throws DefinedException {
        giftOrderService.writePostageInfo(req.getOrderid(), req.getCompany(), req.getDeliveryNo(), WebAppGlobalContext.getCurrentAppProjectId(), WebAppGlobalContext.getCurrentAppUserId());
        return ResultJson.ok();
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-更新收货地址信息", notes = "更新收货地址")
    @RequestMapping(value = "/updaddr")
    public ResultJson<Common_response> updAddr(@Valid @RequestBody AppUpdAddrReq req) {
//        Integer num = userActionService.updItem(req);
//        Common_response common_response = new Common_response();
//        common_response.setNum(num);
        Long id = userActionService.updAddr(req);
        Common_response response = new Common_response();
        response.setId(id);
        return ResultJson.ok().data(response);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-收货地址信息删除", notes = "删除收货地址")
    @RequestMapping(value = "/deladdr")
    public ResultJson<Common_response> delAddr(@Valid @RequestBody Common_Del_Req req) {
//        userActionService.delItem(req);
        userActionService.delAddr(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取收货地址列表", notes = "加载收货地址")
    @RequestMapping(value = "/addrList")
    public ResultJson<List<AppAddrItemListNode>> getAddrList() {
//        List<AppShopItemListNode> list = userActionService.itemList();
        List<AppAddrItemListNode> nodes = userActionService.getAddrList();
        return ResultJson.ok().data(nodes);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取默认收货地址", notes = "获取默认收货地址")
    @RequestMapping(value = "/getDefAddr")
    public ResultJson<AppAddrItemListNode> getDfAddr() {
//        List<AppShopItemListNode> list = userActionService.itemList();
        AppAddrItemListNode node = userActionService.getActiveAddrNode();
        return ResultJson.ok().data(node);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-更新发票抬头信息", notes = "更新发票抬头信息")
    @RequestMapping(value = "/updInvoiceTitle")
    public ResultJson<Common_response> updInvoiceTitle(@Valid @RequestBody AppUpdInvoiceTitleReq req) {
        Long id = userActionService.updInvoiceTitle(req);
        Common_response response = new Common_response();
        response.setId(id);
        return ResultJson.ok().data(response);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-发票抬头信息删除", notes = "发票抬头信息删除")
    @RequestMapping(value = "/delInvoiceTitle")
    public ResultJson<Common_response> delInvoiceTitle(@Valid @RequestBody Common_Del_Req req) {
        userActionService.delInvoiceTitle(req);
        return ResultJson.ok().data(new Common_response());
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取用户发票抬头信息列表", notes = "加载用户发票抬头信息列表")
    @RequestMapping(value = "/InvoiceTitleList")
    public ResultJson<List<AppInvoiceTitleListNode>> getInvoiceTitleList() {
        List<AppInvoiceTitleListNode> nodes = userActionService.getInvoiceTitleList();
        return ResultJson.ok().data(nodes);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取默认发票抬头", notes = "获取获取默认发票抬头")
    @RequestMapping(value = "/getDefInvoiceTitle")
    public ResultJson<AppInvoiceTitleListNode> getDfTitle() {
        AppInvoiceTitleListNode node = userActionService.getActiveInvoiceTitleNode();
        return ResultJson.ok().data(node);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-订单发票查询", notes = "订单发票查询（没有数据则开票）")
    @RequestMapping(value = "/invoiceQuery")
    public ResultJson<AppOrderInvoiceResult> invoiceQuery(@Valid @RequestBody AppOrderOpReq req) throws DefinedException {
        AppOrderInvoiceResult result = userActionService.invoiceQuery(req);
        return ResultJson.ok().data(result);

    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-发票开具申请", notes = "发票开具申请")
    @RequestMapping(value = "/invoiceCreate")
    public ResultJson invoiceCreate(@Valid @RequestBody AppInvoiceCreateReq req) throws DefinedException {
        userActionService.invoiceCreate(req);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-发票重新推送", notes = "发票重新推送")
    @RequestMapping(value = "/invoicePush")
    public ResultJson invoicePush(@Valid @RequestBody AppInvoicePushReq req) throws DefinedException {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        userActionService.invoicePush(req, projectId);
        return ResultJson.ok();
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-待申请发票订单列表", notes = "待申请发票订单列表")
    @RequestMapping(value = "/unInvoiceOrderList")
    public ResultJson<AppUserInvoiceOrderListRes> invoiceList() throws DefinedException {
        AppUserInvoiceOrderListRes res = userActionService.unInvoiceOrderListQuery();
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-已申请发票订单列表订单记录", notes = "已申请发票订单列表订单记录")
    @RequestMapping(value = "/invoiceOrderList")
    public ResultJson<AppUserInvoiceOrderListRes> invoiceOrderList() throws DefinedException {
        AppUserInvoiceOrderListRes res = userActionService.invoiceOrderListQuery();
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  个人中心优惠券列表", notes = "优惠券列表")
    @RequestMapping(value = "/usercouponList", method = RequestMethod.POST)
    public ResultJson<List<AppUsercouponItemListNode>> getUserCouponList() {
        List<AppUsercouponItemListNode> res = userActionService.getUserCouponList();
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-  个人中心获取优惠券详情", notes = "获取优惠券详情")
    @RequestMapping(value = "/usercoupon", method = RequestMethod.POST)
    public ResultJson<AppUsercouponRes> getUserCoupon(@Valid @RequestBody AppUsercouponQueryReq req) {
        AppUsercouponRes res = userActionService.getUserCoupon(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-领取优惠券", notes = "领取优惠券")
    @RequestMapping(value = "/getCoupon")
    public ResultJson<AppUsercouonGetRes> getCoupon(@RequestBody AppUsercouponReq req) throws DefinedException {
        AppUsercouonGetRes res = userActionService.getCoupon(req);
        return ResultJson.ok().data(res);
    }

    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-获取线下优惠券凭证", notes = "获取线下优惠券凭证")
    @RequestMapping(value = "/getCouponVoucher")
    public ResultJson<AppCouponVoucherRes> getCouponVoucher() throws DefinedException {
        AppCouponVoucherRes res = userActionService.getCouponVoucher();
        return ResultJson.ok().data(res);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-比赛投稿", notes = "比赛投稿")
    @RequestMapping(value = "/create_contestEntry")
    public ResultJson<AppContestEntryCreateRes> create_contestEntry(@Valid @RequestBody AppContestEntryCreateReq req) throws DefinedException {
        AppContestEntryCreateRes result = contestService.createContestEntry(req);
        return ResultJson.ok().data(result);
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-查询是否可以上传视频比赛投稿,返回0-图片投稿，返回1-视频投稿", notes = "查询是否可以上传视频比赛投稿")
    @RequestMapping(value = "/canUpload_contestEntry")
    public ResultJson<String> canUpload_contestEntry(@Valid @RequestBody AppCanUploadContestReq req) throws DefinedException {
        return ResultJson.ok().data(contestService.queryUserCanUpLoad(req.getContestid()));
    }


    @ApiOperationSupport(order = SwaggerUtil.SubIndex.App.WXLOGIN_INDEX)
    @ApiOperation(value = "PC和移动端-比赛作品投票", notes = "比赛作品投票")
    @RequestMapping(value = "/vote_contestEntry")
    public ResultJson vote_contestEntry(@Valid @RequestBody AppContestEntryReq req) throws DefinedException {
        contestService.vote_contestEntry(req);
        return ResultJson.ok();
    }


    //todo 授权OSS上传
}
