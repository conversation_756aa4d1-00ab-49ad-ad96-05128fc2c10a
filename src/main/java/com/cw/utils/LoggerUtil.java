package com.cw.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoggerUtil {

    public static final String pkgpreifix = "com.cw";

    public static final String injectprefix = "com.cw.inject.ExceptionLogInjecter";

    public static Logger getLogger(LoggerType loggerType) {
        return LoggerFactory.getLogger(loggerType.name());
    }

    public static String getExceptionDesc(Exception e, Boolean detail) {
//        String result = e.getClass() + ":" + e.getMessage();
        String result = e.getMessage();
        if (detail) {   //只显示关键业务信息
            result = result + "\n";
            StringBuilder stringBuilder = new StringBuilder(result);
            StackTraceElement[] traceElements = e.getStackTrace();
            if (traceElements.length > 0) {
                for (StackTraceElement traceElement : traceElements) {
                    if (traceElement.getClassName().startsWith(pkgpreifix) && !traceElement.getClassName().startsWith(injectprefix) && traceElement.getLineNumber() > 0) {
                        String fileline = traceElement.getFileName() + ":" + traceElement.getLineNumber();
//                        result = result + traceElement.getClassName() + "." + traceElement.getMethodName() + "(" + fileline + ")" + "\n";
                        stringBuilder.append(traceElement.getClassName() + "." + traceElement.getMethodName() + "(" + fileline + ")" + "\n");
                    }
                }
            }
            return stringBuilder.toString();
        }
        return result;
    }


    public enum LoggerType {
        sys, nalog, resendlog, pmslog, otalog, invoicelog, xmslog
    }
}
