package com.cw.utils;

import cn.hutool.core.date.DateUtil;
import com.cw.config.exception.CustomException;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.menus.MenuPlatformTypeEnums;

import java.time.LocalDateTime;
import java.util.Date;

public class SystemUtil {
    public static final String DEFAULTRATECODE = "DEF";
    public static final String CURRENTUSER = "CURRENTUSER";
    public static final String DEFAULTUSERID = "SYS";
    public static final String PUBLIC_ROOMSKU = "RROOM_PMS"; //默认的资源名
    public static final String OSS_MATERIAL = "material"; //素材
    public static final String OSS_MATERIAL_AUDIO = "video"; //素材音频
    public static final String OSS_MATERIAL_VIDEO = "audio"; //素材视频
    public static final String DEFAULTGIFTITEMCODE = "G000001";//默认伴手礼商品代码 自增
    public static final String RESERVE_ADMINUSER = "supervisor";
    public static final String RESERVE_ROLE = "supervisor";
    public static final String DEFAULT_SPLITCHAR = "_";
    public static final String KEYSPLITSIGNAL = ":";
    public static final String DEFAULT_LOCALTIME = "1900-01-01 00:00:00";
    public static final Integer DEFAULT_NA_MOVE_DAY = 45; //夜审迁移订单指定提前天数时间
    public static final Integer DEFAULT_NA_RESOURCE_DAY = 30; //夜审产品资源自动延长天数
    public static final Integer DEFAULT_NA_CONTESTENTRY_DAY = 730; //夜审投稿作品按照2年后删除时间
    public static final Date EMPTY_DATETIME = DateUtil.parse("1900-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
    ;
    public static final LocalDateTime EMPTY_LOCALTIME = LocalDateTime.of(1900, 1, 1, 0, 0, 0);//LocalTime默认值
    public static final String DEFAULT_CHANNELID = "FS";  //默认库存来源渠道

    public static final String projectClumn = "projectid";
    public static final String RULECANCELFREE = "可免费取消";

    public static final String SPLITSIGNAL = "$$$";

    public static final String PARAMSIGNAL = "|";

    public static final Integer invoiceUpdNum = 2;


    public class ScheduleOp {
        public final static int SIMPLETRIGER = 0; //更新普通间隔线程
        public final static int CRONTRIGER = 1;//更新指定时间的线程
        public final static int TRIGERNOW = 2;//马上触发一次定时任务
        public final static int DELETETRIGER = 999;//删除定时线程任务

    }

    /**
     * 判断是否属于线下支付订单
     *
     * @param value
     * @return
     */
    public static boolean isOfflineMainStatus(String value) {
        StatusUtil.PassrsStatusEnum[] values = StatusUtil.PassrsStatusEnum.values();
        for (StatusUtil.PassrsStatusEnum enums : values) {
            if (enums.getCode().equals(value)) {
                return true;
            }
        }
        return false;
    }

    public class ColRefreshType {
        public final static String ALL = "A"; //全部
        public final static String ROOM = "R";
        public final static String TICKET = "T";
        public final static String CANYIN = "C";
        public final static String BOAT = "V";
        public final static String PREPAY = "P";
        public final static String DEFINED = "D";
    }


    public enum UserLogType {//用户日志类型
        //GTICKET("团购票"),//团购票
        //TICKET("散客票"),//散客票
        //PREPAY("预付款"),//预付款
        //COL("主单"),//综合预定
        //GRID("房表"), //房表
        //CATERING("餐饮"), //餐饮
        //BLOCK("锁房"), //锁房
        //ROOMRS("客房订单"),//客房订单
        //MEETING("会议订单"),//会议订单
        //PROFILE("档案"),//档案
        //SUBATTR("渠道属性");//渠道属性

        HOTEL("酒店"),
        ROOMTYPE("房型"),
        TICKET("票务"),
        PRODUCTKIT("套餐"),
        MEETING("会务"),
        ACTGROUP("预约"),
        PERFORM("演出节目"),
        SPU("景区产品"),
        GIFT("伴手礼"),
        MAP("地图"),
        COUPON("优惠券"),
        TRAVELTIP("路线规划"),
        SHOPSITE("站点管理"),
        MENUS("菜单栏目"),
        MENU_CONTENT("卡片内容"),
        FACTOR("资源"),
        FEEDBACK("意见反馈"),
        ROBOT("消息机器人"),
        OPROLE("角色"),
        OPUSER("用户"),
        RULE("规则"),
        INVOICE("发票"),
        COL("订单"),
        ACTCOL("预订订单"),
        PREPAY("预付款"),
        NA("夜审设置"),
        TEMPLATE("模板"),
        WXTEMPLATE("微信订阅模板"),
        REFUND("退款申请"),
        SYSTEMCONFIG("系统设置");


        private String desc;

        UserLogType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum FactoryType {//自定义数据类型
        MEETTYPE("活动类型"), TICKETTYPE("票务分组"), TICKETBATCH("票型时段"), HOTELTYPE("酒店分类"), KITTHEME("套餐主题"),
        HOTELKITTYPE("酒店配套分类"), HOTELKIT("酒店配套"), RMKITTYPE("房型配套类别"), RMKIT("房型配套"), MGKIT("会场设施"), MEETKIT("会议室设施"), PARKSITEKIT("站点服务"),
        USETIPS("产品核销提示"),
        //标签
        KITTAGS("套餐标签"), COMMONTAGS("通用标签"), HOTELTAGS("酒店标签"), TICKETTAGS("票务标签"), RMTAGS("房型标签"), MEETINGGROUPTAGS("会场标签"), TVLTAGS("路线标签"),
        RULES("业务规则"),
        //原因
        CANCELORDERREASON("取消订单原因"), CLOSEREASON("关闭购买原因"), REFUNDREASON("退款原因"), UNRECREASON("未收货退款原因"),
        SOURCEONE("一级资源"), SOURCETWO("二级资源"), MEETINGPEOPLE("会议人数"), MEETINGBUDGET("会议预算"), MEETINGDEMAND("会议需求"),
        INVOICETYPE("发票类型"), SCORINGCRITERIA("评分标准"), LUGGEXPTAG("行李异常标签"),
        PAYMENT("支付方式"), WARESTYPE("产品分组"), PROVINCE("省份"), POSTAGETYPE("邮费类型"),
        MAPBUSCLASS("地图业态标注分类"), DEPARTMENT("宾客意见分类"),FEEDBACKAREA("反馈区域"),
        ;

        private String desc;

        FactoryType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 资源上级分类类型
     */
    public enum SourceType {
        HOTELTYPE("酒店分类"), HOTELKIT("酒店设施"), RMKIT("房型设施"), MGKIT("会场设施"), MEETKIT("会议室设施"), PARKSITEKIT("站点服务"), TICKETTYPE("票务分组"), WARESTYPE("产品分组"), KITTHEME("套餐主题"),
        MEETTYPE("活动类型"), MEETINGPEOPLE("会议人数"), MEETINGBUDGET("会议预算"), MEETINGDEMAND("会议需求"),
        INVOICETYPE("发票类型"), SCORINGCRITERIA("评分标准"), TICKETBATCH("票型时段"), USETIPS("产品核销提示"),
        //标签
        HOTELTAGS("酒店标签"), TICKETTAGS("票务标签"), RMTAGS("房型标签"), KITTAGS("套餐标签"), MEETINGGROUPTAGS("会场标签"), TVLTAGS("路线标签"), POSTAGETYPE("邮费类型"), LUGGEXPTAG("行李异常标签"),
        //取消订单原因，退款原因，关闭购买原因
        CANCELORDERREASON("取消订单原因"), CLOSEREASON("关闭购买原因"), REFUNDREASON("退款原因"), UNRECREASON("未收货退款原因"),
        //地图业态
        MAPBUSCLASS("地图业态标注分类"), DEPARTMENT("宾客意见分类"),FEEDBACKAREA("反馈区域"),
        //CANYIN("餐饮地图标注分类"), CHANGGUAN("场馆地图标注分类"), CHENGLANDIAN("乘览点地图标注分类"), CHURUKOU("出入口地图标注分类"), GONGJIAOZHAN("公交站地图标注分类"), HUICHANG("会场地图标注分类"),
        //JIJIUDIAN("急救点地图标注分类"), JINGDIAN("景点地图标注分类"), SHANGCHANG("商场地图标注分类"), TINGCHECHANG("停车场地图标注分类"), WEISHENGJIAN("卫生间地图标注分类"), YANCHU("演出地图标注分类"),
        //YOUKEZHONGXIN("游客中心地图标注分类"), ZHUSU("住宿地图标注分类"), YULE("娱乐地图标注分类"),
        ;
        private String desc;

        SourceType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 栏目上级分类
     */
    public enum ColumnType {
        TABBAR("标签栏"), TOPCOLUMN("顶级栏目");
        private String desc;

        ColumnType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }


    public static String transFactoryTypeName(String type) {
        SystemUtil.FactoryType[] values = SystemUtil.FactoryType.values();
        String typeName = null;
        for (SystemUtil.FactoryType factoryType : values) {
            if (factoryType.name().equalsIgnoreCase(type)) {
                typeName = factoryType.name();
            }
        }
        if (typeName == null) {
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("查询类型不存在"));
        }
        return typeName;

    }


    public enum SequenceKey {//订单序列号类型
        BOOKINGID, SUBORDER, PREPAY
    }

    public enum GlobalDataType {//放到内存中存的数据表
        ALL,
        //渠道
        BLOCK, MENUS("栏目"), MENUCONTENT("栏目内容"),
        //基础设置
        TICKET("票型"), TICKETGROUP("票型大类"), RATECODE("价格类别"), ROOMTYPE("房型"), FACTOR, RULES("销售规则"), RULESPOLICY("销售策略"),
        PRODUCTKIT("套餐"), KITITEM("套餐明细"), KITGROUP("套餐大类"), RESTAURANT("餐厅"), POLICY, MEETING, MEETINGGROUP,
        CONTENTVALUE("内容编号"), SPUGROUP("产品大类"), SPUSITEM("产品小类"), GIFT("伴手礼"), GIFTITEM("伴手礼商品"),
        ACTGROUP("预约项目"), ACTSITE("预约站点"), ACTPERIOD("预约时间段"), ACTQR("预约二维码大组"), OSSDIR("素材目录"), POSTAGE("邮费配置"),
        SPUQR("景区产品组合"), PARK("园区"), PARKBUSS("园区业态"), PARKSITE("园区业态点位"), TRAVELTIP("路线规划"), SHOPSITE("站点"),
        //优惠券
        COUPONGROUP("优惠券大类"), COUPON("优惠券"), USERCOUPON("用户优惠券"),
        //演出活动
        PERFORM("演出节目"),
        //投稿
        CONTESTENTRY("投稿作品"),
        //配置
        SYSCONF, TEMPLATE("短信模板"), WXTEMPLATE("微信订阅模板"), NOTIFYROBOT("微信,钉钉通知机器人"), OPTIONSWITCH("开关列表"), PRODGROUP, VENDORCONFIG("接口配置"), WEBCONF("网站页脚"),
        //用户
        USER_ROLE, USER, OP_ROLE_RIGHT,
        HOTEL("酒店楼号"),
        SALES, FEEDBACK,
        RLANG("翻译资源"),
        CONTRACT, TRANS, VISIT, ADDITION, TAG_GROUP, TAG, SUB_RATECODE("PMS 房价码"), NA_PARAM;

        private String desc = "";

        GlobalDataType(String... desc) {
            if (desc != null && desc.length > 0) {
                this.desc = desc[0];
            }
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum MsgType {//消息类型
        G, T, M, Z, R, V//G-团购票 T-散客票 M-主订单 Z-自定义 R-退款 V-验证码
    }

    public enum TemplateTrigger {//模板触发器
        NEW, MODIFY, CANCEL, MANUAL, PRINT, OTA, PAY
    }

//    public class ProductType {
//        public final static String ROOM = "R";
//        public final static String TICKET = "T";
//        public final static String CANYIN = "C";
//        public final static String MEETING = "M";
//        public final static String Other = "O";
//    }

    public enum UserLogOpType {//用户日志操作类型
        NEW,//新建
        MODIFY,//编辑
        CANCEL,//取消
        DELETE,//删除
        SYNC,//同步
        PAY,//付款
        REFUND,//退款
    }

    public enum QueryAccountType {//查询账单类型
        ALL, PAY, CONSUME
    }

    public class TicketType {//票务类型
        public final static String DAYTICKET = "D";//日票
        public final static String NIGHTTICKET = "N";//夜票
        public final static String DISCOUNTTICKET = "Z";//折扣票
    }

    public class SystemerrorCode {
        public final static int ERR999_SYSERR = 999;
        public final static int PMSERR = 998;
        public final static int PARAM_ERR = 900;
        public final static int HTTP_BADGATEWAY_502 = 9502;//502异常就抛这个
        public final static int NOTSUPPORT = 401;

        public final static int RETRY_ORDERERR = 800; //重试下单 定义8 开头的异常为可重试异常代码

        public final static int ERR001_OVERBOOKROOM = 101; //客房超预定
        public final static int ERR002_DUPLICATEID = 102;//重复订单号
        public final static int ERR003_OVERBOOKTICKET = 103;//门票超预定
        public final static int ERR004_OVERBOOKCATER = 104;//餐饮超预定
        public final static int ERR005_IDNOTFOUND = 105;//订单不存在
        public final static int ERR006_PRICENOTMATCH = 106;//OTA订单价格错误
        public final static int ERR007_OVERBOOKPRODUCTKIT = 107;//套餐可卖资源不足
        public final static int ERR008_DUPLICATEPAYID = 108;//重复的网络支付单号
        public final static int ERR009_CANCELFAIL = 109;//订单不可取消
        public final static int ERR010_AUDITING = 110;//操作审核中
        public final static int ERR011_CANCELOVERTIME = 111;//超过最晚取消时间
        public final static int ERR012_DISCOUNTOVERROOMS = 112;//房价冲减超过最大可订房间数
        public final static int ERR013_PAYFAIL = 113;//支付失败
        public final static int ERR014_AROVER = 114;//超过消费限额
        public final static int ERR015_FORMERR = 115;//表单输入失败
        public final static int ERR016_NOTREG = 116;//未注册用户访问
    }

    public enum PayType {//付款类型
        PAY,//付款
        REFUND,//退款
        DEBIT//扣款
    }

    public class TicketQueryResultCode {
        public final static String SUCCESS = "0";//查询成功
        public final static String ORDER_NOT_FOUND = "1";//订单不存在
        public final static String INTERFACE_CLOSE = "2";//接口关闭
        public final static String QUERY_FAIL = "3";//查询失败
    }


    /**
     * 判断是否属于商品类型
     *
     * @param value
     * @return
     */
    public static boolean isProdType(String value) {
        ProdType[] values = ProdType.values();
        for (ProdType enums : values) {
            if (enums.val().equals(value)) {
                return true;
            }
        }
        return false;
    }

    public enum CustomDataKey {//下拉框键值
        subchannel("渠道"), roomtype("房型"), ratecode("价格大组"), payment("付款方式"), ticket("门票"), ticketbatch("票型时段"), ticketgroup("门票大组"),
        province("省份"), city, idtype("证件类型"), country("国家"), mlevel("会员等级"), kitgroup("套餐大组"), productkit("套餐"), kititem("套餐明细"),
        restaurant("餐厅"), restaurant_time("餐厅时间段"), userrole("用户角色"), hotel("酒店"), prodgroup("产品分组"), prodtages("标签内容"),
        rules("业务规则"), rulespolicy("规则策略"), ruletype("业务规则类型"), meeting("会议室"), meetinggroup("会议大组"), category("资源上级分类"), column("栏目上级分类"),
        sourceone("一级资源"), sourcetwo("二级资源"), menus("所有栏目"), menusone("一级栏目"), menustwo("二级栏目"), menusthree("三级栏目"), menucontent("栏目内容"),
        comptype("栏目模板"), pccomptype("pc栏目模板"), menulevel("栏目等级"), contentseq("内容排序"),
        appointtype("预约类型"),
        template("模板"), wxtemplate("微信订阅模板"), userlogtype("操作日志类型"), naparam("夜审参数"), warestype("产品分组"),
        //原因
        closereason("关闭购买原因"), cancelorderreason("取消订单原因"), refundreason("退款原因"), unrecreason("未收货退款原因"),
        //产品类型
        ptype("商品类型"), olptype("线下产品类型"),
        //酒店搜索下拉框
        hotelsort("酒店排序"), bedtype("房间床型"),
        //查询状态
        passrsstatus("线下订单状态"), mainstatus("主订单状态"), mstatus("会场预约状态"), invoicestatus("发票状态"), prepaystatus("付款状态"), refundstatus("退款状态"), auditingstatus("审核状态"),
        actstatus("预约状态"), sendstatus("发货状态"),
        //产品
        shopitem("站点"),
        spugroup("产品大类"), spusitem("产品小类"), spuqr("产品组合"), spustype("产品核销类型"), gift("伴手礼类目"), giftitem("伴手礼商品"),
        //预约活动
        actgroup("预约项目"), actsite("预约场所"), actperiod("预约时段"), actqr("预约二维码大组"),
        //同步类型下拉框以及配置
        syncdata("同步数据"), syncresource("同步资源库存"), vendortype("厂商类型"),
        //产品库存和价格
        ticketdetail("门票价格&库存"), roomtypedetail("房型价格&库存"), productkitdetail("套餐价格&库存"), actsitedetail("预约场所价格&库存"), kitprice("套餐价格"), kitaval("套餐库存"),
        //机器人通知
        robotgroup("机器人组"),
        //短信
        msgtrigger("短信触发方式"), template_field("短信触发参数"),
        //oss
        ossroot("素材一级分组"), ossformat("文件格式"),
        //邮费类型
        postagetype("邮费类型"), expresscompany("快递公司"),
        //站点
        luggageexp("行李异常"),
        //地图园区
        mapbusinesstype("园区业态类型"), park("园区"), mapbusclass("地图业态标注分类"), parksite("地图标注"), tvltags("路线标签"), traveltip("攻略"),
        //一级资源类型
        hoteltype("酒店分类"), hotelkit("酒店设施"), rmkit("房型设施"), mgkit("会场设施"), meetkit("会议室设施"), tickettype("票务分组"), kittheme("套餐主题"),
        meettype("活动类型"), meetingpeople("会议人数"), meetingbudget("会议预算"), meetingdemand("会议需求"),
        invoicetype("发票类型"), scoringcriteria("评分标准"), meetingstyle("会场布置风格"),
        kittags("套餐标签"), commontags("通用标签"), hoteltags("酒店标签"), tickettags("票务标签"),
        rmtags("房型标签"), meetinggrouptags("会场标签"), department("部门分类"), usetips("产品核销提示"),
        feedbackarea("反馈区域");

        private String desc = "";

        CustomDataKey(String... desc) {
            if (desc != null && desc.length > 0) {
                this.desc = desc[0];
            }
        }

        public String getDesc() {
            return desc;
        }

        public static CustomDataKey getCustomDataKey(String name) {
            SystemUtil.CustomDataKey[] values = SystemUtil.CustomDataKey.values();
            for (SystemUtil.CustomDataKey customDataKey : values) {
                if (customDataKey.name().equalsIgnoreCase(name)) {
                    return customDataKey;
                }
            }
            return CustomDataKey.userlogtype;

        }

    }

    /**
     * 判断是否属于平台类型
     *
     * @param value
     * @return
     */
    public static boolean isMenuPlatformType(String value) {
        MenuPlatformTypeEnums[] values = MenuPlatformTypeEnums.values();
        for (MenuPlatformTypeEnums enums : values) {
            if (enums.getCode().equals(value)) {
                return true;
            }
        }
        return false;
    }

    public static enum StatisticType {
        all, new_user, new_res, new_amount, occupancy, passenger_flow, room_price, revenues_statistic,
        channel_res, channel_price, res_top10, price_top10, revenues_compare
    }

    public static enum StatisticType2 {
        total, room, ticket, productkit
    }


}
