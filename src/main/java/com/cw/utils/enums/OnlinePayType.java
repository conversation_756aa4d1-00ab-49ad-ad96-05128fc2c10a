package com.cw.utils.enums;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/7 15:49
 **/
public enum OnlinePayType {
    WX("微信支付", VendorType.WX_PAY),
    TB("支付宝", VendorType.ALI_PAY),
    YL("银联", VendorType.YL_PAY);

    private String desc;
    private VendorType vendorType;

    OnlinePayType(String desc, VendorType type) {
        this.desc = desc;
        this.vendorType = type;
    }

    public static OnlinePayType getPayType(String value) {
        OnlinePayType[] payTypes = OnlinePayType.values();
        for (OnlinePayType payType : payTypes) {
            if (value.equals(payType.name())) {
                return payType;
            }
        }
        return OnlinePayType.WX;
    }

    public VendorType getVendorType() {
        return vendorType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
