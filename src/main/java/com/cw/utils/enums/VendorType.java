package com.cw.utils.enums;

/**
 * 厂商适配容器表
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:22
 **/
public enum VendorType {
    LOCAL(" 本地", true, VendorGroup.defualt),
    ZJPLPATFORM("中景中台", true, VendorGroup.col),
    CITYBAYPLPATFORM("数景中台", true, VendorGroup.none),
    XMSPMS("西软XMS", true, VendorGroup.none),
    CAMBRIGEPMS("康桥Cambrige", true, VendorGroup.none),
    ZJ_TICKET("中景门票接口", false, VendorGroup.ticket),
    SD_TICKET("深大门票系统", false, VendorGroup.ticket),
    MINI_TICKET("微票门票系统", false, VendorGroup.ticket),
    LOCAL_TICKET("本系统发码", false, VendorGroup.ticket),
    GALAXY_TICKET("银科环企门票系统", false, VendorGroup.ticket),
    PFT_TICKET("票付通票务系统", false, VendorGroup.ticket),


    XMS_POS("西软餐饮POS", false, VendorGroup.none),
    CITYBAY_POS("数景餐饮POS", false, VendorGroup.none),
    DSPARKING("数景停车场接口", false, VendorGroup.none),


    ALI_SMS("阿里云短信", false, VendorGroup.sms),
    GB_SMS("北京移动短信", false, VendorGroup.sms),
    ZJ_SMS("中景短信接口", false, VendorGroup.sms),
    GST_SMS("高斯通短信接口", false, VendorGroup.sms),

    SIMP_SUP("信普飞科客服系统", false, VendorGroup.support),

    GZ_INVOICE("贵州电子发票云系统", false, VendorGroup.invoice),
    NN_INVOICE("诺诺发票系统", false, VendorGroup.invoice),
    JL_INVOICE("嘉利发票系统", false, VendorGroup.invoice),
    LOCAL_INVOICE("本地开票", false, VendorGroup.invoice),

    ZJ_CRM("中景CRM", false, VendorGroup.crm),
    ZHIJIAN_CRM("智简CRM", false, VendorGroup.crm),

    GLAB_LIFE("glab美好人生", false, VendorGroup.app),
    PC_H5("PC端官网", false, VendorGroup.app),
    M_H5("移动端H5官网", false, VendorGroup.app),
    WX_MP("微信公众号", false, VendorGroup.app),
    WX_APP("微信小程序", false, VendorGroup.app),
    WX_PAY("微信支付", false, VendorGroup.pay),
    WX_OPEN("微信开放平台", false, VendorGroup.app),
    ALI_PAY("支付宝支付", false, VendorGroup.pay),
    YL_PAY("银联支付", false, VendorGroup.pay);

    private String desc; //描述
    private Boolean lbeforePay = false;  // 是支付前发送即刻确认.还是支付完成后发送订单
    private String group;

    /**
     * @param desc       厂商描述
     * @param lbeforePay 支付前确认订单还是支付后才能确认订单
     */
    VendorType(String desc, Boolean lbeforePay, String group) {
        this.desc = desc;
        this.lbeforePay = lbeforePay;
        this.group = group;
    }


    public Boolean getLbeforePay() {
        return lbeforePay;
    }

    public String getDesc() {
        return desc;
    }

    public String getGroup() {
        return group;
    }


//    public List<VendorType> getProdVendor(ProdType prodType) {
//        switch (prodType){
//            case ROOM:
//                return Arrays.asList(values());
//            case TICKET:
//                return List.of(SD_TICKET, GALAXY_TICKET);
//            case MAIN:
//                return List.of(LOCAL, ZJPLPATFORM, CITYBAYPLPATFORM);
//            default:
//                break;
//        }
//    }
}
