package com.cw.utils.enums.menus;

public enum MobileMenuCardType {
    //    tiitle,subtitle,imgurl,pcimgurl,videourl,fileurl,clickurl,richtext
    //Banner("移动端轮播图", "750*375", "subtitle,pcimgurl,fileurl,richtext,iconfont,audiourl"),
    Verticalbanner("移动端竖形轮播图", "750*422", "subtitle,pcimgurl,fileurl,richtext,iconfont,audiourl"),
    Marquee("滚动公告", "", "pcimgurl,fileurl,iconfont,audiourl"),
    Searchbar("移动端客房预订搜索栏", "iconfont,audiourl,audiourl", ""),
    Btns("功能入口-按钮组", "", "subtitle,imgurl,pcimgurl,videourl,fileurl,richtext,audiourl"),
    TextImg("单图", "686*386", "pcimgurl,videourl,fileurl,richtext,iconfont,audiourl"),
    Entrance("功能入口-1+2图片入口", "336*328", "pcimgurl,videourl,fileurl,richtext,iconfont,iconfont,audiourl"),
    HorizontalTextImg("横向滑动图文", "320*180", "pcimgurl,videourl,fileurl,richtext,iconfont,audiourl"),
    VerticalFourTextImg("竖向图文(4个一组)", "331*248", "pcimgurl,videourl,fileurl,richtext,iconfont,audiourl"),
    BookingNote("预订须知", "", "imgurl,pcimgurl,videourl,fileurl,clickurl,audiourl"),
    PopupImg("弹窗图片", "1106*622", "pcimgurl,videourl,fileurl,richtext,iconfont,audiourl"),
    VersonInfo("版权制作信息", "1106*622", "pcimgurl,videourl,fileurl,richtext,iconfont,audiourl"),
    Waterfall("瀑布信息流", "336*328", "pcimgurl,videourl,fileurl,richtext,iconfont,iconfont,audiourl"),
    TwoPicfall("两图一行信息流", "336*328", "pcimgurl,videourl,fileurl,richtext,iconfont,iconfont,audiourl"),
    DefaultContent("默认内容", "750*600", "");//备胎..默认都显示

//    1 轮播图  Banner (加载调用内容接口.获取图片,跳转链接)
//2 搜索    Searchbar
//3 功能按钮  Btns (加载调用内容接口.渲染按钮文字,跳转链接)
//4 图文  TextImg
//5 横向滑动图文  HorizontalTextImg
//6 竖向图文（4个一组）    VerticalFourTextImg


    private String desc; //描述
    private String picsize;//推荐图标尺寸
    private String hiddeColumn;//隐藏列 对应API 中的返回字段 MenuContentRes中的内容 /api/menus/loadContent

    MobileMenuCardType(String desc, String picsize, String hiddeColumn) {
        this.desc = desc;
        this.picsize = picsize;
        this.hiddeColumn = hiddeColumn;
    }

    public String getDesc() {
        return desc;
    }

    public String getPicsize() {
        return picsize;
    }

    public String getHiddeColumn() {
        return hiddeColumn;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

//    @ApiModelProperty(value = "菜单id")
//    private String menuid; //菜单 ID  目前没有用.使用唯一的权限 ID 做绑定 留在这.后面需要区分时可以用
//    @ApiModelProperty(value = "标题")
//    private String title;/* 标题 */
//    @ApiModelProperty(value = "副标题")
//    private String subtitle;/* 副标题 */
//    @ApiModelProperty(value = "移动端图片 url ")
//    private String imgurl;/* 移动端图片 url */
//    @ApiModelProperty(value = "PC 端图片 url ")
//    private String pcimgurl;/* PC端图片 url */
//    @ApiModelProperty(value = "视频文件 url ")
//    private String videourl;/*  视频文件 url */
//    @ApiModelProperty(value = "附件 url ")
//    private String fileurl;/* 附件上传url */
//    @ApiModelProperty(value = "跳转链接 ")
//    private String clickurl;/* 附件上传url */
//    @ApiModelProperty(value = "富文本介绍")
//    private String richtext;/* 富文本内容 */
}
