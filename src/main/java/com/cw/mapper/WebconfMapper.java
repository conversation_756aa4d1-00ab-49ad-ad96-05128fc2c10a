package com.cw.mapper;

import com.cw.entity.Webconf;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-02-22
 */
public interface WebconfMapper extends JpaRepository<Webconf, Long>, JpaSpecificationExecutor<Webconf> {

    Webconf findFirstByProjectid(String projectId);

    Webconf findBySqlid(Long sqlid);
}
