package com.cw.mapper;

import com.cw.entity.Rratedet;
import com.cw.pojo.sqlresult.Produce_calcpricePo;
import com.cw.pojo.sqlresult.Produce_ratesqueryPo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface RratedetMapper extends JpaRepository<Rratedet, Long>, JpaSpecificationExecutor<Rratedet> {


    @Query(value = "select new com.cw.pojo.sqlresult.Produce_calcpricePo(r.datum,r.rate) " +
            "from Rratedet  r where r.projectid=?1 and r.code=?2  " +
            "and r.productcode=?3 and r.datum between  ?4 and ?5")
    List<Produce_calcpricePo> calcPrice(String projectId, String ratecode,
                                        String productcode, Date querystart, Date queryend);


    @Query(value = "select new com.cw.pojo.sqlresult.Produce_ratesqueryPo(r.productcode,r.rate) " +
            "from Rratedet  r where r.projectid=?1 and r.code=?2  " +
            "and r.productcode in ?3 and r.datum  = ?4 ")
    List<Produce_ratesqueryPo> rateQuery(String projectId, String ratecode,
                                         List<String> productcodes, Date useDate);

    @Query(value = "select * from Rratedet  where projectid=?1 and `code`=?2 and productcode=?3 and datum between ?4 and ?5", nativeQuery = true)
    List<Rratedet> queryRmtypeRateList(String projectid, String ratecode, String productcode, Date start, Date end);

    @Query(value = "select MAX(datum) from Rratedet  where projectid=?1", nativeQuery = true)
    Date findRratedetMaxDate(String projectid);


    @Query(value = "SELECT * from Rratedet WHERE productcode=?1 and projectid=?2 ORDER BY datum desc LIMIT 1", nativeQuery = true)
    Rratedet findRratedetByLastDatum(String productcode, String projectid);
}
