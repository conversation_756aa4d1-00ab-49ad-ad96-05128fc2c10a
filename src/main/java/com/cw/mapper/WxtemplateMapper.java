package com.cw.mapper;

import com.cw.entity.Wxtemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface WxtemplateMapper extends JpaRepository<Wxtemplate, Long>, JpaSpecificationExecutor<Wxtemplate> {

    @Query(value = "select * from Wxtemplate where `status`=?1", nativeQuery = true)
    List<Wxtemplate> findAllByStatus(Boolean status);


    @Query(value = "select count(sqlid) from Wxtemplate where `code`=?1 or `trigger`=?2   and projectid=?3", nativeQuery = true)
    long countByCodeAndTrigger(String code, String trigger, String projectid);


    @Query(value = "select count(sqlid) from Wxtemplate where  `trigger`=?1 and  projectid=?2 and `status`=true", nativeQuery = true)
    long countByTrigger(String trigger, String projectid);
}
