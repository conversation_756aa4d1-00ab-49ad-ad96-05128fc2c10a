package com.cw.mapper;

import com.cw.entity.Rrooms;
import com.cw.pojo.sqlresult.Produce_avlPo;
import com.cw.pojo.sqlresult.Produce_minAvlPo;
import com.cw.pojo.sqlresult.Product_maxDatePo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface RroomsMapper extends JpaRepository<Rrooms, Long>, JpaSpecificationExecutor<Rrooms> {

    @Transactional
    @Modifying
    @Query(value = "update Rrooms set pickup=pickup+?1,avl=avl-?1 where rmtype=?2 and datum between ?3 and ?4 and projectid=?5 ", nativeQuery = true)
    void updateRroomsPickup(Integer pickup, String roomtype, Date start, Date end, String projectId);

    @Query(value = "select new com.cw.pojo.sqlresult.Produce_avlPo(datum, avl) from Rrooms " +
            "where datum between ?1 and ?2  and rmtype=?3 and projectid=?4 order by datum")
    List<Produce_avlPo> queryAvailNum(Date startdate, Date enddate, String product, String projectid);


    @Query(value = "select new com.cw.pojo.sqlresult.Produce_minAvlPo(rmtype, min(avl)) from Rrooms " +
            "where datum between ?1 and ?2  and rmtype in ?3 and projectid=?4 group by rmtype having min(avl)>=?5 ")
    List<Produce_minAvlPo> queryMinAvlNum(Date startdate, Date enddate, List<String> prods, String projectid, Integer requireNum);

    @Query(value = "select * from Rrooms  where projectid=?1  and rmtype=?2 and datum between ?3 and ?4", nativeQuery = true)
    List<Rrooms> findRmtypeAvlList(String projectId, String roomtype, Date startDate, Date endDate);

    @Query(value = "select MAX(datum) from Rrooms  where projectid=?1", nativeQuery = true)
    Date findRmtypeAvlMaxDate(String projectid);

    @Query(value = "select new com.cw.pojo.sqlresult.Product_maxDatePo(rmtype,MAX(datum)) from Rrooms" +
            "  where projectid=?1 group by rmtype order by MAX(datum)")
    List<Product_maxDatePo> getRroomsMaxDate(String projectid);
}
