package com.cw.mapper;

import com.cw.entity.Spuratedet;
import com.cw.pojo.sqlresult.Produce_calcpricePo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-02-21
 */
public interface SpuratedetMapper extends JpaRepository<Spuratedet, Long>, JpaSpecificationExecutor<Spuratedet> {

    @Query(value = "select new com.cw.pojo.sqlresult.Produce_calcpricePo(r.datum,r.rate) " +
            "from Spuratedet  r where r.projectid=?1 and r.code=?2  " +
            "and r.productcode=?3 and r.datum between  ?4 and ?5")
    List<Produce_calcpricePo> calcPrice(String projectId, String ratecode,
                                        String productcode, Date querystart, Date queryend);

    @Query(value = "select MAX(datum) from Spuratedet  where projectid=?1", nativeQuery = true)
    Date findSpuratedetMaxDate(String projectid);
}
