package com.cw.mapper;

import com.cw.entity.Room_rs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public interface RoomrsMapper extends JpaRepository<Room_rs, Long>, JpaSpecificationExecutor<Room_rs> {

    List<Room_rs> findRoom_rsByBookingidAndProjectid(String bookingid, String projectId);

    @Query("select count(*) from Room_rs  where rmtype = ?1 and projectid=?2 and deptdate>=?3")
    long countRoom_rsByRmtype(String rmtype, String projectId, Date depDate);


    @Query(value = "select rmtype,count(sqlid) as nums from Room_rs where date_format(createdate,'%Y-%m-%d')=curdate() and canceldate='1900-01-01' and rmtype<>'' and rmtype not like 'P%' group by hotelcode order by nums desc limit 10", nativeQuery = true)
    public List<Object[]> todayResTop10();

    @Query(value = "select rmtype,sum(amount) as prices from Room_rs where date_format(createdate,'%Y-%m-%d')=curdate() and canceldate='1900-01-01' group by rmtype order by prices desc limit 10", nativeQuery = true)
    public List<Object[]> todayPriceTop10();

    @Query("select coalesce(sum(anz), 0) from Room_rs  where  projectid=?1 and rmtype=?2 and uid=?3 and  createdate between  ?4 and ?5 and canceldate<= ?6")
    Integer countTodayUserOrderNum(String projectId, String productCode, String uid, LocalDateTime startDate, LocalDateTime endDate, LocalDateTime defaultDete);
}
