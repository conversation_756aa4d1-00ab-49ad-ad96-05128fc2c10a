package com.cw.mapper;

import com.cw.entity.Ticket_rs_his;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022/1/8 0008
 */
public interface Ticket_rsHisMapper extends JpaRepository<Ticket_rs_his, Long>, JpaSpecificationExecutor<Ticket_rs_his> {

    List<Ticket_rs_his> findAllByBookingidAndProjectid(String bookingid, String projectid);

    @Transactional
    @Modifying
    @Query(value = "DELETE t from Ticket_rs_his t  LEFT JOIN app_user a ON t.uid=a.userid WHERE  a.lastb < ?1 AND a.regdate< ?2 limit 50", nativeQuery = true)
    int deleteUnActiveUserData(Date lastActiveTime, Date regDate);
}
