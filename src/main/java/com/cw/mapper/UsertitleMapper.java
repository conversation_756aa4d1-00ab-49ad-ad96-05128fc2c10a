package com.cw.mapper;

import com.cw.entity.Usertitle;
import com.cw.exception.DefinedException;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-03-10
 */
public interface UsertitleMapper extends JpaRepository<Usertitle, Long>, JpaSpecificationExecutor<Usertitle> {

    Integer countUsertitleByAppuserid(String userId);

    Usertitle findByTitletypeAndTitleAndProjectid(Integer titleType, String title, String projectId);

    @Transactional
    @Modifying
    @Query(value = "update Usertitle set active=false where sqlid<>?1 and appuserid=?2", nativeQuery = true)
    void inactiveOthers(Long id, String appuserid);

    @Transactional(rollbackFor = DefinedException.class)
    @Modifying
    @Query(value = "delete  from Usertitle where  sqlid = ?1", nativeQuery = true)
    void deleteBySqlid(Long sqlid);


    @Transactional
    @Modifying
    @Query(value = "DELETE u from Usertitle u  LEFT JOIN app_user a ON u.appuserid=a.userid WHERE  a.lastb < ?1 AND a.regdate< ?2 limit 50", nativeQuery = true)
    int deleteUnActiveUserData(Date lastActiveTime, Date regDate);

    List<Usertitle> findAllByAppuseridAndProjectid(String appuserid, String projectid);
}
