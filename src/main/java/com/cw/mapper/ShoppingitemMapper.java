package com.cw.mapper;

import com.cw.entity.Shopping_item;
import com.cw.exception.DefinedException;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ShoppingitemMapper extends JpaRepository<Shopping_item, Long>, JpaSpecificationExecutor<Shopping_item> {

    @Query(value = "from Shopping_item where appuserid = ?1 and projectid = ?2 order by ptype")
    List<Shopping_item> findAllByAppuseridAndProjectid(String appuserid, String projectId);

    Integer countShopping_itemByAppuserid(String appuserid);

    @Query(value = "select  * from  Shopping_item as s  where s.appuserid=:appuserid and s.projectid=:projectId and s.startdate=:arrdate" +
            " and s.enddate=:deptdate and s.ptype=:productType and s.skuid=:productCode limit 1", nativeQuery = true)
    Shopping_item findSameItem(String appuserid, String projectId, Date arrdate, Date deptdate,
                               String productType, String productCode);

    @Query(value = "from Shopping_item  where sqlid in ?1")
    List<Shopping_item> findAllByIds(List<Long> ids);

    @Transactional(rollbackFor = DefinedException.class)
    @Modifying
    @Query(value = "delete  from Shopping_item where  sqlid in ?1", nativeQuery = true)
    void deleteAllBySqlid(List<Long> ids);

}
