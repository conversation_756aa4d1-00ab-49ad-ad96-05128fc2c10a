package com.cw.mapper;

import com.cw.entity.Userlog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

public interface UserlogMapper extends JpaRepository<Userlog, Long>, JpaSpecificationExecutor<Userlog> {

    @Transactional
    @Modifying
    @Query(value = "DELETE u from Userlog  u LEFT JOIN Booking_rs_his b ON u.regno=b.bookingid LEFT JOIN App_user a ON b.uid=a.userid WHERE " +
            "  a.lastb < ?1 AND a.regdate< ?2 AND b.bookingid=u.regno limit 50", nativeQuery = true)
    int deleteUserLogUnActiveUserData(Date lastActiveTime, Date regDate);
}
