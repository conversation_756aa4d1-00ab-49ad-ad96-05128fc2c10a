package com.cw.mapper;

import com.cw.entity.Rspuitems;
import com.cw.pojo.sqlresult.Produce_avlPo;
import com.cw.pojo.sqlresult.Product_maxDatePo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface RspuitemsMapper extends JpaRepository<Rspuitems, Long>, JpaSpecificationExecutor<Rspuitems> {

    @Transactional
    @Modifying
    @Query(value = "update Rspuitems set pickup=pickup+?1,avl=avl-?1 where spuitem=?2  and projectid=?3 ",
            nativeQuery = true)
    void updateRspuitemPickup(Integer pickup, String spuitem, String projectId);

    @Query(value = "select MAX(datum) from Rspuitems  where projectid=?1", nativeQuery = true)
    Date findSpuitemsAvlMaxDate(String projectid);


    @Query(value = "select new com.cw.pojo.sqlresult.Produce_avlPo(datum,avl) from Rspuitems " +
            "where datum between ?1 and ?2  and spuitem=?3 and projectid=?4 order by datum")
    List<Produce_avlPo> queryAvailNum(Date startdate, Date enddate, String product, String projectid);

    @Query(value = "select new com.cw.pojo.sqlresult.Product_maxDatePo(spuitem,MAX(datum)) from Rspuitems" +
            "  where projectid=?1 group by spuitem order by MAX(datum)")
    List<Product_maxDatePo> getRspuitemsMaxDate(String projectid);

    @Modifying
    @Transactional
    @Query(value = "update  Rspuitems set datum =?2 where projectid=?1")
    void updateDate(String projectId, Date generateDate);

    Rspuitems findByProjectidAndSpuitem(String projectId, String spuitem);
}
