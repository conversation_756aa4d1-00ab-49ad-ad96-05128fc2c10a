package com.cw.mapper;

import com.cw.entity.Usercoupon;
import com.cw.pojo.dto.conf.res.coupon.CouponGroupStatiDetail;
import com.cw.pojo.dto.conf.res.coupon.CouponStaticDetail;
import com.cw.pojo.dto.conf.res.coupon.CouponToDayDetail;
import com.cw.pojo.dto.conf.res.coupon.CouponUserData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-01-21
 */
public interface UserCouponMapping extends JpaRepository<Usercoupon, Long>, JpaSpecificationExecutor<Usercoupon> {
    /**
     * 未过期核销
     *
     * @param code
     * @param projectid
     * @return
     */
    @Query(value = "SELECT * from Usercoupon where lcheck =false and couponcode =?1 and projectid=?2 and  enddate>?3", nativeQuery = true)
    List<Usercoupon> findUnCheckCouponByCouponCode(String code, String projectid, Date nowDate);

    /**
     * 未过期核销
     *
     * @param groupid
     * @param projectid
     * @param nowDate
     * @return
     */
    @Query(value = "SELECT * from Usercoupon where lcheck =false and groupid =?1 and projectid=?2 and enddate>?3", nativeQuery = true)
    List<Usercoupon> findUnCheckCouponByGroupid(String groupid, String projectid, Date nowDate);

    Usercoupon findBySqlid(Long sqlid);

    List<Usercoupon> findAllByAppuseridAndProjectid(String appuserid, String projectId);


    Usercoupon findByCouponcodeAndAppuserid(String couponcode, String userid);

    /**
     * @param appUserId
     * @param startDate
     * @param endDate
     * @return 查看用户领取所有的优惠券代码
     */
    // @Query(value = "SELECT COUNT(sqlid) from Usercoupon where appuserid =?1 and groupid=?2 and createtime between  ?3 and ?4 ", nativeQuery = true)
    Long countByAppuseridAndGroupidAndCreatetimeBetween(String appUserId, String groupid, Date startDate, Date endDate);

    Usercoupon findByCouponid(String couponid);

    @Query(value = "select * from Usercoupon where appuserid= ?1 and groupid=?2 and projectid=?3 order by createtime desc limit 1", nativeQuery = true)
    Usercoupon findLastCouponAppuseridAndGroupid(String userid, String groupid, String projectId);

    /**
     * @param userid
     * @param groupid
     * @param projectId
     * @return 统计用户每日领取优惠券大组数量
     */
    @Query(value = "select new com.cw.pojo.dto.conf.res.coupon.CouponUserData(count(sqlid),createtime,groupid) from Usercoupon where " +
            "appuserid= ?1 and groupid=?2 and projectid=?3 group by DATE(createtime) order by createtime desc")
    List<CouponUserData> findTodayNumByAppuseridAndGroupid(String userid, String groupid, String projectId);


    @Query(value = "select  SUM(price) from Usercoupon where projectid=?1 and  createtime between  ?2 and ?3 and  groupid=?4")
    BigDecimal countTodayTotalPriceByGroupid(String projectid, Date startdate, Date enddate, String groupid);

    /**
     * @param projectid
     * @param startdate
     * @param enddate
     * @param couponCode
     * @return
     */
    //todo 限制查询在优惠券使用时间日期范围内
    //@Query(value = "select  count(sqlid) from Usercoupon where couponcode=?1  and  createtime between ?2 and ?3 and projectid=?4")
    //long countTodaySellByCouponCode(String couponCode, Date startdate, Date enddate, String projectid);
    @Query(value = "select new com.cw.pojo.dto.conf.res.coupon.CouponToDayDetail(coalesce(sum(case when createtime between ?2 and ?3 then 1 else 0 end ),0)," +
            "coalesce(sum(case when checkdate between ?2 and ?3 and lcheck=true then 1 else 0 end ),0)," +
            "count(sqlid), " +
            "coalesce(sum(case when lcheck=true then 1 else 0 end ),0)) " +
            "  from Usercoupon where couponcode=?1   and projectid=?4")
    CouponToDayDetail countTodaySellByCouponCode(String couponCode, Date startdate, Date enddate, String projectid);


    /**
     * @param groupId
     * @return
     */
    @Query(value = "select new com.cw.pojo.dto.conf.res.coupon.CouponGroupStatiDetail( coalesce(sum(u.price),0),count(u.sqlid)," +
            "coalesce(sum(case when u.lcheck=true then u.price else 0 end ),0)," +
            "coalesce(sum(case when u.lcheck=true then 1 else 0 end ),0)) " +
            "from Usercoupon  u where u.groupid=?1 and u.createtime between ?2 and ?3 ")
    CouponGroupStatiDetail countUserCouponDataByGroupid(String groupId, Date startDate, Date endDate);


    /**
     * @param groupId
     * @return
     */
    @Query(value = "select new com.cw.pojo.dto.conf.res.coupon.CouponGroupStatiDetail( coalesce(sum(u.price),0),count(u.sqlid)," +
            "coalesce(sum(case when u.lcheck=true then u.price else 0 end ),0)," +
            "coalesce(sum(case when u.lcheck=true then 1 else 0 end ),0)) " +
            "from Usercoupon  u where u.groupid=?1 and u.checkdate between ?2 and ?3 ")
    CouponGroupStatiDetail countUserCouponCheckDataByGroupid(String groupId, Date startDate, Date endDate);

    /**
     * @param userid
     * @param startDate
     * @param endDate
     * @param unit
     * @return 按角色核销券种统计
     */
    @Query(value = "select new com.cw.pojo.dto.conf.res.coupon.CouponStaticDetail(u.description, coalesce(sum(u.price),0),count(u.sqlid)) " +
            "from Usercoupon  u where u.lcheck ='1' and u.checkrole=?1 and u.checkdate between ?2 and ?3 and u.unit =?4 group by u.couponcode order by u.couponcode")
    List<CouponStaticDetail> staticCheckUsercoupon(String userid, Date startDate, Date endDate, Integer unit);
}
