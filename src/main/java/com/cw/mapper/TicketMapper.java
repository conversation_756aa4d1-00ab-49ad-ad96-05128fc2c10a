package com.cw.mapper;

import com.cw.entity.Ticket;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

public interface TicketMapper extends JpaRepository<Ticket, Long>, JpaSpecificationExecutor<Ticket> {

    @Query(value = "select * from Ticket where rulecode=?1 and projectid=?2", nativeQuery = true)
    List<Ticket> getByRulecodeAndProjectid(String ruleCode, String projectId);

    @Query(value = "select code from Ticket where  projectid =?1 and  groupid=?2", nativeQuery = true)
    List<String> getAllTicketByGroupid(String projectid, String groupid);

    @Query(value = "select count(sqlid) from Ticket where code = ?1 and  projectid =?2", nativeQuery = true)
    long countByCode(String code, String projectId);

    @Query(value = "select count(sqlid) from Ticket where `groupid` = ?1", nativeQuery = true)
    long countByGroup(String groupCode);

    @Query(value = "select * from Ticket where `groupid` = ?1 and projectid =?2", nativeQuery = true)
    List<Ticket> findAllByGroupidAndProjectid(String groupid, String projectid);

    Ticket findByCodeAndProjectid(String code, String projectId);


    @Query(value = "select new map(r.ticket as ticket, t.code as code, r.datum as date, r.avl as avl, " +
            "t.rate as rate, r.pickup as pickup) from Rtickets r left JOIN Tratedet t ON r.ticket = t.productcode and r.datum = t.datum" +
            " where t.productcode=?1 and t.projectid=?2 and r.datum between ?3 and ?4 group by r.datum,t.productcode order by r.datum")
    List<HashMap<String, Object>> getTicketResourceAndPrice(String code, String projectId, Date startDate, Date endDate);

    @Modifying
    @Transactional(rollbackFor = Exception.class)
    void deleteBySqlid(long id);

    @Query(value = "select code from Ticket where projectid=?1", nativeQuery = true)
    List<String> findAllTicket(String projectid);
}
