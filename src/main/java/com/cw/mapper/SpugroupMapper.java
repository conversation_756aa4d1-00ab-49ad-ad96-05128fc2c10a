package com.cw.mapper;

import com.cw.entity.Spugroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface SpugroupMapper extends JpaRepository<Spugroup, Long>, JpaSpecificationExecutor<Spugroup> {
    Spugroup findBySqlid(Long sqlid);

    @Query(value = "select count(sqlid) from Spugroup where code=?1 and projectid=?2", nativeQuery = true)
    long countByCode(String code, String projectId);
}
