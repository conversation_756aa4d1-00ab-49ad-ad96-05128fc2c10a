package com.cw.mapper;

import com.cw.entity.Vendorconfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface VendorconfigMapper extends JpaRepository<Vendorconfig, Long>, JpaSpecificationExecutor<Vendorconfig> {

    /**
     * 统计是否有重复 厂商
     *
     * @param code
     * @return
     */
    @Query(value = "select count(sqlid) from Vendorconfig where vendorconfig.vtype=?1  and  projectid=?2", nativeQuery = true)
    long countByCodeAndProjectIdNot(String code, String projectId);

}
