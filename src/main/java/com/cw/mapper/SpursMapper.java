package com.cw.mapper;

import cn.hutool.core.date.DateTime;
import com.cw.entity.Spu_rs;
import com.cw.pojo.dto.conf.res.coupon.ProductStaticDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;


public interface SpursMapper extends JpaRepository<Spu_rs, Long>, JpaSpecificationExecutor<Spu_rs> {

    List<Spu_rs> findAllByBookingidAndProjectid(String bookingId, String projectId);

    Spu_rs findByBookingidAndProjectid(String bookingId, String projectId);

    @Query("select coalesce(sum(anz), 0) from Spu_rs  where  projectid=?1 and code=?2 and uid=?3 and  createtime between  ?4 and ?5 and canceldate <= ?6 ")
    Integer countTodayUserOrderNum(String projectId, String productCode, String uid, LocalDateTime startDate, LocalDateTime endDate, LocalDateTime defaultDate);


    @Query(value = "select new com.cw.pojo.dto.conf.res.coupon.ProductStaticDetail(s.code, coalesce(sum(s.anz),0), s.code) " +
            "from Spu_rs s where  s.checkrole=?1 and s.checkdate between ?2 and ?3  group by s.code order by s.code")
    List<ProductStaticDetail> staticCheck(String userid, DateTime beginOfDay, DateTime endOfDay);
}
