package com.cw.mapper;

import com.cw.entity.Roomtype;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public interface RoomtypeMapper extends JpaRepository<Roomtype, Long>, JpaSpecificationExecutor<Roomtype> {

    @Query(value = "select count(sqlid) from Roomtype where code=?1 and projectid=?2", nativeQuery = true)
    long countByCodeAndProjectId(String code, String projectId);


    @Query(value = "select code from Roomtype where projectid=?1", nativeQuery = true)
    List<String> findAllRoomtype(String projectid);


    @Query(value = "select * from Roomtype where rulecode=?1 and projectid=?2", nativeQuery = true)
    List<Roomtype> getByRulecodeAndProjectid(String ruleCode, String projectId);


    @Query(value = "select new map(t.code as code, r.rmtype as rmtype, r.datum as date, r.avl as avl, " +
            "t.rate as rate, r.pickup as pickup) from Rrooms r LEFT JOIN Rratedet t ON r.rmtype = t.productcode and r.datum = t.datum" +
            " where r.rmtype=?1 and r.projectid=?2 and t.projectid=?2 and r.datum between ?3 and ?4 group by r.datum,t.productcode order by r.datum")
    List<HashMap<String, Object>> getRmtypeResourceAndPrice(String code, String projectId, Date startDate, Date endDate);

    List<Roomtype> findAllByHotelcodeAndProjectid(String hotelCode, String projectId);

    @Query(value = "select code from Roomtype where hotelcode=?1 and projectid=?2", nativeQuery = true)
    List<String> findRoomtypeByHotelCodeAndProjectid(String hotelCode, String projectId);

    @Modifying
    @Transactional
    void deleteAllByHotelcodeAndProjectid(String hotelCode, String projectId);


    @Modifying
    @Transactional
    @Query(value = "update Roomtype set lsell=false where code=?1 and  projectid=?2")
    void upDateRoomtypeUnSell(String roomtype, String projectId);
}
