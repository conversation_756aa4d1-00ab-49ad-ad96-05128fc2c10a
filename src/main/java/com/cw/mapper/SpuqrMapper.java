package com.cw.mapper;

import com.cw.entity.Spuqr;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-01-13
 */
public interface SpuqrMapper extends JpaRepository<Spuqr, Long>, JpaSpecificationExecutor<Spuqr> {

    @Query(value = "select count(sqlid) from Spuqr where code = ?1 and groupid=?2 and  projectid =?3", nativeQuery = true)
    long countByCodeAndgroupid(String code, String groupid, String projectId);
}
