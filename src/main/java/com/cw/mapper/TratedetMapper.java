package com.cw.mapper;

import com.cw.entity.Tratedet;
import com.cw.pojo.sqlresult.Produce_calcpricePo;
import com.cw.pojo.sqlresult.Produce_ratesqueryPo;
import com.cw.pojo.sqlresult.Ticket_ratePo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface TratedetMapper extends JpaRepository<Tratedet, Long>, JpaSpecificationExecutor<Tratedet> {


    @Query(value = "select new com.cw.pojo.sqlresult.Produce_calcpricePo(r.datum,r.rate) " +
            "from Tratedet  r where r.projectid=?1 and r.code=?2  " +
            "and r.productcode=?3 and r.datum between  ?4 and ?5")
    List<Produce_calcpricePo> calcPrice(String projectId, String ratecode,
                                        String productcode, Date querystart, Date queryend);

    @Query(value = "select new com.cw.pojo.sqlresult.Produce_ratesqueryPo(r.productcode,r.rate) " +
            "from Tratedet  r where r.projectid=?1 and r.code=?2  " +
            "and r.productcode in ?3 and r.datum  = ?4 ")
    List<Produce_ratesqueryPo> rateQuery(String projectId, String ratecode,
                                         List<String> productcodes, Date useDate);

    @Query(value = "select * from Tratedet  where code =?1  and projectid=?2 and productcode=?3 and datum between ?4 and ?5", nativeQuery = true)
    List<Tratedet> findTicketPriceList(String rateCode, String projectId, String ticket, Date startDate, Date endDate);

    @Query(value = "select MAX(datum) from Tratedet  where projectid=?1", nativeQuery = true)
    Date findTratedetMaxDate(String projectid);

    @Query(value = "select MAX(datum) from Tratedet  where projectid=?1 and productcode=?2", nativeQuery = true)
    Date findTratedetMaxDateByProductcode(String projectid, String productcode);

    @Query(value = "select new com.cw.pojo.sqlresult.Ticket_ratePo(code, productcode,MAX(datum)) from Tratedet" +
            "  where projectid=?1 group by productcode order by MAX(datum)")
    List<Ticket_ratePo> getTratedetMaxDate(String projectid);

    @Query(value = "select count(sqlid) from Tratedet where productcode = ?1 and  projectid =?2", nativeQuery = true)
    long countByCode(String ticket, String projectId);

    @Query(value = "SELECT * from Tratedet WHERE productcode=?1 and projectid=?2 ORDER BY datum desc LIMIT 1", nativeQuery = true)
    Tratedet findTratedetByLastDatum(String productcode, String projectid);
}
