package com.cw.mapper;

import com.cw.entity.Useraddr;
import com.cw.exception.DefinedException;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface UseraddrMapper extends JpaRepository<Useraddr, Long>, JpaSpecificationExecutor<Useraddr> {

    @Query(value = "from Useraddr where appuserid = ?1 and projectid = ?2 ")
    List<Useraddr> findAllByAppuseridAndProjectid(String appuserid, String projectId);


    Integer countUseraddrsByAppuserid(String appuserid);


    @Transactional(rollbackFor = DefinedException.class)
    @Modifying
    @Query(value = "delete  from Useraddr where  sqlid = ?1", nativeQuery = true)
    void deleteBySqlid(Long id);


    @Transactional
    @Modifying
    @Query(value = "update Useraddr set active=false where sqlid<>?1 and appuserid=?2", nativeQuery = true)
    void inactiveOthers(Long id, String appuserid);

    @Transactional
    @Modifying
    @Query(value = "DELETE u from Useraddr u  LEFT JOIN app_user a ON u.appuserid=a.userid WHERE  a.lastb < ?1 AND regdate< ?2 limit 50", nativeQuery = true)
    int deleteUnActiveUserData(Date lastActiveTime, Date regDate);

}
