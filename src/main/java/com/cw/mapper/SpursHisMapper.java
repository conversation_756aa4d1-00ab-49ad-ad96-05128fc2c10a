package com.cw.mapper;

import com.cw.entity.Spu_rs_his;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-06-15
 */
public interface SpursHisMapper extends JpaRepository<Spu_rs_his, Long>, JpaSpecificationExecutor<Spu_rs_his> {
    List<Spu_rs_his> findAllByBookingidAndProjectid(String bookingId, String projectId);

    @Transactional
    @Modifying
    @Query(value = "DELETE s from Spu_rs_his s  LEFT JOIN app_user a ON s.uid=a.userid WHERE a.lastb < ?1 AND a.regdate< ?2 limit 50", nativeQuery = true)
    int deleteUnActiveUserData(Date lastActiveTime, Date regDate);
}
