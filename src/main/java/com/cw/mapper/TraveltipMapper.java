package com.cw.mapper;

import com.cw.entity.Traveltip;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-07-12
 */
public interface TraveltipMapper extends JpaRepository<Traveltip, Long>, JpaSpecificationExecutor<Traveltip> {

    Traveltip findBySqlid(long sqlid);

    @Query(value = "select count(sqlid) from Traveltip where code=?1 and projectid=?2", nativeQuery = true)
    long countByCodeAndProjectid(String code, String projectid);
}
