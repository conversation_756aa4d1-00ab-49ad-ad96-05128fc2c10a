package com.cw.mapper;

import com.cw.entity.Ticket_rs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/14 0014
 */
public interface Ticket_rsMapper extends JpaRepository<Ticket_rs, Long>, JpaSpecificationExecutor<Ticket_rs> {

    Ticket_rs findTicket_rsByRegno(String regno);

    Ticket_rs findTicket_rsByBookingid(String bookingid);

    List<Ticket_rs> findTicket_rsByBookingidAndProjectid(String bookingid, String projectid);

    @Query(value = "select count(sqlid) from Ticket_rs where bookingid=?1 and qrcode<>'' ", nativeQuery = true)
    long getAlreadyGetQrTicket(String bookingid);

    @Query(value = "select count(sqlid) from Ticket_rs where ticketgroup=?1 and status<>'C' and status<>'R'", nativeQuery = true)
    long countByTicketgroupAndStatus(String ticketgroup);

    @Query(value = "select count(sqlid) from Ticket_rs where tcode=?1", nativeQuery = true)
    long countByTcodeUnused(String tCode);

    @Query("select coalesce(sum(anz), 0) from Ticket_rs  where  projectid=?1 and tcode=?2 and uid=?3 and  createdate between  ?4 and ?5 and  canceldate<=?6")
    Integer countTodayUserOrderNum(String projectId, String productCode, String uid, LocalDateTime startDate, LocalDateTime endDate, LocalDateTime defaultTime);
}
