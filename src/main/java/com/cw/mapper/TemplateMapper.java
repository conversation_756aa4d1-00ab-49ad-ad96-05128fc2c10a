package com.cw.mapper;

import com.cw.entity.Template;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/24 0024
 */
public interface TemplateMapper extends JpaRepository<Template, Long>, JpaSpecificationExecutor<Template> {

    @Query(value = "select count(sqlid) from Template where  `code`=?1 and `projectid`=?2", nativeQuery = true)
    long countByCode(String code, String projectid);

    /**
     * @param trigger
     * @param condition
     * @return 启动触发模板公式限制的个数
     */
    @Query(value = "select count(sqlid) from Template where  `trigger`=?1 and `condition`=?2 and `status`=true", nativeQuery = true)
    long countByTrigger(String trigger, String condition);


    @Query(value = "select count(sqlid) from Template where (`code`=?1 or (`trigger`=?2  and `condition`=?3 )) and sqlid<>?4", nativeQuery = true)
    long countByCodeAndTrigger(String code, String trigger, String condition, Long sqlid);

    @Query(value = "select * from Template where `status`=?1", nativeQuery = true)
    List<Template> findAllByStatus(Boolean status);
}
