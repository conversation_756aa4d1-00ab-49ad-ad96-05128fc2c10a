package com.cw.mapper;

import com.cw.entity.Ticketgroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface TicketgroupMapper extends JpaRepository<Ticketgroup, Long>, JpaSpecificationExecutor<Ticketgroup> {

    @Query(value = "select count(sqlid) from Ticketgroup where code = ?1 and projectId=?2", nativeQuery = true)
    long countByCode(String code, String projectId);
}
