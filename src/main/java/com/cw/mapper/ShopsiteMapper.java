package com.cw.mapper;

import com.cw.entity.Shopsite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-10-31
 */
public interface ShopsiteMapper extends JpaRepository<Shopsite, Long>, JpaSpecificationExecutor<Shopsite> {

    long countByCodeAndProjectid(String code, String currentProjectId);
}
