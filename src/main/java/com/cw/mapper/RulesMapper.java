package com.cw.mapper;

import com.cw.entity.Rules;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/8 0008
 */
public interface RulesMapper extends JpaRepository<Rules, Long>, JpaSpecificationExecutor<Rules> {

    Rules findBySqlid(Long sqlid);

    Rules findByCode(String code);

    long countByCodeAndProjectid(String code, String projectId);

    @Query(value = "select type from Rules where code=?1 and projectid=?2")
    String findTypeByCodeAndProjectid(String code, String projectId);

}
