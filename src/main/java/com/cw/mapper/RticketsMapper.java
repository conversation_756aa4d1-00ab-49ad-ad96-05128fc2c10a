package com.cw.mapper;

import com.cw.entity.Rtickets;
import com.cw.pojo.sqlresult.Produce_avlPo;
import com.cw.pojo.sqlresult.Product_maxDatePo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface RticketsMapper extends JpaRepository<Rtickets, Long>, JpaSpecificationExecutor<Rtickets> {

    @Transactional
    @Modifying
    @Query(value = "update Rtickets set pickup=pickup+?1,avl=avl-?1 where ticket=?2 and datum between ?3 and ?4 and projectid=?5 ", nativeQuery = true)
    void updateRticketsPickup(Integer pickup, String ticket, Date start, Date end, String projectId);


    @Query(value = "select new com.cw.pojo.sqlresult.Produce_avlPo(datum, avl) from Rtickets " +
            "where datum between ?1 and ?2  and ticket=?3 and projectid=?4 order by datum")
    List<Produce_avlPo> queryAvailNum(Date startdate, Date enddate, String product, String projectid);

    @Query(value = "select * from Rtickets  where  projectid=?1 and ticket=?2 and datum between ?3 and ?4", nativeQuery = true)
    List<Rtickets> findTicketAvlList(String projectId, String ticket, Date startDate, Date endDate);

    @Query(value = "select MAX(datum) from Rtickets  where projectid=?1", nativeQuery = true)
    Date findTicketAvlMaxDate(String projectid);

    @Query(value = "select MAX(datum) from Rtickets  where projectid=?1 and ticket=?2", nativeQuery = true)
    Date findTicketAvlMaxDateByTicket(String projectid, String tcode);

    @Query(value = "select new com.cw.pojo.sqlresult.Product_maxDatePo(ticket,MAX(datum)) from Rtickets" +
            "  where projectid=?1 group by ticket order by MAX(datum)")
    List<Product_maxDatePo> getRticketsMaxDate(String projectid);
}
