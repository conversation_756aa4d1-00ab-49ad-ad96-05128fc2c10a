package com.cw.mapper;

import com.cw.entity.Room_rs_his;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022/1/7 0007
 */
public interface RoomrsHisMapper extends JpaRepository<Room_rs_his, Long>, JpaSpecificationExecutor<Room_rs_his> {
    List<Room_rs_his> findAllByBookingidAndProjectid(String bookingid, String projectId);


    @Transactional
    @Modifying
    @Query(value = "DELETE r from Room_rs_his r  LEFT JOIN app_user a ON r.uid=a.userid WHERE a.lastb < ?1 AND a.regdate< ?2 limit 50", nativeQuery = true)
    int deleteUnActiveUserData(Date lastActiveTime, Date regDate);
}
