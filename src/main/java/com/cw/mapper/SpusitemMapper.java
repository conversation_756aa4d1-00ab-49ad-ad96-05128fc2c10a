package com.cw.mapper;

import com.cw.entity.Spusitem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Describe 景区商品
 * <AUTHOR> <PERSON>
 * @Create on 2022-02-21
 */
public interface SpusitemMapper extends JpaRepository<Spusitem, Long>, JpaSpecificationExecutor<Spusitem> {
    @Query(value = "select count(*) from Spusitem where groupid=?1 and projectid=?2")
    long countByGroupid(String groupid, String projectid);

    @Query(value = "select count(sqlid) from Spusitem where code=?1 and projectid=?2 ", nativeQuery = true)
    long countByCodeAndProjectid(String code, String projectid);

    Spusitem findBySqlid(Long sqlid);

    Spusitem findByCode(String code);

    @Query(value = "select new map(t.code as code, r.spuitem as spuitem, r.datum as date, r.avl as avl, " +
            "t.rate as rate, r.pickup as pickup) from Rspuitems r LEFT JOIN Spuratedet t ON r.spuitem = t.productcode and r.datum = t.datum" +
            " where r.spuitem=?1 and r.projectid=?2 and t.projectid=?2 and r.datum between ?3 and ?4 group by r.datum,t.productcode order by r.datum")
    List<HashMap<String, Object>> getProductResourceAndPrice(String code, String projectid, Date startDate, Date endDate);

    @Query(value = "select * from Spusitem where rulecode=?1 and projectid=?2", nativeQuery = true)
    List<Spusitem> getByRulecodeAndProjectid(String code, String projectid);
}
