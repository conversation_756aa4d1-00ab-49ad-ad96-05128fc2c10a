package com.cw.mapper.common.bean;

import com.cw.entity.Parksite;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * @Describe 地图园区场所
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-06
 */
@Repository
@Slf4j
public class ParksiteDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    public void bathInsertParksite(List<Parksite> postParksites) {
        String insetSql = "insert into  Parksite(`code`,`description`,`parkid`,`businesscode`,`seq`,`lshow`,`coordinate`,`clickurl`," +
                "`openingtime`,`slidepics`,`introduction`,`richtext`,`audiourl`,`projectid`,`vrviewurl`)  VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        int count = 0;
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postParksites.size(); i++) {
                Parksite postR = postParksites.get(i);
                if (postR.getSqlid() <= 0L) {
                    insertpstmt.setString(1, postR.getCode());
                    insertpstmt.setString(2, postR.getDescription());
                    insertpstmt.setString(3, postR.getParkid());
                    insertpstmt.setString(4, postR.getBusinesscode());
                    insertpstmt.setInt(5, postR.getSeq());
                    insertpstmt.setBoolean(6, postR.getLshow());
                    insertpstmt.setString(7, postR.getCoordinate());
                    insertpstmt.setString(8, postR.getClickurl());
                    insertpstmt.setString(9, postR.getOpeningtime());
                    insertpstmt.setString(10, postR.getSlidepics());
                    insertpstmt.setString(11, postR.getIntroduction());
                    insertpstmt.setString(12, postR.getRichtext());
                    insertpstmt.setString(13, postR.getAudiourl());
                    insertpstmt.setString(14, postR.getProjectid());
                    insertpstmt.setString(15, postR.getVrviewurl());
                    insertpstmt.addBatch();
                    count++;
                }
            }
            insertpstmt.executeBatch();
            connection.commit();
            log.info("提交一次parksite{}条记录批量插入{}", count, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
