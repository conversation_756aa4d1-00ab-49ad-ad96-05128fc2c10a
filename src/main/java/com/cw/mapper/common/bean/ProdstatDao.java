package com.cw.mapper.common.bean;

import cn.hutool.core.date.DateUtil;
import com.cw.entity.Prodstat;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-05-26
 */
@Repository
@Slf4j
public class ProdstatDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    public void batchUpdateProdstat(List<Prodstat> postProdstat) {

        String sql = "update Prodstat set `ordernum`=?, `sellcount`=?, `amount`=? where `projectid`=?" +
                " and `prodcode`=?  and `datum`=?";
        String insetSql = "insert into  Prodstat(`ordernum`,`sellcount`,`amount`,`projectid`,`prodcode`, `datum`)  VALUES(?,?,?,?,?,?)";

        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postProdstat.size(); i++) {
                Prodstat postK = postProdstat.get(i);
                if (postK.getSqlid() > 0L) {//更新
                    cstmt.setInt(1, postK.getOrdernum());
                    cstmt.setInt(2, postK.getSellcount());
                    cstmt.setBigDecimal(3, postK.getAmount());
                    cstmt.setString(4, postK.getProjectid());
                    cstmt.setString(5, postK.getProdcode());
                    cstmt.setDate(6, DateUtil.date(postK.getDatum()).toSqlDate());
                    cstmt.addBatch();
                } else {
                    insertpstmt.setInt(1, postK.getOrdernum());
                    insertpstmt.setInt(2, postK.getSellcount());
                    insertpstmt.setBigDecimal(3, postK.getAmount());
                    insertpstmt.setString(4, postK.getProjectid());
                    insertpstmt.setString(5, postK.getProdcode());
                    insertpstmt.setDate(6, DateUtil.date(postK.getDatum()).toSqlDate());
                    insertpstmt.addBatch();
                }
            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次Prodstat{}条记录批量更新{}", postProdstat.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }

                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
