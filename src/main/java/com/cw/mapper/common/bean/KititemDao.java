package com.cw.mapper.common.bean;


import com.cw.cache.GlobalCache;
import com.cw.entity.Kititem;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * @Describe 保存套餐明细
 * <AUTHOR> <PERSON>
 * @Created on 2020/10/22.
 */
@Repository
@Slf4j
public class KititemDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    public void batchUpdKitItem(List<Kititem> postKitItem) {
        String sql = "update kititem set `kitcode`=?, `num`=?, `productcode`=?, `productdesc`=?, `producttype`=?, `productgroup`=?,`days`=?," +
                "`time`=?,`itemtype`=?,`groupcode`=?,`rate1`=?,`rate2`=?,`rate3`=?,`rate4`=?,`rate5`=?,`rate6`=?,`rate7`=? " +
                " where `kitcode`=? and `productcode`=? and `producttype`=? and `productgroup`=? and `projectid`=?";
        String insetSql = "insert into  kititem(`kitcode`,`num`,`productcode`,`productdesc`, `producttype`,`productgroup`,`days`,`time`,`itemtype`," +
                "`groupcode`,`projectid`,`rate1`,`rate2`,`rate3`,`rate4`,`rate5`,`rate6`,`rate7`)  " +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postKitItem.size(); i++) {
                Kititem postK = postKitItem.get(i);
                //通过kitcode+productcode获取缓存
                String key = postK.getKitcode() + postK.getProductcode();
                if (postK.getProducttype().equals(ProdType.CANYIN.val()) && StringUtils.isBlank(postK.getProductcode())) {
                    key = postK.getKitcode() + postK.getProductgroup();
                }
                Kititem kititem = (Kititem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITITEM)
                        .getRecord(postK.getProjectid(), key);
                if (kititem == null) {
                    //新增数据
                    insertpstmt.setString(1, postK.getKitcode());
                    insertpstmt.setBigDecimal(2, postK.getNum());
                    insertpstmt.setString(3, postK.getProductcode());
                    insertpstmt.setString(4, postK.getProductdesc());
                    insertpstmt.setString(5, postK.getProducttype());
                    insertpstmt.setString(6, postK.getProductgroup());
                    insertpstmt.setBigDecimal(7, postK.getDays());
                    insertpstmt.setString(8, postK.getTime());
                    insertpstmt.setString(9, postK.getItemtype());
                    insertpstmt.setString(10, postK.getGroupcode());
                    insertpstmt.setString(11, postK.getProjectid());
                    insertpstmt.setBigDecimal(12, postK.getRate1());
                    insertpstmt.setBigDecimal(13, postK.getRate2());
                    insertpstmt.setBigDecimal(14, postK.getRate3());
                    insertpstmt.setBigDecimal(15, postK.getRate4());
                    insertpstmt.setBigDecimal(16, postK.getRate5());
                    insertpstmt.setBigDecimal(17, postK.getRate6());
                    insertpstmt.setBigDecimal(18, postK.getRate7());
                    insertpstmt.addBatch();
                } else {
                    cstmt.setString(1, postK.getKitcode());
                    cstmt.setBigDecimal(2, postK.getNum());
                    cstmt.setString(3, postK.getProductcode());
                    cstmt.setString(4, postK.getProductdesc());
                    cstmt.setString(5, postK.getProducttype());
                    cstmt.setString(6, postK.getProductgroup());
                    cstmt.setBigDecimal(7, postK.getDays());
                    cstmt.setString(8, postK.getTime());
                    cstmt.setString(9, postK.getItemtype());
                    cstmt.setString(10, postK.getGroupcode());


                    cstmt.setBigDecimal(11, postK.getRate1());
                    cstmt.setBigDecimal(12, postK.getRate2());
                    cstmt.setBigDecimal(13, postK.getRate3());
                    cstmt.setBigDecimal(14, postK.getRate4());
                    cstmt.setBigDecimal(15, postK.getRate5());
                    cstmt.setBigDecimal(16, postK.getRate6());
                    cstmt.setBigDecimal(17, postK.getRate7());
                    cstmt.setString(18, postK.getKitcode());
                    cstmt.setString(19, postK.getProductcode());
                    cstmt.setString(20, postK.getProducttype());
                    cstmt.setString(21, postK.getProductgroup());
                    cstmt.setString(22, postK.getProjectid());


                    //cstmt.setString(11, postK.getKitcode());
                    //cstmt.setString(12, postK.getProductcode());
                    //cstmt.setString(13, postK.getProducttype());
                    //cstmt.setString(14, postK.getProductgroup());
                    //cstmt.setString(15, postK.getProjectid());
                    cstmt.addBatch();
                }

            }
            int[] insertCount = insertpstmt.executeBatch();
            int[] updateCount = cstmt.executeBatch();
            connection.commit();
            log.info("提交一次kititem{}条记录批量更新{},更新:{},插入:{}", postKitItem.size(), stopwatch.stop(), updateCount.length, insertCount.length);
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
