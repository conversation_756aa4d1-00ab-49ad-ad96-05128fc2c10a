package com.cw.mapper.common.bean;

import com.cw.core.CorePrice;
import com.cw.entity.Giftitem;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-02
 */
@Repository
@Slf4j
public class GiftratedetDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * 新建伴手礼价格
     *
     * @param giftitem
     */
    @Async("commonPool")
    public void batchInsertGiftratedet(Giftitem giftitem, String itemcode) {
        String rateCode = CorePrice.getRateCode(giftitem.getProjectid(), "");
        String insertSql = "Insert into Giftratedet(`productcode`,`projectid`, `code`,`rate`,`itemcode`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            //查询最大日期数据 对应插入日期数据
            Date maxDate = SpringUtil.getBean(CorePrice.class).getGiftratedetMaxDate(giftitem.getProjectid());
            cstmt.setString(1, giftitem.getCode());
            cstmt.setString(2, giftitem.getProjectid());
            cstmt.setString(3, rateCode);
            //cstmt.setDate(4, DateUtil.date(maxDate).toSqlDate());
            cstmt.setBigDecimal(4, giftitem.getShowprice());
            cstmt.setString(5, itemcode);
            cstmt.addBatch();

            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
            corePrice.calc2Cache(giftitem.getProjectid(), rateCode, ProdType.ITEMS, giftitem.getCode(), maxDate,
                    maxDate);
            log.info("提交giftitem产品{}，价格记录更新，用时{}", giftitem.getCode(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));

        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    ///**
    // * 按日期区间 指定星期数更新客房房价
    // *
    // * @param giftitem
    // * @param startDate
    // * @param endDate
    // * @param projectId
    // * @param price
    // * @param week
    // */
    //public void batchUpdateGiftratedet(String giftitem, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price, List<Integer> week) {
    //    String updateSql;
    //    if (CollectionUtil.isNotEmpty(week)) {
    //        updateSql = "update Giftratedet set rate=?1 where productcode =?2 and projectid=?3 and datum between ?4 and ?5 and code =?6 and" +
    //                " weekday(datum) in ?7";
    //        daoLocal.batchOption(updateSql, price, giftitem, projectId, startDate, endDate, rateCode, week);
    //    } else {
    //        updateSql = "update Giftratedet set rate=?1 where productcode =?2 and projectid=?3 and datum between ?4 and ?5 and code =?6 ";
    //        daoLocal.batchOption(updateSql, price, giftitem, projectId, startDate, endDate, rateCode);
    //    }
    //}
    public void updateGiftratedet(String giftitem, String rateCode, String projectId, BigDecimal price) {
        String updateSql = "update Giftratedet set rate=?1 where productcode=?2 and projectid=?3  and code =?4";
        daoLocal.batchOption(updateSql, price, giftitem, projectId, rateCode);
    }

    public void updateGiftratedetLike(String giftitem, String rateCode, String projectId, BigDecimal price) {
        String updateSql = "update Giftratedet set rate=?1 where productcode like ?2 and projectid=?3  and code =?4";
        daoLocal.batchOption(updateSql, price, giftitem, projectId, rateCode);
    }


    public void batchUpdateGiftratedetBySearch(String giftitem1, String giftitem2, String rateCode, String projectId, BigDecimal price) {
        String updateSql = "update Giftratedet set rate=?1 where productcode like ?2 or productcode like ?3 and projectid=?4  and code =?5";
        daoLocal.batchOption(updateSql, price, giftitem1, giftitem2, projectId, rateCode);
    }
}
