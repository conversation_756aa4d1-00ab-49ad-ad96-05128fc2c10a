package com.cw.mapper.common.bean;

import com.cw.core.CoreAvl;
import com.cw.entity.Giftitem;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-02
 */
@Repository
@Slf4j
public class RgiftitemsDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * 新建伴手礼库存资源
     *
     * @param giftitem
     */
    @Async("commonPool")
    public void batchInsertGiftitems(Giftitem giftitem, String itemcode) {
        String insertSql = "Insert into Rgiftitems(`giftitem`,`projectid`,`avl`,`pickup`,`itemcode`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            Date maxDate = SpringUtil.getBean(CoreAvl.class).getRgiftitemsMaxDate(giftitem.getProjectid());
            cstmt.setString(1, giftitem.getCode());
            cstmt.setString(2, giftitem.getProjectid());
            cstmt.setInt(3, 999);
            cstmt.setInt(4, 0);
            cstmt.setString(5, itemcode);
            cstmt.addBatch();

            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
            coreAvl.calc2Cache(giftitem.getProjectid(), giftitem.getCode(), maxDate,
                    maxDate, ProdType.ITEMS, "");
            log.info("提交giftitem产品{}，库存记录更新，用时{}", giftitem.getCode(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    ///**
    // * 批量更新指定项目指定日期指定星期数的客房库存
    // *
    // * @param giftitem
    // * @param startDate
    // * @param endDate
    // * @param projectId
    // * @param avl
    // * @param week
    // */
    //public void batchUpdateGiftitems(String giftitem, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week) {
    //    String updateSql;
    //    if (CollectionUtil.isNotEmpty(week)) {
    //        updateSql = "update Rgiftitems set avl=?1 where giftitem =?2 and projectid=?3 and datum between ?4 and ?5 and  weekday(datum) in ?6";
    //        daoLocal.batchOption(updateSql, avl, giftitem, projectId, startDate, endDate, week);
    //    } else {
    //        updateSql = "update Rgiftitems set avl=?1 where giftitem =?2 and projectid=?3 and datum between ?4 and ?5";
    //        daoLocal.batchOption(updateSql, avl, giftitem, projectId, startDate, endDate);
    //    }
    //}

    /**
     * 更新具体规格的伴手礼规格库存
     *
     * @param giftitem  具体规格代码，giftitem:specs1:specs2
     * @param projectId 项目ID
     * @param avl       设置的库存
     */
    public void updateGiftitems(String giftitem, String projectId, Integer avl) {
        String updateSql = "update Rgiftitems set avl=?1 where giftitem=?2 and projectid=?3";
        daoLocal.batchOption(updateSql, avl, giftitem, projectId);
    }

    public void updateGiftitemsLike(String giftitem, String projectId, Integer avl) {
        String updateSql = "update Rgiftitems set avl=?1 where giftitem Like ?2 and projectid=?3";
        daoLocal.batchOption(updateSql, avl, giftitem, projectId);
    }

    /**
     * 批量提交更新giftitem1和giftitem2模糊查询伴手礼商品库存
     *
     * @param giftitem1 模糊查询规格1
     * @param giftitem2 模糊查询规格2
     * @param projectId 项目ID
     * @param avl       设置的库存量
     */
    public void batchUpdateGiftitemsBySearch(String giftitem1, String giftitem2, String projectId, Integer avl) {
        String updateSql = "update Rgiftitems set avl=?1 where giftitem like ?2 or giftitem like ?3 and projectid=?4";
        daoLocal.batchOption(updateSql, avl, giftitem1, giftitem2, projectId);
    }
}
