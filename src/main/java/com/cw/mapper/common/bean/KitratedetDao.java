package com.cw.mapper.common.bean;

import cn.hutool.core.date.DateUtil;
import com.cw.arithmetic.others.DateRange;
import com.cw.entity.Kitratedet;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * @Describe 套餐价格
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/30 0030
 */
@Repository
@Slf4j
public class KitratedetDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    public void batchUpdateKitratedet(List<Kitratedet> postKitratedet) {

        String sql = "update Kitratedet set `code`=?, `datum`=?, `rate`=?, `productcode`=?, `projectid`=?, `type`=? where `projectid`=?" +
                " and `code`=? and `productcode`=? and `type`=? and `datum`=?";
        String insetSql = "insert into  Kitratedet(`code`,`datum`,`rate`,`productcode`,`projectid`, `type`)  VALUES(?,?,?,?,?,?)";

        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postKitratedet.size(); i++) {
                Kitratedet postK = postKitratedet.get(i);
                if (postK.getSqlid() > 0L) {//更新
                    cstmt.setString(1, postK.getCode());
                    cstmt.setDate(2, DateUtil.date(postK.getDatum()).toSqlDate());
                    cstmt.setBigDecimal(3, postK.getRate());
                    cstmt.setString(4, postK.getProductcode());
                    cstmt.setString(5, postK.getProjectid());
                    cstmt.setString(6, postK.getType());

                    cstmt.setString(7, postK.getProjectid());
                    cstmt.setString(8, postK.getCode());
                    cstmt.setString(9, postK.getProductcode());
                    cstmt.setString(10, postK.getType());
                    cstmt.setDate(11, DateUtil.date(postK.getDatum()).toSqlDate());
                    cstmt.addBatch();
                } else {
                    insertpstmt.setString(1, postK.getCode());
                    insertpstmt.setDate(2, DateUtil.date(postK.getDatum()).toSqlDate());
                    insertpstmt.setBigDecimal(3, postK.getRate());
                    insertpstmt.setString(4, postK.getProductcode());
                    insertpstmt.setString(5, postK.getProjectid());
                    insertpstmt.setString(6, postK.getType());
                    insertpstmt.addBatch();
                }
            }
            int[] insertCount = insertpstmt.executeBatch();
            int[] updateCount = cstmt.executeBatch();
            connection.commit();
            log.info("提交一次Kitratedet{}条记录批量更新{},插入{}条记录,更新{}条记录", postKitratedet.size(), stopwatch.stop(), insertCount.length, updateCount.length);
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }

                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }


    public void batchUpdateKitratedet2(List<Kitratedet> postKitratedet) {
        if (postKitratedet == null || postKitratedet.isEmpty()) {
            return;
        }

        // 按更新和插入分组
        Map<Boolean, List<Kitratedet>> operationGroups = postKitratedet.stream()
                .collect(Collectors.partitioningBy(k -> k.getSqlid() > 0L));

        List<Kitratedet> updateRecords = operationGroups.get(true);
        List<Kitratedet> insertRecords = operationGroups.get(false);

        // 更新记录按分组条件聚合  目前按每组的项目ID、套餐代码、产品类型、产品进行分组
        Map<String, Map<BigDecimal, DateRange>> updateGroups = new HashMap<>();
        if (!updateRecords.isEmpty()) {
            updateGroups = updateRecords.stream().collect(
                    Collectors.groupingBy(
                            k -> String.format("%s_%s_%s_%s",
                                    k.getProjectid(), k.getCode(), k.getType(), k.getProductcode()),
                            Collectors.groupingBy(
                                    Kitratedet::getRate,
                                    Collector.of(
                                            DateRange::new,
                                            (range, kit) -> range.add(kit.getDatum()),
                                            DateRange::merge
                                    )
                            )
                    )
            );
        }

        String updateSql = "UPDATE Kitratedet SET rate = ? " +
                "WHERE projectid = ? AND code = ? AND type = ? " +
                "AND productcode = ? AND datum BETWEEN ? AND ?";

        String insertSql = "INSERT INTO Kitratedet(code, datum, rate, productcode, projectid, type) " +
                "VALUES (?, ?, ?, ?, ?, ?)";

        DataSource source = daoLocal.getDataSource();
        Stopwatch stopwatch = Stopwatch.createStarted();

        try (Connection connection = source.getConnection();
             PreparedStatement updateStmt = connection.prepareStatement(updateSql);
             PreparedStatement insertStmt = connection.prepareStatement(insertSql)) {

            connection.setAutoCommit(false);

            // 处理更新
            for (Map.Entry<String, Map<BigDecimal, DateRange>> group : updateGroups.entrySet()) {
                String[] keys = group.getKey().split("_");
                String projectId = keys[0];
                String code = keys[1];
                String type = keys[2];
                String productCode = keys[3];

                for (Map.Entry<BigDecimal, DateRange> rateGroup : group.getValue().entrySet()) {
                    BigDecimal rate = rateGroup.getKey();
                    DateRange dateRange = rateGroup.getValue();

                    updateStmt.setBigDecimal(1, rate);
                    updateStmt.setString(2, projectId);
                    updateStmt.setString(3, code);
                    updateStmt.setString(4, type);
                    updateStmt.setString(5, productCode);
                    updateStmt.setDate(6, new java.sql.Date(dateRange.getStart().getTime()));
                    updateStmt.setDate(7, new java.sql.Date(dateRange.getEnd().getTime()));
                    updateStmt.addBatch();
                }
            }
            updateStmt.executeBatch();

            // 处理插入
            for (Kitratedet insert : insertRecords) {
                insertStmt.setString(1, insert.getCode());
                insertStmt.setDate(2, DateUtil.date(insert.getDatum()).toSqlDate());
                insertStmt.setBigDecimal(3, insert.getRate());
                insertStmt.setString(4, insert.getProductcode());
                insertStmt.setString(5, insert.getProjectid());
                insertStmt.setString(6, insert.getType());
                insertStmt.addBatch();
            }
            insertStmt.executeBatch();

            connection.commit();
            log.info("批量更新完成 - 更新组数:{}, 插入数量:{}, 耗时:{}",
                    updateGroups.size(), insertRecords.size(), stopwatch.stop());

        } catch (Exception e) {
            log.error("批量更新失败", e);
            SystemLogTool.getInstance().sendOfficeMsg(
                    RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage())
            );
        }
    }
}
