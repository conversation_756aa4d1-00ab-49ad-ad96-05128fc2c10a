package com.cw.mapper.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.core.CoreAvl;
import com.cw.entity.Coupon;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RedisKey;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-01-22
 */
@Repository
@Slf4j
public class RcouponDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    @Async("commonPool")
    public void insertRcoupons(Coupon coupon) {
        String insertSql = "Insert into Rcoupon(`couponcode`,`projectid`,`datum`,`avl`,`pickup`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            Date maxDate = SpringUtil.getBean(CoreAvl.class).getRcouponMaxDate(coupon.getProjectid());

            cstmt.setString(1, coupon.getCode());
            cstmt.setString(2, coupon.getProjectid());
            cstmt.setDate(3, DateUtil.date(maxDate).toSqlDate());
            cstmt.setInt(4, 999);
            cstmt.setInt(5, 0);
            cstmt.addBatch();

            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            RedisTemplate redisTemplate = (RedisTemplate) SpringUtil.getBean("redisTemplate");
            redisTemplate.opsForValue().set(RedisKey.COUPONAVL + coupon.getCode() + coupon.getProjectid(), 999 + "");
            log.info("提交Rcoupon产品{}库存记录插入{}", coupon.getCode(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }


    /**
     * 批量更新指定项目指定日期指定星期数的景区产品库存
     *
     * @param couponcode
     * @param startDate
     * @param endDate
     * @param projectId
     * @param avl
     * @param week
     */
    public void batchUpdateRcoupons(String couponcode, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week) {
        String updateSql;
        if (CollectionUtil.isNotEmpty(week)) {
            updateSql = "update Rcoupon set avl=?1 where couponcode =?2 and projectid=?3 and datum between ?4 and ?5 and  weekday(datum) in ?6";
            daoLocal.batchOption(updateSql, avl, couponcode, projectId, startDate, endDate, week);
        } else {
            updateSql = "update Rcoupon set avl=?1 where couponcode =?2 and projectid=?3 and datum between ?4 and ?5";
            daoLocal.batchOption(updateSql, avl, couponcode, projectId, startDate, endDate);
        }
    }

    /**
     * 批量更新指定项目指定日期指定星期数的景区产品库存
     *
     * @param couponcode
     * @param projectId
     * @param avl
     */
    public void updateRcoupons(String couponcode, String projectId, Integer avl, Integer pickup) {
        String updateSql;
        if (pickup == null) {
            updateSql = "update Rcoupon set avl=?1 where couponcode =?2 and projectid=?3";
            daoLocal.batchOption(updateSql, avl, couponcode, projectId);
        } else {
            updateSql = "update Rcoupon set avl=?1 , pickup=?2 where couponcode =?3 and projectid=?4";
            daoLocal.batchOption(updateSql, avl, pickup, couponcode, projectId);
        }
        RedisTemplate redisTemplate = (RedisTemplate) SpringUtil.getBean("redisTemplate");
        redisTemplate.opsForValue().set(RedisKey.COUPONAVL + couponcode + projectId, avl + "");
    }
}
