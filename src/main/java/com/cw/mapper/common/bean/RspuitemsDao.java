package com.cw.mapper.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.core.CoreAvl;
import com.cw.entity.Spusitem;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @Describe 产品资源表
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2022-02-22
 */
@Repository
@Slf4j
public class RspuitemsDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    @Async("commonPool")
    public void insertRspuitems(Spusitem spusitem) {
        String insertSql = "Insert into Rspuitems(`spuitem`,`projectid`,`datum`,`avl`,`pickup`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            //Date maxDate = SpringUtil.getBean(CoreAvl.class).getRspuitemsMaxDate(spusitem.getProjectid());
            //景区产品去默认日期
            Date maxDate = SystemUtil.EMPTY_DATETIME;

            cstmt.setString(1, spusitem.getCode());
            cstmt.setString(2, spusitem.getProjectid());
            cstmt.setDate(3, DateUtil.date(maxDate).toSqlDate());
            cstmt.setInt(4, 999);
            cstmt.setInt(5, 0);
            cstmt.addBatch();

            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
            coreAvl.calc2Cache(spusitem.getProjectid(), spusitem.getCode(), maxDate,
                    maxDate, ProdType.WARES, "");
            log.info("提交Rspuitems产品{}库存记录插入{}", spusitem.getCode(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    /**
     * 新建产品默认保存180天库存数据
     *
     * @param spusitem
     */
    @Async("commonPool")
    public void batchInsertRspuitems(Spusitem spusitem) {
        String insertSql = "Insert into Rspuitems(`spuitem`,`projectid`,`datum`,`avl`,`pickup`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            Date maxDate = SpringUtil.getBean(CoreAvl.class).getRspuitemsMaxDate(spusitem.getProjectid());
            Date startDate = DateUtil.beginOfDay(new Date());
            int days = CalculateDate.compareDates(maxDate, startDate).intValue();
            for (int i = 0; i < days; i++) {
                Date datum = CalculateDate.reckonDay(startDate, 5, i);
                cstmt.setString(1, spusitem.getCode());
                cstmt.setString(2, spusitem.getProjectid());
                cstmt.setDate(3, DateUtil.date(datum).toSqlDate());
                cstmt.setInt(4, 999);
                cstmt.setInt(5, 0);
                cstmt.addBatch();

            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
            coreAvl.calc2Cache(spusitem.getProjectid(), spusitem.getCode(), startDate,
                    maxDate, ProdType.WARES, "");
            log.info("提交Rspuitems产品{}库存，{}条记录批量插入{}", spusitem.getCode(), days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    /**
     * 批量更新指定项目指定日期指定星期数的景区产品库存
     *
     * @param spusitem
     * @param startDate
     * @param endDate
     * @param projectId
     * @param avl
     * @param week
     */
    public void batchUpdateRspuitems(String spusitem, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week) {
        String updateSql;
        if (CollectionUtil.isNotEmpty(week)) {
            updateSql = "update Rspuitems set avl=?1 where spuitem =?2 and projectid=?3 and datum between ?4 and ?5 and  weekday(datum) in ?6";
            daoLocal.batchOption(updateSql, avl, spusitem, projectId, startDate, endDate, week);
        } else {
            updateSql = "update Rspuitems set avl=?1 where spuitem =?2 and projectid=?3 and datum between ?4 and ?5";
            daoLocal.batchOption(updateSql, avl, spusitem, projectId, startDate, endDate);
        }
    }

    /**
     * 批量更新指定项目指定日期指定星期数的景区产品库存
     *
     * @param spusitem
     * @param projectId
     * @param avl
     */
    public void updateRspuitems(String spusitem, String projectId, Integer avl, Integer pickup, Date startDate, Date endDate) {
        String updateSql;
        if (pickup == null) {
            updateSql = "update Rspuitems set avl=?1 where spuitem =?2 and projectid=?3";
            daoLocal.batchOption(updateSql, avl, spusitem, projectId);
        } else {
            updateSql = "update Rspuitems set avl=?1 , pickup=?2 where spuitem =?3 and projectid=?4";
            daoLocal.batchOption(updateSql, avl, pickup, spusitem, projectId);
        }
    }
}
