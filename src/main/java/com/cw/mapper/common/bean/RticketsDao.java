package com.cw.mapper.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.TicketgroupCache;
import com.cw.config.exception.CustomException;
import com.cw.core.CoreAvl;
import com.cw.entity.Rtickets;
import com.cw.entity.Ticket;
import com.cw.entity.Ticketgroup;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @Describe 门票资源
 * <AUTHOR> Tony Leung
 * @Create on 2021/11/30 0030
 */
@Repository
@Slf4j
public class RticketsDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * 新建票型保存按最大库存日期
     *
     * @param ticket
     */
    @Async("commonPool")
    public void batchInsertRtickets(Ticket ticket) {
        String insertSql = "Insert into Rtickets(`ticket`,`projectid`,`datum`,`avl`,`pickup`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        boolean lstart = false;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            TicketgroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP);
            Ticketgroup ticketgroup = cache.getRecord(ticket.getProjectid(), ticket.getGroupid());
            if (ticketgroup == null) {
                throw new CustomException(ResultJson.failure(ResultCode.NOT_FOUND).msg("票务大组不存在"));
            }
            Date groupStartDate = ticketgroup.getStartdate();//大组控制有效日期开始时间
            Date groupEndDate = ticketgroup.getEnddate();//大组控制有效日期结束时间
            if (ticketgroup.getStartdate().after(SystemUtil.EMPTY_DATETIME)) {
                lstart = true;
            }
            Date maxDate = SpringUtil.getBean(CoreAvl.class).getRticketMaxDate(ticket.getProjectid());
            Date startDate = DateUtil.beginOfDay(new Date());
            int days = CalculateDate.compareDates(maxDate, startDate).intValue();
            if (days < 0) {
                days = 90;//从今天开始新建90天
            }
            for (int i = 0; i <= days; i++) {
                Date datum = CalculateDate.reckonDay(startDate, 5, i);
                cstmt.setString(1, ticket.getCode());
                cstmt.setString(2, ticket.getProjectid());
                cstmt.setDate(3, DateUtil.date(datum).toSqlDate());
                if (lstart && DateUtil.isIn(datum, groupStartDate, groupEndDate)) {
                    cstmt.setInt(4, 999);
                } else {
                    cstmt.setInt(4, 0);
                }
                cstmt.setInt(5, 0);
                cstmt.addBatch();

            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
            coreAvl.calc2Cache(ticket.getProjectid(), ticket.getCode(), startDate,
                    maxDate, ProdType.TICKET, "");
            log.info("提交Rtickets票型{}，{}条记录批量插入{}", ticket.getCode(), days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    public void batchUpdateRticket(String tCode, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week) {
        String updateSql;
        if (CollectionUtil.isNotEmpty(week)) {
            updateSql = "update Rtickets set avl=?1 where ticket =?2 and projectid=?3 and datum between ?4 and ?5 " +
                    "and WEEKDAY(datum) in ?6";
            daoLocal.batchOption(updateSql, avl, tCode, projectId, startDate, endDate, week);
        } else {
            updateSql = "update Rtickets set avl=?1 where ticket =?2 and projectid=?3 and datum between ?4 and ?5";
            daoLocal.batchOption(updateSql, avl, tCode, projectId, startDate, endDate);
        }
    }

    public void batchUpdRticket(List<Rtickets> postRtickets) {
        String sql = "update Rtickets set `datum`=?, `avl`=?, `ticket`=?, `projectid`=?, `pickup`=? where ticket=? and projectid=? and `datum`=?";
        String insetSql = "insert into  Rtickets(`datum`,`avl`,`ticket`,`projectid`)  VALUES(?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postRtickets.size(); i++) {
                Rtickets postR = postRtickets.get(i);
                if (postR.getSqlid() > 0L) {//更新操作
                    cstmt.setDate(1, DateUtil.date(postR.getDatum()).toSqlDate());
                    cstmt.setInt(2, postR.getAvl());
                    cstmt.setString(3, postR.getTicket());
                    cstmt.setString(4, postR.getProjectid());
                    cstmt.setInt(5, postR.getPickup());
                    cstmt.setString(6, postR.getTicket());
                    cstmt.setString(7, postR.getProjectid());
                    cstmt.setDate(8, DateUtil.date(postR.getDatum()).toSqlDate());
                    cstmt.addBatch();
                } else {//插入
                    insertpstmt.setDate(1, DateUtil.date(postR.getDatum()).toSqlDate());
                    insertpstmt.setInt(2, postR.getAvl());
                    insertpstmt.setString(3, postR.getTicket());
                    insertpstmt.setString(4, postR.getProjectid());
                    insertpstmt.addBatch();
                }

            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次Rticket{}条记录批量更新{}", postRtickets.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    /**
     * @param tCode
     * @param startDate
     * @param endDate
     * @param projectId
     * @param avl
     */
    public void batchInsertRticket(String tCode, Date startDate, Date endDate, String projectId, Integer avl) {
        String insertSql = "Insert into Rtickets(`ticket`,`projectid`,`datum`,`avl`,`pickup`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            startDate = DateUtil.beginOfDay(startDate);
            int days = CalculateDate.compareDates(endDate, startDate).intValue();
            for (int j = 0; j < days; j++) {
                Date datum = CalculateDate.reckonDay(startDate, 5, j);
                cstmt.setString(1, tCode);
                cstmt.setString(2, projectId);
                cstmt.setDate(3, DateUtil.date(datum).toSqlDate());
                cstmt.setInt(4, avl);
                cstmt.setInt(5, 0);
                cstmt.addBatch();
            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
            coreAvl.calc2Cache(projectId, tCode, startDate,
                    endDate, ProdType.TICKET, "");
            log.info("提交Rticket票型{}，{}条记录批量插入{}", tCode, days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
