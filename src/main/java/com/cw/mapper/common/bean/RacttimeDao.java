package com.cw.mapper.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.core.CoreAvl;
import com.cw.entity.Actsite;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Repository
@Slf4j
public class RacttimeDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * 新建预约场所保存按最大库存日期
     *
     * @param actsite
     */
    @Async("commonPool")
    public void batchInsertRacttime(Actsite actsite) {
        String periods = actsite.getPeriods();
        List<String> periodList = Arrays.asList(periods.split(","));
        String insertSql = "Insert into Racttime(`sitecode`,`projectid`,`period`,`datum`,`avl`,`pickup`) VALUES(?,?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            Date maxDate = SpringUtil.getBean(CoreAvl.class).getRacttimeMaxDate(actsite.getProjectid());
            Date startDate = DateUtil.beginOfDay(new Date());
            int days = CalculateDate.compareDates(maxDate, startDate).intValue();
            if (days < 0) {
                days = 90;//从今天开始新建90天
            }
            for (int i = 0; i < periodList.size(); i++) {//多时段循环查询
                for (int j = 0; j < days; j++) {
                    Date datum = CalculateDate.reckonDay(startDate, 5, j);
                    cstmt.setString(1, actsite.getCode());
                    cstmt.setString(2, actsite.getProjectid());
                    cstmt.setString(3, periodList.get(i));
                    cstmt.setDate(4, DateUtil.date(datum).toSqlDate());
                    cstmt.setInt(5, actsite.getDfallownum());
                    cstmt.setInt(6, 0);
                    cstmt.addBatch();
                }
            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
            for (String specode : periodList) {
                coreAvl.calc2Cache(actsite.getProjectid(), actsite.getCode(), startDate,
                        maxDate, ProdType.ACTGROUP, specode);
            }

            log.info("提交Racttime预约场所{}，{}条记录批量插入{}", actsite.getCode(), days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }


    /**
     * 批量更新指定项目指定日期指定星期数的预约场所库存
     *
     * @param sitecode
     * @param period    时段
     * @param startDate
     * @param endDate
     * @param projectId
     * @param avl
     * @param week
     */
    public void batchUpdateRacttime(String sitecode, String period, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week) {
        String updateSql;
        if (CollectionUtil.isNotEmpty(week)) {
            updateSql = "update Racttime set avl=?1 where sitecode =?2 and period=?3 and projectid=?4 and datum between ?5 and ?6 and  weekday(datum) in ?7";
            daoLocal.batchOption(updateSql, avl, sitecode, period, projectId, startDate, endDate, week);

        } else {
            updateSql = "update Racttime set avl=?1 where sitecode =?2 and period=?3 and projectid=?4 and datum between ?5 and ?6";
            daoLocal.batchOption(updateSql, avl, sitecode, period, projectId, startDate, endDate);
        }
    }

    @Async("commonPool")
    public void batchInsertRacttime(String sitecode, String period, Date startDate, Date endDate, String projectId, Integer avl) {
        String insertSql = "Insert into Racttime(`sitecode`,`projectid`,`period`,`datum`,`avl`,`pickup`) VALUES(?,?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            startDate = DateUtil.beginOfDay(startDate);
            int days = CalculateDate.compareDates(endDate, startDate).intValue();
            for (int j = 0; j < days; j++) {
                Date datum = CalculateDate.reckonDay(startDate, 5, j);
                cstmt.setString(1, sitecode);
                cstmt.setString(2, projectId);
                cstmt.setString(3, period);
                cstmt.setDate(4, DateUtil.date(datum).toSqlDate());
                cstmt.setInt(5, avl);
                cstmt.setInt(6, 0);
                cstmt.addBatch();
            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
            coreAvl.calc2Cache(projectId, sitecode, startDate,
                    endDate, ProdType.ACTGROUP, period);
            log.info("提交Racttime预约场所{}，{}条记录批量插入{}", sitecode, days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
