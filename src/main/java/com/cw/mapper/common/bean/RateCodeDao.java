package com.cw.mapper.common.bean;


import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RateCodeCache;
import com.cw.entity.Ratecode;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * @Describe PMS房价码类别
 * <AUTHOR> <PERSON>
 * @Created on 2020/11/2.
 */
@Repository
@Slf4j
public class RateCodeDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    public void saveRateCode(Ratecode syncRateCode) {
        //只有一个房价码 更新
        RateCodeCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RATECODE);
        Ratecode ratecode = cache.getRecord(syncRateCode.getProjectid(), syncRateCode.getCode());
        if (ratecode == null) {
            ratecode = new Ratecode();
        }
        ratecode.setCode(syncRateCode.getCode());
        ratecode.setDescription(syncRateCode.getDescription());
        ratecode.setProjectid(syncRateCode.getProjectid());
        daoLocal.merge(ratecode);
        log.info("同步和更新房价码类别代码,房价码代码：{}，房价码描述:{}", syncRateCode.getCode(), syncRateCode.getDescription());
    }
}
