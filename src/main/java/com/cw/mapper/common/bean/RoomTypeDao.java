package com.cw.mapper.common.bean;


import com.cw.cache.GlobalCache;
import com.cw.entity.Roomtype;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.cw.utils.SystemUtil;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Created on 2020/10/22.
 */
@Repository
@Slf4j
public class RoomTypeDao {

    @Autowired
    private DaoLocal<?> daoLocal;

    public void batchUpdRoomType(List<Roomtype> postRoomType) {
        String sql = "update roomtype set `code`=?, `description`=?, `hotelcode`=?, `maxp`=?, `bedenum`=?, `num`=?  where code=? and projectid=?";
        String insetSql = "insert into  roomtype(`code`,`description`,`hotelcode`,`maxp`, `bedenum`,`num`,`projectid`,`seq`)  VALUES(?,?,?,?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postRoomType.size(); i++) {
                Roomtype postR = postRoomType.get(i);
                Roomtype roomtype = (Roomtype) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE)
                        .getRecord(postR.getProjectid(), postR.getCode());
                if (roomtype == null) {
                    //新增数据
                    insertpstmt.setString(1, postR.getCode());
                    insertpstmt.setString(2, postR.getDescription());
                    insertpstmt.setString(3, postR.getHotelcode());
                    insertpstmt.setBigDecimal(4, postR.getMaxp());
                    insertpstmt.setString(5, postR.getBedenum());
                    insertpstmt.setBigDecimal(6, postR.getNum());
                    insertpstmt.setString(7, postR.getProjectid());
                    insertpstmt.setInt(8, postR.getSeq());
                    insertpstmt.addBatch();
                } else {
                    cstmt.setString(1, postR.getCode());
                    cstmt.setString(2, postR.getDescription());
                    cstmt.setString(3, postR.getHotelcode());
                    cstmt.setBigDecimal(4, postR.getMaxp());
                    cstmt.setString(5, postR.getBedenum());
                    cstmt.setBigDecimal(6, postR.getNum());
                    cstmt.setString(7, postR.getCode());
                    cstmt.setString(8, postR.getProjectid());
                    cstmt.addBatch();
                }

            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次roomType{}条记录批量更新{}", postRoomType.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }


}
