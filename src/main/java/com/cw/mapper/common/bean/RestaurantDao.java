package com.cw.mapper.common.bean;

import com.cw.cache.GlobalCache;
import com.cw.entity.Restaurant;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.cw.utils.SystemUtil;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @<PERSON> on 2022-05-05
 */
@Repository
@Slf4j
public class RestaurantDao {
    @Autowired
    private DaoLocal<?> daoLocal;


    public void batchUpdRestaurant(List<Restaurant> postRestaurant) {
        String sql = "update restaurant set `code`=?, `description`=?, `seq`=?  where code=? and projectid=?";
        String insetSql = "insert into  restaurant(`code`,`description`,`projectid`,`seq`)  VALUES(?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postRestaurant.size(); i++) {
                Restaurant postR = postRestaurant.get(i);
                Restaurant restaurant = (Restaurant) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RESTAURANT)
                        .getRecord(postR.getProjectid(), postR.getCode());

                if (restaurant == null) {
                    //新增数据
                    insertpstmt.setString(1, postR.getCode());
                    insertpstmt.setString(2, postR.getDescription());
                    insertpstmt.setString(3, postR.getProjectid());
                    insertpstmt.setInt(4, postR.getSeq());
                    insertpstmt.addBatch();
                } else {
                    cstmt.setString(1, postR.getCode());
                    cstmt.setString(2, postR.getDescription());
                    cstmt.setInt(3, postR.getSeq());
                    cstmt.setString(4, postR.getCode());
                    cstmt.setString(5, postR.getProjectid());
                    cstmt.addBatch();
                }

            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次restaurant{}条记录批量更新{}", postRestaurant.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
