package com.cw.mapper.common.bean;


import com.cw.cache.GlobalCache;
import com.cw.entity.Hotel;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.cw.utils.SystemUtil;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Created on 2020/10/22.
 */
@Repository
@Slf4j
public class HotelDao {
    @Autowired
    private DaoLocal<?> daoLocal;


    public void batchUpdHotel(List<Hotel> postHotel) {
        String sql = "update hotel set `code`=?, `description`=?, `address`=?, `city`=?, `coordinate`=?, `tel`=?, `seq`=?  where code=? and projectid=?";
        String insetSql = "insert into  hotel(`code`,`description`,`address`,`city`, `coordinate`,`tel`,`projectid`,`seq`)  VALUES(?,?,?,?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postHotel.size(); i++) {
                Hotel postH = postHotel.get(i);
                Hotel hotel = (Hotel) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL)
                        .getRecord(postH.getProjectid(), postH.getCode());

                if (hotel == null) {
                    //新增数据
                    insertpstmt.setString(1, postH.getCode());
                    insertpstmt.setString(2, postH.getDescription());
                    insertpstmt.setString(3, postH.getAddress());
                    insertpstmt.setString(4, postH.getCity());
                    insertpstmt.setString(5, postH.getCoordinate());
                    insertpstmt.setString(6, postH.getTel());
                    insertpstmt.setString(7, postH.getProjectid());
                    insertpstmt.setInt(8, postH.getSeq());
                    insertpstmt.addBatch();
                } else {
                    cstmt.setString(1, postH.getCode());
                    cstmt.setString(2, postH.getDescription());
                    cstmt.setString(3, postH.getAddress());
                    cstmt.setString(4, postH.getCity());
                    cstmt.setString(5, postH.getCoordinate());
                    cstmt.setString(6, postH.getTel());
                    cstmt.setInt(7, postH.getSeq());
                    cstmt.setString(8, postH.getCode());
                    cstmt.setString(9, postH.getProjectid());
                    cstmt.addBatch();
                }

            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次hotel{}条记录批量更新{}", postHotel.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
