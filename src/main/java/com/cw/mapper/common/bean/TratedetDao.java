package com.cw.mapper.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.core.CorePrice;
import com.cw.entity.Ticket;
import com.cw.entity.Tratedet;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @Describe 门票价格
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/30 0030
 */
@Repository
@Slf4j
public class TratedetDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * 新建保存最大日期数据
     *
     * @param ticket
     */
    @Async("commonPool")
    public void batchInsertTratedet(Ticket ticket) {
        String rateCode = CorePrice.getRateCode(ticket.getProjectid(), "");
        String insertSql = "Insert into Tratedet(`productcode`,`projectid`, `code`,`datum`,`rate`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            //获取最大日期
            Date maxDate = SpringUtil.getBean(CorePrice.class).getTratedetMaxDate(ticket.getProjectid());
            Date startDate = DateUtil.beginOfDay(new Date());
            int days = CalculateDate.compareDates(maxDate, startDate).intValue();
            if (days < 0) {
                days = 90;//从今天开始新建90天价格
            }
            for (int i = 0; i <= days; i++) {
                Date datum = CalculateDate.reckonDay(startDate, 5, i);
                cstmt.setString(1, ticket.getCode());
                cstmt.setString(2, ticket.getProjectid());
                cstmt.setString(3, rateCode);
                cstmt.setDate(4, DateUtil.date(datum).toSqlDate());
                cstmt.setBigDecimal(5, ticket.getShowprice());
                cstmt.addBatch();

            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
            corePrice.calc2Cache(ticket.getProjectid(), rateCode, ProdType.TICKET, ticket.getCode(), startDate,
                    maxDate);
            log.info("提交Tratedet票型{}价格，{}条记录批量插入{}", ticket.getCode(), days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }


    /**
     * 批量更新门票价格
     *
     * @param tCode
     * @param startDate
     * @param endDate
     * @param rateCode
     * @param projectId
     * @param price
     * @param week  0-6 0是星期一
     */
    public void batchupdateTratedet(String tCode, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price, List<Integer> week) {
        try {
            String updateSql;
            if (CollectionUtil.isNotEmpty(week)) {
                updateSql = "update Tratedet set rate=?1 where productcode =?2 and projectid=?3 and datum between ?4 and ?5 " +
                        "and code=?6 and  WEEKDAY(datum) in ?7";
                daoLocal.batchOption(updateSql, price, tCode, projectId, startDate, endDate, rateCode, week);
            } else {
                updateSql = "update Tratedet set rate=?1 where productcode =?2 and projectid=?3 and datum between ?4 and ?5 " +
                        "and code=?6";
                daoLocal.batchOption(updateSql, price, tCode, projectId, startDate, endDate, rateCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void batchUpdTratedet(List<Tratedet> postTratedet) {

        String sql = "update Tratedet set `code`=?, `datum`=?, `rate`=?, `productcode`=?, `projectid`=? where code=? and projectid=? " +
                "and `productcode`=? and `datum`=?";
        String insetSql = "insert into  Tratedet(`code`,`datum`,`rate`,`productcode`, `projectid`)  VALUES(?,?,?,?,?)";

        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postTratedet.size(); i++) {
                Tratedet postT = postTratedet.get(i);
                if (postT.getSqlid() > 0L) {//更新
                    cstmt.setString(1, postT.getCode());
                    cstmt.setDate(2, DateUtil.date(postT.getDatum()).toSqlDate());
                    cstmt.setBigDecimal(3, postT.getRate());
                    cstmt.setString(4, postT.getProductcode());
                    cstmt.setString(5, postT.getProjectid());
                    cstmt.setString(6, postT.getCode());
                    cstmt.setString(7, postT.getProjectid());
                    cstmt.setString(8, postT.getProductcode());
                    cstmt.setDate(9, DateUtil.date(postT.getDatum()).toSqlDate());
                    cstmt.addBatch();
                } else {
                    insertpstmt.setString(1, postT.getCode());
                    insertpstmt.setDate(2, DateUtil.date(postT.getDatum()).toSqlDate());
                    insertpstmt.setBigDecimal(3, postT.getRate());
                    insertpstmt.setString(4, postT.getProductcode());
                    insertpstmt.setString(5, postT.getProjectid());
                    insertpstmt.addBatch();
                }


            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次Tratedet{}条记录批量更新{}", postTratedet.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    /**
     * @param tCode
     * @param startDate
     * @param endDate
     * @param rateCode
     * @param projectId
     * @param price
     */
    public void batchInsertTratedet(String tCode, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price) {
        String insertSql = "Insert into Tratedet(`code`,`projectid`, `productcode`,`datum`,`rate`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            //获取最大日期
            int days = CalculateDate.compareDates(endDate, startDate).intValue();
            for (int j = 0; j < days; j++) {
                Date datum = CalculateDate.reckonDay(DateUtil.beginOfDay(startDate), 5, j);
                cstmt.setString(1, rateCode);
                cstmt.setString(2, projectId);
                cstmt.setString(3, tCode);
                cstmt.setDate(4, DateUtil.date(datum).toSqlDate());
                cstmt.setBigDecimal(5, price);
                cstmt.addBatch();

            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
            corePrice.calc2Cache(projectId, rateCode, ProdType.TICKET, tCode, startDate,
                    endDate);
            log.info("提交Tratedet票型{}价格，{}条记录批量插入{}", tCode, days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
