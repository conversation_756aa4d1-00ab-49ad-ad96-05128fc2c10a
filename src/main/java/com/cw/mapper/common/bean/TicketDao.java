package com.cw.mapper.common.bean;


import com.cw.cache.GlobalCache;
import com.cw.entity.Ticket;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.cw.utils.SystemUtil;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Created on 2020/10/22.
 */
@Repository
@Slf4j
public class TicketDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    public void batchUpdTicket(List<Ticket> postTicket) {
        String sql = "update ticket set code=?, description=?, `type`=?  where code=? and projectid=?";
        String insetSql = "insert into  ticket(`code`,`description`,`projectid`,`type`,`seq`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        String groupid = "";
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            //先默认绑定第一个个大组
            //List<Ticketgroup> ticketgroupList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP)
            //        .getDataList(postTicket.get(0).getProjectid());
            //if (CollectionUtil.isNotEmpty(ticketgroupList)) {
            //    groupid = ticketgroupList.get(0).getCode();
            //}
            for (int i = 0; i < postTicket.size(); i++) {
                Ticket postT = postTicket.get(i);
                Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET)
                        .getRecord(postT.getProjectid(), postT.getCode());
                if (ticket == null) {
                    //新增数据
                    insertpstmt.setString(1, postT.getCode());
                    insertpstmt.setString(2, postT.getDescription());
                    insertpstmt.setString(3, postT.getProjectid());
                    insertpstmt.setString(4, postT.getType());
                    insertpstmt.setInt(5, postT.getSeq());
                    insertpstmt.addBatch();
                } else {
                    cstmt.setString(1, postT.getCode());
                    cstmt.setString(2, postT.getDescription());
                    cstmt.setString(3, postT.getType());
                    cstmt.setString(4, postT.getCode());
                    cstmt.setString(5, postT.getProjectid());
                    cstmt.addBatch();
                }

            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次ticket{}条记录批量更新{}", postTicket.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
