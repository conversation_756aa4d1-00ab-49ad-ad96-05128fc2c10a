package com.cw.mapper.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.core.CorePrice;
import com.cw.entity.Roomtype;
import com.cw.entity.Rratedet;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @Describe 房型房价
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/30 0030
 */
@Repository
@Slf4j
public class RratedetDao {
    @Autowired
    private DaoLocal<?> daoLocal;


    /**
     * 新建房型默认录入最大日期  使用PMS后不需要此项操作
     *
     * @param roomtype
     */
    @Async("commonPool")
    public void batchInsertRratedet(Roomtype roomtype) {
        String rateCode = CorePrice.getRateCode(roomtype.getProjectid(), "");
        String insertSql = "Insert into Rratedet(`productcode`,`projectid`, `code`,`datum`,`rate`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            Date maxDate = SpringUtil.getBean(CorePrice.class).getRratedetMaxDate(roomtype.getProjectid());
            Date startDate = DateUtil.beginOfDay(new Date());
            int days = CalculateDate.compareDates(maxDate, startDate).intValue();
            //判断是否过期 测试环境不会推送房价，不会更新最大日期
            if (days < 0) {
                days = 90;//从今天开始新建90天价格
            }
            for (int i = 0; i <= days; i++) {
                Date datum = CalculateDate.reckonDay(startDate, 5, i);
                cstmt.setString(1, roomtype.getCode());
                cstmt.setString(2, roomtype.getProjectid());
                cstmt.setString(3, rateCode);
                cstmt.setDate(4, DateUtil.date(datum).toSqlDate());
                cstmt.setBigDecimal(5, roomtype.getShowprice());
                cstmt.addBatch();

            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
            corePrice.calc2Cache(roomtype.getProjectid(), rateCode, ProdType.ROOM, roomtype.getCode(), startDate,
                    maxDate);
            log.info("提交Rratedet房型{}价格，{}条记录批量插入{}", roomtype.getCode(), days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();

        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    /**
     * 按日期区间 指定星期数更新客房房价
     *
     * @param roomType
     * @param startDate
     * @param endDate
     * @param projectId
     * @param price
     * @param week
     */
    public void batchUpdateRratedet(String roomType, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price, List<Integer> week) {
        String updateSql;
        if (CollectionUtil.isNotEmpty(week)) {
            updateSql = "update Rratedet set rate=?1 where productcode =?2 and projectid=?3 and datum between ?4 and ?5 and code =?6 and" +
                    " weekday(datum) in ?7";
            daoLocal.batchOption(updateSql, price, roomType, projectId, startDate, endDate, rateCode, week);
        } else {
            updateSql = "update Rratedet set rate=?1 where productcode =?2 and projectid=?3 and datum between ?4 and ?5 and code =?6 ";
            daoLocal.batchOption(updateSql, price, roomType, projectId, startDate, endDate, rateCode);
        }
    }

    /**
     * PMS同步更新
     *
     * @param postRecords
     */
    public void batchUpdRratedet(List<Rratedet> postRecords) {
        String sql = "update Rratedet set `rate`=?, `datum`=?, `projectid`=?, `code`=?, `productcode`=? where code=? and projectid=? " +
                "and `productcode`=? and `datum`=?";
        String insetSql = "insert into  Rratedet(`rate`,`datum`,`projectid`,`code`, `productcode`)  VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postRecords.size(); i++) {
                Rratedet postR = postRecords.get(i);
                if (postR.getSqlid() > 0L) { //更新操作
                    cstmt.setBigDecimal(1, postR.getRate());
                    cstmt.setDate(2, DateUtil.date(postR.getDatum()).toSqlDate());
                    cstmt.setString(3, postR.getProjectid());
                    cstmt.setString(4, postR.getCode());
                    cstmt.setString(5, postR.getProductcode());
                    cstmt.setString(6, postR.getCode());
                    cstmt.setString(7, postR.getProjectid());
                    cstmt.setString(8, postR.getProductcode());
                    cstmt.setDate(9, DateUtil.date(postR.getDatum()).toSqlDate());
                    cstmt.addBatch();
                } else {//插入操作
                    insertpstmt.setBigDecimal(1, postR.getRate());
                    insertpstmt.setDate(2, DateUtil.date(postR.getDatum()).toSqlDate());
                    insertpstmt.setString(3, postR.getProjectid());
                    insertpstmt.setString(4, postR.getCode());
                    insertpstmt.setString(5, postR.getProductcode());
                    insertpstmt.addBatch();
                }

            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次ratedet{}条记录批量更新{}", postRecords.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
