package com.cw.mapper.common.bean;

import cn.hutool.core.date.DateUtil;
import com.cw.cache.GlobalCache;
import com.cw.entity.Productkit;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SystemUtil;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @Describe 套餐
 * <AUTHOR> <PERSON>
 * @Created on 2020/10/22.
 */
@Repository
@Slf4j
public class ProductkitDao {
    @Autowired
    private DaoLocal<?> daoLocal;


    public void batchUpdProductKit(List<Productkit> postKit) {
        String sql = "update productkit set `code`=?, `description`=?, `startdate`=?, `enddate`=?, `valid`=?  where code=? and projectid=?";
        String insetSql = "insert into  productkit(`code`,`description`,`startdate`,`enddate`, `valid`,`projectid`) VALUES(?,?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postKit.size(); i++) {
                Productkit postK = postKit.get(i);
                //判断同步过来数据是否是默认日期数据，不是则代表长期售卖
                if (!postK.getStartdate().before(SystemUtil.EMPTY_DATETIME)) {
                    postK.setStartdate(postK.getStartdate());
                    postK.setEnddate(postK.getEnddate());
                } else {
                    //长期售卖套餐则先设置一年时间
                    postK.setStartdate(CalculateDate.returnDate_ZeroTime(new Date()));
                    postK.setEnddate(CalculateDate.reckonDay(new Date(), 1, 1));
                }
                Productkit productkit = (Productkit) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT)
                        .getRecord(postK.getProjectid(), postK.getCode());
                if (productkit == null) {
                    //新增数据
                    insertpstmt.setString(1, postK.getCode());
                    insertpstmt.setString(2, postK.getDescription());
                    insertpstmt.setDate(3, DateUtil.date(postK.getStartdate()).toSqlDate());
                    insertpstmt.setDate(4, DateUtil.date(postK.getEnddate()).toSqlDate());
                    insertpstmt.setBoolean(5, postK.getValid());
                    insertpstmt.setString(6, postK.getProjectid());
                    insertpstmt.addBatch();
                } else {
                    cstmt.setString(1, postK.getCode());
                    cstmt.setString(2, postK.getDescription());
                    cstmt.setDate(3, DateUtil.date(postK.getStartdate()).toSqlDate());
                    cstmt.setDate(4, DateUtil.date(postK.getEnddate()).toSqlDate());
                    cstmt.setBoolean(5, postK.getValid());
                    cstmt.setString(6, postK.getCode());
                    cstmt.setString(7, postK.getProjectid());
                    cstmt.addBatch();
                }

            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次productkit{}条记录批量更新{}", postKit.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
