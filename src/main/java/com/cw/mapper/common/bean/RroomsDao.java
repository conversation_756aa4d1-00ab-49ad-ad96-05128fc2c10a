package com.cw.mapper.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.core.CoreAvl;
import com.cw.entity.Roomtype;
import com.cw.entity.Rrooms;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @Describe 房型资源
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/30 0030
 */
@Repository
@Slf4j
public class RroomsDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * 新建房型默认保存最大日期
     *
     * @param roomtype
     */
    @Async("commonPool")
    public void batchInsertRrooms(Roomtype roomtype) {
        String insertSql = "Insert into Rrooms(`rmtype`,`projectid`,`datum`,`avl`,`pickup`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            Date maxDate = SpringUtil.getBean(CoreAvl.class).getRroomsMaxDate(roomtype.getProjectid());
            Date startDate = DateUtil.beginOfDay(new Date());
            int days = CalculateDate.compareDates(maxDate, startDate).intValue();
            if (days < 0) {
                days = 90;//从今天开始新建90天
            }
            for (int i = 0; i <= days; i++) {
                Date datum = CalculateDate.reckonDay(startDate, 5, i);
                cstmt.setString(1, roomtype.getCode());
                cstmt.setString(2, roomtype.getProjectid());
                cstmt.setDate(3, DateUtil.date(datum).toSqlDate());
                cstmt.setInt(4, roomtype.getNum().intValue());
                cstmt.setInt(5, 0);
                cstmt.addBatch();

            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CoreAvl coreAvl = SpringUtil.getBean(CoreAvl.class);
            coreAvl.calc2Cache(roomtype.getProjectid(), roomtype.getCode(), startDate,
                    maxDate, ProdType.ROOM, "");
            log.info("提交Rrooms房型{}库存，{}条记录批量插入{}", roomtype.getCode(), days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    /**
     * 批量更新指定项目指定日期指定星期数的客房库存
     *
     * @param roomType
     * @param startDate
     * @param endDate
     * @param projectId
     * @param avl
     * @param week
     */
    public void batchUpdateRrooms(String roomType, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week) {
        String updateSql;
        if (CollectionUtil.isNotEmpty(week)) {
            updateSql = "update Rrooms set avl=?1 where rmtype =?2 and projectid=?3 and datum between ?4 and ?5 and  weekday(datum) in ?6";
            daoLocal.batchOption(updateSql, avl, roomType, projectId, startDate, endDate, week);
        } else {
            updateSql = "update Rrooms set avl=?1 where rmtype =?2 and projectid=?3 and datum between ?4 and ?5";
            daoLocal.batchOption(updateSql, avl, roomType, projectId, startDate, endDate);
        }
    }

    public void batchUpdRrooms(List<Rrooms> postRrooms) {
        String sql = "update Rrooms set `avl`=?, `rmtype`=?, `datum`=?, `projectid`=?, `pickup`=? where  projectid=? and `rmtype`=? and `datum`=?";
        String insetSql = "insert into  Rrooms(`avl`,`rmtype`,`datum`,`projectid`)  VALUES(?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postRrooms.size(); i++) {
                Rrooms postR = postRrooms.get(i);
                if (postR.getSqlid() > 0L) {//更新操作
                    cstmt.setInt(1, postR.getAvl());
                    cstmt.setString(2, postR.getRmtype());
                    cstmt.setDate(3, DateUtil.date(postR.getDatum()).toSqlDate());
                    cstmt.setString(4, postR.getProjectid());
                    cstmt.setInt(5, postR.getPickup());
                    cstmt.setString(6, postR.getProjectid());
                    cstmt.setString(7, postR.getRmtype());
                    cstmt.setDate(8, DateUtil.date(postR.getDatum()).toSqlDate());
                    cstmt.addBatch();
                } else {
                    insertpstmt.setInt(1, postR.getAvl());
                    insertpstmt.setString(2, postR.getRmtype());
                    insertpstmt.setDate(3, DateUtil.date(postR.getDatum()).toSqlDate());
                    insertpstmt.setString(4, postR.getProjectid());
                    insertpstmt.addBatch();
                }


            }
            insertpstmt.executeBatch();
            cstmt.executeBatch();
            connection.commit();
            log.info("提交一次rrooms{}条记录批量更新{}", postRrooms.size(), stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
