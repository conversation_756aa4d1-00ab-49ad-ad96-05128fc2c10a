package com.cw.mapper.common.bean;

import cn.hutool.core.date.DateUtil;
import com.cw.entity.Rkits;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * @Describe 套餐库存
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2021/11/30 0030
 */
@Repository
@Slf4j
public class RkitsDao {
    @Autowired
    private DaoLocal<?> daoLocal;



    public void batchUpdRkits(List<Rkits> postRkits) {

        String sql = "update Rkits set `datum`=?, `avl`=?, `kitcode`=?, `projectid`=?, `pickup`=? where  projectid=? and `kitcode`=? and `datum`=?";
        String insetSql = "insert into  Rkits(`datum`,`avl`,`kitcode`,`projectid`)  VALUES(?,?,?,?)";

        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        PreparedStatement insertpstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(sql);
            insertpstmt = connection.prepareStatement(insetSql);
            for (int i = 0; i < postRkits.size(); i++) {
                Rkits postR = postRkits.get(i);
                if (postR.getSqlid() > 0L) {//更新

                    cstmt.setDate(1, DateUtil.date(postR.getDatum()).toSqlDate());
                    cstmt.setInt(2, postR.getAvl());
                    cstmt.setString(3, postR.getKitcode());
                    cstmt.setString(4, postR.getProjectid());
                    cstmt.setInt(5, postR.getPickup());
                    cstmt.setString(6, postR.getProjectid());
                    cstmt.setString(7, postR.getKitcode());
                    cstmt.setDate(8, DateUtil.date(postR.getDatum()).toSqlDate());
                    cstmt.addBatch();
                } else {
                    insertpstmt.setDate(1, DateUtil.date(postR.getDatum()).toSqlDate());
                    insertpstmt.setInt(2, postR.getAvl());
                    insertpstmt.setString(3, postR.getKitcode());
                    insertpstmt.setString(4, postR.getProjectid());
                    insertpstmt.addBatch();
                }

            }
            int[] insertCount = insertpstmt.executeBatch();
            int[] updateCount = cstmt.executeBatch();
            connection.commit();
            log.info("提交一次Rkits{}条记录批量更新{},更新:{},插入:{}", postRkits.size(), stopwatch.stop(), updateCount.length, insertCount.length);
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (insertpstmt != null) {
                    insertpstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
