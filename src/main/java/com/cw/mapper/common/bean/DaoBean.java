package com.cw.mapper.common.bean;

import cn.hutool.core.bean.BeanUtil;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.sql.DataSource;
import javax.transaction.Transactional;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;

@Repository
@Transactional
public class DaoBean<T> implements DaoLocal<T> {

    @PersistenceContext
    private EntityManager manager;  //还要考虑多数据源的情况


    @Override
    public <A> List<A> getObjectList(String jpql, Object... objects) {
        Query query = manager.createQuery(jpql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        List<A> list = null;
        try {
            list = query.getResultList();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    public <A> List<A> getObjectListWithLimit(String jpql, int maxSize, Object... objects) {
        Query query = manager.createQuery(jpql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        query.setMaxResults(maxSize);
        List<A> list = null;
        try {
            list = query.getResultList();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public <A> List<A> queryObjectListWithLimit(String jpql, int maxSize, Map<String, Object> params) {
        Query query = manager.createNativeQuery(jpql);
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                query.setParameter(entry.getKey(), entry.getValue());
            }
        }
        query.setMaxResults(maxSize);
        List<A> list = null;
        try {
            list = query.getResultList();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public <A> List<A> getProjectObjectList(Class<A> t, String projectId) {
        String jpql = "from {0} where projectid=?1 ";
        jpql = MessageFormat.format(jpql, t.getSimpleName());
        return getObjectList(jpql, projectId);
    }

    @Override
    public <A> List<A> getNativeObjectList(String jpql, Object... objects) {
        List<A> list = null;
        try {
            Query query = manager.createNativeQuery(jpql);
            if (objects != null && objects.length > 0) {
                for (int i = 0; i < objects.length; i++) {
                    query.setParameter(i + 1, objects[i]);
                }
            }
            list = query.getResultList();
        } catch (Exception e) {
            System.err.println("错误的SQL: " + jpql);
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public <T> T merge(T obj) {
        return manager.merge(obj);
    }

    @Override
    public void persist(Object obj) {
        manager.persist(obj);
        manager.flush();
        manager.clear();
    }

    @Override
    public <A> A find(Class<A> cls, Long sqlid) {
        return manager.find(cls, sqlid);
    }

    @Override
    public <A> A findNoCache(Class<A> cls, Long sqlid) {
        manager.clear();
        return manager.find(cls, sqlid);
    }

    @Override
    public <A> A getObject(String jpql, Object... objects) {
        Query query = manager.createQuery(jpql);
        query.setMaxResults(1);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        List list = query.getResultList();
        if (list == null || list.isEmpty()) {
            return null;
        }
        return (A) list.get(0);
    }

    @Override
    public <A> A getNativeObject(String sql, Object... objects) {
        Query query = manager.createNativeQuery(sql);
        query.setMaxResults(1);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        List list = query.getResultList();
        if (list == null || list.isEmpty()) {
            return null;
        }
        return (A) list.get(0);
    }

    @Override
    public int batchOption(String jpql, Object... objects) {
        Query query = manager.createQuery(jpql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        int result = query.executeUpdate();
        return result;
    }

    @Override
    public int batchNativeOption(String sql, Object... objects) {
        Query query = manager.createNativeQuery(sql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        int result = query.executeUpdate();
        return result;
    }

    @Override
    public int batchNativeOptionWithLimit(String sql, int maxSize, Object... objects) {
        Query query = manager.createNativeQuery(sql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        query.setMaxResults(maxSize);
        int result = query.executeUpdate();
        return result;
    }

    @Override
    public <T> void removeById(T t) {
        Class c = t.getClass();
        Long sqlid = 0L;
        sqlid = (Long) BeanUtil.getFieldValue(t, "sqlid");
        Query q = manager.createQuery(String.format("delete from %s where sqlid= %d", c.getSimpleName(), sqlid));
        q.executeUpdate();
    }

    @Override
    public int getCountOption(String jpql, Object... objects) {
        Query query = manager.createQuery(jpql);
        if (objects != null && objects.length > 0) {
            for (int i = 0; i < objects.length; i++) {
                query.setParameter(i + 1, objects[i]);
            }
        }
        Object count = query.getSingleResult();
        int result = 0;
        if (count != null) {
            result = Integer.parseInt(count.toString());
        }
        return result;
    }

    @Override
    public DataSource getDataSource() {
        DataSource dataSource = SpringUtil.getBean(DataSource.class);
        return dataSource;
    }


}
