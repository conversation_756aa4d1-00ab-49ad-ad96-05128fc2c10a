package com.cw.mapper.common.bean;


import com.cw.entity.Factor;
import com.cw.mapper.common.DaoLocal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Repository;

import java.util.concurrent.Future;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Created on 2020/10/22.
 */
@Repository
@Slf4j
public class FactorDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    public Future<String> saveFactor(Factor syncFactor) {
        Factor factor = null;
        String seekSql = "from Factor as f where f.code=?1";
        factor = daoLocal.getObject(seekSql, syncFactor.getCode());
        if (factor == null) {
            factor = new Factor();
        }
        factor.setCode(syncFactor.getCode());
        factor.setDescription(syncFactor.getDescription());
        factor.setType(syncFactor.getType());
        factor.setProjectid(syncFactor.getProjectid());
        daoLocal.merge(factor);
        log.info("同步和更新基础代码,基础代码：{}，基础代码描述:{}，基础代码类型：{}",
                syncFactor.getCode(), syncFactor.getDescription(), syncFactor.getType());
        return new AsyncResult<>("OK");

    }
}
