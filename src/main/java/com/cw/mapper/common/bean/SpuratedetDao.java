package com.cw.mapper.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.core.CorePrice;
import com.cw.entity.Spusitem;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @Describe 产品价格表操作
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2022-02-22
 */
@Repository
@Slf4j
public class SpuratedetDao {
    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * 新建产品默认录入景区产品
     *
     * @param spusitem
     */
    @Async("commonPool")
    public void batchInsertSpuratedet(Spusitem spusitem) {
        String rateCode = CorePrice.getRateCode(spusitem.getProjectid(), "");
        String insertSql = "Insert into Spuratedet(`productcode`,`projectid`, `code`,`datum`,`rate`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            Date maxDate = SpringUtil.getBean(CorePrice.class).getSpuratedetMaxDate(spusitem.getProjectid());
            Date startDate = DateUtil.beginOfDay(new Date());
            int days = CalculateDate.compareDates(maxDate, startDate).intValue();
            if (days < 0) {
                days = 90;//从今天开始新建90天价格
            }
            for (int i = 0; i < days; i++) {
                Date datum = CalculateDate.reckonDay(startDate, 5, i);
                cstmt.setString(1, spusitem.getCode());
                cstmt.setString(2, spusitem.getProjectid());
                cstmt.setString(3, rateCode);
                cstmt.setDate(4, DateUtil.date(datum).toSqlDate());
                cstmt.setBigDecimal(5, spusitem.getShowprice());
                cstmt.addBatch();

            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
            corePrice.calc2Cache(spusitem.getProjectid(), rateCode, ProdType.WARES, spusitem.getCode(), startDate,
                    maxDate);
            log.info("提交Spusitem产品{}价格，{}条记录批量插入{}", spusitem.getCode(), days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    /**
     * 按日期区间 指定星期数更新景区产品房价
     *
     * @param spusitem
     * @param startDate
     * @param endDate
     * @param projectId
     * @param price
     * @param week
     */
    public void batchUpdateSpuratedet(String spusitem, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price, List<Integer> week) {
        String updateSql;
        if (CollectionUtil.isNotEmpty(week)) {
            updateSql = "update Spuratedet set rate=?1 where productcode =?2 and projectid=?3 and datum between ?4 and ?5 and code =?6 and" +
                    " weekday(datum) in ?7";
            daoLocal.batchOption(updateSql, price, spusitem, projectId, startDate, endDate, rateCode, week);
        } else {
            updateSql = "update Spuratedet set rate=?1 where productcode =?2 and projectid=?3 and datum between ?4 and ?5 and code =?6 ";
            daoLocal.batchOption(updateSql, price, spusitem, projectId, startDate, endDate, rateCode);
        }
    }

}
