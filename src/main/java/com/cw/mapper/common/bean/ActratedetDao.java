package com.cw.mapper.common.bean;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.core.CorePrice;
import com.cw.entity.Actsite;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Repository
@Slf4j
public class ActratedetDao {

    @Autowired
    private DaoLocal<?> daoLocal;

    /**
     * 新建预约场所保存最大日期数据价格
     *
     * @param actsite
     */
    @Async("commonPool")
    public void batchInsertActratedet(Actsite actsite) {
        String periods = actsite.getPeriods();
        List<String> periodList = Arrays.asList(periods.split(","));
        String rateCode = CorePrice.getRateCode(actsite.getProjectid(), "");
        String insertSql = "Insert into Actratedet(`sitecode`,`projectid`, `period`,`datum`,`rate`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            //获取最大日期
            Date maxDate = SpringUtil.getBean(CorePrice.class).getActratedetMaxDate(actsite.getProjectid());
            Date startDate = DateUtil.beginOfDay(new Date());
            int days = CalculateDate.compareDates(maxDate, startDate).intValue();
            for (int i = 0; i < periodList.size(); i++) {//多时段循环查询
                for (int j = 0; j < days; j++) {
                    Date datum = CalculateDate.reckonDay(startDate, 5, j);
                    cstmt.setString(1, actsite.getCode());
                    cstmt.setString(2, actsite.getProjectid());
                    cstmt.setString(3, periodList.get(i));
                    cstmt.setDate(4, DateUtil.date(datum).toSqlDate());
                    cstmt.setBigDecimal(5, actsite.getDfprice());
                    cstmt.addBatch();

                }
            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
            corePrice.calc2Cache(actsite.getProjectid(), rateCode, ProdType.ACTGROUP, actsite.getCode(), startDate,
                    maxDate);
            log.info("提交Actratedet预约场所{}价格，{}条记录批量插入{}", actsite.getCode(), days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }

    /**
     * 按日期区间 指定星期数更新预约场所房价
     *
     * @param sitecode
     * @param period
     * @param startDate
     * @param endDate
     * @param projectId
     * @param price
     * @param week
     */
    public void batchUpdateActratedet(String sitecode, String period, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price, List<Integer> week) {
        String updateSql;
        if (CollectionUtil.isNotEmpty(week)) {
            updateSql = "update Actratedet set rate=?1 where sitecode =?2 and period=?3 and projectid=?4 and datum between ?5 and ?6  and" +
                    " weekday(datum) in ?7";
            daoLocal.batchOption(updateSql, price, sitecode, period, projectId, startDate, endDate, week);
        } else {
            updateSql = "update Actratedet set rate=?1 where sitecode =?2 and period=?3 and projectid=?4 and datum between ?5 and ?6";
            daoLocal.batchOption(updateSql, price, sitecode, period, projectId, startDate, endDate);
        }
    }

    public void batchInsertActratedet(String sitecode, String period, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price) {

        String insertSql = "Insert into Actratedet(`sitecode`,`projectid`, `period`,`datum`,`rate`) VALUES(?,?,?,?,?)";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement cstmt = null;
        Connection connection = null;
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            cstmt = connection.prepareStatement(insertSql);
            //获取最大日期
            int days = CalculateDate.compareDates(endDate, startDate).intValue();
            for (int j = 0; j < days; j++) {
                Date datum = CalculateDate.reckonDay(DateUtil.beginOfDay(startDate), 5, j);
                cstmt.setString(1, sitecode);
                cstmt.setString(2, projectId);
                cstmt.setString(3, period);
                cstmt.setDate(4, DateUtil.date(datum).toSqlDate());
                cstmt.setBigDecimal(5, price);
                cstmt.addBatch();

            }
            cstmt.executeBatch();
            connection.commit();
            //通知缓存
            CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
            corePrice.calc2Cache(projectId, rateCode, ProdType.ACTGROUP, sitecode, startDate,
                    endDate);
            log.info("提交Actratedet预约场所{}价格，{}条记录批量插入{}", sitecode, days, stopwatch.stop());
        } catch (Exception e) {
            e.printStackTrace();
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg(e.getMessage()));
        } finally {
            stopwatch.reset();
            try {
                if (cstmt != null) {
                    cstmt.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭连接失败");
                e.printStackTrace();
            }
        }
    }
}
