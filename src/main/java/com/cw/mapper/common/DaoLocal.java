package com.cw.mapper.common;

import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

@Service
public interface DaoLocal<T> {

    <A> List<A> getObjectList(String jpql, Object... objects);


    /**
     * 获取配置表的
     *
     * @param t
     * @param projectId
     * @param <A>
     * @return
     */
    <A> List<A> getProjectObjectList(Class<A> t, String projectId);

    <A> List<A> getNativeObjectList(String jpql, Object... objects);

    public <A> A getNativeObject(String sql, Object... objects);


    <A> A getObject(String jpql, Object... objects);


    <A> List<A> getObjectListWithLimit(String jpql, int maxSize, Object... objects);

    <A> List<A> queryObjectListWithLimit(String jpql, int maxSize, Map<String, Object> params);

    <A> A find(Class<A> cls, Long sqlid);

    public <A> A findNoCache(Class<A> cls, Long sqlid);

    <T> T merge(T obj);

    void persist(Object obj);

    int batchOption(String jpql, Object... objects);

    int batchNativeOption(String sql, Object... objects);

    int batchNativeOptionWithLimit(String sql, int maxSize, Object... objects);

    /**
     * 获取查询个数
     *
     * @param jpql
     * @param objects
     * @return
     */
    int getCountOption(String jpql, Object... objects);

    <T> void removeById(T t);

    DataSource getDataSource();

//    public DataSource getLogSource();

}
