package com.cw.entity;

import cn.hutool.core.util.StrUtil;

import javax.persistence.*;
import java.io.Serializable;
@SuppressWarnings("serial")
@Entity
@Table(name = "Actperiod")
public class Actperiod implements Serializable {
     private Long sqlid = 0L;/* SQL主键 */
     private String projectid = "";/* 项目id */
     private String code = "";/* 时段代码 */
     private String description = "";/* 描述 */
     private String time = "";/* 用英文-分割 */

     @Id
     @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "Actperiod")
     @Column(length = 10, nullable = false, name = "[sqlid]")
     public Long getSqlid(){
        return sqlid;
     }

     public void setSqlid(Long sqlid){
        this.sqlid = sqlid;
     }

     @Column(length = 20, nullable = true, name = "[projectid]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '项目id' ")
     public String getProjectid(){
         return projectid == null ? StrUtil.EMPTY : projectid;
     }

     public void setProjectid(String projectid){
         this.projectid = StrUtil.subWithLength(projectid, 0, 20);
     }

     @Column(length = 20, nullable = true, name = "[code]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '时段代码' ")
     public String getCode(){
         return code == null ? StrUtil.EMPTY : code;
     }

     public void setCode(String code){
         this.code = StrUtil.subWithLength(code, 0, 20);
     }

     @Column(length = 60, nullable = true, name = "[description]",columnDefinition=" varchar(60)  DEFAULT '' COMMENT '描述' ")
     public String getDescription(){
         return description == null ? StrUtil.EMPTY : description;
     }

     public void setDescription(String description){
         this.description = StrUtil.subWithLength(description, 0, 60);
     }

     @Column(length = 20, nullable = true, name = "[time]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '用英文-分割' ")
     public String getTime(){
         return time == null ? StrUtil.EMPTY : time;
     }

     public void setTime(String time){
         this.time = StrUtil.subWithLength(time, 0, 20);
     }
}
