package com.cw.entity;

import cn.hutool.core.util.StrUtil;
import com.cw.conf.EntityFiled;
import com.cw.utils.EntityUtil;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
@SuppressWarnings("serial")
@Entity
@Table(name = "Act_rs_his")
public class Act_rs_his implements Serializable {
     Date sdf = EntityUtil.stringToDate("1900-01-01");
     private Long sqlid = 0L;/* SQL主键 */
     private String projectid = "";/* 项目id */
     private String bookingid = "";/* 预约单号 */
     private String bookername = "";/* 预约人姓名 */
     private String telephone = "";/* 电话号码 */
     private String status = "";/* 预约状态  待支付:C 已支付/待出行:P 取消:X 核销完成:F */
     private Integer persons = 0;/* 预约人数 */
     private String roomno = "";/* 房间号 */
     private String uid = "";/* 关联用户id */
     private Date usedate =  sdf ;/* 预约日期 */
     private String period = "";/* 预约时间段 */
     private String sitecode = "";/* 预约项目 */
     private LocalDateTime createtime =  LocalDateTime.of(1900,1,1,0,0,0,0) ;/* 创建时间 */
     private LocalDateTime paytime =  LocalDateTime.of(1900,1,1,0,0,0,0) ;/* 付款时间 */
     private String payid = "";/* 微信支付宝支付流水号 */
     private String payno = "";/* 本地支付流水号 */
     private LocalDateTime refundtime =  LocalDateTime.of(1900,1,1,0,0,0,0) ;/* 收到退款通知时间 */
     private String qrcode = "";/* 二维码QRCODE */
     private String assistcode = "";/* 门票辅助助记码 */
     private String cancelno = "";/*  门票系统取消申请批次号cancelno */
     private BigDecimal amount =  BigDecimal.valueOf(0.00) ;/* 订单总金额 */
     private LocalDateTime canceltime =  LocalDateTime.of(1900,1,1,0,0,0,0) ;/* 取消时间 */
     private String payment = "";/* 付款方式微信,淘宝等 */
     private BigDecimal price =  BigDecimal.valueOf(0.00) ;/* 单价 */
     private String groupid = "";/* 项目大组代码 */
     private String outid = "";/* 外部订单号 */

     @Id
     @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "Act_rs_his")
     @Column(length = 10, nullable = false, name = "[sqlid]")
     public Long getSqlid(){
        return sqlid;
     }

     public void setSqlid(Long sqlid){
        this.sqlid = sqlid;
     }

     @Column(length = 20, nullable = true, name = "[projectid]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '项目id' ")
     public String getProjectid(){
         return projectid == null ? StrUtil.EMPTY : projectid;
     }

     public void setProjectid(String projectid){
         this.projectid = StrUtil.subWithLength(projectid, 0, 20);
     }

     @Column(length = 20, nullable = true, name = "[bookingid]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '预约单号' ")
     public String getBookingid(){
         return bookingid == null ? StrUtil.EMPTY : bookingid;
     }

     public void setBookingid(String bookingid){
         this.bookingid = StrUtil.subWithLength(bookingid, 0, 20);
     }

     @Column(length = 20, nullable = true, name = "[bookername]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '预约人姓名' ")
     public String getBookername(){
         return bookername == null ? StrUtil.EMPTY : bookername;
     }

     public void setBookername(String bookername){
         this.bookername = StrUtil.subWithLength(bookername, 0, 20);
     }

     @Column(length = 20, nullable = true, name = "[telephone]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '电话号码' ")
     public String getTelephone(){
         return telephone == null ? StrUtil.EMPTY : telephone;
     }

     public void setTelephone(String telephone){
         this.telephone = StrUtil.subWithLength(telephone, 0, 20);
     }

     @Column(length = 1, nullable = true, name = "[status]",columnDefinition=" varchar(1)  DEFAULT '' COMMENT '预约状态  待支付:C 已支付/待出行:P 取消:X 核销完成:F' ")
     public String getStatus(){
         return status == null ? StrUtil.EMPTY : status;
     }

     public void setStatus(String status){
         this.status = StrUtil.subWithLength(status, 0, 1);
     }

     @Column(precision = 10, nullable = true, name = "[persons]",columnDefinition=" decimal(10,0)  DEFAULT '0' COMMENT '预约人数' ")
     public Integer getPersons(){
        return null == persons ? 0 : persons;
     }

     public void setPersons(Integer persons){
        this.persons = null == persons ? 0 : persons;
     }

     @Column(length = 20, nullable = true, name = "[roomno]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '房间号' ")
     public String getRoomno(){
         return roomno == null ? StrUtil.EMPTY : roomno;
     }

     public void setRoomno(String roomno){
         this.roomno = StrUtil.subWithLength(roomno, 0, 20);
     }

     @Column(length = 32, nullable = true, name = "[uid]",columnDefinition=" varchar(32)  DEFAULT '' COMMENT '关联用户id' ")
     public String getUid(){
         return uid == null ? StrUtil.EMPTY : uid;
     }

     public void setUid(String uid){
         this.uid = StrUtil.subWithLength(uid, 0, 32);
     }

     @Column(nullable = true, name = "[usedate]",columnDefinition=" datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '预约日期' ")
     public Date getUsedate(){
         return null == usedate ? EntityFiled.NULLDATE : usedate;
     }

     public void setUsedate(Date usedate){
         this.usedate = null == usedate ? EntityFiled.NULLDATE : usedate;
     }

     @Column(length = 20, nullable = true, name = "[period]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '预约时间段' ")
     public String getPeriod(){
         return period == null ? StrUtil.EMPTY : period;
     }

     public void setPeriod(String period){
         this.period = StrUtil.subWithLength(period, 0, 20);
     }

     @Column(length = 20, nullable = true, name = "[sitecode]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '预约项目' ")
     public String getSitecode(){
         return sitecode == null ? StrUtil.EMPTY : sitecode;
     }

     public void setSitecode(String sitecode){
         this.sitecode = StrUtil.subWithLength(sitecode, 0, 20);
     }

     @Column(nullable = true, name = "[createtime]",columnDefinition=" datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '创建时间' ")
     public LocalDateTime getCreatetime(){
        return null == createtime ? LocalDateTime.of(1900,1,1,0,0,0,0):createtime;
     }

     public void setCreatetime(LocalDateTime createtime){
        this.createtime = null == createtime ? LocalDateTime.of(1900,1,1,0,0,0,0):createtime;
     }

     @Column(nullable = true, name = "[paytime]",columnDefinition=" datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '付款时间' ")
     public LocalDateTime getPaytime(){
        return null == paytime ? LocalDateTime.of(1900,1,1,0,0,0,0):paytime;
     }

     public void setPaytime(LocalDateTime paytime){
        this.paytime = null == paytime ? LocalDateTime.of(1900,1,1,0,0,0,0):paytime;
     }

     @Column(length = 100, nullable = true, name = "[payid]",columnDefinition=" varchar(100)  DEFAULT '' COMMENT '微信支付宝支付流水号' ")
     public String getPayid(){
         return payid == null ? StrUtil.EMPTY : payid;
     }

     public void setPayid(String payid){
         this.payid = StrUtil.subWithLength(payid, 0, 100);
     }

     @Column(length = 100, nullable = true, name = "[payno]",columnDefinition=" varchar(100)  DEFAULT '' COMMENT '本地支付流水号' ")
     public String getPayno(){
         return payno == null ? StrUtil.EMPTY : payno;
     }

     public void setPayno(String payno){
         this.payno = StrUtil.subWithLength(payno, 0, 100);
     }

     @Column(nullable = true, name = "[refundtime]",columnDefinition=" datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '收到退款通知时间' ")
     public LocalDateTime getRefundtime(){
        return null == refundtime ? LocalDateTime.of(1900,1,1,0,0,0,0):refundtime;
     }

     public void setRefundtime(LocalDateTime refundtime){
        this.refundtime = null == refundtime ? LocalDateTime.of(1900,1,1,0,0,0,0):refundtime;
     }

     @Lob
     @Basic(fetch = FetchType.LAZY)
     @Column(length = 150, nullable = true, name = "[qrcode]")
     public String getQrcode(){
         return qrcode == null ? StrUtil.EMPTY : qrcode;
     }

     public void setQrcode(String qrcode){
         this.qrcode = qrcode == null ? StrUtil.EMPTY : qrcode;
     }

     @Lob
     @Basic(fetch = FetchType.LAZY)
     @Column(length = 150, nullable = true, name = "[assistcode]")
     public String getAssistcode(){
         return assistcode == null ? StrUtil.EMPTY : assistcode;
     }

     public void setAssistcode(String assistcode){
         this.assistcode = assistcode == null ? StrUtil.EMPTY : assistcode;
     }

     @Column(length = 60, nullable = true, name = "[cancelno]",columnDefinition=" varchar(60)  DEFAULT '' COMMENT ' 门票系统取消申请批次号cancelno' ")
     public String getCancelno(){
         return cancelno == null ? StrUtil.EMPTY : cancelno;
     }

     public void setCancelno(String cancelno){
         this.cancelno = StrUtil.subWithLength(cancelno, 0, 60);
     }

     @Column(precision = 8, scale = 2, nullable = true, name = "[amount]",columnDefinition=" decimal(8,2)  DEFAULT '0.00' COMMENT '订单总金额' ")
     public BigDecimal getAmount(){
         return amount == null ? BigDecimal.ZERO : amount;
     }

     public Double getAmount(int i){
        return Double.valueOf(amount.toString());
     }

     public void setAmount(Object amount){
         if (amount == null || "".equals(amount)) {
             this.amount = BigDecimal.ZERO;
         } else {
             this.amount = amount instanceof BigDecimal ? (BigDecimal) amount : new BigDecimal(amount.toString());
         }
     }

     @Column(nullable = true, name = "[canceltime]",columnDefinition=" datetime  DEFAULT '1900-01-01 00:00:00' COMMENT '取消时间' ")
     public LocalDateTime getCanceltime(){
        return null == canceltime ? LocalDateTime.of(1900,1,1,0,0,0,0):canceltime;
     }

     public void setCanceltime(LocalDateTime canceltime){
        this.canceltime = null == canceltime ? LocalDateTime.of(1900,1,1,0,0,0,0):canceltime;
     }

     @Column(length = 20, nullable = true, name = "[payment]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '付款方式微信,淘宝等' ")
     public String getPayment(){
         return payment == null ? StrUtil.EMPTY : payment;
     }

     public void setPayment(String payment){
         this.payment = StrUtil.subWithLength(payment, 0, 20);
     }

     @Column(precision = 8, scale = 2, nullable = true, name = "[price]",columnDefinition=" decimal(8,2)  DEFAULT '0.00' COMMENT '单价' ")
     public BigDecimal getPrice(){
         return price == null ? BigDecimal.ZERO : price;
     }

     public Double getPrice(int i){
        return Double.valueOf(price.toString());
     }

     public void setPrice(Object price){
         if (price == null || "".equals(price)) {
             this.price = BigDecimal.ZERO;
         } else {
             this.price = price instanceof BigDecimal ? (BigDecimal) price : new BigDecimal(price.toString());
         }
     }

     @Column(length = 20, nullable = true, name = "[groupid]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '项目大组代码' ")
     public String getGroupid(){
         return groupid == null ? StrUtil.EMPTY : groupid;
     }

     public void setGroupid(String groupid){
         this.groupid = StrUtil.subWithLength(groupid, 0, 20);
     }

     @Column(length = 60, nullable = true, name = "[outid]",columnDefinition=" varchar(60)  DEFAULT '' COMMENT '外部订单号' ")
     public String getOutid(){
         return outid == null ? StrUtil.EMPTY : outid;
     }

     public void setOutid(String outid){
         this.outid = StrUtil.subWithLength(outid, 0, 60);
     }
}
