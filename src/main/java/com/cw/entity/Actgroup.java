package com.cw.entity;

import cn.hutool.core.util.StrUtil;

import javax.persistence.*;
import java.io.Serializable;
@SuppressWarnings("serial")
@Entity
@Table(name = "Actgroup")
public class Actgroup implements Serializable {
    private Long sqlid = 0L;/* SQL主键 */
    private String projectid = "";/* 项目id */
    private String code = "";/* 项目代码 */
    private String description = "";/* 项目描述 */
    private Integer checkmode = 0;/* 核销模式.0:普通模式1:门票发码模式,2:发码核销 */
    private Integer advdays = 0;/* 可预约未来天数,默认0为今天,1为明天 */
    private Integer advhour = 0;/* 当天项目提前预约小时数,默认0.假如配置2,则必须提前2小时预约 */
    private String ndallowtime = "";/* 可预约明日项目时段 */
    private Boolean lfree = false;/* 是否免费 */
    private Boolean lsell = false;/* 是否开放 */
    private Boolean lrmno = false;/* 是否需要房间号 */
    private Integer seq = 0;/* 排序字段 */
    private String listpic = "";/* 列表图片 */
    private String slidepics = "";/* 轮播图片 */
    private String noticetext = "";/* 预约须知富文本 */
    private Boolean lshowalert = false;/* 是否弹窗提示 */
    private Integer minstart = 0;/* 最小提前预约天数,0可预约今天,1,从明天开始预约 */
    private Integer mincancel = 0;/* 最小提前取消小时数,默认0 */
    private Integer limprdnum = 0;/* 用户每个时段预约限制人数 */
    private Integer maxoneday = 0;/* 用户每天预约限制次数 */
    private Integer cycle = 0;/* 购买间隔时间0为不限制 */
    private String nodesc = "";/* 证件描述 */
    private Integer notype = 0;/* 证件类型 */
    private String whitelist = "";/* 发送者白名单 */

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "Actgroup")
    @Column(length = 10, nullable = false, name = "[sqlid]")
    public Long getSqlid() {
        return sqlid;
    }

    public void setSqlid(Long sqlid) {
        this.sqlid = sqlid;
    }

    @Column(length = 20, nullable = true, name = "[projectid]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目id' ")
    public String getProjectid() {
        return projectid == null ? StrUtil.EMPTY : projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = StrUtil.subWithLength(projectid, 0, 20);
    }

    @Column(length = 20, nullable = true, name = "[code]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '项目代码' ")
    public String getCode() {
        return code == null ? StrUtil.EMPTY : code;
    }

    public void setCode(String code) {
        this.code = StrUtil.subWithLength(code, 0, 20);
    }

    @Column(length = 60, nullable = true, name = "[description]", columnDefinition = " varchar(60)  DEFAULT '' COMMENT '项目描述' ")
    public String getDescription() {
        return description == null ? StrUtil.EMPTY : description;
    }

    public void setDescription(String description) {
        this.description = StrUtil.subWithLength(description, 0, 60);
    }

    @Column(precision = 10, nullable = true, name = "[checkmode]", columnDefinition = " decimal(10,0)  DEFAULT '0' COMMENT '核销模式.0:普通模式1:门票发码模式,2:发码核销' ")
    public Integer getCheckmode() {
        return null == checkmode ? 0 : checkmode;
    }

    public void setCheckmode(Integer checkmode) {
        this.checkmode = null == checkmode ? 0 : checkmode;
    }

    @Column(precision = 2, nullable = true, name = "[advdays]", columnDefinition = " decimal(2,0)  DEFAULT '0' COMMENT '可预约未来天数,默认0为今天,1为明天' ")
    public Integer getAdvdays() {
        return null == advdays ? 0 : advdays;
    }

    public void setAdvdays(Integer advdays) {
        this.advdays = null == advdays ? 0 : advdays;
    }

    @Column(precision = 2, nullable = true, name = "[advhour]", columnDefinition = " decimal(2,0)  DEFAULT '0' COMMENT '当天项目提前预约小时数,默认0.假如配置2,则必须提前2小时预约' ")
    public Integer getAdvhour() {
        return null == advhour ? 0 : advhour;
    }

    public void setAdvhour(Integer advhour) {
        this.advhour = null == advhour ? 0 : advhour;
    }

    @Column(length = 20, nullable = true, name = "[ndallowtime]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '可预约明日项目时段' ")
    public String getNdallowtime() {
        return ndallowtime == null ? StrUtil.EMPTY : ndallowtime;
    }

    public void setNdallowtime(String ndallowtime) {
        this.ndallowtime = StrUtil.subWithLength(ndallowtime, 0, 20);
    }

    @Column(name = "[lfree]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否免费'")
    public Boolean getLfree() {
        return null == lfree ? false : lfree;
    }

    public void setLfree(Boolean lfree) {
        this.lfree = null == lfree ? false : lfree;
    }

    @Column(name = "[lsell]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否开放'")
    public Boolean getLsell() {
        return null == lsell ? false : lsell;
    }

    public void setLsell(Boolean lsell) {
        this.lsell = null == lsell ? false : lsell;
    }

    @Column(name = "[lrmno]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否需要房间号'")
    public Boolean getLrmno() {
        return null == lrmno ? false : lrmno;
    }

    public void setLrmno(Boolean lrmno) {
        this.lrmno = null == lrmno ? false : lrmno;
    }

    @Column(precision = 4, nullable = true, name = "[seq]", columnDefinition = " decimal(4,0)  DEFAULT '0' COMMENT '排序字段' ")
    public Integer getSeq() {
        return null == seq ? 0 : seq;
    }

    public void setSeq(Integer seq) {
        this.seq = null == seq ? 0 : seq;
    }

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[listpic]")
    public String getListpic() {
        return listpic == null ? StrUtil.EMPTY : listpic;
    }

    public void setListpic(String listpic) {
        this.listpic = listpic == null ? StrUtil.EMPTY : listpic;
    }

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[slidepics]")
    public String getSlidepics() {
        return slidepics == null ? StrUtil.EMPTY : slidepics;
    }

    public void setSlidepics(String slidepics) {
        this.slidepics = slidepics == null ? StrUtil.EMPTY : slidepics;
    }

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[noticetext]")
    public String getNoticetext() {
        return noticetext == null ? StrUtil.EMPTY : noticetext;
    }

    public void setNoticetext(String noticetext) {
        this.noticetext = noticetext == null ? StrUtil.EMPTY : noticetext;
    }

    @Column(name = "[lshowalert]", columnDefinition = " bit(1)  DEFAULT b'0' COMMENT '是否弹窗提示'")
    public Boolean getLshowalert() {
        return null == lshowalert ? false : lshowalert;
    }

    public void setLshowalert(Boolean lshowalert) {
        this.lshowalert = null == lshowalert ? false : lshowalert;
    }

    @Column(precision = 2, nullable = true, name = "[minstart]", columnDefinition = " decimal(2,0)  DEFAULT '0' COMMENT '最小提前预约天数,0可预约今天,1,从明天开始预约' ")
    public Integer getMinstart() {
        return null == minstart ? 0 : minstart;
    }

    public void setMinstart(Integer minstart) {
        this.minstart = null == minstart ? 0 : minstart;
    }

    @Column(precision = 2, nullable = true, name = "[mincancel]", columnDefinition = " decimal(2,0)  DEFAULT '0' COMMENT '最小提前取消小时数,默认0' ")
    public Integer getMincancel() {
        return null == mincancel ? 0 : mincancel;
    }

    public void setMincancel(Integer mincancel) {
        this.mincancel = null == mincancel ? 0 : mincancel;
    }

    @Column(precision = 3, nullable = true, name = "[limprdnum]", columnDefinition = " decimal(3,0)  DEFAULT '0' COMMENT '用户每个时段预约限制人数' ")
    public Integer getLimprdnum() {
        return null == limprdnum ? 0 : limprdnum;
    }

    public void setLimprdnum(Integer limprdnum) {
        this.limprdnum = null == limprdnum ? 0 : limprdnum;
    }

    @Column(precision = 3, nullable = true, name = "[maxoneday]", columnDefinition = " decimal(3,0)  DEFAULT '0' COMMENT '用户每天预约限制次数' ")
    public Integer getMaxoneday() {
        return null == maxoneday ? 0 : maxoneday;
    }

    public void setMaxoneday(Integer maxoneday) {
        this.maxoneday = null == maxoneday ? 0 : maxoneday;
    }

    @Column(precision = 3, nullable = true, name = "[cycle]", columnDefinition = " decimal(3,0)  DEFAULT '0' COMMENT '购买间隔时间0为不限制' ")
    public Integer getCycle() {
        return null == cycle ? 0 : cycle;
    }

    public void setCycle(Integer cycle) {
        this.cycle = null == cycle ? 0 : cycle;
    }

    @Column(length = 20, nullable = true, name = "[nodesc]", columnDefinition = " varchar(20)  DEFAULT '' COMMENT '证件描述' ")
    public String getNodesc() {
        return nodesc == null ? StrUtil.EMPTY : nodesc;
    }

    public void setNodesc(String nodesc) {
        this.nodesc = StrUtil.subWithLength(nodesc, 0, 20);
    }

    @Column(precision = 2, nullable = true, name = "[notype]", columnDefinition = " decimal(2,0)  DEFAULT '0' COMMENT '证件类型' ")
    public Integer getNotype() {
        return null == notype ? 0 : notype;
    }

    public void setNotype(Integer notype) {
        this.notype = null == notype ? 0 : notype;
    }

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(length = 150, nullable = true, name = "[whitelist]")
    public String getWhitelist() {
        return whitelist == null ? StrUtil.EMPTY : whitelist;
    }

    public void setWhitelist(String whitelist) {
        this.whitelist = whitelist == null ? StrUtil.EMPTY : whitelist;
    }
}
