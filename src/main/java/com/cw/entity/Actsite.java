package com.cw.entity;

import cn.hutool.core.util.StrUtil;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
@SuppressWarnings("serial")
@Entity
@Table(name = "Actsite")
public class Actsite implements Serializable {
     private Long sqlid = 0L;/* SQL主键 */
     private String projectid = "";/* 项目id */
     private String code = "";/* 场地代码 */
     private String description = "";/* 描述 */
     private String periods = "";/* 场所关联多个时段代码 */
     private String groupid = "";/* 项目代码 */
     private String outcode = "";/* 门票厂商票型代码 */
     private Integer seq = 0;/* 排序字段 */
     private Boolean lsell = false;/* 是否开放 */
     private Integer dfallownum = 0;/* 默认库存 */
     private BigDecimal dfprice =  BigDecimal.valueOf(0.00) ;/* 默认价格 */
     private String usetips = "";/* 使用提示 */

     @Id
     @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "Actsite")
     @Column(length = 10, nullable = false, name = "[sqlid]")
     public Long getSqlid(){
        return sqlid;
     }

     public void setSqlid(Long sqlid){
        this.sqlid = sqlid;
     }

     @Column(length = 20, nullable = true, name = "[projectid]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '项目id' ")
     public String getProjectid(){
         return projectid == null ? StrUtil.EMPTY : projectid;
     }

     public void setProjectid(String projectid){
         this.projectid = StrUtil.subWithLength(projectid, 0, 20);
     }

     @Column(length = 20, nullable = true, name = "[code]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '场地代码' ")
     public String getCode(){
         return code == null ? StrUtil.EMPTY : code;
     }

     public void setCode(String code){
         this.code = StrUtil.subWithLength(code, 0, 20);
     }

     @Column(length = 60, nullable = true, name = "[description]",columnDefinition=" varchar(60)  DEFAULT '' COMMENT '描述' ")
     public String getDescription(){
         return description == null ? StrUtil.EMPTY : description;
     }

     public void setDescription(String description){
         this.description = StrUtil.subWithLength(description, 0, 60);
     }

     @Lob
     @Basic(fetch = FetchType.LAZY)
     @Column(length = 150, nullable = true, name = "[periods]")
     public String getPeriods(){
         return periods == null ? StrUtil.EMPTY : periods;
     }

     public void setPeriods(String periods){
         this.periods = periods == null ? StrUtil.EMPTY : periods;
     }

     @Column(length = 10, nullable = true, name = "[groupid]",columnDefinition=" varchar(10)  DEFAULT '' COMMENT '项目代码' ")
     public String getGroupid(){
         return groupid == null ? StrUtil.EMPTY : groupid;
     }

     public void setGroupid(String groupid){
         this.groupid = StrUtil.subWithLength(groupid, 0, 10);
     }

     @Column(length = 20, nullable = true, name = "[outcode]",columnDefinition=" varchar(20)  DEFAULT '' COMMENT '门票厂商票型代码' ")
     public String getOutcode(){
         return outcode == null ? StrUtil.EMPTY : outcode;
     }

     public void setOutcode(String outcode){
         this.outcode = StrUtil.subWithLength(outcode, 0, 20);
     }

     @Column(precision = 4, nullable = true, name = "[seq]",columnDefinition=" decimal(4,0)  DEFAULT '0' COMMENT '排序字段' ")
     public Integer getSeq(){
        return null == seq ? 0 : seq;
     }

     public void setSeq(Integer seq){
        this.seq = null == seq ? 0 : seq;
     }

     @Column(name="[lsell]", columnDefinition=" bit(1)  DEFAULT b'0' COMMENT '是否开放'") 
     public Boolean getLsell(){
        return null == lsell ? false : lsell;
     }

     public void setLsell(Boolean lsell){
        this.lsell = null == lsell ? false : lsell;
     }

     @Column(precision = 10, nullable = true, name = "[dfallownum]",columnDefinition=" decimal(10,0)  DEFAULT '0' COMMENT '默认库存' ")
     public Integer getDfallownum(){
        return null == dfallownum ? 0 : dfallownum;
     }

     public void setDfallownum(Integer dfallownum){
        this.dfallownum = null == dfallownum ? 0 : dfallownum;
     }

     @Column(precision = 8, scale = 2, nullable = true, name = "[dfprice]",columnDefinition=" decimal(8,2)  DEFAULT '0.00' COMMENT '默认价格' ")
     public BigDecimal getDfprice(){
         return dfprice == null ? BigDecimal.ZERO : dfprice;
     }

     public Double getDfprice(int i){
        return Double.valueOf(dfprice.toString());
     }

     public void setDfprice(Object dfprice){
         if (dfprice == null || "".equals(dfprice)) {
             this.dfprice = BigDecimal.ZERO;
         } else {
             this.dfprice = dfprice instanceof BigDecimal ? (BigDecimal) dfprice : new BigDecimal(dfprice.toString());
         }
     }

     @Column(length = 100, nullable = true, name = "[usetips]",columnDefinition=" varchar(100)  DEFAULT '' COMMENT '使用提示' ")
     public String getUsetips(){
         return usetips == null ? StrUtil.EMPTY : usetips;
     }

     public void setUsetips(String usetips){
         this.usetips = StrUtil.subWithLength(usetips, 0, 100);
     }
}
