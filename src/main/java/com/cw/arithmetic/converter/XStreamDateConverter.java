package com.cw.arithmetic.converter;

import com.cw.utils.CalculateDate;
import com.cw.utils.datetime.DateStyle;
import com.thoughtworks.xstream.converters.Converter;
import com.thoughtworks.xstream.converters.MarshallingContext;
import com.thoughtworks.xstream.converters.UnmarshallingContext;
import com.thoughtworks.xstream.io.HierarchicalStreamReader;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;

import java.sql.Timestamp;
import java.util.Date;

public class XStreamDateConverter implements Converter {

    @Override
    public void marshal(Object o, HierarchicalStreamWriter hierarchicalStreamWriter, MarshallingContext marshallingContext) {
        if (o != null && o instanceof Date) {
            hierarchicalStreamWriter.setValue(CalculateDate.dateToString((Date) o));
        }

        if (o != null && o instanceof Timestamp) {
            hierarchicalStreamWriter.setValue(CalculateDate.dateToString((Date) o));
        }
    }

    @Override
    public Object unmarshal(HierarchicalStreamReader hierarchicalStreamReader, UnmarshallingContext unmarshallingContext) {
        String dateStr = hierarchicalStreamReader.getValue();
        if (!dateStr.isEmpty()) {
            if (dateStr.length() > 10) {
                return CalculateDate.stringToDate(dateStr, DateStyle.YYYY_MM_DD_HH_MM_SS);
            } else {
                return CalculateDate.stringToDate(dateStr);
            }
        }
        return null;
    }

    @Override
    public boolean canConvert(Class aClass) {
        return Date.class == aClass;
    }
}
