package com.cw.arithmetic.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 退款信息.提供给退款支付,支付通知使用
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/7 16:25
 **/
@Data
public class PassByPayRefundAttachInfo {
    String bookingid; //申请退款的订单号
    String proejectid;
    String uid;
    String reqTime;//实际请求时间 2021-11-07T16:25:00  LocalDateTime 格式
    BigDecimal refundAmount;//退款金额
    boolean lduplicate = false; //是否取消多余支付
}
