package com.cw.arithmetic.pay;

import com.cw.entity.Pass_rs;
import com.cw.utils.StatusUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线下支付单支付信息
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/7 16:25
 **/
@Data
public class PassByPayAttachInfo {
    int paymode;

    String projectId;
    String outTradeNo;  //本地生成支付单号 单个订单支付时等幂
    String orderDesc; //商品描述.注意.不同厂商的要求长度会不一样.各自截取

    String payerId;//支付环境中的支付用户 id  微信小程序内则是 openid

    String bookingid;//订单号
    String outid;//外部订单号
    String memo;//客人订单备注
    String ptype;//购买产品类型.暂时为空
    String tel;//预订电话
    String guestname;//客人姓名

    String createdate;
    String payment; //付款方式
    BigDecimal amount;//订单支付金额
    Integer onlinePayMethod;//微信浏览器or h5 or 支付宝


    public Pass_rs getParseOrder() {
        Pass_rs passRs = new Pass_rs();
        passRs.setBookingid(bookingid);
        passRs.setOutid(outid);
        passRs.setMemo(memo);
        passRs.setPtype(ptype);
        passRs.setTel(tel);
        passRs.setGuestname(guestname);
        passRs.setCreatedate(LocalDateTime.now());
        passRs.setAmount(amount);
        passRs.setOpenid(payerId);
        passRs.setOstatus(StatusUtil.PassRsStatus.WAIT);
        return passRs;
    }

}
