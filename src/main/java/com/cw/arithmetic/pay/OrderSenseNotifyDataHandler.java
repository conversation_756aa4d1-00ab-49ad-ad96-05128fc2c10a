package com.cw.arithmetic.pay;

import com.cw.core.SeqNoService;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.Prepay;
import com.cw.exception.DefinedException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/10/14 09:06
 **/
public interface OrderSenseNotifyDataHandler<T> {

    //    void setData(T t);
    void initData();

    boolean isCancelStatus();

    String getOrderStatus();

    String getOrderUid();

    String getBookingId();

    BigDecimal getOrderAmount();


    void saveOrderPayStatus(String transactionId, String outTradeNo, String payment);

    void dispatchOrderPaySuccessEvent(RabbitTemplate rabbitTemplate, Prepay prepay, boolean lnotifyWebsocket);

    void cancelNoPay(Prepay prepay) throws Exception;

    StdOrderData getStdOrerData(String bookingId, String projectId);

    void writeQrCodeAndSave(StdOrderData orderData, String qrCode, String assistCode, String outid);


    void updateTicketOrderStatus(String projectId, String bookingId, String status);

    void initOrders(List<String> orderids);

    int getOrdersSize();

    BigDecimal getPayOrderAmount();

    String getPayOrderDesc();

    Long getPayExpireTimeStamp(String projectId) throws DefinedException;

    String getPrepaySeqNo(SeqNoService seqNoService, int onlinePayMethod);

}
