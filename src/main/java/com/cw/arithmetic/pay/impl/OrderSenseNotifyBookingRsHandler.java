package com.cw.arithmetic.pay.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.events.SysPushEvent;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.arithmetic.pay.OrderSenseNotifyDataHandler;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.cache.impl.TicketCache;
import com.cw.core.CoreOrderWriter;
import com.cw.core.CoreRs;
import com.cw.core.SeqNoService;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.BookingrsMapper;
import com.cw.mapper.Ticket_rsMapper;
import com.cw.pojo.common.ResultCode;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushPayPlatformMsg;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushPayWebSocketMsg;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushTicketMsg;
import com.cw.utils.*;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.cw.utils.rule.OrderCheckMode;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/10/14 11:26
 **/
@Slf4j
public class OrderSenseNotifyBookingRsHandler implements OrderSenseNotifyDataHandler<Booking_rs> {
    private Booking_rs bookingRs;
    private String bookingId;
    private List<Booking_rs> booking_rsList;

    public OrderSenseNotifyBookingRsHandler(String bookingId) {
        this.bookingId = bookingId;
    }


    @Override
    public void initData() {
        BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
        Booking_rs dbRs = bookingrsMapper.findBooking_rsByBookingid(bookingId);
        this.bookingRs = dbRs;
    }

    @Override
    public boolean isCancelStatus() {
        return bookingRs.getMainstatus().equals(StatusUtil.BookingRsStatus.CANCEL);
    }

    @Override
    public String getOrderStatus() {
        return bookingRs.getMainstatus();
    }

    @Override
    public String getOrderUid() {
        return bookingRs.getUid();
    }

    @Override
    public String getBookingId() {
        return bookingRs.getBookingid();
    }

    @Override
    public BigDecimal getOrderAmount() {
        return bookingRs.getAmount();
    }

    @Override
    public void saveOrderPayStatus(String transactionId, String outTradeNo, String payment) {
        Rules productRules = SysFuncLibTool.getProductRule(bookingRs.getPtype(), bookingRs.getProduct(), bookingRs.getProjectid());
        if (productRules != null && productRules.getBuyaudit()) {//如果产品规则是人工审核模式
            bookingRs.setMainstatus(StatusUtil.BookingRsStatus.WAIT);
            //发送短信
            if (ProdType.ROOM.val().equals(bookingRs.getPtype())) {
                try {
                    //异步发送短信中景短信人工审核
                    SysPushEvent sysPushEvent = new SysPushEvent(bookingRs, MsgTriggerEnum.ORDERAUDITING, bookingRs.getTel(), "", bookingRs.getBookingid(), bookingRs.getProjectid());
                    SpringUtil.getApplicationContext().publishEvent(sysPushEvent);


                    //短信发送通知后台人员成功
                    SysPushEvent sysPushEventConsole = new SysPushEvent(bookingRs, MsgTriggerEnum.TICKETQRNOTIFY, bookingRs.getTel(),
                            "", bookingRs.getBookingid(), bookingRs.getProjectid());
                    SpringUtil.getApplicationContext().publishEvent(sysPushEventConsole);//异步发送发码成功短信

                } catch (Exception e) {
                    e.printStackTrace();
                }

            }

        } else {
            bookingRs.setMainstatus(StatusUtil.BookingRsStatus.PAY);
            if (ProdType.WARES.val().equals(bookingRs.getPtype())) {//音频产品.支付后直接完成
                if (!ContentCacheTool.getAudioUrl(bookingRs.getProduct(), bookingRs.getProjectid()).isEmpty()) {
                    bookingRs.setMainstatus(StatusUtil.BookingRsStatus.FINISH);
                } else {
                    Spugroup msgSpu = (Spugroup) ProdFactory.getProd(ProdType.WARES).getProdGroup(bookingRs.getProduct(), bookingRs.getProjectid(), null); //ContentCacheTool.getNeedSendMsgSpu(bookingRs.getProduct(), bookingRs.getProjectid());
                    if (msgSpu != null && msgSpu.getCheckmode() == OrderCheckMode.SYS_QR) {
                        //SysPushEvent sysPushEvent = new SysPushEvent(bookingRs, MsgTriggerEnum.SPUCONFIRM, bookingRs.getTel(),
                        //        "", bookingRs.getBookingid(), bookingRs.getProjectid());
                        //SpringUtil.getApplicationContext().publishEvent(sysPushEvent);
                    }
                }
                //景区商品类
            }
            //直连产品不需要审核直接发送客房短信
            if (ProdType.ROOM.val().equals(bookingRs.getPtype()) || ProdType.TAOCAN.val().equals(bookingRs.getPtype())) {
                //中景短信
                try {
                    //异步发送短信中景短信预定成功
                    SysPushEvent sysPushEvent = new SysPushEvent(bookingRs, MsgTriggerEnum.ORDERCONFIRM, bookingRs.getTel(), "", bookingRs.getBookingid(), bookingRs.getProjectid());
                    SpringUtil.getApplicationContext().publishEvent(sysPushEvent);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        //直接写入订单
        BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
        bookingRs.setPayid(transactionId);//支付流水号  result.getResult().getTransactionId()
        bookingRs.setPayno(outTradeNo);//本地订单号
        bookingRs.setPayment(payment);//OnlinePayType.WX.name());
        bookingrsMapper.saveAndFlush(bookingRs);

        //写日志
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        String content = "订单支付成功，订单号:" + bookingRs.getBookingid() + "，支付金额：" + bookingRs.getAmount() + "元";
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.PAY, SystemUtil.DEFAULTUSERID,
                bookingRs.getBookingid(), content, bookingRs.getProjectid());
    }

    @Override
    public void dispatchOrderPaySuccessEvent(RabbitTemplate rabbitTemplate, Prepay prepay, boolean lnotifyWebsocket) {
        //向中台推送预付款信息
        if (bookingRs.getPtype().equals(ProdType.ROOM.val()) || bookingRs.getPtype().equals(ProdType.TAOCAN.val())) {//TODO 套餐里可能含有客房产品
            Bussness_PushPayPlatformMsg platformMsg = new Bussness_PushPayPlatformMsg();
            platformMsg.setProjectId(bookingRs.getProjectid());
            platformMsg.setBookingId(bookingRs.getBookingid());
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.PUSHPAYINFO),
                    JSON.toJSONString(platformMsg));
        }
        //如果是深大门票的.发码
        if (bookingRs.getPtype().equals(ProdType.TICKET.val())) {
            TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
            Ticket ticket = ticketCache.getRecord(bookingRs.getProjectid(), bookingRs.getProduct());

            if (!ticket.getOutcode().isEmpty()) {
                Bussness_PushTicketMsg ticketMsg = new Bussness_PushTicketMsg();
                ticketMsg.setBookingId(bookingRs.getBookingid());
                ticketMsg.setProjectId(bookingRs.getProjectid());
                rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.SENDTICKETQR),
                        JSON.toJSONString(ticketMsg));

            }
        }
        if (lnotifyWebsocket) {//二维码之类的websocket  才广播通知
            Bussness_PushPayWebSocketMsg msg = new Bussness_PushPayWebSocketMsg();
            msg.setProjectId(bookingRs.getProjectid());
            msg.setBookingId(bookingRs.getBookingid());
            //发送队列消息
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.PAYINFOWEB),
                    JSON.toJSONString(msg));
            log.info("发送websocket推送消息:{}", JSON.toJSONString(msg));
        }

    }

    @Override
    public void cancelNoPay(Prepay prepay) throws Exception {
        //获取订单操作锁
        if (bookingRs != null && bookingRs.getMainstatus().equals(StatusUtil.BookingRsStatus.CONFIRM) && prepay == null) {
            CoreRs coreRs = SpringUtil.getBean(CoreRs.class);
            boolean lok = true;
            try {
                coreRs.cancelOrder_Now(bookingRs.getBookingid(), bookingRs.getProjectid());  //TODO 强制取消.不走规则判断
//                System.out.println(this.hashCode() + "定时取消未支付订单成功" + msg.toString());
//                logger.info("定时自动取消未支付订单:{}", msg.toString());
            } catch (Exception e) {
                SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                        RobotUtils.transRobotExceptionMsg("定时自动取消未支付订单异常，订单号:" + bookingRs.getBookingid()));
                throw e;
//                handlerError(JSON.toJSONString(msg));
                //  调用报警机器人

            }
        }
    }

    @Override
    public StdOrderData getStdOrerData(String bookingId, String projectId) {
        StdOrderData stdOrderData = new StdOrderData();
        BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
        Ticket_rsMapper ticket_rsMapper = SpringUtil.getBean(Ticket_rsMapper.class);
        Booking_rs bookingRs = bookingrsMapper.findBooking_rsByBookingid(bookingId);
        List<Ticket_rs> ticketRsList = ticket_rsMapper.findTicket_rsByBookingidAndProjectid(bookingId, projectId);
        stdOrderData.setBookingRs(bookingRs);
        stdOrderData.getTickets().addAll(ticketRsList);

        if (!bookingRs.getMainstatus().equals(StatusUtil.BookingRsStatus.PAY) || ticketRsList.size() == 0) {
            return null;
        }
        return stdOrderData;
    }

    @Override
    public void writeQrCodeAndSave(StdOrderData orderData, String qrCode, String assistCode, String outid) {
        Ticket_rsMapper ticket_rsMapper = SpringUtil.getBean(Ticket_rsMapper.class);
        for (Ticket_rs ticket : orderData.getTickets()) {
            ticket.setQrcode(qrCode);
            ticket.setAssistcode(assistCode);
        }
        ticket_rsMapper.saveAll(orderData.getTickets());
        if (orderData.getBookingRs() != null && StrUtil.isNotBlank(outid)) {
            BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
            orderData.getBookingRs().setOutid(outid);
            bookingrsMapper.saveAndFlush(orderData.getBookingRs());
        }
    }

    @Override
    public void updateTicketOrderStatus(String projectId, String bookingId, String status) {
        RLock orderLock = RedisTool.getRedissonClient().getLock(RedisKey.getOrderLockKey(projectId, bookingId));
        boolean lok = false;//等待5秒.
        try {
            lok = orderLock.tryLock(3, 5, TimeUnit.SECONDS);
            if (lok) {
                CoreOrderWriter coreOrderWriter = SpringUtil.getBean(CoreOrderWriter.class);
                coreOrderWriter.updProdBookingRsStatus(bookingId, status, ProdType.TICKET);
                orderLock.unlock();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void initOrders(List<String> orderids) {
        BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
        this.booking_rsList = bookingrsMapper.findNeedPayBookingids(orderids);
    }

    @Override
    public int getOrdersSize() {
        return booking_rsList == null ? 0 : booking_rsList.size();
    }

    @Override
    public BigDecimal getPayOrderAmount() {
        return ContentCacheTool.getPayOrderAmount(booking_rsList);
    }

    @Override
    public String getPayOrderDesc() {
        return ContentCacheTool.getPayOrderDesc(booking_rsList);
    }

    @Override
    public Long getPayExpireTimeStamp(String projectId) throws DefinedException {
        int expireMin = ContentCacheTool.getSysOrderExpireMinutue(projectId);
        long now = DateTime.now().getTime();
        List<Long> expires = booking_rsList.stream().map(r -> {
            return CalculateDate.asUtilDate(r.getCreatedate()).getTime() + expireMin * 60 * 1000;
        }).collect(Collectors.toList());
        Long minExpire = Collections.min(expires);

        if (minExpire < now) {
            throw new DefinedException("订单支付时间已经过期", ResultCode.PAYFAIL.code());
        }
        return minExpire;
    }

    @Override
    public String getPrepaySeqNo(SeqNoService seqNoService, int onlinePayMethod) {
        String prepeySeqno = booking_rsList.size() == 1 ?//单独订单支付时.使用订单号作为支付 ID .防止重复支付
                seqNoService.getSequenceID_withOrderId(SystemUtil.SequenceKey.PREPAY, booking_rsList.get(0).getBookingid() + onlinePayMethod)
                : seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);
        return prepeySeqno;
    }


}
