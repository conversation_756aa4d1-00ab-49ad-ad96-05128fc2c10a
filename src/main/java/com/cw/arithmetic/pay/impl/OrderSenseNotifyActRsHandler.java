package com.cw.arithmetic.pay.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.events.SysPushEvent;
import com.cw.arithmetic.pay.OrderSenseNotifyDataHandler;
import com.cw.cache.RedisTool;
import com.cw.core.CoreOrderWriter;
import com.cw.core.SeqNoService;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.ActrsMapper;
import com.cw.pojo.common.ResultCode;
import com.cw.service.app.impl.AppActServiceImpl;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushPayWebSocketMsg;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushTicketMsg;
import com.cw.utils.*;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.cw.utils.pay.PayUtil;
import com.cw.utils.rule.OrderCheckMode;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/10/14 11:26
 **/
@Slf4j
public class OrderSenseNotifyActRsHandler implements OrderSenseNotifyDataHandler<Act_rs> {
    private Act_rs actRs;
    private String bookingId;

    public OrderSenseNotifyActRsHandler(String bookingId) {
        this.bookingId = bookingId;
    }

    @Override
    public void initData() {
        ActrsMapper actrsMapper = SpringUtil.getBean(ActrsMapper.class);
        this.actRs = actrsMapper.findAct_rsByBookingid(bookingId);
    }

    @Override
    public boolean isCancelStatus() {
        return actRs.getStatus().equals(StatusUtil.ActRsStatus.CANCEL);
    }

    @Override
    public String getOrderStatus() {
        return actRs.getStatus();
    }

    @Override
    public String getOrderUid() {
        return actRs.getUid();
    }

    @Override
    public String getBookingId() {
        return actRs.getBookingid();
    }

    @Override
    public BigDecimal getOrderAmount() {
        return actRs.getAmount();
    }

    @Override
    public void saveOrderPayStatus(String transactionId, String outTradeNo, String payment) {
        ActrsMapper actrsMapper = SpringUtil.getBean(ActrsMapper.class);
        actRs.setPayid(transactionId);//支付流水号  result.getResult().getTransactionId()
        actRs.setPayno(outTradeNo);//本地订单号
        actRs.setPayment(payment);//付款方式
//        actRs.setPay(payment);//OnlinePayType.WX.name());
//        actRs.saveAndFlush(bookingRs);
        actRs.setPaytime(LocalDateTime.now());
        actRs.setStatus(StatusUtil.ActRsStatus.PAY);
        actrsMapper.saveAndFlush(actRs);

        log.info("活动预约id:{}付款成功", actRs.getBookingid());

        //短信发送通知后台人员成功
        SysPushEvent sysPushEventConsole = new SysPushEvent(actRs, MsgTriggerEnum.ACTCONFIRM, "",
                "", actRs.getBookingid(), actRs.getProjectid());
        SpringUtil.getApplicationContext().publishEvent(sysPushEventConsole);//异步发送发码成功短信

    }

    @Override
    public void dispatchOrderPaySuccessEvent(RabbitTemplate rabbitTemplate, Prepay prepay, boolean lnotifyWebsocket) {

        Actsite actsite = ContentCacheTool.getActsite(actRs.getProjectid(), actRs.getSitecode());
        Actgroup actgroup = ContentCacheTool.getActgroup(actsite);
        if (actgroup.getCheckmode() == OrderCheckMode.TICKET_QR && !actsite.getOutcode().isEmpty()) {
            Bussness_PushTicketMsg ticketMsg = new Bussness_PushTicketMsg();
            ticketMsg.setBookingId(actRs.getBookingid());
            ticketMsg.setProjectId(actRs.getProjectid());
            ticketMsg.setScene(PayUtil.ACT_SCENE);
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.SENDTICKETQR),
                    JSON.toJSONString(ticketMsg));
        }

        if (lnotifyWebsocket) {//二维码之类的websocket  才广播通知
            Bussness_PushPayWebSocketMsg msg = new Bussness_PushPayWebSocketMsg();
            msg.setProjectId(actRs.getProjectid());
            msg.setBookingId(actRs.getBookingid());
            //发送队列消息
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.PAYINFOWEB),
                    JSON.toJSONString(msg));
            log.info("发送websocket推送消息:{}", JSON.toJSONString(msg));
        }
    }

    @Override
    public void cancelNoPay(Prepay prepay) throws Exception {
        //获取订单操作锁
        if (actRs != null && prepay == null) {
            boolean lok = true;
            try {
                AppActServiceImpl appActService = SpringUtil.getBean(AppActServiceImpl.class);
                appActService.cancelOrder(actRs.getBookingid(), actRs.getProjectid(), "", "");
            } catch (Exception e) {
                SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                        RobotUtils.transRobotExceptionMsg("定时自动取消未支付预约订单异常，订单号:" + actRs.getBookingid()));
                throw e;
//                handlerError(JSON.toJSONString(msg));
                //  调用报警机器人

            }
        }

    }

    @Override
    public StdOrderData getStdOrerData(String bookingId, String projectId) {//虚拟成订单表.方便接口做转换 目前主要用在门票发码
        initData();
        Actsite actsite = ContentCacheTool.getActsite(projectId, actRs.getSitecode());
        StdOrderData stdOrderData = new StdOrderData();
        Booking_rs fakerRs = new Booking_rs();
        Ticket_rs ticketRs = new Ticket_rs();
        fakerRs.setBookingid(actRs.getBookingid());
        fakerRs.setProjectid(actRs.getProjectid());
        fakerRs.setAmount(actRs.getAmount());
        fakerRs.setAnz(actRs.getPersons());
        fakerRs.setGuestname(actRs.getBookername());
        fakerRs.setBookername(actRs.getBookername());
        fakerRs.setTel(actRs.getTelephone());
        fakerRs.setPtype(ProdType.ACTGROUP.val());

        ticketRs.setBookingid(actRs.getBookingid());
        ticketRs.setProjectid(actRs.getProjectid());
        ticketRs.setAnz(actRs.getPersons());
        ticketRs.setUsedate(actRs.getUsedate());
        ticketRs.setRegno(actRs.getBookingid());
        ticketRs.setTcode(actsite.getOutcode()); //这里直接用深大的票型代码
        ticketRs.setPrice(actRs.getAmount());


        stdOrderData.setBookingRs(fakerRs);
        stdOrderData.getTickets().add(ticketRs);


        return stdOrderData;
    }

    @Override
    public void writeQrCodeAndSave(StdOrderData orderData, String qrCode, String assistCode, String outid) {
        ActrsMapper actrsMapper = SpringUtil.getBean(ActrsMapper.class);
        actRs.setQrcode(qrCode);
        actRs.setAssistcode(assistCode);
        actRs.setOutid(outid);
        actrsMapper.save(actRs);
    }

    @Override
    public void updateTicketOrderStatus(String projectId, String bookingId, String status) {
        RLock orderLock = RedisTool.getRedissonClient().getLock(RedisKey.getOrderLockKey(projectId, bookingId));
        boolean lok = false;//等待5秒.
        try {
            lok = orderLock.tryLock(3, 5, TimeUnit.SECONDS);
            if (lok) {
                CoreOrderWriter coreOrderWriter = SpringUtil.getBean(CoreOrderWriter.class);
                coreOrderWriter.updActRsStatus(bookingId, StatusUtil.ActRsStatus.FINISH);
                orderLock.unlock();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void initOrders(List<String> orderids) {
        ActrsMapper actrsMapper = SpringUtil.getBean(ActrsMapper.class);
        if (orderids.size() > 0) {
            this.actRs = actrsMapper.findAct_rsByBookingid(orderids.get(0));
        }


    }

    @Override
    public int getOrdersSize() {
        return actRs == null ? 0 : 1;
    }

    @Override
    public BigDecimal getPayOrderAmount() {
        return actRs == null ? BigDecimal.ZERO : actRs.getAmount();
    }

    @Override
    public String getPayOrderDesc() {
        Actsite actsite = ContentCacheTool.getActsite(actRs.getProjectid(), actRs.getSitecode());
        return "预约" + actsite.getDescription();
    }

    @Override
    public Long getPayExpireTimeStamp(String projectId) throws DefinedException {
        int expireMin = ContentCacheTool.getSysOrderExpireMinutue(projectId);
        long now = DateTime.now().getTime();
        Long myExpire = CalculateDate.asUtilDate(actRs.getCreatetime()).getTime() + expireMin * 60 * 1000;

        if (myExpire < now) {
            throw new DefinedException("订单支付时间已经过期", ResultCode.PAYFAIL.code());
        }
        return myExpire;
    }

    @Override
    public String getPrepaySeqNo(SeqNoService seqNoService, int onlinePayMethod) {
        return seqNoService.getSequenceID_withOrderId(SystemUtil.SequenceKey.PREPAY, actRs.getBookingid() + onlinePayMethod);
    }


}
