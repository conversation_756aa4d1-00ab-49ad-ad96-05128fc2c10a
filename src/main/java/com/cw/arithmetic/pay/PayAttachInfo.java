package com.cw.arithmetic.pay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 创建支付信息.提供给查询支付,支付通知使用
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/7 16:25
 **/
@Data
public class PayAttachInfo {
    //    String bookingids;  //单个,多个支付的所有 订单号.逗号分割
    List<String> bookingids = new ArrayList<>(0);
    String proejectid;  //项目 ID
//    String uid;  //支付的用户 ID
    String outTradeNo;//将提交给支付厂商的 ID 流水号带回来
//    String reqTime;//实际请求时间 2021-11-07T16:25:00  LocalDateTime 格式
    BigDecimal totalPay;//支付总金额  记录当时的合并支付总金额
    boolean lnotify = false;//是否需要服务器websocket 通知关闭支付窗口
    Integer scene = 0;//支付场景值.0为普通订单 .对应PAYUTIL中的常量

}
