package com.cw.arithmetic.lang;

import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RlangCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.entity.Rlang;
import com.cw.entity.Sysconf;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.utils.SystemUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.Hashtable;
import java.util.List;
import java.util.Locale;
import java.util.Properties;

/**
 * 翻译资源中心
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/10/23 16:28
 **/
@Slf4j
public class R {

    public static final String uniqueKey = "sqlid";

    public static final String zh = Locale.CHINESE.getLanguage();
    private static Hashtable<String, Hashtable<String, String>> consoleLangInfo = new Hashtable<>(); // 后台的一些返回提示翻译.异常中文对应的翻译
    public static final String en = Locale.ENGLISH.getLanguage();
    private static Table<String, String, Hashtable<String, String>> langInfo = HashBasedTable.create(); //基础内容多语言翻译本地缓存

  /*  static {
        Hashtable<String, String> enLang = new Hashtable<>();
        enLang.put("你好", "hello");
        enLang.put("欢迎来到景区", "welcome to XX scenic spot");
        enLang.put("测试参数:{},{}", "test param:{},{}");
        langInfo.put("001", Locale.ENGLISH.getLanguage(), enLang);


        consoleLangInfo.put(Locale.ENGLISH.getLanguage(), enLang);  //代码翻译资源
    }*/


    /**
     * 加载
     */
    public static void init() {
        //Hashtable<String, String> enLang = new Hashtable<>();
        //enLang.put("你好", "hello");
        //enLang.put("欢迎来到景区", "welcome to XX scenic spot");
        ////加载各个景区做好的多语言翻译
        //langInfo.put("001", Locale.ENGLISH.getLanguage(), enLang);

        RlangCache rlangCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RLANG);
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getConfCache();
        ;


        for (Sysconf sysconf : sysConfCache.getAllSysConf()) {
            List<Rlang> rlangs = rlangCache.getGroupList(sysconf.getProjectid(), en);

            Hashtable<String, String> langHashTable = new Hashtable<>();
            rlangs.forEach(r -> langHashTable.put(r.getTextype() == 0 ? r.getStd() : r.getKeyid(), r.getOutput()));

            langInfo.put(sysconf.getProjectid(), en, langHashTable);

            if (langHashTable.size() > 0) {
                log.info("{} 已加载多语言翻译{} 条", sysconf.getProjectid(), langHashTable.size());
            }
        }

        //暂时只加载英文资源.把项目中的中文全部翻译成英文
        Properties base = new Properties();
        InputStream is = R.class.getClassLoader().getResourceAsStream("lang/" + en + ".properties");
        try {
            base.load(is);
            // 将Properties文件的内容转存到Hashtable中
            Hashtable<String, String> langtable = new Hashtable<>();
            for (String key : base.stringPropertyNames()) {
                String value = base.getProperty(key);
                langtable.put(key, value);
            }

            consoleLangInfo.put(en, langtable);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void refreshProjectLang(String projectId) {
        RlangCache rlangCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RLANG);
        List<Rlang> rlangs = rlangCache.getGroupList(projectId, en);

        Hashtable<String, String> langHashTable = new Hashtable<>();
        rlangs.forEach(r -> langHashTable.put(r.getTextype() == 0 ? r.getStd() : r.getKeyid(), r.getOutput()));

        langInfo.put(projectId, en, langHashTable);
    }





    /**
     * 通用多语言翻译
     *
     * @param str
     * @return
     */
    public static String lang(String str) {
        String lang = WebAppGlobalContext.getCurrentLang();
        if (lang.equals(zh)) {
            return str;
        }
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();

        if (langInfo.contains(projectId, lang)) {
            String result = langInfo.get(projectId, lang).getOrDefault(str, str).trim();
            return StrUtil.isBlank(result) ? str : result;
        }
        return str;
    }

    /**
     * 获取富文本翻译
     * @param t
     * @param field
     * @param <T>
     * @return
     */
    public static <T> String richlang(String orgText, T t, String columnName) {
        String key = null;
        try {
            key = SecureUtil.md5(ReflectUtil.getFieldValue(t, uniqueKey) + columnName);
        } catch (UtilException e) {
            return orgText;
        }
        String transLang = lang(key);
        if (transLang.equals(key)) {  //富文本跟翻译的结果一样.返回的还是orgtext
            return orgText;
        } else {
            return transLang;
        }
    }

    /**
     * 带参数的多语言翻译
     *
     * @param str
     * @param param
     * @return
     */
    public static String plang(String str, String... param) {
        String targetLang = en;  //WebAppGlobalContext.getCurrentLang();  //TODO  改成从请求中获取
        if (targetLang == null || targetLang.equals(zh)) {
            return str;
        }

        String langid = SecureUtil.md5(str);
        if (consoleLangInfo.containsKey(targetLang) && consoleLangInfo.get(targetLang).containsKey(langid)) {
            //return new Me.format(str,param);
            String pattern = consoleLangInfo.get(targetLang).getOrDefault(langid, str);
            return StrUtil.isBlank(pattern) ? str : StrUtil.format(pattern, param);
        }
        return str;
    }


    public static String contentLang(String key, String content) {
        return content;
    }

    /**
     * 获取字段对应的记录的多语言资源ID
     *
     * @param o
     * @param id
     * @param field
     * @return
     */
    public static <T> String getLangSourceId(T o, Long id, String field) {
        return SecureUtil.md5(o.getClass().getName() + id + field);
    }
}
