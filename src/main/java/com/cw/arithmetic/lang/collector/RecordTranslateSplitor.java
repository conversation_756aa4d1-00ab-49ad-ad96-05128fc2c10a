package com.cw.arithmetic.lang.collector;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.cw.arithmetic.lang.R;
import com.cw.entity.Rlang;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.reflect.FieldUtils;

import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 将记录身上的需要翻译的字段拆成待翻译记录
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/10/26 10:05
 **/
public class RecordTranslateSplitor {


    static Pattern htmlPattern = Pattern.compile("<[^>]+>", Pattern.CASE_INSENSITIVE);

    public static boolean lRichText(String content) {
        //String htmlTagPattern = "<(\"[^\"]*\"|'[^']*'|[^'\">])*>";

        //Pattern pattern = Pattern.compile(htmlTagPattern);

        Matcher matcher = htmlPattern.matcher(content);

        // 判断是否匹配成功
        return matcher.find();
    }

    public static <T> List<Rlang> splitRecord(List<T> records, String columns, String searchColumn) {
        List<Rlang> result = Lists.newArrayList();
        List<String> columnList = Lists.newArrayList(columns.split(","));
        try {
            for (T record : records) {
                for (String columnName : columnList) {
                    Rlang rlang = new Rlang();

                    String chword = FieldUtils.readField(record, columnName, true) + "";
                    if (StrUtil.isBlank(chword)) { //空字段不做翻译
                        continue;
                    }

                    boolean lrichtext = lRichText(chword);  //判断是否为富文本类型内容
                    if (lrichtext) {
                        chword = chword.replaceAll("\t", "").replaceAll("\n", ""); //去掉换行符 制表符号
                    }

                    String key = lrichtext ? ReflectUtil.getFieldValue(record, R.uniqueKey) + columnName : chword;  //如果是相同的内容Id
                    rlang.setKeyid(SecureUtil.md5(key));// 生成资源唯一KEY sqlid 值+ 字段名作为KEY   字段名做KEY .文字有小改动.原翻译还在.  文字内容做KEY.加个标点.原来的翻译就没了.
                    rlang.setSearchid(ReflectUtil.getFieldValue(record, searchColumn) + "");//生成搜索关键字
                    rlang.setStd(chword);
                    rlang.setTextype(lrichtext ? 1 : 0);
                    if (Validator.hasChinese(rlang.getStd())) {
                        result.add(rlang);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return result;
    }


    public static List<Rlang> collectTransTexts(Set<String> records) {
        List<Rlang> result = Lists.newArrayList();
        try {
            for (String record : records) {
                Rlang rlang = new Rlang();
                record = record.replaceAll("\t", "").replaceAll("\n", ""); //去掉换行符 制表符号
                //String key = record + typeName;  //
                rlang.setKeyid(SecureUtil.md5(record));
                rlang.setSearchid(record);//生成搜索关键字
                rlang.setStd(record);
                rlang.setTextype(0);
                if (Validator.hasChinese(rlang.getStd())) {
                    result.add(rlang);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return result;
    }

}
