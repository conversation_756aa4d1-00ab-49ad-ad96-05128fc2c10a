package com.cw.arithmetic.lang.collector;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.cache.BaseCacheData;
import com.cw.cache.GlobalCache;
import com.cw.entity.Rlang;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/10/26 09:43
 **/
public class ConfigDataCollector implements LangTranslateCollector {

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        SystemUtil.GlobalDataType[] fetchData = new SystemUtil.GlobalDataType[]{
                SystemUtil.GlobalDataType.ACTGROUP,
                SystemUtil.GlobalDataType.ACTPERIOD,
                SystemUtil.GlobalDataType.HOTEL,
                SystemUtil.GlobalDataType.ROOMTYPE,
                SystemUtil.GlobalDataType.KITGROUP,
                SystemUtil.GlobalDataType.PRODUCTKIT,
                SystemUtil.GlobalDataType.KITITEM,
                SystemUtil.GlobalDataType.MEETING,
                SystemUtil.GlobalDataType.MEETINGGROUP,
                SystemUtil.GlobalDataType.MENUS,
                SystemUtil.GlobalDataType.MENUCONTENT,
                SystemUtil.GlobalDataType.PARK,
                SystemUtil.GlobalDataType.PARKSITE,
                SystemUtil.GlobalDataType.TICKET,
                SystemUtil.GlobalDataType.TICKETGROUP,
                SystemUtil.GlobalDataType.TRAVELTIP,
                SystemUtil.GlobalDataType.FACTOR,
                SystemUtil.GlobalDataType.SYSCONF

        };
        //TODO  补充sysconf, factor 的翻译

        List<Rlang> totalList = Lists.newArrayList();
        Set<String> exid = new HashSet<>();
        for (SystemUtil.GlobalDataType value : fetchData) {
            BaseCacheData baseCacheData = GlobalCache.getDataStructure().getCache(value);
            List<Rlang> cacheLangs = baseCacheData.collectSourceLang(projectId);
            if (CollectionUtil.isNotEmpty(cacheLangs)) {
                for (Rlang cacheLang : cacheLangs) {
                    if (!exid.contains(cacheLang.getKeyid())) {  //避免相同的词条造成重复翻译
                        cacheLang.setProjectid(projectId);
                        totalList.add(cacheLang);
                        exid.add(cacheLang.getKeyid());
                    }
                }
            }
        }
        Collections.sort(totalList, (o1, o2) -> {
            return o1.getTextype() - o2.getTextype();
        });


        return totalList;
    }


}
