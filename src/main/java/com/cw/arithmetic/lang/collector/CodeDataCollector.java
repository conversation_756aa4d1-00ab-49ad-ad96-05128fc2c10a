package com.cw.arithmetic.lang.collector;

import cn.hutool.core.lang.Validator;
import com.cw.entity.Rlang;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/10/26 09:46
 **/
public class CodeDataCollector implements LangTranslateCollector {
    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        String folderbase = System.getProperty("user.dir") + "/src/main/java/com/cw/";
        //String filePath =folderbase;  //"/Users/<USER>/IdeaProjects/BEIHAI/dsmall/src/main/java/com/cw/";
        //Set<String> lang= Sets.newHashSet();
        //traverseDirectory(filePath,lang);
        //
        //System.out.println("最终翻译结果大小"+lang.size());
        return null;
    }

    public List<String> findStringsInFile(String filePath) {
        List<String> strings = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            Pattern pattern = Pattern.compile("\"([^\"]*)\"");

            while ((line = reader.readLine()) != null) {
                if (line.startsWith("//") || line.trim().startsWith("@")) {
                    continue;
                }
                Matcher matcher = pattern.matcher(line);
                while (matcher.find()) {
                    String content = matcher.group(1);
                    if (Validator.hasChinese(content)) {
                        strings.add(content);
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return strings;
    }

    public void traverseDirectory(String directoryPath, Set<String> lang) {
        File directory = new File(directoryPath);

        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();

            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        traverseDirectory(file.getAbsolutePath(), lang);
                    } else {
                        List<String> result = findStringsInFile(file.getAbsolutePath());
                        if (result.size() > 0) {
                            for (String string : result) {
                                System.out.println(file.getAbsolutePath() + " " + string);
                            }
                            lang.addAll(result);
                        }
                    }
                }
            }
        }
    }


}
