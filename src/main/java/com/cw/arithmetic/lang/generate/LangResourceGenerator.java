package com.cw.arithmetic.lang.generate;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import lombok.Data;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 运行这个类.可以收集代码中需要翻译的内容写到一个currentlang.xlsx 文件中
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/10/27 10:43
 **/
public class LangResourceGenerator {

    private static List<String> findStringsInFile(String filePath) {
        List<String> strings = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            Pattern pattern = Pattern.compile("\"([^\"]*)\"");   //查找所有被""包裹的内容

            //Pattern pattern = Pattern.compile("R\\.plang\\((.*?)\\)");

            while ((line = reader.readLine()) != null) {
                if (line.startsWith("//") || line.trim().startsWith("@")) {
                    continue;
                }
                Matcher matcher = pattern.matcher(line);
                while (matcher.find()) {
                    String content = matcher.group(1);
                    if (Validator.hasChinese(content)) {
                        content = content.replaceAll("\"", "");
                        if (content.contains(",")) {
                            String[] cs = content.split(",");
                            strings.add(cs[0].trim());
                        } else {
                            strings.add(content);
                        }
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return strings;
    }

    private static void traverseDirectory(String directoryPath, Hashtable<String, String> codeInfo) {
        File directory = new File(directoryPath);

        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();

            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        traverseDirectory(file.getAbsolutePath(), codeInfo);
                    } else {
                        List<String> result = findStringsInFile(file.getAbsolutePath());
                        if (result.size() > 0) {
                            for (String string : result) {
                                codeInfo.put(SecureUtil.md5(string), string);//把代码里的字符串md5 作为唯一key 保存到codeInfo
                            }
                        }
                    }
                }
            }
        }
    }


    public static void main(String[] args) {
        String userdir = System.getProperty("user.dir");
        String folderbase = userdir + "/src/main/java/com/cw/";

        String filePath = folderbase;  //"/Users/<USER>/IdeaProjects/BEIHAI/dsmall/src/main/java/com/cw/";
        Hashtable<String, String> codeLangTable = new Hashtable<>();

        traverseDirectory(filePath, codeLangTable);

        //List<CodeLang> codeLangs=new ArrayList<>();
        String targetLang = reqInput("当前翻译词条:" + codeLangTable.size() + "条.请输入要生成翻译的语言:直接回车默认en ");


        if (StrUtil.isBlank(targetLang)) {
            targetLang = "en";
        }


        String excelfilePath = userdir + "/src/main/resources/lang/transexcel/currentlang.xlsx";  //TODO  将最新翻译与已经翻译的lang.xlsx 做对比.把没翻译的新词条放在最底下


        outputExcel(excelfilePath, generateLangExcel(targetLang, codeLangTable));

        System.out.println("最终翻译结果大小" + codeLangTable.size());
    }

    private static List<CodeLang> generateLangExcel(String targetLang, Hashtable<String, String> codeLangTable) {
        List<CodeLang> codeLangs = new ArrayList<>();
        codeLangTable.forEach((k, v) -> {
            CodeLang codeLang = new CodeLang();
            codeLang.setId(k);
            codeLang.setOrglang(v);
            codeLang.setTarget(v);
            codeLangs.add(codeLang);
        });
        //TODO  于当前语言的properties 做对比.没有的就生成

        //排序  未翻译的词条排在下面.已经翻译的放在上面
        Collections.sort(codeLangs, new Comparator<CodeLang>() {
            @Override
            public int compare(CodeLang p1, CodeLang p2) {
                if (p1.getTarget().isEmpty() && !p2.getTarget().isEmpty()) {
                    return 1;
                } else if (!p1.getTarget().isEmpty() && p2.getTarget().isEmpty()) {
                    return -1;
                } else {
                    return 0;
                }
            }
        });
        return codeLangs;
    }


    public static void outputExcel(String fileName, List<CodeLang> userList) {

        // 构建Excel写入器
        ExcelWriterBuilder writerBuilder = EasyExcel.write(fileName, CodeLang.class);

        // 定义单元格样式策略
        //WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置表头样式
        // headWriteCellStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        // headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        //HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, null);

        // 写入数据到Excel文件
        writerBuilder.sheet("Sheet1").doWrite(userList);

        System.out.println("Excel文件写入完成！");
    }

    public static String reqInput(String tip) {
        Scanner scanner = new Scanner(System.in);
        StringBuilder help = new StringBuilder();
        help.append("" + tip + "：");
        System.out.println(help.toString());
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (ipt != null || !ipt.trim().isEmpty()) {
                return ipt.trim();
            }
        }
        String s = "Business hours:{}={}\n" +
                "Hello\n" +
                "Good bye";

        String b = "营业时间:{}={}\n" +
                "你好\n" +
                "再见";

        return "";
    }

    @Data
    private static class CodeLang {
        private String id;
        private String orglang;
        private String target;
    }


}
