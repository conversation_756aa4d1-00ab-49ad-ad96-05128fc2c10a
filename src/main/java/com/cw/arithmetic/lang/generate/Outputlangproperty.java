package com.cw.arithmetic.lang.generate;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;

public class Outputlangproperty {

    public static void main(String[] args) {
        String userdir = System.getProperty("user.dir");
        String folderbase = userdir + "/src/main/java/com/cw/";


        String excelFilePath = userdir + "/src/main/resources/lang/transexcel/currentlang.xlsx";
        String propertiesFilePath = userdir + "/src/main/resources/lang/ennew.properties";

        // 创建Properties对象
        Properties properties = new Properties();

        try (Workbook workbook = new XSSFWorkbook(excelFilePath)) {
            // 读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 遍历行
            for (Row row : sheet) {
                Cell keyCell = row.getCell(0);
                Cell valueCell = row.getCell(2);

                // 检查单元格是否为空
                if (keyCell != null && valueCell != null) {
                    String key = keyCell.getStringCellValue();
                    String value = valueCell.getStringCellValue();

                    // 将属性和值添加到Properties对象
                    properties.setProperty(key, value);
                }
            }

            // 保存Properties对象到文件
            try (FileOutputStream fos = new FileOutputStream(propertiesFilePath)) {
                properties.store(fos, "Generated from Excel");
                System.out.println("Properties文件生成成功！");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
