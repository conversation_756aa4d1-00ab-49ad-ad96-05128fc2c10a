package com.cw.arithmetic.others;

import com.cw.utils.CalculateDate;

import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/11/20 11:41
 **/
public class DateRange {
    private Date start;
    private Date end;

    public void add(Date date) {
        if (start == null || CalculateDate.beforeEqual(date, start)) {
            start = date;
        }
        if (end == null || CalculateDate.afterEqual(date, end)) {
            end = date;
        }
    }

    public DateRange merge(DateRange other) {
        if (other.start != null) {
            add(other.start);
        }
        if (other.end != null) {
            add(other.end);
        }
        return this;
    }

    public Date getStart() {
        return start;
    }

    public Date getEnd() {
        return end;
    }
}
