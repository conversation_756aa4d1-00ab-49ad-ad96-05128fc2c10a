package com.cw.arithmetic.others;


import com.cw.arithmetic.PojoUtils;
import com.cw.entity.Room_rs;
import com.cw.utils.CalculateDate;

import java.util.Date;
import java.util.HashMap;

/**
 * 保存一个预订的状态
 *
 * <AUTHOR>
 */
public class TRsdata {

    private Room_rs rs = null;

//	private String rmtype="";

//	private String channel="";

    private boolean emp = false;

    private HashMap<Date, TRsitem> items;

    public TRsdata(Room_rs rs) {
        items = new HashMap<Date, TRsitem>();
        this.rs = PojoUtils.cloneEntity(rs);
        if (rs.getRmtype().isEmpty() || !CalculateDate.emptyDate(CalculateDate.asUtilDate(rs.getCanceldate()))) {
            emp = true;
        } else {
            produceItem(rs);
        }
    }

    public TRsdata(Room_rs rs, boolean createEmp) {
        items = new HashMap<Date, TRsitem>();
        this.rs = PojoUtils.cloneEntity(rs);
        emp = true;
    }

    private void produceItem(Room_rs rs) {
        int count = CalculateDate.compareDates(rs.getDeptdate(), rs.getArrdate()).intValue();
        for (int i = 0; i < count; i++) {
            Date d = CalculateDate.reckonDay(rs.getArrdate(), 5, i);
            items.put(d, new TRsitem(d, rs));
        }
    }

    public String getRmtype() {
        if (emp) {
            return "";
        }
        return rs.getRmtype();
    }

    public String getBlock() {
        if (emp) {
            return "";
        }
        return rs.getBlock();
    }

    public Date getArrDate() {
        if (emp) {
            return CalculateDate.NULLDATE;
        } else {
            return rs.getArrdate();
        }
    }

    public Date getDeptDate() {
        if (emp) {
            return CalculateDate.NULLDATE;
        } else {
            return rs.getDeptdate();
        }
    }

    public boolean isEmp() {
        return emp;
    }

    public Room_rs getRoom_rs() {
        return rs;
    }

    public int getRoomOcc(Date date) {
        if (emp) {
            return 0;
        }
        if (!(date instanceof Date)) {
            date = (Date) date;
        }
        if (items.containsKey(date)) {
            return CalculateDate.isEqual(rs.getArrdate(), rs.getDeptdate()) ? 0 : rs.getAnz();
        }
        return 0;
    }

    public boolean needUpdateThisDate(Date d) {
        if (!(d instanceof Date)) {
            d = (Date) d;
        }
        return items.containsKey(d);
    }

    /**
     * 创建这个类是为了记录预订每天不同的东西.虽然现在大部分属性每天都是一样的.暂时还没增加每一天不同的属性
     *
     * <AUTHOR>
     */
    private class TRsitem {
        public Date itemDate = null;

        public TRsitem(Date date, Room_rs rs) {
            this.itemDate = date;
        }
    }


}
