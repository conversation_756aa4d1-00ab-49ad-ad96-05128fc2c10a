package com.cw.arithmetic.others;
import com.cw.entity.Room_rs;
import com.cw.utils.CalculateDate;
import java.text.MessageFormat;
import java.util.*;
import java.util.Map.Entry;

public class CalcRoomsPickup {

    public static void main(String[] args) {
        Room_rs rs = new Room_rs();
        rs.setRmtype("BTW");
        rs.setAnz(1);
        rs.setArrdate(new Date());
        rs.setDeptdate(CalculateDate.reckonDay(new Date(), 5, 1));
//        rs.setChannel("WX");

        TRsdata old = new TRsdata(rs, true);
        rs.setAnz(2);
        rs.setBlock("TB");
        rs.setRmtype("ATM");
        TRsdata newd = new TRsdata(rs);

        List<TRoomsUpdate> lis = CalcRoomsPickup.calcAvailability(old, rs);

        printResult("新增", lis);


    }

    public static void printResult(String op, List<TRoomsUpdate> lis) {
        if (lis != null && lis.size() > 0) {
            for (TRoomsUpdate upd : lis) {
                String updateInfo = upd.getChangeNum() > 0 ? " 增加" : "减少";
                updateInfo += Math.abs(upd.getChangeNum()) + "间";

                System.out.println(op + "            " + MessageFormat.format("更新渠道{0}中的房型{1}  {2},日期{3}---{4}",
                        upd.getBlock(), upd.getRmtype(), updateInfo, CalculateDate.dateToString(upd.getStartdate()), CalculateDate.dateToString(upd.getEnddate())));

            }
        } else {
            System.out.println(op + "            " + "不用更新!!!");
        }
    }

    public static void testA() {

    }

    public static TRsdata readOldRs(Room_rs rs) {
        TRsdata oldD = new TRsdata(rs);
        return oldD;
    }

    public static List<TRoomsUpdate> getPickupData(Room_rs oldRs, Room_rs newRs) {
        TRsdata oldD = readOldRs(oldRs);
        List<TRoomsUpdate> roomUpd = calcAvailability(oldD, newRs);
        List<TRoomsUpdate> result = new ArrayList<TRoomsUpdate>();
        for (TRoomsUpdate rpd : roomUpd) {
            if (rpd.getChangeNum() > 0) {
                result.add(rpd);
            }
        }
        return result;
    }


    public static List<TRoomsUpdate> calcAvailability(TRsdata oldData, Room_rs newRs) {
        TRsdata newData = new TRsdata(newRs);
        String oldRmtype = oldData.getRmtype();
        String newRmtype = newData.getRmtype();
        Date startdate = CalculateDate.minDate(oldData.getArrDate(), newData.getArrDate());
        Date enddate = CalculateDate.maxDate(oldData.getDeptDate(), newData.getDeptDate());
        String oldBlock = oldData.getBlock();
        String newBlock = newData.getBlock();

        HashMap<String, List<Tinfo>> hm = new HashMap<String, List<Tinfo>>();
        HashSet<String> blocks = new HashSet<String>();
//        if (!oldChannel.isEmpty()) {
        blocks.add(oldBlock);
//        }
//        if (!newChannel.isEmpty()) {
        blocks.add(newBlock);
//        }


        if (CalculateDate.emptyDate(startdate) || CalculateDate.emptyDate(enddate)) {//删除取消的预订这种操作..不用做更新了
            return new ArrayList<TRoomsUpdate>();
        }

        int count = CalculateDate.compareDates(enddate, startdate).intValue();
        if (count == 0) {  //住多天改为住0天时.要释放房间..住0天改成住多天时.要增加占用
            count++;
        }


        if (!oldRmtype.isEmpty()) {
            if (!hm.containsKey(oldRmtype)) {
                hm.put(oldRmtype, new ArrayList<Tinfo>());
            }

        }
        if (!newRmtype.isEmpty()) {
            if (!hm.containsKey(newRmtype)) {
                hm.put(newRmtype, new ArrayList<Tinfo>());
            }
        }

        for (int i = 0; i < count; i++) {
            for (String rmtype : hm.keySet()) {
                hm.get(rmtype).add(new Tinfo());
            }
        }

        for (Entry<String, List<Tinfo>> entry : hm.entrySet()) {
            String updateRoomtype = entry.getKey();
            List<Tinfo> ls = entry.getValue();

            for (int i = 0; i < count; i++) {
                Date d = CalculateDate.reckonDay(startdate, 5, i);
                Tinfo info = ls.get(i);//读取本房型的每日占用情况
                info.setDate(d);
                if (oldData.needUpdateThisDate(d) && updateRoomtype.equals(oldData.getRmtype())) {//如果旧记录产生该房型的占用信息.就收集
                    info.setOrgPickup(oldData.getBlock(), oldData.getRoomOcc(d));
                }
                if (newData.needUpdateThisDate(d) && updateRoomtype.equals(newData.getRmtype())) {//如果旧记录产生该房型的占用信息.就收集
                    info.setCurrentPickup(newData.getBlock(), newData.getRoomOcc(d));
                }
            }
        }

        for (List<Tinfo> infolist : hm.values()) {//SUM统计占用
            for (Tinfo tinfo : infolist) {
                tinfo.sumPicupChange();
            }
        }
        List<TRoomsUpdate> result = new ArrayList<TRoomsUpdate>();
        for (Entry<String, List<Tinfo>> entry : hm.entrySet()) {
            String kat = entry.getKey();
            result.addAll(collectInfo(kat, blocks, entry.getValue()));
        }

        //返回数据结构?EG:
        //channel:WX,KAT, STARTDATE: 2015-10-22 ENDDATE:2015-10-30,CHANGENUM:-2
        return result;
    }

    private static List<TRoomsUpdate> collectInfo(String kat, HashSet<String> channels, List<Tinfo> infos) {
        HashMap<String, List<TRoomsUpdate>> updateInfos = new HashMap<String, List<TRoomsUpdate>>();
        HashMap<String, List<Date>> datas = new HashMap<String, List<Date>>();//相同更新条件的.放在一个MAP里

        for (Tinfo info : infos) {
            for (String channel : channels) {
                int changeNum = info.getChannelUpdateNum(channel);
                if (changeNum != 0) {
                    String key = channel + "," + changeNum;  //CHANNEL.加减数字作为KEY
                    if (!datas.containsKey(key)) {
                        datas.put(key, new ArrayList<Date>());
                    }
                    datas.get(key).add(info.getDate());
                }
            }
        }

        String block = "";
        Integer num = 0;
        List<TRoomsUpdate> result = new ArrayList<TRoomsUpdate>();//Result

        for (Entry<String, List<Date>> entry : datas.entrySet()) {
            String[] keys = entry.getKey().split(",");
            block = keys[0];
            num = Integer.parseInt(keys[1]);
            List<Date[]> dates = CalculateDate.getDateStartEnd2(entry.getValue());
            for (Date[] range : dates) {
                TRoomsUpdate rUpd = new TRoomsUpdate();
                rUpd.setChangeNum(num);
                rUpd.setBlock(block);
                rUpd.setRmtype(kat);
                rUpd.setStartdate(range[0]);
                rUpd.setEnddate(range[1]);
                result.add(rUpd);
            }
        }

        return result;
    }


}
