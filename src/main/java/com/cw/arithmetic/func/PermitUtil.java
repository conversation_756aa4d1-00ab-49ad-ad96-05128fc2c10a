package com.cw.arithmetic.func;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.pojo.common.form.PerimitValidateResult;
import com.cw.utils.CalculateDate;
import com.cw.utils.SystemUtil;

import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/2/28 20:36
 **/
public class PermitUtil {
    public static String generatePermit(String projectId, Date expirDate) {
        String permit = projectId + SystemUtil.KEYSPLITSIGNAL + CalculateDate.dateToString(expirDate);
        return SysFuncLibTool.encodeAesContent(permit, projectId);
    }


    public static PerimitValidateResult getPermitCheckResult(String permit, String currentProjectId, Date currentDate) {
        PerimitValidateResult result = new PerimitValidateResult();
        String decodePermitStr = SysFuncLibTool.decodeAesContent(permit, currentProjectId);
        if (StrUtil.isNotBlank(permit) && StrUtil.isBlank(decodePermitStr)) {  //解码不出任何内容
            result.setValidPermit(false);
        }
        if (decodePermitStr != null && decodePermitStr.split(SystemUtil.KEYSPLITSIGNAL).length == 2) {
            String[] permitArr = decodePermitStr.split(SystemUtil.KEYSPLITSIGNAL);
            if (!permitArr[0].equals(currentProjectId)) {
                result.setError("非有效License");
                return result;
            }
            Date permitDate = CalculateDate.stringToDate(permitArr[1]);
            int length = CalculateDate.compareDates(permitDate, currentDate).intValue();
            if (length >= 0 && length <= 35) {
                result.setWarn(StrUtil.format("License将在{}到期 ,请及时更新 ", CalculateDate.dateToString(permitDate)));
            }
            if (length < 0) {
                result.setError("License 已过期");
            }
        }
        return result;
    }


}
