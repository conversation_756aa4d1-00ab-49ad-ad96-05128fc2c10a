package com.cw.arithmetic.func.checkinfo.template;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.cw.arithmetic.func.checkinfo.CheckInfoTemplate;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SpusitemCache;
import com.cw.entity.Checkqr_log;
import com.cw.entity.Spusitem;
import com.cw.mapper.CheckqrlogMapper;
import com.cw.pojo.dto.conf.req.coupon.ProductCheckStaticReq;
import com.cw.pojo.dto.conf.req.coupon.ProductChecktListReq;
import com.cw.pojo.dto.conf.res.coupon.CouponStaticDetail;
import com.cw.pojo.dto.conf.res.coupon.ProductCheckListRes;
import com.cw.pojo.dto.conf.res.coupon.ProductStaticDetail;
import com.cw.service.context.GlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;

import javax.persistence.criteria.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/11/8 11:04
 **/
public class WareCheckInfoTemplate extends CheckInfoTemplate {


    @Override
    protected ProductCheckListRes queryCheckListData(ProductChecktListReq req) {
        int currentPage = req.getPages().getQueryStartPage();
        int pageSize = req.getPages().getPagesize();
        String userid = GlobalContext.getCurrentUserId();
        String projectId = GlobalContext.getCurrentProjectId();
        if ("".equals(userid)) {
            return new ProductCheckListRes();
        }
        ProductCheckListRes result = new ProductCheckListRes();

        Page<Checkqr_log> checkqrLogPage = getCheckqrlogMapper().findAll((Root<Checkqr_log> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            List<Predicate> list = new ArrayList<Predicate>();
            if (StringUtils.isNotBlank(userid)) {
                list.add(cb.equal(root.get("checkuser"), userid)); //核销账号
            }

            list.add(cb.equal(root.get(SystemUtil.projectClumn), projectId));
            //核销时间降序
            List<Order> orderList = new ArrayList<>();
            orderList.add(cb.desc(root.get("checkdate")));
            Predicate[] p = new Predicate[list.size()];
            query.where(cb.and(list.toArray(p))).orderBy(orderList);
            return query.getRestriction();
        }, org.springframework.data.domain.PageRequest.of(currentPage, pageSize));

        SpusitemCache spusitemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM);
        List<ProductCheckListRes.ProductListData> productListDataList = checkqrLogPage.getContent().stream().map(
                row -> {
                    ProductCheckListRes.ProductListData data = new ProductCheckListRes.ProductListData();
                    data.setSqlid(row.getSqlid());
                    data.setCouponid(row.getQrid());
                    data.setCouponcode(row.getProdcode());
                    Spusitem spuitem = spusitemCache.getRecord(projectId, row.getProdcode());
                    if (spuitem != null) {
                        data.setDescription(spuitem.getDescription());

                    }
                    data.setNum(row.getAnz() == 0 ? 1 : row.getAnz());

                    data.setCheckdate(CalculateDate.asUtilDate(row.getCheckdate()));

                    return data;
                }
        ).collect(Collectors.toList());
        result.setRecords(productListDataList);
        result.setTotal((int) checkqrLogPage.getTotalElements());
        result.setPagesize(checkqrLogPage.getPageable().getPageSize());
        result.setCurrentpage(checkqrLogPage.getPageable().getPageNumber() + 1);
        result.setTotalpage(checkqrLogPage.getTotalPages());


        return result;
    }

    @Override
    protected List<CouponStaticDetail> queryStaticData(ProductCheckStaticReq req) {
        String userid = GlobalContext.getCurrentUserId();
        String projectId = GlobalContext.getCurrentProjectId();
        List<ProductStaticDetail> productStaticDetails = getCheckqrlogMapper().staticCheck(userid, DateUtil.toLocalDateTime(DateUtil.beginOfDay(req.getFromdate())),
                DateUtil.toLocalDateTime(DateUtil.endOfDay(req.getEnddate())));
        List<CouponStaticDetail> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(productStaticDetails)) {
            for (ProductStaticDetail node : productStaticDetails) {
                CouponStaticDetail detail = new CouponStaticDetail();
                detail.setCheckNum(node.getCheckNum());
                detail.setDescription(CustomData.getDesc(projectId, node.getDescription(), SystemUtil.CustomDataKey.spusitem));
                detail.setCheckPrice(BigDecimal.ZERO);
                list.add(detail);
            }
        }
        return list;
    }

    private CheckqrlogMapper getCheckqrlogMapper() {
        return SpringUtil.getBean(CheckqrlogMapper.class);
    }


}
