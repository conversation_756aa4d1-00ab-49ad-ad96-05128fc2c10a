package com.cw.arithmetic.func.checkinfo;

import com.cw.pojo.dto.conf.req.coupon.ProductCheckStaticReq;
import com.cw.pojo.dto.conf.req.coupon.ProductChecktListReq;
import com.cw.pojo.dto.conf.res.coupon.CouponStaticDetail;
import com.cw.pojo.dto.conf.res.coupon.ProductCheckListRes;
import com.cw.pojo.dto.conf.res.coupon.ProductCheckStaticRes;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/11/8 10:49
 **/
public abstract class CheckInfoTemplate {


    protected abstract ProductCheckListRes queryCheckListData(ProductChecktListReq req);

    protected abstract List<CouponStaticDetail> queryStaticData(ProductCheckStaticReq req);


    // 通用的统计结果构建方法
    protected ProductCheckStaticRes buildStaticResult(List<CouponStaticDetail> list) {
        ProductCheckStaticRes result = new ProductCheckStaticRes();
        result.setStaticList(list);
        Integer totalCheckNum = list.stream().map(CouponStaticDetail::getCheckNum).reduce(0, Integer::sum);
        result.setCheckNum(totalCheckNum);
        BigDecimal totalCheckPrice = list.stream().map(CouponStaticDetail::getCheckPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setCheckPrice(totalCheckPrice);
        return result;
    }

    public ProductCheckStaticRes executeStaticQuery(ProductCheckStaticReq req) {
        List<CouponStaticDetail> processedData = queryStaticData(req);
        ProductCheckStaticRes result = buildStaticResult(processedData);
        return result;
    }


    public ProductCheckListRes executeCheckListQuery(ProductChecktListReq req) {
        ProductCheckListRes result = queryCheckListData(req);
        return result;
    }


}
