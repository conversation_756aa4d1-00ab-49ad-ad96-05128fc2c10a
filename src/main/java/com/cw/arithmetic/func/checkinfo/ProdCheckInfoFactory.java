package com.cw.arithmetic.func.checkinfo;

import com.cw.arithmetic.func.checkinfo.template.WareCheckInfoTemplate;
import com.cw.utils.enums.ProdType;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/11/8 10:48
 **/
public class ProdCheckInfoFactory {
    public static HashMap<ProdType, CheckInfoTemplate> templateFactory = new HashMap();

    static {
        templateFactory.put(ProdType.WARES, new WareCheckInfoTemplate());
    }

    public static CheckInfoTemplate getCheckInfoTemplate(ProdType type) {
        if (templateFactory.containsKey(type)) {
            return templateFactory.get(type);
        }
        throw new IllegalArgumentException("产品类型未支持: " + type);
    }

}
