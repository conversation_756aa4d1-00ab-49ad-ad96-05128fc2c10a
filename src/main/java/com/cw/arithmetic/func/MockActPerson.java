package com.cw.arithmetic.func;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Random;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/7/16 15:16
 **/
public class MockActPerson {
    private static final LocalDateTime START_DATE = LocalDateTime.of(2024, 7, 16, 0, 0);
    private static final LocalDateTime END_DATE = LocalDateTime.of(2024, 7, 29, 23, 59);
    private static final int INITIAL_PARTICIPANTS = 1846;
    private static final int MIN_TARGET_PARTICIPANTS = 120000;
    private static final int MAX_TARGET_PARTICIPANTS = 140000;
    private static final Random RANDOM = new Random();

    private static int targetParticipants = -1;

    public static int calculateParticipants(LocalDateTime currentDateTime) {
        if (targetParticipants == -1) {
            targetParticipants = MIN_TARGET_PARTICIPANTS + RANDOM.nextInt(MAX_TARGET_PARTICIPANTS - MIN_TARGET_PARTICIPANTS + 1);
        }

        if (currentDateTime.isBefore(START_DATE)) {
            return INITIAL_PARTICIPANTS;
        }
        if (currentDateTime.isAfter(END_DATE)) {
            return targetParticipants;
        }

        long totalMinutes = ChronoUnit.MINUTES.between(START_DATE, END_DATE);
        long minutesPassed = ChronoUnit.MINUTES.between(START_DATE, currentDateTime);

        double baseIncrease = (targetParticipants - INITIAL_PARTICIPANTS) * ((double) minutesPassed / totalMinutes);

        // Add small random variation
        int randomVariation = RANDOM.nextInt(51) - 25; // Random number between -25 and 25

        int calculatedParticipants = (int) Math.round(INITIAL_PARTICIPANTS + baseIncrease) + randomVariation;

        // Ensure the result is not less than the previous day's count and not more than the target
        return Math.max(INITIAL_PARTICIPANTS, Math.min(calculatedParticipants, targetParticipants));
    }

    public static void main(String[] args) {
        int previousCount = INITIAL_PARTICIPANTS;
        System.out.println("Initial participants: " + INITIAL_PARTICIPANTS);

        // Simulate progression
        for (int i = 1; i <= 624; i++) {
            LocalDateTime currentDate = START_DATE.plusMinutes(i * 30);
            int currentCount = calculateParticipants(currentDate);

            // Ensure current count is at least as high as the previous count
            currentCount = Math.max(currentCount, previousCount);

            System.out.println("Participants on " + currentDate + ": " + currentCount);
            previousCount = currentCount;
        }
    }
}
