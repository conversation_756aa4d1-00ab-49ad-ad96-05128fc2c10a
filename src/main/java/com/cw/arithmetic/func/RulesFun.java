package com.cw.arithmetic.func;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.SpelFomulaFactory;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RulespolicyCache;
import com.cw.entity.Prepay;
import com.cw.entity.Rules;
import com.cw.entity.Rulespolicy;
import com.cw.mapper.*;
import com.cw.pojo.common.rule.CancelOrderRuleValidData;
import com.cw.pojo.common.rule.CreateOrderRuleValidData;
import com.cw.pojo.dto.conf.req.rules.StrategyJson;
import com.cw.pojo.dto.order.req.RefundReq;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.CancelDebitOptions;
import com.cw.utils.enums.ProdType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 增加一个类.方便做单元测试.
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/12/30 14:10
 **/
@Data
public class RulesFun {
    Rules rules;

    public RulesFun(Rules rules) {
        this.rules = rules;
    }

    /**
     * @param bookingDate   预定时间
     * @param bookingNumber 预定数量
     * @param lcalendar
     * @return
     */
    public CreateOrderRuleValidData getCreatOrderValidate(Date bookingDate, Integer bookingNumber, boolean lcalendar) {
        Date nowDate = new Date();
        CreateOrderRuleValidData createOrderRuleValidData = new CreateOrderRuleValidData();
        if (rules == null || !rules.getStatus()) {//如果规则不存在或者规则状态为不启用.默认为可以预定
            createOrderRuleValidData.setLallow(true);
            return createOrderRuleValidData;
        }

        //设置人工确定审核字段
        createOrderRuleValidData.setLNeedAudit(rules.getBuyaudit());
        //判断日期和数量是否符合规则
        Integer inAdvanceMin = rules.getInadvancemin();
        Integer inAdvanceMax = rules.getInadvancemax();
        int postAdvDay = CalculateDate.compareDates(bookingDate, nowDate).intValue();
        //判断时间区间
        String startTime = rules.getStarttime();
        String endTime = rules.getEndtime();
        //判断购买预定数量
        if (bookingNumber < rules.getLimitmin() && bookingNumber > 0) {
            createOrderRuleValidData.setReason("产品每单起步购买数量:" + rules.getLimitmin());
            createOrderRuleValidData.setLallow(false);
        } else if (bookingNumber > rules.getLimitmax() && bookingNumber > 0) {
            createOrderRuleValidData.setReason("产品每单最多可买数量:" + rules.getLimitmax());
            createOrderRuleValidData.setLallow(false);
        } else if (lcalendar && postAdvDay < inAdvanceMin) {
            //换算日期
            String adMinDate = getCanBookingDate(bookingDate, -rules.getInadvancemin());
            createOrderRuleValidData.setReason("购买提前日期至少从" + adMinDate + "起");
            createOrderRuleValidData.setLallow(false);
        } else if (lcalendar && postAdvDay > inAdvanceMax) {
            String adMaxDate = getCanBookingDate(bookingDate, -rules.getInadvancemax());
            createOrderRuleValidData.setReason("购买提前日期最多至" + adMaxDate);
            createOrderRuleValidData.setLallow(false);
        } else if (lcalendar && rules.getLimittime()) {
            //限制时间判断
            boolean ltoday = CalculateDate.isEqual(bookingDate, nowDate);

            String format = "HH:mm";
            if (ltoday && !CalculateDate.isInTimeRange(nowDate, startTime, endTime, format)) { //检验是否在下单限制时间段
                createOrderRuleValidData.setReason("购买时间为:" + startTime + " ~ " + endTime);
                createOrderRuleValidData.setLallow(false);
            }

        }
        if (lcalendar && StringUtils.isNotBlank(rules.getLimitweek())) {
            //判断当前日期是否在限制星期数内
            List<String> week = Arrays.asList(rules.getLimitweek().split(","));//保存0-6 0是星期天
            Integer bookingWeekDay = CalculateDate.getDayinWeek(bookingDate);//预定星期数
            if (week.contains(String.valueOf(bookingWeekDay))) {//当前星期在限制星期数内
                createOrderRuleValidData.setReason("当前日期不可购买");
                createOrderRuleValidData.setLallow(false);
            }

        }
        return createOrderRuleValidData;
    }

    /**
     * @param nowDate 当前系统日期
     * @param days    规则设定天数
     * @return 返回可预定日期 格式为 yyyy-MM-dd
     */
    private String getCanBookingDate(Date nowDate, Integer days) {
        Date canBookingDate = CalculateDate.reckonDay(nowDate, 5, days);
        return CalculateDate.timeToString(canBookingDate, "yyyy-MM-dd");

    }

    /**
     * 判断是否可以取消
     *
     * @param arrDate
     * @param deptDate
     * @param postDate
     * @return
     */
    public CancelOrderRuleValidData getCancelValidate(Date arrDate, Date deptDate, Date postDate, Prepay prepay) {
        CancelOrderRuleValidData cancelOrderValidData = new CancelOrderRuleValidData();
        if (rules == null || !rules.getStatus()) {//如果规则不存在或者规则状态为不启用.默认为可以取消
            cancelOrderValidData.setLallow(true);
            return cancelOrderValidData;
        }
        if (prepay == null) {
            cancelOrderValidData.setLallow(true);
            return cancelOrderValidData;
        }
        //todo 检查规则策略是否启动
        if (StringUtils.isNotBlank(rules.getPolicys())) {
            List<String> policys = Arrays.asList(rules.getPolicys().split(","));
            RulespolicyCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RULESPOLICY);
            //获取取消校验的策略集合 校验日期和当前时间判断
            List<Rulespolicy> list = cache.getDataListWithCondition(rules.getProjectid(), p -> policys.contains(p.getCode()) && p.getPolicytype() == 0);
            if (CollectionUtil.isNotEmpty(list)) {
                for (Rulespolicy node : list) {
                    Date startDate = node.getStartdate();
                    Date endDate = node.getEnddate();
                    if (ProdType.ITEMS.val().equals(rules.getType())) {//不支持伴手礼策略
                        continue;
                    }
                    //预定时段不可取消
                    if (ProdType.ROOM.val().equals(rules.getType()) || ProdType.TAOCAN.val().equals(rules.getType())) {//房型订单
                        //到店时间或离店时间在策略控制日期区间内，不可取消
                        //if (DateUtil.isIn(arrDate, startDate, endDate) || DateUtil.isIn(deptDate, startDate, endDate)) {
                        if (DateUtil.isIn(arrDate, startDate, endDate)) { //房 套餐到店日期在限制时段内
                            checkCancelRulespolicyScript(node, cancelOrderValidData, postDate, arrDate);
                            if (!cancelOrderValidData.getLallow()) {
                                return cancelOrderValidData;
                            }
                        }
                    } else if (node.getControltype() == 0 &&
                            (DateUtil.isIn(arrDate, startDate, endDate) || DateUtil.isIn(deptDate, startDate, endDate))) { //其他类型订单 判断使用时间
                        checkCancelRulespolicyScript(node, cancelOrderValidData, postDate, arrDate);
                        if (!cancelOrderValidData.getLallow()) {
                            return cancelOrderValidData;
                        }
                    } else if (node.getControltype() == 1 && DateUtil.isIn(arrDate, startDate, endDate)) { //取消限制
                        checkCancelRulespolicyScript(node, cancelOrderValidData, postDate, arrDate);
                        if (!cancelOrderValidData.getLallow()) {
                            return cancelOrderValidData;
                        }
                    }
                }
            }
        }
        if (rules.getRefundfreetime() != 0) { //判断有无设置期限分钟免费取消订单
            int refundFreeTime = rules.getRefundfreetime();
            Date prepayTime = Date.from(prepay.getCreatetime().atZone(ZoneId.systemDefault()).toInstant());
            int postAdvMin = (int) ((postDate.getTime() - prepayTime.getTime()) / 60 / 1000);//日期差分钟数
            if (postAdvMin < refundFreeTime) {
                //下单支付后在设置分钟数内 免费取消
                cancelOrderValidData.setLallow(true);
                return cancelOrderValidData;
            }
        }

        if (!rules.getRefundstatus()) {//不允许退单
            cancelOrderValidData.setLallow(false);
            cancelOrderValidData.setReason("不允许取消");
            return cancelOrderValidData;
        }
        //int postAdvDay = CalculateDate.compareDates(arrDate, postDate).intValue();
        Date arrEnd = DateUtil.offsetHour(arrDate, 24);//游玩日期当天晚
        int postAdvHour = (int) ((postDate.getTime() - arrEnd.getTime()) / 60 / 60 / 1000) * -1;//日期差小时数

        if (rules.getIsrefund() == 1) {//如果取消时间设置了提前取消的有效期
            Integer ruleAdvRefundDay = NumberUtil.parseInt(rules.getRefundday());//设置提前取消小时数
            if (postAdvHour < ruleAdvRefundDay) { //日期差为整数数标识提前
                cancelOrderValidData.setLallow(false);
                cancelOrderValidData.setReason("订单已过期.不能取消");
                cancelOrderValidData.setDebugReason("取消提前时间不能小于" + ruleAdvRefundDay + "小时");
//                cancelOrderValidData.setReason("取消时间不能小于" + (ruleAdvRefundDay == 0 ? "当" : ruleAdvRefundDay) + "天");
            }
        }
        cancelOrderValidData.setLNeedAudit(rules.getLauditing());//需要审核
        return cancelOrderValidData;
    }

    /**
     * @param node
     * @param cancelOrderValidData
     * @param postDate
     * @param arrDate
     * @return 判断规则是否可以返回
     */
    private CancelOrderRuleValidData checkCancelRulespolicyScript(Rulespolicy node, CancelOrderRuleValidData cancelOrderValidData, Date postDate, Date arrDate) {
        Date arrEndDate = DateUtil.endOfDay(arrDate);
        if (StringUtils.isNotBlank(node.getScript())) {
            //获取参数控制提前取消分钟数
            String minusStr = SpelFomulaFactory.getStringFomulaResult(node.getScript(), node);
            if (StringUtils.isNotBlank(minusStr)) {
                Integer minus = Integer.parseInt(minusStr);//参数控制提前分钟可免费取消
                long advMinus = DateUtil.between(postDate, arrEndDate, DateUnit.MINUTE, false);//提前分钟数，为正数标识posDate还未到arrDate，数字为剩余分钟数
                if (advMinus < 0 || advMinus < minus) { //超过到店时间 或者 未在提前时间范围内
                    cancelOrderValidData.setLallow(false);
                    cancelOrderValidData.setReason("不允许取消");
                    return cancelOrderValidData;
                }
            }
        } else {
            cancelOrderValidData.setLallow(false);
            cancelOrderValidData.setReason("不允许取消");
            return cancelOrderValidData;
        }
        return cancelOrderValidData;
    }

    /**
     * @param cancelData
     * @param postDate   客人提交取消申请的时间
     * @param prepay
     * @param refundReq
     */
    public void calcReFundAmount(CancelOrderRuleValidData cancelData, Date arrDate, Date postDate, Prepay prepay, RefundReq refundReq) {
        if (prepay == null) {
            return;
        }
        if (refundReq == null || (refundReq != null && !refundReq.getLManual())) {//非后台审核的取消 或者后台审核按规则走
            if (rules.getRefundfreetime() != 0) { //判断有无设置期限分钟免费取消订单
                int refundFreeTime = rules.getRefundfreetime();
                Date prepayTime = Date.from(prepay.getCreatetime().atZone(ZoneId.systemDefault()).toInstant());
                int postAdvMin = (int) ((postDate.getTime() - prepayTime.getTime()) / 60 / 1000);//日期差分钟数
                if (postAdvMin < refundFreeTime) {
                    //下单支付后在设置分钟数内，全额退款
                    cancelData.setRefund(prepay.getAmount());
                    cancelData.setRefundAmountInfo(StrUtil.format("订单将全额退款{}元 ", prepay.getAmount()));
                    return;
                }
            }
            if (rules.getStrategystaus()) {
                Date arrEnd = DateUtil.offsetHour(arrDate, 24);
                int postAdvHour = (int) ((arrEnd.getTime() - postDate.getTime()) / 60 / 60 / 1000);//日期差小时数
                StrategyJson matchStrategy = null;
                //匹配所有退款策略. 按提交时间从远到近匹配
                List<StrategyJson> strategyJsonList = JSON.parseArray(rules.getStrategyjson(), StrategyJson.class);
                if (CollectionUtil.isNotEmpty(strategyJsonList)) {
                    StrategyJson.sort(strategyJsonList);
                    for (StrategyJson strategyJson : strategyJsonList) {
                        String[] strategyArray;
                        int min = 0, max = 0;
                        if (StringUtils.isNotBlank(strategyJson.getStrategy())) {
                            strategyArray = strategyJson.getStrategy().split("-");
                            min = Integer.parseInt(strategyArray[0]);
                            max = Integer.parseInt(strategyArray[1]);
                        }
                        if (postAdvHour >= min && postAdvHour <= max) {
                            matchStrategy = strategyJson;
                            break;
                        }

                    }
                }
                if (matchStrategy != null) {  //按照策略计算退款金额
                    int scale = rules.getRefundretain();
                    if (rules.getStrategytype() == CancelDebitOptions.FIXAMOUNT) {//按照固定金额计算
                        cancelData.setDebit(NumberUtil.min(prepay.getAmount(), matchStrategy.getDebit()));
                        cancelData.setDebitpercent(cancelData.getDebit().divide(prepay.getAmount(), 6, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));//防止无限循环小数
                    } else {
                        BigDecimal debitAmount = prepay.getAmount()
                                .multiply(matchStrategy.getDebit().divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP)).setScale(scale, RoundingMode.HALF_UP);
                        cancelData.setDebit(debitAmount);
                        cancelData.setDebitpercent(matchStrategy.getDebit());//记录扣款百分比
                    }
                    cancelData.setRefund(prepay.getAmount().subtract(cancelData.getDebit()));
                    cancelData.setRefundAmountInfo(StrUtil.format("订单总价{}元 退款{}元 扣款{} 元", prepay.getAmount(), cancelData.getRefund(), cancelData.getDebit()));

                } else {
                    //没有找到策略计算退款则全额
                    cancelData.setRefund(prepay.getAmount());
                    cancelData.setRefundAmountInfo(StrUtil.format("订单将全额退款{}元 ", prepay.getAmount()));
                }
            } else {
                //不设置退款分段策略.全额退款
                cancelData.setRefund(prepay.getAmount());
                cancelData.setRefundAmountInfo(StrUtil.format("订单将全额退款{}元 ", prepay.getAmount()));
            }

        } else {//系统后台的审核取消
            BigDecimal debitAmount = prepay.getAmount()
                    .multiply(refundReq.getDebitPercent().
                            divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP)).setScale(0, RoundingMode.HALF_UP);
            cancelData.setDebit(debitAmount);
            cancelData.setRefund(prepay.getAmount().subtract(cancelData.getDebit()));
            cancelData.setRefundAmountInfo(StrUtil.format("订单总价{}元 退款{}元 扣款{} 元", prepay.getAmount(), cancelData.getRefund(), cancelData.getDebit()));
        }
    }

    public String getCancelDescription(Date bookingDate) {
        boolean lcancancel = rules.getRefundstatus();
        if (!lcancancel) {
            return "预订后不可取消";
        } else {
            int hour = NumberUtil.parseInt(rules.getRefundday());
            Date arrEnd = DateUtil.offsetHour(bookingDate, 24);
            Date cancancelDate = DateUtil.offsetHour(arrEnd, -hour);
            String cancancelDateStr = DateUtil.format(cancancelDate, "yyyy-MM-dd HH:mm");
            String cancelInfo = StrUtil.format("订单可在{} 前免费取消.", cancancelDateStr);
            String refundInfo = "";
            if (!rules.getStrategyjson().isEmpty()) {
                List<StrategyJson> strategyJsonList = JSON.parseArray(rules.getStrategyjson(), StrategyJson.class);
                StrategyJson.sort(strategyJsonList);
                for (StrategyJson strategyJson : strategyJsonList) {
                    String rowinfo = "";
                    //if (strategyJson.getStrategy() =0) {
                    //    rowinfo = StrUtil.format("使用/到店当天取消扣款{}%", strategyJson.getStrategy(), strategyJson.getDebit().intValue());
                    //} else {
                    //    rowinfo = StrUtil.format("提前{}天内取消扣款{}%", strategyJson.getStrategy(), strategyJson.getDebit().intValue());
                    //}
                    rowinfo = StrUtil.format("提前{}小时内取消扣款{}%", strategyJson.getStrategy(), strategyJson.getDebit().intValue());
                    refundInfo = refundInfo.isEmpty() ? rowinfo : refundInfo + "," + rowinfo;
                }
            }
            return cancelInfo + " " + refundInfo;
        }
    }


    /**
     * @param uid           用户ID
     * @param bookingNumber 预定数量
     * @param productType   预定产品类型
     * @param productCode   预定产品代码
     * @param projectId     项目ID
     * @return uid当天购买产品最大数量
     */
    public CreateOrderRuleValidData getCreatOrderValidUid(String uid, Integer bookingNumber, String productType,
                                                          String productCode, String projectId) {
        CreateOrderRuleValidData createOrderRuleValidData = new CreateOrderRuleValidData();
        if (rules != null && rules.getLimituser()) {
            Integer limitNum = rules.getLimitnum();
            Integer todayProductNum = 0;
            LocalDateTime startDate = DateUtil.toLocalDateTime(DateUtil.beginOfDay(new Date()));
            LocalDateTime endDate = DateUtil.toLocalDateTime(DateUtil.endOfDay(new Date()));
            String reason = "";
            //查询对应订单下不包含取消的订单
            if (ProdType.ROOM.val().equals(productType)) {
                todayProductNum = SpringUtil.getBean(RoomrsMapper.class).countTodayUserOrderNum(projectId, productCode, uid, startDate, endDate, SystemUtil.EMPTY_LOCALTIME);
            } else if (ProdType.TICKET.val().equals(productType)) {
                todayProductNum = SpringUtil.getBean(Ticket_rsMapper.class).countTodayUserOrderNum(projectId, productCode, uid, startDate, endDate, SystemUtil.EMPTY_LOCALTIME);
            } else if (ProdType.TAOCAN.val().equals(productType)) {
                todayProductNum = SpringUtil.getBean(BookingrsMapper.class).countTodayUserTaoCANOrderNum(projectId, productType, productCode, uid, startDate, endDate, SystemUtil.EMPTY_LOCALTIME);
            } else if (ProdType.WARES.val().equals(productType)) {
                todayProductNum = SpringUtil.getBean(SpursMapper.class).countTodayUserOrderNum(projectId, productCode, uid, startDate,
                        endDate, SystemUtil.EMPTY_LOCALTIME);
            } else if (ProdType.ITEMS.val().equals(productType)) {
                todayProductNum = SpringUtil.getBean(GiftrsMapper.class).countTodayUserOrderNum(projectId, productCode, uid, startDate, endDate, SystemUtil.EMPTY_LOCALTIME);
            }

            if (todayProductNum >= limitNum) {
                createOrderRuleValidData.setLallow(false);
                reason = StrUtil.format("用户当日购买当前产品最大数量为{}, 剩余购买量为{}.", limitNum, 0);
                createOrderRuleValidData.setReason(reason);
            } else if (todayProductNum + bookingNumber > limitNum) {
                createOrderRuleValidData.setLallow(false);
                reason = StrUtil.format("用户当日购买当前产品最大数量为{}, 剩余购买量为{}.", limitNum, Math.max(0, (limitNum - todayProductNum)));
                createOrderRuleValidData.setReason(reason);
            }

        }
        return createOrderRuleValidData;
    }
}
