package com.cw.arithmetic.func;

import com.cw.utils.CalculateDate;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/3/25 11:40
 **/
public class Idgenerate {
    private JFrame frame;
    private JTextField snField;
    private JTextField expireField;
    private JTextField projectId;

    public static void main(String[] args) {
        EventQueue.invokeLater(() -> {
            Idgenerate app = new Idgenerate();
            app.createAndShowGUI();
        });
    }

    private void createAndShowGUI() {
        frame = new JFrame("某项目的生成器");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        JPanel panel = new JPanel(new BorderLayout());
        panel.setLayout(null);

        JButton generateButton = new JButton("点我生成许可证");
        generateButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                generateRandomString();
            }
        });


        snField = new JTextField(100); // 设置文本框宽度为20个字符
        expireField = new JTextField(20);
        expireField.setText(CalculateDate.dateToString(CalculateDate.reckonDay(new Date(), 5, 15)));
        projectId = new JTextField(50);
        projectId.setText("001");

        JLabel projectIdLabel = new JLabel("项目ID");
        projectIdLabel.setBounds(10, 20, 60, 60);
        JLabel expireLabel = new JLabel("过期时间");
        expireLabel.setBounds(10, 90, 60, 60);
        JLabel snLabel = new JLabel("序列号");
        snLabel.setBounds(10, 160, 60, 60);


        projectId.setBounds(80, 20, 300, 60);
        expireField.setBounds(80, 90, 300, 60); // 第二个文本框从 (140, 20) 开始，宽高也是 100x60，与第一个之间保持间距
        snField.setBounds(80, 160, 300, 60); // 第一个文本框从 (20, 20) 开始，宽高都是 100x60
        generateButton.setBounds(80, 230, 180, 60);

        panel.add(generateButton);
        panel.add(projectId);
        panel.add(expireField);
        panel.add(snField);
        panel.add(projectIdLabel);
        panel.add(expireLabel);
        panel.add(snLabel);


        frame.getContentPane().add(panel);
        frame.pack();
        frame.setSize(420, 400);
        frame.setLocationRelativeTo(null);
        frame.setVisible(true);
    }

    // 生成指定长度的随机字符串
    private void generateRandomString() {

        Date date = null;
        try {
            date = CalculateDate.stringToDate(expireField.getText());
        } catch (Exception e) {
            return;
        }

        String permitStr = PermitUtil.generatePermit(projectId.getText(), date);
        snField.setText(permitStr);

        StringSelection stringSelection = new StringSelection(permitStr);
        Toolkit.getDefaultToolkit().getSystemClipboard().setContents(stringSelection, null);

        JOptionPane.showMessageDialog(
                snField,
                "内容已成功复制到剪贴板!",
                "复制成功",
                JOptionPane.INFORMATION_MESSAGE
        );

    }


}
