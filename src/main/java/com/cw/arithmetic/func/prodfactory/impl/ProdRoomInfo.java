package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.lang.R;
import com.cw.cache.GlobalCache;
import com.cw.entity.Hotel;
import com.cw.entity.Roomtype;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 酒店产品业态页面信息逻辑
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/20 11:04
 **/
public class ProdRoomInfo extends BaseProdInfo<Roomtype, Hotel> {

    @Override
    public Roomtype getProdRecord(String productCode, String projectId, String specCode) {
        Roomtype roomtype = (Roomtype) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).
                getRecord(projectId, productCode);
        return roomtype;
    }

    @Override
    public Hotel getProdGroup(String productCode, String projectId, String specCode) {
        Roomtype roomtype = getProdRecord(productCode, projectId, specCode);
        if (roomtype != null) {
            Hotel hotel = (Hotel) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).
                    getRecord(projectId, roomtype.getHotelcode());
            return hotel;
        }
        return null;
    }

    @Override
    public String getProductDesc(String productCode, String projectId) {
        Roomtype record = getProdRecord(productCode, projectId, null);
        return record == null ? StrUtil.EMPTY : record.getDescription();
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        Roomtype roomtype = getProdRecord(productCode, projectId, null);
        if (roomtype == null) {
            return Lists.newArrayList();
        }
        return Arrays.asList((lmobile ? roomtype.getMslidepics() : roomtype.getSlidepics()).split(","));
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        Hotel hotel = getProdGroup(productCode, projectId, null);
        return hotel == null ? StrUtil.EMPTY : hotel.getCode();
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        Hotel hotel = getProdGroup(productCode, projectId, null);
        return hotel == null ? StrUtil.EMPTY : hotel.getDescription();
    }

    @Override
    public String getProductGroupShowPic(String groupcode, String projectId, boolean lmobile) {
        Hotel hotel = getProdGroup(groupcode, projectId, null);
        return hotel == null ? StrUtil.EMPTY : (ContentCacheTool.getFIrstPic(lmobile ? hotel.getMslidepics() : hotel.getMslidepics()));
    }


    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        Roomtype roomtype = getProdRecord(productCode, projectId, null);
        return roomtype == null ? StrUtil.EMPTY : roomtype.getRulecode();
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        Hotel group = (Hotel) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).
                getRecord(projectId, groupCode);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }


    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        Roomtype roomtype = getProdRecord(productCode, projectId, null);
        return roomtype == null ? StrUtil.EMPTY : ContentCacheTool.getFIrstPic(lmobile ? roomtype.getMslidepics() : roomtype.getSlidepics());
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        Hotel hotel = null;
        if (StrUtil.isNotBlank(productCode)) {
            hotel = getProdGroup(productCode, projectId, null);
        } else {
            hotel = (Hotel) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).
                    getRecord(projectId, groupid);
        }
        return hotel == null ? StrUtil.EMPTY : R.richlang(hotel.getNoticetext(), hotel, "noticetext");// hotel.getNoticetext();
    }

    @Override
    public String getBannerPic(String projectId) {
        return ContentCacheTool.getProdBannerPic(projectId, ProdType.ROOM);
    }


    @Override
    public BigDecimal getOrgPrice(String productCode, String projectId, BigDecimal defaultShowPrice) {
        Roomtype minRoomtype = getProdRecord(productCode, projectId, null);
        if (minRoomtype != null) {
            return minRoomtype.getShowprice();
        }
        return defaultShowPrice;
    }

    @Override
    public boolean lsellProduct(String productCode, String projectId) {
        Roomtype record = getProdRecord(productCode, projectId, null);
        if (record == null || !record.getLsell()) {
            return false;
        }
        return true;
    }


}
