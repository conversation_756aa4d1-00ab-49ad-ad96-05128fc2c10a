package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.cache.GlobalCache;
import com.cw.entity.Meeting;
import com.cw.entity.Meetinggroup;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/20 17:17
 **/
public class ProdMeetingInfo extends BaseProdInfo<Meeting, Meetinggroup> {
    @Override
    public String getProductDesc(String productCode, String projectId) {
        Meeting meeting = getProdRecord(productCode, projectId, null);
        Meetinggroup meetinggroup = getProdGroup(productCode, projectId, null);
        if (meetinggroup != null && meeting != null) {
            return meetinggroup.getDescription() + " " + meeting.getDescription();
        }
        return StrUtil.EMPTY;
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        Meeting meeting = getProdRecord(productCode, projectId, null);
        return Arrays.asList((lmobile ? meeting.getMslidepics() : meeting.getSlidepics()).split(","));
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        Meetinggroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getCode();
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        Meetinggroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }

    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        return "";
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        Meetinggroup group = (Meetinggroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETINGGROUP).
                getRecord(projectId, groupCode);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }


    @Override
    public boolean lneedQrDisplay(String productCode, String projectId) {
        return false;
    }

    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        Meetinggroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : (ContentCacheTool.getFIrstPic(lmobile ? group.getMslidepics() : group.getSlidepics()));
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        Meetinggroup meetinggroup = null;
        if (StrUtil.isNotBlank(productCode)) {
            meetinggroup = getProdGroup(productCode, projectId, null);
        } else {
            meetinggroup = (Meetinggroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETINGGROUP).
                    getRecord(projectId, groupid);
        }
        return meetinggroup == null ? StrUtil.EMPTY : meetinggroup.getNoticetext();
    }

    @Override
    public String getBannerPic(String projectId) {
        return ContentCacheTool.getProdBannerPic(projectId, ProdType.MEETING);
    }

    @Override
    public Meeting getProdRecord(String productCode, String projectId, String specCode) {
        Meeting meeting = (Meeting) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETING).
                getRecord(projectId, productCode);
        return meeting;
    }

    @Override
    public Meetinggroup getProdGroup(String productCode, String projectId, String specCode) {
        Meeting meeting = getProdRecord(productCode, projectId, specCode);
        if (meeting == null) {
            return null;
        }
        Meetinggroup meetinggroup = (Meetinggroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETINGGROUP).
                getRecord(projectId, meeting.getGroupid());
        return meetinggroup;
    }
}
