package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.lang.R;
import com.cw.arithmetic.pay.OrderSenseNotifyDataHandler;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ActqrCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.config.exception.CustomException;
import com.cw.core.CoreRs;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.OrderHandler;
import com.cw.core.orderhandler.OrderVendorSwitcher;
import com.cw.core.vendor.order.VendorHandler;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.outsys.stdop.common.array.StdTicketSubStatusNode;
import com.cw.outsys.stdop.request.StdTicketQueryQrPicRequest;
import com.cw.outsys.stdop.request.StdTicketQueryStatusRequest;
import com.cw.outsys.stdop.request.StdTicketSendMsgRequest;
import com.cw.outsys.stdop.response.StdOrderResponse;
import com.cw.outsys.stdop.response.StdTicketQueryQrPicResponse;
import com.cw.outsys.stdop.response.StdTicketQueryStatusResponse;
import com.cw.outsys.stdop.response.StdTicketSendMsgResponse;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.LoggerType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.TicketUtil;
import com.cw.utils.enums.VendorType;
import com.cw.utils.pagedata.SelectDataNode;
import com.cw.utils.pay.PayUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/20 17:17
 **/
public class ProdTicketInfo extends BaseProdInfo<Ticket, Ticketgroup> {
    private Logger logger = LoggerFactory.getLogger(LoggerType.sys.name());

    @Override
    public String getProductDesc(String productCode, String projectId) {
        Ticket ticket = getProdRecord(productCode, projectId, null);
        return null == ticket ? StrUtil.EMPTY : ticket.getDescription();
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        Ticketgroup ticketgroup = getProdGroup(productCode, projectId, null);
        return Arrays.asList((lmobile ? ticketgroup.getMslidepics() : ticketgroup.getSlidepics()).split(","));
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        Ticketgroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getCode();
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        Ticketgroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }

    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        Ticket ticket = getProdRecord(productCode, projectId, null);
        return ticket == null ? StrUtil.EMPTY : ticket.getRulecode();
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        Ticketgroup group = (Ticketgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP).
                getRecord(projectId, groupCode);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }


    @Override
    public boolean lneedQrDisplay(String productCode, String projectId) {
        Ticket ticket = getProdRecord(productCode, projectId, null);
        return ticket == null ? false : !ticket.getOutcode().isEmpty();
    }

    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        Ticketgroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : (ContentCacheTool.getFIrstPic(lmobile ? group.getMslidepics() : group.getSlidepics()));
    }

    @Override
    public String getProductGroupShowPic(String groupcode, String projectId, boolean lmobile) {
        Ticketgroup group = (Ticketgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP).
                getRecord(projectId, groupcode);
        return group == null ? StrUtil.EMPTY : (ContentCacheTool.getFIrstPic(lmobile ? group.getMslidepics() : group.getSlidepics()));
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        Ticketgroup ticketgroup = null;
        if (StrUtil.isNotBlank(productCode)) {
            Ticket t = getProdRecord(productCode, projectId, null);
            if (t != null && StrUtil.isNotBlank(t.getRichtext())) {  //如果票型设置有富文本预订须知.就直接返回
                return t.getRichtext();
            }
            ticketgroup = getProdGroup(productCode, projectId, null);
        } else {
            ticketgroup = (Ticketgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP).
                    getRecord(projectId, groupid);
        }
        return ticketgroup == null ? StrUtil.EMPTY : R.richlang(ticketgroup.getIntrotext(), ticketgroup, "introtext");// ticketgroup.getIntrotext();
    }

    @Override
    public String getBannerPic(String projectId) {
        return ContentCacheTool.getProdBannerPic(projectId, ProdType.TICKET);
    }

    @Override
    public Ticket getProdRecord(String productCode, String projectId, String specCode) {
        Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                getRecord(projectId, productCode);
        return ticket;
    }

    @Override
    public Ticketgroup getProdGroup(String productCode, String projectId, String specCode) {
        Ticket ticket = getProdRecord(productCode, projectId, specCode);
        if (ticket == null) {
            return null;
        }
        Ticketgroup ticketgroup = (Ticketgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP).
                getRecord(projectId, ticket.getGroupid());
        return ticketgroup;
    }


    @Override
    public BigDecimal getOrgPrice(String productCode, String projectId, BigDecimal defaultShowPrice) {
        Ticket ticket = getProdRecord(productCode, projectId, null);
        if (ticket != null) {
            return ticket.getShowprice();
        }
        return defaultShowPrice;
    }


    @Override
    public String getUseTips(String productCode, String projectId) {
        Ticket ticket = getProdRecord(productCode, projectId, null);
        return ticket == null ? StrUtil.EMPTY : CustomData.getDesc(projectId, ticket.getUsetips(), SystemUtil.CustomDataKey.usetips, StrUtil.EMPTY);
    }

    @Override
    public boolean sendOrderQr(String orderId, String projectId) throws DefinedException {
        //创建门票订单
        OrderVendorSwitcher switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getConfCache();
        Sysconf sysconf = sysConfCache.getOne(projectId);

        VendorType vendorType = EnumUtil.fromString(VendorType.class, sysconf.getTicketvendor(), VendorType.LOCAL);
        if (vendorType.equals(VendorType.LOCAL)) {
            return true;
        }
        VendorHandler vendorHandler = switcher.getVendorHandler(vendorType);
        OrderSenseNotifyDataHandler<?> notifyDataHandler = OrderVendorSwitcher.getSenseHandler(PayUtil.ORDER_SCENE, orderId);
        StdOrderData stdOrderData = notifyDataHandler.getStdOrerData(orderId, projectId);    //getStdOrerData(msg.getBookingId(), msg.getProjectId());

        boolean lsend = true;


        if (stdOrderData.getBookingRs() == null) {
            lsend = false;
        }

        if (stdOrderData.getBookingRs().getPtype().equals(ProdType.TICKET.val())) {
            Ticket ticket = getProdRecord(stdOrderData.getBookingRs().getProduct(), projectId, null);
            if (ticket == null || ticket.getOutcode().isEmpty()) {
                lsend = false;
            }
        }

        if (!lsend) {//如果不需要发码
            return true;
        }


        String assistCode = "";
        String qrCode = "";
        String outid = "";
        String err = "";
        boolean lok = true;
        if (stdOrderData != null) {
            try {
                StdOrderResponse result = vendorHandler.createOrder(stdOrderData);
                assistCode = result.getTcheckNo();
                outid = result.getOutid();
            } catch (Exception e) {
                Rules rules = SysFuncLibTool.getProductRule(stdOrderData.getBookingRs().getPtype(), stdOrderData.getBookingRs().getProduct(), projectId);
                if (rules.getAutocancel()) {
                    CoreRs coreRs = SpringUtil.getBean(CoreRs.class);
                    coreRs.cancelOrder_Local(stdOrderData.getBookingRs().getBookingid(), stdOrderData.getBookingRs().getProjectid());//自动取消.申请退款
                    logger.error("门票发码失败.根据规则执行自动取消{}", stdOrderData.getBookingRs().getBookingid());
                    String errReson = e.getMessage() == null ? "票务二维码生成失败,请检查订单信息" : e.getMessage();
                    boolean lidcardReson = e.getMessage().contains("身份证");//简单处理下.身份证类型的错误原因 才提示门票接口的返回给到用户
                    String msg = lidcardReson ? e.getMessage() : errReson;
                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg(msg));

                } else {
                    e.printStackTrace();
                    throw new DefinedException("二维码订单生成失败,请检查订单信息");
                }

            }
        }
        if (lok) {
            StdTicketQueryQrPicRequest qrPicRequest = new StdTicketQueryQrPicRequest();
            qrPicRequest.setColno(stdOrderData.getBookingRs().getBookingid());
            qrPicRequest.setOutId(outid);
            qrPicRequest.setProjectId(stdOrderData.getBookingRs().getProjectid());
            qrPicRequest.setProdType(stdOrderData.getBookingRs().getPtype());
            qrPicRequest.setAssistCode(assistCode);
            //预约活动
            try {
                StdTicketQueryQrPicResponse response = vendorHandler.queryTicketQrPic(qrPicRequest);
                qrCode = response.getImg();//把二维码查回来 展示给用户
            } catch (DefinedException e) {
                e.printStackTrace();
                lok = false;
            }
        }


        if (lok && !qrCode.isEmpty()) { //如果门票发码成功.
            notifyDataHandler.writeQrCodeAndSave(stdOrderData, qrCode, assistCode, outid);
//            writeQrCodeAndSave(stdOrderData, qrCode, assistCode);
            logger.info("门票发码成功：" + orderId);

            //发送短信
            StdTicketSendMsgRequest smsRequest = new StdTicketSendMsgRequest();
            smsRequest.setColno(stdOrderData.getBookingRs().getBookingid());
            smsRequest.setProjectId(stdOrderData.getBookingRs().getProjectid());

            try {
                StdTicketSendMsgResponse response = vendorHandler.sendTicketMsg(smsRequest);//发送门票短信
                logger.info("门票短信发送成功：" + response.toString());
            } catch (DefinedException e) {
                e.printStackTrace();
            }
        } else {
            logger.info("门票发码失败：" + orderId);
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLINFO,
                    RobotUtils.transRobotTicketMsg("门票发码失败", orderId, RobotUtils.RobotGroup.DSMALLINFO, new JSONObject().toJSONString()));

            return false;

        }
        return true;
    }

    @Override
    public boolean lsellProduct(String productCode, String projectId) {
        Ticket record = getProdRecord(productCode, projectId, null);
        if (record == null || !record.getLsell()) {
            return false;
        }
        return true;
    }


    @Override
    public boolean queryIsUse(String bookingId, String projectId, String outId) {  //查询接口.判断门票是否已经检票.使用
        OrderHandler orderHandler = SpringUtil.getBean(OrderHandler.class);
        VendorHandler vendorHandler = orderHandler.getVendorHandler(ProdType.TICKET, projectId);
        if (vendorHandler != null) {//有二维码的票.才去做取消前的判断
            StdTicketQueryStatusRequest ticketQueryRequest = new StdTicketQueryStatusRequest();
            ticketQueryRequest.setProjectId(projectId);
            ticketQueryRequest.setColno(bookingId);
            if (StringUtils.isNotBlank(outId)) {
                ticketQueryRequest.setOutId(outId); //微票需要传outid
            }
            StdTicketQueryStatusResponse response = null;
            try {
                response = vendorHandler.queryTicketStatus(ticketQueryRequest);
            } catch (DefinedException e) {
                return false;
            }
            if (response.getStd_flag()) {
                if (CollUtil.isNotEmpty(response.getSubOrders())) {
                    for (StdTicketSubStatusNode subOrder : response.getSubOrders()) {
                        if (subOrder.getStatus().equals(TicketUtil.TicketCheckStatus.CHECKING.name()) || subOrder.getStatus().equals(TicketUtil.TicketCheckStatus.CHECKED.name())) {
                            return true;
                        }
                    }
                }
            } else {
                return false;
            }
        }
        return false;
    }

    @Override
    public List<SelectDataNode> getTabsGroup(String projectId, String groupId) {
        List<SelectDataNode> nodes = Lists.newArrayList();

        ActqrCache actqrCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTQR);
        List<Actqr> actqrList = actqrCache.getDataList(projectId);

        actqrList.sort(Comparator.comparing(Actqr::getSeq));

        nodes = actqrList.stream().filter(r -> r.getType().equals("TICKET")).map(actqr -> new SelectDataNode(actqr.getCode(), actqr.getDescription())).collect(Collectors.toList());
        if (nodes.size() > 0) {
            nodes.add(0, new SelectDataNode("ALL", "不限"));
        }
        return nodes;
    }
}
