package com.cw.arithmetic.func.prodfactory.impl;

import com.cw.arithmetic.func.prodfactory.ProdInfoProxyer;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/20 14:13
 **/
public abstract class BaseProdInfo<T, G> implements ProdInfoProxyer {

/*    @Override
    public List<String> getSlidPics() {
        return Lists.newArrayList();
    }

    @Override
    public String getProdGroupCode() {
        return StrUtil.EMPTY;
    }

    @Override
    public String getProdGroupDesc() {
        return StrUtil.EMPTY;

    }

    @Override
    public String getProdGroupIntro() {
        return StrUtil.EMPTY;

    }

    @Override
    public String getProdCachedPrice() {
        return StrUtil.EMPTY;
    }

    @Override
    public String getProdCancelFreeTime() {
        return StrUtil.EMPTY;
    }

    @Override
    public String getProductShowPic(boolean lmobile) {
        return StrUtil.EMPTY;

    }*/

    public abstract T getProdRecord(String productCode, String projectId, String specCode);


    public abstract G getProdGroup(String productCode, String projectId, String specCode);

}
