package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.lang.R;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.GiftItemCache;
import com.cw.entity.Gift;
import com.cw.entity.Giftitem;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import jodd.util.StringUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/20 17:17
 **/
public class ProdGiftiemInfo extends BaseProdInfo<Giftitem, Gift> {
    @Override
    public String getProductDesc(String productCode, String projectId) {
        List<String> list = new ArrayList<>();
        if (productCode.contains(":")) {
            list = Arrays.asList(productCode.split(":"));
            productCode = list.get(0);//产品代码
        }
        Giftitem giftitem = getProdRecord(productCode, projectId, null);
        if (giftitem != null) {
            if (list.size() > 0) { //如果包含产品规格则显示产品规格
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(giftitem.getDescription());
                stringBuffer.append("(");
                stringBuffer.append(ContentCacheTool.giftItemSpecsCodeTransToDesc(list.get(0), list.get(1), null, projectId));
                if (list.size() > 2) {
                    stringBuffer.append(" ");
                    stringBuffer.append(ContentCacheTool.giftItemSpecsCodeTransToDesc(list.get(0), null, list.get(2), projectId));
                }
                stringBuffer.append(")");
                return stringBuffer.toString();//输出名称（规格描述）
            } else {
                return giftitem.getDescription();//直接输出名称
            }
        }
        return productCode;
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        Giftitem giftitem = getProdRecord(productCode, projectId, null);
        return Arrays.asList(giftitem.getMlistpic().split(","));
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        Giftitem giftitem = getProdRecord(productCode, projectId, null);
        return giftitem == null ? StrUtil.EMPTY : giftitem.getCode();
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        Giftitem giftitem = getProdRecord(productCode, projectId, null);
        return giftitem == null ? StrUtil.EMPTY : giftitem.getDescription();
    }

    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        Gift gift = getProdGroup(productCode, projectId, null);
        return gift == null ? StrUtil.EMPTY : gift.getRulecode();
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        Gift group = (Gift) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT).
                getRecord(projectId, groupCode);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }


    @Override
    public boolean lneedQrDisplay(String productCode, String projectId) {
        return false;//判断下是否为自提???
    }

    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        String specs1code = null;
        if (productCode != null && productCode.contains(":")) {
            List<String> list = Arrays.asList(productCode.split(":"));
            productCode = list.get(0);//伴手礼代码
            specs1code = list.get(1);//截取商品规格代码
            //productCode = productCode.substring(0, productCode.indexOf(":"));
        }
        Giftitem giftitem = (Giftitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM).getRecord(projectId, productCode);
        if (giftitem != null) {
            if (specs1code != null) {
                return ContentCacheTool.getGiftitemShowPic(productCode, specs1code, projectId);//返回对应伴手礼规格1代码对应的图片
            } else {

                return ContentCacheTool.getFIrstPic(giftitem.getMlistpic());
            }
        }
        return StrUtil.EMPTY;
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        Giftitem giftitem = null;
        if (StrUtil.isNotBlank(productCode)) {
            giftitem = getProdRecord(productCode, projectId, null);
        } else if (StringUtil.isNotBlank(groupid)) {
            GiftItemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
            List<Giftitem> list = cache.getGroupList(projectId, groupid);
            if (CollectionUtils.isNotEmpty(list)) {
                giftitem = list.get(0);
            }
        }
        return giftitem == null ? StrUtil.EMPTY : R.richlang(giftitem.getRichtext(), giftitem, "richtext");// giftitem.getRichtext();
    }

    @Override
    public String getBannerPic(String projectId) {
        return ContentCacheTool.getProdBannerPic(projectId, ProdType.ITEMS);
    }

    @Override
    public Giftitem getProdRecord(String productCode, String projectId, String specCode) {
        if (productCode.contains(":")) { //如果传过来的是包含规格代码 只取第一部分
            productCode = Arrays.asList(productCode.split(":")).get(0);//产品代码
        }
        Giftitem record = (Giftitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM).
                getRecord(projectId, productCode);
        return record;
    }

    @Override
    public Gift getProdGroup(String productCode, String projectId, String specCode) {
        if (productCode.contains(":")) { //如果传过来的是包含规格代码 只取第一部分
            productCode = Arrays.asList(productCode.split(":")).get(0);//产品代码
        }
        Giftitem record = getProdRecord(productCode, projectId, specCode);
        if (record == null) {
            return null;
        }
        Gift group = (Gift) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT).
                getRecord(projectId, record.getGroupid());
        return group;
    }

    @Override
    public boolean lsellProduct(String productCode, String projectId) {
        Giftitem record = getProdRecord(productCode, projectId, null);
        if (record == null || !record.getLsell()) {
            return false;
        }
        return true;
    }
}
