package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.lang.R;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ShopsiteCache;
import com.cw.cache.impl.SpugroupCache;
import com.cw.cache.impl.SpuqrCache;
import com.cw.cache.impl.SpusitemCache;
import com.cw.entity.*;
import com.cw.mapper.BookingrsMapper;
import com.cw.mapper.SpursMapper;
import com.cw.pojo.common.spuitem.SpuPackCheckInfo;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.SpusType;
import com.cw.utils.pagedata.SelectDataNode;
import com.cw.utils.rule.OrderCheckMode;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/20 17:17
 **/
public class ProdWareInfo extends BaseProdInfo<Spusitem, Spugroup> {
    @Override
    public String getProductDesc(String productCode, String projectId) {
        Spusitem spusitem = getProdRecord(productCode, projectId, null);
        return null == spusitem ? StrUtil.EMPTY : spusitem.getDescription();
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        Spugroup spugroup = getProdGroup(productCode, projectId, null);
        return Arrays.asList((lmobile ? spugroup.getMslidepics() : spugroup.getSlidepics()).split(","));
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        Spugroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getCode();
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        Spugroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }

    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        Spusitem spusitem = getProdRecord(productCode, projectId, null);
        return spusitem == null ? StrUtil.EMPTY : spusitem.getRulecode();
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        Spugroup group = (Spugroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP).
                getRecord(projectId, groupCode);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }


    @Override
    public boolean lneedQrDisplay(String productCode, String projectId) {
        Spugroup spugroup = getProdGroup(productCode, projectId, null);
        return spugroup == null ? false : (spugroup.getCheckmode() == OrderCheckMode.TICKET_QR || spugroup.getCheckmode() == OrderCheckMode.SYS_QR);
    }

    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        Spugroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : (ContentCacheTool.getFIrstPic(lmobile ? group.getMslidepics() : group.getSlidepics()));
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        Spugroup spugroup = null;
        if (StrUtil.isNotBlank(productCode)) {
            Spusitem spusitem = getProdRecord(productCode, projectId, null);
            if (spusitem != null && StrUtil.isNotBlank(spusitem.getRichtext())) {
                return spusitem.getRichtext();
            }
            spugroup = getProdGroup(productCode, projectId, null);
        } else {
            spugroup = (Spugroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP).
                    getRecord(projectId, groupid);
        }
        return spugroup == null ? StrUtil.EMPTY : R.richlang(spugroup.getNoticetext(), spugroup, "noticetext");// spugroup.getNoticetext();
    }

    @Override
    public String getBannerPic(String projectId) {
        return ContentCacheTool.getProdBannerPic(projectId, ProdType.WARES);
    }

    @Override
    public Spusitem getProdRecord(String productCode, String projectId, String specCode) {
        Spusitem record = (Spusitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM).
                getRecord(projectId, productCode);
        return record;
    }

    @Override
    public Spugroup getProdGroup(String productCode, String projectId, String specCode) {
        Spusitem record = getProdRecord(productCode, projectId, specCode);
        if (record == null) {
            return null;
        }
        Spugroup spugroup = (Spugroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP).
                getRecord(projectId, record.getGroupid());
        return spugroup;
    }


    @Override
    public String getUseTips(String productCode, String projectId) {
        Spusitem spusitem = getProdRecord(productCode, projectId, null);
        return spusitem == null ? StrUtil.EMPTY : CustomData.getDesc(projectId, spusitem.getUsetips(), SystemUtil.CustomDataKey.usetips, StrUtil.EMPTY);
    }

    @Override
    public boolean lsellProduct(String productCode, String projectId) {
        Spusitem record = getProdRecord(productCode, projectId, null);
        if (record == null || !record.getLsell()) {
            return false;
        }
        return true;
    }

    @Override
    public Integer getExpireLen(String product, Date useDate, String projectId) {
        Spusitem r = getProdRecord(product, projectId, null);
        if (r != null) {
            Spugroup spugroup = (Spugroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP).getRecord(projectId, r.getGroupid());
            if (spugroup != null && spugroup.getType().equals(SpusType.AUDIO.getVal())) {//音频产品
                return r.getExpireday();
            }
            Date expireDate = CalculateDate.reckonDay(useDate, 5, r.getExpireday());
            if (spugroup != null && !CalculateDate.emptyDate(spugroup.getEndsell())) {
                expireDate = CalculateDate.minDate(expireDate, spugroup.getEndsell());
                return Math.max(0, CalculateDate.compareDates(expireDate, useDate).intValue());//比如暑假游泳票.设置为7-1.到8-31有效. 有效期设置为60.但是7.1号购买.有效期最大还是到8-31
            }
            return r.getExpireday();
        }
        return 0;
    }

    /**
     * 检查卡券景区商品 未过期是否被使用
     *
     * @param bookingId
     * @param projectId
     * @param outId
     * @return
     */
    @Override
    public boolean queryIsUse(String bookingId, String projectId, String outId) {
        BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
        SpursMapper spursMapper = SpringUtil.getBean(SpursMapper.class);
        Booking_rs rs = bookingrsMapper.findBooking_rsByBookingid(bookingId);
        Spu_rs spuRs = spursMapper.findByBookingidAndProjectid(bookingId, projectId);
        SpusitemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM);
        Spusitem spusitem = cache.getRecord(projectId, rs.getProduct());
        String groupId = spusitem.getGroupid();
        //获取产品大组
        SpugroupCache spugroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP);
        Spugroup spugroup = spugroupCache.getRecord(projectId, groupId);
        //非卡券景区产品直接返回已使用看作已使用
        if (!SpusType.CARDORCODE.getVal().equals(spugroup.getType())) {
            return true;
        }
        if (spuRs != null && !CalculateDate.emptyDate(spuRs.getCheckdate())) { //被员工端核销过.就是用过了
            return true;
        }
        return false;
    }

    public List<SpuPackCheckInfo> updPackShopDesc(List<SpuPackCheckInfo> packCheckInfos, String projectId) {
        ShopsiteCache shopsiteCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SHOPSITE);
        for (SpuPackCheckInfo packCheckInfo : packCheckInfos) {
            String shopCode = packCheckInfo.getCode();
            Shopsite shopsite = shopsiteCache.getRecord(projectId, shopCode);
            if (shopsite != null) {
                packCheckInfo.setProductName(packCheckInfo.getProductName() + "|" + shopsite.getDescription());
            }
        }
        packCheckInfos.sort(Comparator.comparing(SpuPackCheckInfo::getCheck));//未核销的排在前面

        return packCheckInfos;
    }


    @Override
    public List<SelectDataNode> getTabsGroup(String projectId, String groupId) {
        List<SelectDataNode> nodes = Lists.newArrayList();
        nodes.add(new SelectDataNode("ALL", "不限"));
        SpuqrCache actqrCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUQR);
        List<Spuqr> actqrList = actqrCache.getDataList(projectId);

        actqrList.sort(Comparator.comparing(Spuqr::getSeq));

        nodes = actqrList.stream().filter(actqr -> actqr.getGroupid().equals(groupId)).map(spuqr -> new SelectDataNode(spuqr.getCode(), spuqr.getDescription())).collect(Collectors.toList());
        if (nodes.size() > 0) {
            nodes.add(0, new SelectDataNode("ALL", "不限"));
        }

        return nodes;
    }
}