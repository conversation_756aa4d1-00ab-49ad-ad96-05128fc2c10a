package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.entity.Coupon;
import com.cw.entity.Coupongroup;
import com.cw.utils.SystemUtil;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/10/16 17:12
 **/
public class ProdCounponInfo extends BaseProdInfo<Coupon, Coupongroup> {


    @Override
    public Coupon getProdRecord(String productCode, String projectId, String specCode) {
        Coupon coupon = (Coupon) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPON).getRecord(projectId, productCode);
        return coupon;
    }

    @Override
    public Coupongroup getProdGroup(String productCode, String projectId, String specCode) {
        Coupon coupon = getProdRecord(productCode, projectId, specCode);
        if (coupon != null) {
            return null;
        }
        Coupongroup coupongroup = (Coupongroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPONGROUP).
                getRecord(projectId, productCode);
        return coupongroup;
    }


    @Override
    public String getProductDesc(String productCode, String projectId) {
        Coupon coupon = getProdRecord(productCode, projectId, null);
        return null == coupon ? StrUtil.EMPTY : coupon.getDescription();
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        return Collections.emptyList();
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        Coupongroup coupongroup = getProdGroup(productCode, projectId, null);
        return coupongroup == null ? StrUtil.EMPTY : coupongroup.getCode();
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        Coupongroup coupongroup = getProdGroup(productCode, projectId, null);
        return coupongroup == null ? StrUtil.EMPTY : coupongroup.getDescription();
    }

    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        return "";
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        Coupongroup coupongroup = (Coupongroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.COUPONGROUP).
                getRecord(projectId, groupCode);
        return coupongroup == null ? StrUtil.EMPTY : coupongroup.getDescription();
    }

    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        return "";
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        return "";
    }

    @Override
    public String getBannerPic(String projectId) {
        return "";
    }
}
