package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.entity.Menu_content;
import com.cw.utils.enums.menus.MenuReserveId;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/21 16:56
 **/
public class ProdPassByInfo extends BaseProdInfo<Object, Object> {
    @Override
    public String getProductDesc(String productCode, String projectId) {
        return StrUtil.EMPTY;
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        return Lists.newArrayList();
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        return StrUtil.EMPTY;
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        return StrUtil.EMPTY;
    }

    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        return StrUtil.EMPTY;
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        return StrUtil.EMPTY;
    }

    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        return StrUtil.EMPTY;
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        Menu_content content = ContentCacheTool.getMenuFirstContent(projectId, MenuReserveId.passnote.name());
        return content != null ? content.getRichtext() : "";
    }

    @Override
    public String getBannerPic(String projectId) {
        return null;
    }


    @Override
    public Object getProdRecord(String productCode, String projectId, String specCode) {
        return null;
    }

    @Override
    public Object getProdGroup(String productCode, String projectId, String specCode) {
        return null;
    }


}
