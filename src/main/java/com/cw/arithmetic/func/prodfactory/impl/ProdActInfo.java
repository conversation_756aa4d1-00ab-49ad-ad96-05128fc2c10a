package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.entity.Act_rs;
import com.cw.entity.Actgroup;
import com.cw.entity.Actsite;
import com.cw.exception.DefinedException;
import com.cw.mapper.ActrsMapper;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushTicketMsg;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pay.PayUtil;
import com.cw.utils.rule.OrderCheckMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/20 17:17
 **/
public class ProdActInfo extends BaseProdInfo<Actsite, Actgroup> {
    @Override
    public String getProductDesc(String productCode, String projectId) {
        Actsite record = getProdRecord(productCode, projectId, null);
        return null == record ? StrUtil.EMPTY : record.getDescription();
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        Actgroup actgroup = getProdGroup(productCode, projectId, null);
        return Arrays.asList(actgroup.getSlidepics().split(","));
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        Actgroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getCode();
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        Actgroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }

    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        return "";
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        Actgroup group = (Actgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTGROUP).
                getRecord(projectId, groupCode);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }

    @Override
    public boolean lneedQrDisplay(String productCode, String projectId) {
        Actgroup Actgroup = getProdGroup(productCode, projectId, null);
        return Actgroup == null ? false : (Actgroup.getCheckmode() == 1 || Actgroup.getCheckmode() == 2);
    }

    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        Actgroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : (ContentCacheTool.getFIrstPic(group.getSlidepics()));
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        return StrUtil.EMPTY;
    }

    @Override
    public String getBannerPic(String projectId) {
        return null;
    }

    @Override
    public Actsite getProdRecord(String productCode, String projectId, String specCode) {
        Actsite record = (Actsite) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTSITE).
                getRecord(projectId, productCode);
        return record;
    }

    @Override
    public Actgroup getProdGroup(String productCode, String projectId, String specCode) {
        Actsite record = getProdRecord(productCode, projectId, specCode);
        if (record == null) {
            return null;
        }
        Actgroup actgroup = (Actgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTGROUP).
                getRecord(projectId, record.getGroupid());
        return actgroup;
    }


    @Override
    public String getUseTips(String productCode, String projectId) {
        Actsite record = getProdRecord(productCode, projectId, null);
        return record == null ? StrUtil.EMPTY : CustomData.getDesc(projectId, record.getUsetips(), SystemUtil.CustomDataKey.usetips, StrUtil.EMPTY);
    }

    @Override
    public boolean sendOrderQr(String orderId, String projectId) throws DefinedException {
        ActrsMapper actrsMapper = SpringUtil.getBean(ActrsMapper.class);
        Act_rs rs = actrsMapper.findAct_rsByBookingid(orderId);
        int qrmode = OrderCheckMode.MANUAL_AUTO;
        if (rs == null) {
            return true;
        }
        Actgroup actgroup = getProdGroup(rs.getSitecode(), rs.getProjectid(), null);

        if (actgroup.getCheckmode() == OrderCheckMode.TICKET_QR) {//交给门票系统发码
            Actsite actsite = getProdRecord(rs.getSitecode(), rs.getProjectid(), "");
            if (!actsite.getOutcode().isEmpty()) {
                Bussness_PushTicketMsg ticketMsg = new Bussness_PushTicketMsg();
                ticketMsg.setBookingId(rs.getBookingid());
                ticketMsg.setProjectId(rs.getProjectid());
                ticketMsg.setScene(PayUtil.ACT_SCENE);
                RabbitTemplate rabbitTemplate = SpringUtil.getBean(RabbitTemplate.class);
                rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.SENDTICKETQR),
                        JSON.toJSONString(ticketMsg));
//                log.info("预约活动{} 免费发码",rs.getBookingid());
            }
        }
        return true;
    }
}
