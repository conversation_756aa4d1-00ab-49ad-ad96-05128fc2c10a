package com.cw.arithmetic.func.prodfactory;

import cn.hutool.core.util.StrUtil;
import com.cw.exception.DefinedException;
import com.cw.utils.pagedata.SelectDataNode;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 业态产品特性描述& 业态产品特殊处理
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/19 17:06
 **/
public interface ProdInfoProxyer {

    String getProductDesc(String productCode, String projectId);

    /**
     * 获取产品轮播图
     *
     * @return
     */
    List<String> getSlidPics(String productCode, String projectId, boolean lmobile);

    /**
     * 获取产品大组代码
     *
     * @return
     */
    String getProdGroupCode(String productCode, String projectId);

    /**
     * 获取产品大组描述
     *
     * @return
     */
    String getProdGroupDesc(String productCode, String projectId);


    /**
     * 获取产品绑定的规则代码
     *
     * @return
     */
    String getProdRuleCode(String productCode, String projectId);


    /**
     * 传入产品的大组代码，获取大组描述
     * 例如传入水镇大酒店SZ 获取到水镇大酒店的描述.跟房型代码没有一点关系
     *
     * @param groupCode
     * @param projectId
     * @return
     */
    String getGroupDesc(String groupCode, String projectId);

    default boolean lneedQrDisplay(String productCode, String projectId) {
        return false;
    }

    /**
     * 获取产品封面图
     *
     * @return
     */
    String getProductShowPic(String productCode, String projectId, boolean lmobile);


    /**
     * 获取产品预订须知
     *
     * @param productCode
     * @param projectId
     * @return
     */
    String getNoticeText(String productCode, String groupid, String projectId);

    default String getNotifyMsgTemplateId(String triggerType, String projectId) {
        return null;
    }

    default String getProductGroupShowPic(String groupcode, String projectId, boolean lmobile) {
        return null;
    }

    default BigDecimal getOrgPrice(String productCode, String projectId, BigDecimal defaultShowPrice) {
        return defaultShowPrice;
    }


    /**
     * 获取产品 PC banner图
     *
     * @param projectId
     * @return
     */
    String getBannerPic(String projectId);


    default boolean sendOrderQr(String orderId, String projectId) throws DefinedException {
        return true;
    }

    default String getUseTips(String productCode, String projectId) {
        return StrUtil.EMPTY;
    }

    default boolean lsellProduct(String productCode, String projectId) {
        return true;
    }

    default boolean queryIsUse(String bookingId, String projectId, String outId) {
        return false;
    }

    default Integer getExpireLen(String product, Date useDate, String projectId) {
        return 0;
    }

    default List<SelectDataNode> getTabsGroup(String projectId, String groupId) {
        return Lists.newArrayList();
    }
}
