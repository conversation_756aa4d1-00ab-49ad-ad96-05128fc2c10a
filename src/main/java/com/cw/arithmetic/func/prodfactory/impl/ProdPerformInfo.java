package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.lang.R;
import com.cw.cache.GlobalCache;
import com.cw.entity.Perform;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-07-03
 */
public class ProdPerformInfo extends BaseProdInfo<Perform, Perform> {
    @Override
    public Perform getProdRecord(String productCode, String projectId, String specCode) {
        Perform perform = (Perform) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PERFORM).
                getRecord(projectId, productCode);
        return perform;
    }

    @Override
    public Perform getProdGroup(String productCode, String projectId, String specCode) {
        return null;
    }

    @Override
    public String getProductDesc(String productCode, String projectId) {
        Perform record = getProdRecord(productCode, projectId, null);
        return record == null ? StrUtil.EMPTY : record.getDescription();
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        Perform perform = getProdRecord(productCode, projectId, null);
        if (perform == null) {
            return Lists.newArrayList();
        }
        return Arrays.asList((perform.getSlidepics()).split(","));
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        return "";
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        return "";
    }

    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        return "";
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        return "";
    }

    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        Perform perform = getProdRecord(productCode, projectId, null);
        return ContentCacheTool.getFIrstPic(perform.getSlidepics());
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        Perform perform = getProdRecord(productCode, projectId, null);
        return perform == null ? StrUtil.EMPTY : R.richlang(perform.getRichtext(), perform, "richtext");
    }

    @Override
    public String getBannerPic(String projectId) {
        return "";
    }
}
