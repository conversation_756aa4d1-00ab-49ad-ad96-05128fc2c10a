package com.cw.arithmetic.func.prodfactory.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.lang.R;
import com.cw.cache.GlobalCache;
import com.cw.entity.Kitgroup;
import com.cw.entity.Productkit;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/20 17:17
 **/
public class ProdTaocanInfo extends BaseProdInfo<Productkit, Kitgroup> {
    @Override
    public String getProductDesc(String productCode, String projectId) {
        Kitgroup kitgroup = getProdGroup(productCode, projectId, null);
        return null == kitgroup ? StrUtil.EMPTY : kitgroup.getDescription();
    }

    @Override
    public List<String> getSlidPics(String productCode, String projectId, boolean lmobile) {
        Kitgroup group = (Kitgroup) GlobalCache.getDataStructure().
                getCache(SystemUtil.GlobalDataType.KITGROUP).getRecord(projectId, productCode);
        return null == group ? Lists.newArrayList() : Arrays.asList((lmobile ? group.getMslidepics() : group.getSlidepics()).split(","));
    }

    @Override
    public String getProdGroupCode(String productCode, String projectId) {
        Kitgroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getCode();
    }

    @Override
    public String getProdGroupDesc(String productCode, String projectId) {
        Kitgroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }

    @Override
    public String getProdRuleCode(String productCode, String projectId) {
        Productkit record = getProdRecord(productCode, projectId, null);
        return record == null ? StrUtil.EMPTY : record.getRulecode();
    }

    @Override
    public String getGroupDesc(String groupCode, String projectId) {
        Kitgroup group = (Kitgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP).
                getRecord(projectId, groupCode);
        return group == null ? StrUtil.EMPTY : group.getDescription();
    }


    @Override
    public boolean lneedQrDisplay(String productCode, String projectId) {
        return false;
    }

    @Override
    public String getProductShowPic(String productCode, String projectId, boolean lmobile) {
        Kitgroup group = getProdGroup(productCode, projectId, null);
        return group == null ? StrUtil.EMPTY : (ContentCacheTool.getFIrstPic(lmobile ? group.getMslidepics() : group.getSlidepics()));
    }

    @Override
    public String getNoticeText(String productCode, String groupid, String projectId) {
        Kitgroup kitgroup = null;
        if (StrUtil.isNotBlank(productCode)) {
            kitgroup = getProdGroup(productCode, projectId, null);
        } else {
            kitgroup = (Kitgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP).getRecord(projectId, groupid);
        }
        return kitgroup == null ? StrUtil.EMPTY : R.richlang(kitgroup.getNoticetext(), kitgroup, "noticetext");// kitgroup.getNoticetext();
    }

    @Override
    public String getBannerPic(String projectId) {
        return ContentCacheTool.getProdBannerPic(projectId, ProdType.TAOCAN);
    }

    @Override
    public Productkit getProdRecord(String productCode, String projectId, String specCode) {
        Productkit record = (Productkit) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT).
                getRecord(projectId, productCode);
        return record;
    }

    @Override
    public Kitgroup getProdGroup(String productCode, String projectId, String specCode) {
        Productkit record = getProdRecord(productCode, projectId, null);
        if (record == null) {
            return null;
        }
        Kitgroup group = (Kitgroup) GlobalCache.getDataStructure().
                getCache(SystemUtil.GlobalDataType.KITGROUP).getRecord(projectId, record.getGroupid());
        return group;
    }

    @Override
    public boolean lsellProduct(String productCode, String projectId) {
        Productkit record = getProdRecord(productCode, projectId, null);
        if (record == null || !record.getLsell()) {
            return false;
        }
        return true;
    }
}
