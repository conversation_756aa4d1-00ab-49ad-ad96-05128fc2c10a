package com.cw.arithmetic.func.prodfactory;

import com.cw.arithmetic.func.prodfactory.impl.*;
import com.cw.utils.enums.ProdType;

import java.util.HashMap;

/**
 * 用于获取产品的各类信息
 * <p>
 * 避免书写过多的产品类型判断分支
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/6/19 17:02
 **/
public class ProdFactory {

    public static HashMap<ProdType, BaseProdInfo> handlerFacotry = new HashMap();

    static {
        handlerFacotry.put(ProdType.ROOM, new ProdRoomInfo());
        handlerFacotry.put(ProdType.TICKET, new ProdTicketInfo());
        handlerFacotry.put(ProdType.TAOCAN, new ProdTaocanInfo());
        handlerFacotry.put(ProdType.MEETING, new ProdMeetingInfo());
        handlerFacotry.put(ProdType.ITEMS, new ProdGiftiemInfo());
        handlerFacotry.put(ProdType.WARES, new ProdWareInfo());
        handlerFacotry.put(ProdType.PERFORM, new ProdPerformInfo());
        handlerFacotry.put(ProdType.ACTGROUP, new ProdActInfo());
        handlerFacotry.put(ProdType.PASSBY, new ProdPassByInfo());
        handlerFacotry.put(ProdType.DISCOUNT, new ProdCounponInfo());
        handlerFacotry.put(ProdType.LUG, new ProdLuggageInfo());
        handlerFacotry.put(ProdType.PET, new ProdPetInfo());
        handlerFacotry.put(ProdType.MAIN, new ProdDefInfo());
    }


    public static <T extends BaseProdInfo> T getProd(ProdType prodType) {
        return (T) (handlerFacotry.containsKey(prodType) ? handlerFacotry.get(prodType) : handlerFacotry.get(ProdType.MAIN));
    }


}
