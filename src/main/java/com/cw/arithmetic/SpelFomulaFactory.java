package com.cw.arithmetic;

import com.cw.utils.CalculateDate;
import com.cw.utils.enums.ProdType;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.Map;

/**
 * 公式运算工厂
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/6/8 13:45
 **/
public class SpelFomulaFactory {

    private static Map<String, Method> scpritMehtod;

    public static Map<String, Method> getScpritMehtods() {
        if (scpritMehtod == null) {
            scpritMehtod = Maps.newHashMap();
            try {
                Method methodd2s = CalculateDate.class.getDeclaredMethod("date2Str", Date.class);
                Method groupdesc = ContentCacheTool.class.getDeclaredMethod("getProductGroupInfo", String.class, String.class, String.class, Boolean.class);
                Method pdesc = ContentCacheTool.class.getDeclaredMethod("getProductDesc", String.class, String.class, String.class);
                Method n2s = ContentCacheTool.class.getDeclaredMethod("getProductCachedPrice", ProdType.class, Date.class, String.class, String.class);
                Method unit = ContentCacheTool.class.getDeclaredMethod("getProductUnit", String.class, String.class, String.class);
                Method advcancel = ContentCacheTool.class.getDeclaredMethod("canAdvCancelByRulePolicy", String.class, String.class);
                Method acttime = ContentCacheTool.class.getDeclaredMethod("getPeriodsDesc", String.class, String.class);


                scpritMehtod.put(SpelComplieMethod.d2s.name(), methodd2s);
                scpritMehtod.put(SpelComplieMethod.gdesc.name(), groupdesc);
                scpritMehtod.put(SpelComplieMethod.pdesc.name(), pdesc);
                scpritMehtod.put(SpelComplieMethod.n2s.name(), n2s);
                scpritMehtod.put(SpelComplieMethod.unit.name(), unit);
                scpritMehtod.put(SpelComplieMethod.advcancel.name(), advcancel);
                scpritMehtod.put(SpelComplieMethod.acttime.name(), acttime);

            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }
        }
        return scpritMehtod;
    }

    public static <T> String getStringFomulaResult(String formula, T... t) {
        try {
            if (StringUtils.isBlank(formula)) {
                return "";
            }
            //创建运算上下文
            StandardEvaluationContext context = new StandardEvaluationContext();

            for (T x : t) {
                context.setVariable(x.getClass().getSimpleName().toLowerCase(), x);
            }
            context.setVariable(t.getClass().getSimpleName().toLowerCase(), t);  //将对象注入

            for (Map.Entry<String, Method> entry : getScpritMehtods().entrySet()) {
                context.registerFunction(entry.getKey(), entry.getValue());
            }

            //创建解析器
            ExpressionParser parser = new SpelExpressionParser();
//DEMO: EG:        "#col_rs.channelno!=''&&#col_rs.creator=='SYS'&&#col_rs.telephone!='' "
            Expression expression = parser.parseExpression(formula);
            //从表达式中获取计算结果
            String str = expression.getValue(context, String.class); //根据上下文.用表达式运算出结果
            return str;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }


    public static <T> boolean getBooleanFomulaResult(String formula, T t) {
        try {
            //创建运算上下文
            EvaluationContext context = new StandardEvaluationContext();
            context.setVariable(t.getClass().getSimpleName().toLowerCase(), t);  //将对象注入
            //创建解析器
            ExpressionParser parser = new SpelExpressionParser();
//DEMO: EG:        "#col_rs.channelno!=''&&#col_rs.creator=='SYS'&&#col_rs.telephone!='' "
            Expression expression = parser.parseExpression(formula);
            //从表达式中获取计算结果
            boolean lok = expression.getValue(context, boolean.class); //根据上下文.用表达式运算出结果
            return lok;
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(formula)) {
            return false;
        }
        return false;

    }

    public enum SpelComplieMethod {
        advcancel, //是否可提前退
        d2s,  //日期格式化
        gdesc,//获取产品大组描述
        pdesc,//获取产品描述
        night,  //获取订单住店房晚数
        n2s, //格式化订单价格
        unit,//获取产品单位描述  房型是取间 还是房
        acttime //获取产品有效期描述
    }


}
