package com.cw.arithmetic;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.unit.DataSize;
import cn.hutool.core.io.unit.DataUnit;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cw.arithmetic.func.RulesFun;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.arithmetic.func.prodfactory.impl.ProdTicketInfo;
import com.cw.arithmetic.func.prodfactory.impl.ProdWareInfo;
import com.cw.arithmetic.others.StateData;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedissonTool;
import com.cw.cache.impl.RulesCache;
import com.cw.cache.impl.SpusitemCache;
import com.cw.cache.impl.TicketCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.config.exception.CustomException;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.common.rule.CancelOrderRuleValidData;
import com.cw.pojo.common.rule.CreateOrderRuleValidData;
import com.cw.pojo.dto.app.req.node.OrderGuestInfo;
import com.cw.pojo.dto.conf.req.rules.StrategyJson;
import com.cw.pojo.dto.order.req.RefundReq;
import com.cw.utils.CalculateDate;
import com.cw.utils.RedisKey;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.IdCardType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 系统配置函数工具类
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/10 11:04
 **/
public class SysFuncLibTool {
    private static final String aesKey = "CITYBAYTECH";
    private static final String checkTimeComptype = "PCmainCarousels";

    public static String getAesKey(String projectId) {
        return SecureUtil.md5(aesKey).substring(0, 16);
    }

    /**
     * 解密客户端传输内容
     *
     * @param content
     * @return
     */
    public static String decodeAesContent(String content, String projectId) {
        String key = getAesKey(projectId);
        AES aes = SecureUtil.aes(key.getBytes());
        String postContent = Base64.decodeStr(content);
        String decryptStr = null;
        try {
            decryptStr = aes.decryptStr(postContent);
        } catch (Exception e) {
            return StrUtil.EMPTY;
        }
        return decryptStr;
    }

    /**
     * 加密内容向客户端传输
     *
     * @param content
     * @return
     */
    public static String encodeAesContent(String content, String projectId) {
        String key = getAesKey(projectId);
        AES aes = SecureUtil.aes(key.getBytes());
        String entry = aes.encryptBase64(content.getBytes(StandardCharsets.UTF_8));//转成字符串后还需要再一次BASE64 .去掉一些符号
        return Base64.encode(entry);
    }


    /**
     * 计算退款金额 .如果人工手动.就按手动的为准.
     *
     * @param prepay
     * @param rules
     * @return
     */
    public static CancelOrderRuleValidData calcRefundData(Date useDate, Prepay prepay, Rules rules) {
        CancelOrderRuleValidData cancelOrderValidData = new CancelOrderRuleValidData();
        cancelOrderValidData.setRefund(prepay.getAmount());
        cancelOrderValidData.setDebit(BigDecimal.ZERO);
        if (!rules.getStrategyjson().isEmpty()) {
            List<StrategyJson> strategyJsonList = JSON.parseArray(rules.getStrategyjson(), StrategyJson.class);
            StrategyJson.sort(strategyJsonList);//按提前天数排序.取消日期按最远的匹配.因为最远的日期扣费最低
            int advanceDay = CalculateDate.compareDates(useDate, CalculateDate.getSystemDate()).intValue();
            for (StrategyJson strategyJson : strategyJsonList) {

            }
        } else { //不设置的默认为全额退款

        }
        return cancelOrderValidData;
    }

    /**
     * 根据 日期 换算出免费取消时间
     *
     * @param bookingDate
     * @param productType
     * @param productCode
     * @param projectId
     * @return
     */
    public static String getCancelRuleDescription(Date bookingDate, String productType, String productCode, String projectId) {
        Rules rules = getProductRule(productType, productCode, projectId);
        RulesFun fun = new RulesFun(rules);
        if (rules == null) {
            return "";
        } else {
            return fun.getCancelDescription(bookingDate);
        }
    }

    /**
     * @param bookingDate   预定日期
     * @param bookingNumber 预定数量
     * @param productType   产品类型
     * @param productCode   产品代码
     * @param projectId     项目代码
     * @param uid           用户id
     * @return 判断产品是否预定对象
     */
    public static CreateOrderRuleValidData judgeRuleCanBook(Date bookingDate, Integer bookingNumber, String productType, String productCode, String projectId, String uid) {
        boolean lsell = ProdFactory.getProd(ProdType.getProdType(productType)).lsellProduct(productCode, projectId);
        if (!lsell) {
            CreateOrderRuleValidData createOrderRuleValidData = new CreateOrderRuleValidData();
            createOrderRuleValidData.setReason("订单产品未上架");
            createOrderRuleValidData.setLallow(false);
            return createOrderRuleValidData;
        }


        Rules rules = getProductRule(productType, productCode, projectId);
        RulesFun fun = new RulesFun(rules);
        if (uid != null && rules != null && rules.getLimituser()) { //如果开启用户购买限制，判断同一当天同类型产品订单数量是否超出限制
            CreateOrderRuleValidData data = fun.getCreatOrderValidUid(uid, bookingNumber, productType, productCode, projectId);
            if (!data.getLallow()) {//同一用户当日超出限定数量不允许预定直接返回
                return data;
            }

        }
        boolean lcalendar = true;
        //没有日历模式 景区产品和伴手礼，不用用规则日期判断 只限制数量
        if (ProdType.ITEMS.val().equals(productType)) {//ProdType.WARES.val().equals(productType) ||
            lcalendar = false;
        }
        CreateOrderRuleValidData data = fun.getCreatOrderValidate(bookingDate, bookingNumber, lcalendar);
        return data;
    }


    /**
     * 判断产品是否可以取消
     *
     * @param arrDate
     * @param deptDate
     * @return
     */
    public static CancelOrderRuleValidData judgeRuleCanCancel(Date arrDate, Date deptDate, Date postDate, String productType,
                                                              String productCode, String projectId, Prepay prepay) {
        //如果未检票则直接返回可以取消
        if (ProdType.WARES.val().equals(productType)) {
            CancelOrderRuleValidData cancelOrderValidData = new CancelOrderRuleValidData();
            if (prepay == null) {
                cancelOrderValidData.setLallow(true);
                return cancelOrderValidData;
            }
            ProdWareInfo prodWareInfo = ProdFactory.getProd(ProdType.WARES);
            boolean isUser = prodWareInfo.queryIsUse(prepay.getBookingid(), projectId, null);
            cancelOrderValidData.setLallow(!isUser);
            return cancelOrderValidData;

        }

        Rules rules = getProductRule(productType, productCode, projectId);
        RulesFun fun = new RulesFun(rules);
        CancelOrderRuleValidData data = fun.getCancelValidate(arrDate, deptDate, postDate, prepay);
        return data;
    }

    public static CancelOrderRuleValidData calcCancelPay(CancelOrderRuleValidData data, Prepay prepay, Date arrDate, Date postDate, String productType,
                                                         String productCode, String projectId, RefundReq refundReq) {
        Rules rules = getProductRule(productType, productCode, projectId);
        RulesFun fun = new RulesFun(rules);
        fun.calcReFundAmount(data, arrDate, postDate, prepay, refundReq);
        return data;
    }

    /**
     * 获取产品关联规则
     *
     * @param productType 产品类型.一般从数据库中取
     * @param productCode 产品代码
     * @param projectId   项目 ID
     * @return
     */
    public static Rules getProductRule(String productType, String productCode, String projectId) {
        ProdType type = ProdType.getProdType(productType);
        String ruleCode = "";
        if (type.equals(ProdType.ROOM)) {
            Roomtype roomtype = (Roomtype) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).
                    getRecord(projectId, productCode);
            if (roomtype != null && !roomtype.getRulecode().isEmpty()) {
                ruleCode = roomtype.getRulecode();
            }
        }
        if (type.equals(ProdType.TICKET)) {
            Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                    getRecord(projectId, productCode);
            if (ticket != null) {
                ruleCode = ticket.getRulecode();
            }
        }
        if (type.equals(ProdType.TAOCAN)) {
            Productkit productkit = (Productkit) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT).
                    getRecord(projectId, productCode);
            if (productkit != null) {
                String groupid = productkit.getGroupid();
                Kitgroup kitgroup = (Kitgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP).getRecord(projectId, groupid);
                if (kitgroup != null) {
                    ruleCode = productkit.getRulecode();
                }
            }
        }
        if (type.equals(ProdType.WARES)) {
            Spusitem spusitem = (Spusitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM).
                    getRecord(projectId, productCode);
            if (spusitem != null) {
                ruleCode = spusitem.getRulecode();
            }
        }
        if (type.equals(ProdType.ITEMS)) { //todo 伴手礼规则
            Rules rules = new Rules();
            rules.setLimitmax(999);
            rules.setLimitmin(1);
            rules.setBuyaudit(false);
            rules.setLimituser(true);
            rules.setLimitnum(999);
            rules.setRefundstatus(true);
            rules.setIsrefund(1);
            rules.setRefundday("0");
            rules.setLauditing(true);
            return rules;
        }
        if (!ruleCode.isEmpty()) {
            RulesCache rulesCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RULES);
            Rules rules = rulesCache.getRecord(projectId, ruleCode);
            if (rules != null && rules.getStatus()) {
                return rules;
            }
        }
        return null;
    }

    private BigDecimal matchStragery(String stragery, int advanceDay, BigDecimal debit) {
        if (!stragery.isEmpty()) {
            String[] subValue = stragery.split("-");
            int preValue = Integer.parseInt(subValue[0]);
            int sufValue = Integer.parseInt(subValue[1]);
            if (advanceDay >= preValue && advanceDay <= sufValue) {
                return debit;
            }
        }
        return BigDecimal.valueOf(-1);
    }

    public static Long getPayExpireTimeStamp(List<Booking_rs> bookingRsList, String projectId) throws DefinedException {
        int expireMin = ContentCacheTool.getSysOrderExpireMinutue(projectId);
        long now = DateTime.now().getTime();
        List<Long> expires = bookingRsList.stream().map(r -> {
            return CalculateDate.asUtilDate(r.getCreatedate()).getTime() + expireMin * 60 * 1000;
        }).collect(Collectors.toList());
        Long minExpire = Collections.min(expires);
        if (minExpire < now) {
            throw new DefinedException("订单支付时间已经过期", ResultCode.PAYFAIL.code());
        }
        return minExpire;
    }

    public static boolean isCheckTimeMenu(String comptype) {
        return checkTimeComptype.contains(comptype);
    }

    public static boolean lcontentCanShow(String comptype, String columnval, int nowHour) {
        if (!columnval.isEmpty() && checkTimeComptype.contains(comptype)) {
            String[] vals = columnval.split("-");
            if (vals.length < 2) {
                return true;
            }
            Integer begin = NumberUtil.parseInt(vals[0]);
            Integer end = NumberUtil.parseInt(vals[1]);
            if (end < begin) {//夜晚时间段
                return nowHour >= begin || nowHour <= end;
            } else if (end > begin) {
                return nowHour >= begin && nowHour <= end;
            }
        }
        return true;
    }

    public static boolean isGridSpus(String code, String projectId) {
        SpusitemCache spusitemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM);
        Spusitem spusitem = spusitemCache.getRecord(projectId, code);
        if (spusitem != null) {
            return spusitem.getLavl();
        }
        return false;
    }

    public static void checkTIcketForm(List<OrderGuestInfo> orderGuestInfos, String localTicketCode, String projectId, Integer ticketNum, OrderGuestInfo checkInGuestInfo) throws DefinedException {
        if (CollectionUtil.isNotEmpty(orderGuestInfos)) {
            Set<String> inputs = Sets.newHashSet();
            for (OrderGuestInfo orderGuestInfo : orderGuestInfos) {
                if (StrUtil.isBlank(orderGuestInfo.getIdno()) || StrUtil.isBlank(orderGuestInfo.getIdtype())) {
                    continue;
                }
                if (inputs.contains(orderGuestInfo.getIdno().toUpperCase().trim())) {
                    throw new DefinedException("输入重复:\n" + orderGuestInfo.getIdno(), SystemUtil.SystemerrorCode.ERR015_FORMERR);
                } else {
                    inputs.add(orderGuestInfo.getIdno().toUpperCase().trim());
                }

                checkIdCardTicketWriteList(localTicketCode, orderGuestInfo.getIdtype(), orderGuestInfo.getIdno(), projectId, ticketNum, checkInGuestInfo);//校验该票型是否限定身份证指定区域才可购买

                if (IdCardType.IDCARD.val().equals(orderGuestInfo.getIdtype())
                        || IdCardType.HKTW_IDCARD.val().equals(orderGuestInfo.getIdtype())) {//身份证或港澳台居住证是18位
                    if (!IdcardUtil.isValidCard(orderGuestInfo.getIdno())) {
                        throw new DefinedException("身份证输入错误:\n" + orderGuestInfo.getIdno(), SystemUtil.SystemerrorCode.ERR015_FORMERR);
                    }
                } else {
                    if (orderGuestInfo.getIdno().length() < 5) {
                        throw new DefinedException("证件号码输入错误:\n" + orderGuestInfo.getIdno(), SystemUtil.SystemerrorCode.ERR015_FORMERR);
                    }
                }
            }
        }
    }

    public static void checkKitCanBuy(String kitCode, Date bookingDate, String projectId) throws DefinedException {
        if (StrUtil.isBlank(kitCode)) {
            return;
        }
        Productkit productkit = (Productkit) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT).
                getRecord(projectId, kitCode);
        if (productkit != null) {
            String groupid = productkit.getGroupid();
            Kitgroup kitgroup = (Kitgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP).getRecord(projectId, groupid);
            if (kitgroup != null) {
                Date now = CalculateDate.getSystemDate();
                if (!CalculateDate.emptyDate(kitgroup.getStartsell())) {
                    if (!CalculateDate.isInRange(now, kitgroup.getStartsell(), kitgroup.getEndsell())) {
                        throw new DefinedException("当前购买套餐未到开卖时间", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                    }
                }
                if (!CalculateDate.isInRange(bookingDate, kitgroup.getStartdate(), kitgroup.getEnddate())) {
                    String msg = "套餐可预订的时间范围是" + CalculateDate.dateToString(kitgroup.getStartdate())
                            + "至" + CalculateDate.dateToString(kitgroup.getEnddate());
                    throw new DefinedException(msg, SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }
            }
        }
    }

    /**
     * 将请求的对象转成链接字符串
     *
     * @return
     */
    public static <T> String getPostUrlParamStr(T t) {
        JSONObject o = JSON.parseObject(JSON.toJSONString(t));
        Map<String, String> params = Maps.newHashMap();
        for (Map.Entry<String, Object> stringObjectEntry : o.entrySet()) {
            if (stringObjectEntry != null) {
                params.put(stringObjectEntry.getKey(), stringObjectEntry.getValue() + "");
            }
        }
        return Joiner.on("&").withKeyValueSeparator("=").join(params);
    }


    /**
     * 返回两个字符串中匹配的字符串
     *
     * @param soap
     * @param rgex
     * @return
     * <AUTHOR>
     */
    public static String getSubUtilSimple(String soap, String rgex) {
        Pattern pattern = Pattern.compile(rgex);// 匹配的模式
        Matcher m = pattern.matcher(soap);
        while (m.find()) {
            return m.group(1);
        }
        return "";
    }

    /**
     * 正则表达式匹配两个指定字符串中间的所有内容
     *
     * @param soap
     * @return
     * <AUTHOR>
     */
    public static List<String> getSubUtil(String soap, String rgex) {
        List<String> list = new ArrayList<String>();
        Pattern pattern = Pattern.compile(rgex);// 匹配的模式
        Matcher m = pattern.matcher(soap);
        while (m.find()) {
            int i = 1;
            list.add(m.group(i));
            i++;
        }
        return list;
    }

    /**
     * 统一性别转换
     *
     * @param postGender
     * @return
     */
    public static String transGender(String postGender) {
        if ("1M".contains(postGender)) {//男性
            return "1";
        } else if ("2F".contains(postGender)) {//女性
            return "2";
        }
        return "0";//未知
    }

    public static String getWapPayReturnUrl(String projectId) {
        VendorConfigCache vendorCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig vendorconfig = vendorCache.getVendorConfig(projectId, VendorType.M_H5);
        if (vendorconfig != null) {
            return vendorconfig.getNotifyurl();  //支付回调地址
        }
        return "";
    }

    public static String generateOauthRandomStateData(String appid, String returnurl, String projectId, String vendorType) {
        StateData stateData = new StateData();
        stateData.setAppid(appid);
        stateData.setReturnurl(returnurl);
        stateData.setProjectId(projectId);
        stateData.setVendortype(vendorType);

        String key = RandomUtil.randomString(RandomUtil.BASE_CHAR_NUMBER, 16) + CalculateDate.dateFormat(new Date(), "MMddHHmmss");
        //防止csrf授权攻击
        RedissonTool.getInstance().getMapCache(RedisKey.STATEINFO).put(key, JSON.toJSONString(stateData), 10, TimeUnit.MINUTES);//TODO 授权回调5分钟内随机数有效
        return key;
    }

    public static String generateOauthStaticStateData(String appid, String returnurl, String projectId, String vendorType) {
        StateData stateData = new StateData();
        stateData.setAppid(appid);
        stateData.setReturnurl(returnurl);
        stateData.setProjectId(projectId);
        stateData.setVendortype(vendorType);

        String key = Base64.encode(appid + "," + returnurl);
        RedissonTool.getInstance().getMapCache(RedisKey.STATEINFO).put(key, JSON.toJSONString(stateData), 10, TimeUnit.MINUTES);//一般用于公众号静默授权获取openid .不设置过期
        return key;
    }


    public static String getInfoFromState(String base64State, int posit) {

        String state = Base64Decoder.decodeStr(base64State);
        String[] states = state.split(",");
//        String enterappid = states[0];
//        String redirect = states[1];
        return states[posit];
    }

    public static StateData getOauthStateData(String stateStr) {
        Object o = RedissonTool.getInstance().getMapCache(RedisKey.STATEINFO).get(stateStr);
        if (o == null) {
            return null;
        } else {
            return JSON.parseObject(o + "", StateData.class);
        }

    }

    /**
     * @param signColumnVal
     * @param postSign
     * @param poststamp
     * @param projectId
     * @return
     */
    public static boolean isVailidReq(String signColumnVal, String postSign, String poststamp, String projectId) {
        String postsign = postSign;
        Long stamp = Long.valueOf(poststamp); //时间戳
        String checkbody = signColumnVal + stamp;

        String localSign = SysFuncLibTool.encodeAesContent(checkbody, projectId);
        if (!postsign.equals(localSign)) {   //签名校验不通过
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("签名校验失败"));
        }
        if (BigDecimal.valueOf(TimeUnit.MILLISECONDS.toMinutes(System.currentTimeMillis() - stamp)).abs().longValue() > 5) {//浏览器跟服务器时间差.暂定1小时
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("时间戳校验失败"));//如果时差大于2分钟.直接失效 测试.设置成600
        }
        return true;
    }

    public static String getCrmShopNo(String projectid) {
        return "GBYDGW";
    }

    public static boolean checkIdCardTicketWriteList(String localCode, String cardType, String idnum, String projectId, Integer ticketNum, OrderGuestInfo checkInGuestInfo) throws DefinedException {
        TicketCache ticketCache = (TicketCache) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
        Ticket ticket = ticketCache.getRecord(projectId, localCode);
        if (ticket != null) {//如果要求输入身份证号

            if (!ticket.getAllowids().isEmpty()) {
                if (!cardType.equals(IdCardType.IDCARD.val())) {
                    throw new DefinedException(ticket.getDescription() + "仅限身份证购买", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                } else {
                    String reason = ticket.getAllowtips().isEmpty() ? ticket.getDescription() + ":身份证非指定区段不可购买" : ticket.getAllowtips();
                    Set<String> ids = CollectionUtil.newHashSet(ticket.getAllowids().replaceAll("，", ",").split(","));//替换中文逗号,统一使用英文逗号
                    boolean lcheck = false;
                    for (String allowid : ids) {
                        if (allowid.equals("*") || idnum.startsWith(allowid)) {//在白名单之内.校验是本地身份证开头的.可以通过  * 号.代表只能身份证.针对要不到本地证件开头,只能线下看证.
                            lcheck = true;
                        }
                    }
                    if (!lcheck) {
                        throw new DefinedException(reason, SystemUtil.SystemerrorCode.ERR015_FORMERR);
                    }
                }
            }

            if (!ticket.getNodesc().isEmpty() && !StrUtil.isAllNotBlank(checkInGuestInfo.getExtranodesc(), checkInGuestInfo.getExtrano())) {
                throw new DefinedException("请填写:" + ticket.getNodesc() + "号码", SystemUtil.SystemerrorCode.ERR015_FORMERR);
            }

            if (ticket.getMaxidage() != 0 && cardType.equals(IdCardType.IDCARD.val())) {
                int age = getIdCardAge(idnum);
                if (projectId.equals("001")) {
                    Date ageDate = CalculateDate.reckonDay(getIdCardBirthday(idnum), Calendar.DATE, -31);//乌江寨生日按月提前加一岁
                    age = DateUtil.ageOfNow(ageDate);
                }
                if (age < ticket.getMaxidage()) {
                    throw new DefinedException(StrUtil.format("仅限{}岁及以上预约购买", ticket.getMaxidage()), SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }
            }

            if (ticket.getIddailylimit() > 0 && ticket.getCycle() > 0 && ticket.getLreal()) {//如果是实名制.并且做周期限购
                int cycle = Math.min(45, ticket.getCycle());  //跟历史库保持一致

                Date endDate = CalculateDate.reckonDay(CalculateDate.getSystemDate(), 5, 1);
                Date startDate = CalculateDate.reckonDay(endDate, 5, -cycle);

                ProdTicketInfo ticketInfo = ProdFactory.getProd(ProdType.TICKET);
                String groupCode = ticketInfo.getProdGroupCode(localCode, projectId);
                String checkCode = localCode;

                String sql = "SELECT COUNT(sqlid)  from ticket_rs where projectid=?1 and tcode=?2 and createdate BETWEEN '" + CalculateDate.dateToString(startDate) + "' and '" + CalculateDate.dateToString(endDate) + "'  and idinfo<>'' and idinfo<>'[]' " +
                        " AND JSON_CONTAINS(idinfo, JSON_OBJECT('idno', '" + idnum + "')) and canceldate='1900-01-01'";  //用其他账号买的.也加入限制.

                if (projectId.equals("005") && groupCode != null) {//李庄5.1 假期临时处理.
                    sql = "SELECT COUNT(sqlid)  from ticket_rs where projectid=?1 and groupid=?2 and createdate BETWEEN '" + CalculateDate.dateToString(startDate) + "' and '" + CalculateDate.dateToString(endDate) + "'  and idinfo<>'' and idinfo<>'[]' " +
                            " AND JSON_CONTAINS(idinfo, JSON_OBJECT('idno', '" + idnum + "')) and canceldate='1900-01-01'";  //用其他账号买的.也加入限制.
                    checkCode = groupCode;
                }


                DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
                Integer num = NumberUtil.parseInt(daoLocal.getNativeObject(sql, projectId, checkCode) + "");

                if (num + 1 > ticket.getIddailylimit()) {//如果已购买数量+当前购买数量>限制数量
                    throw new DefinedException(StrUtil.format("证件号{} 每{}日仅限购买{} 单", idnum, ticket.getCycle(), ticket.getIddailylimit()), SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }

               /* String key = StrUtil.join(":", projectId, localCode, cardType, idnum);
                RMapCache<String, Integer> cache = RedissonTool.getInstance().getMapCache(RedisKey.TICKETIDLIMITCACHE);
                Integer limit = cache.getOrDefault(key, 0);
                //long l1 = cache.remainTimeToLive(key);
                //System.out.println(StrUtil.format("检查校验{}:{} 过期时间:{} ", key, limit, l1 == 0 ? "未设置过期时间" : DateUtil.formatBetween(l1)));
                if (limit + 1 > ticket.getIddailylimit()) {
                    throw new DefinedException(StrUtil.format("证件号{} 每日仅限购买{} 单", idnum, ticket.getIddailylimit()), SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }*/
            }
            return true;
        } else {
            return true;
        }
    }

    /**
     * 根据可用数与物理数返回可订状态
     *
     * @param avlnum
     * @param total
     * @return 0:订满,1:空闲,2:适中,3,紧张
     */
    public static Integer getSkuStatus(Integer avlnum, Integer total) {
//
        if (avlnum <= 0 || total <= 0) {
            return 0;//订满
        } else {
            Double leftnum = NumberUtil.div(avlnum, total).doubleValue();
            if (leftnum >= 0.6 && leftnum <= 1) {
                return 1;//空闲
            } else if (leftnum >= 0.3 && leftnum < 0.6) {
                return 2;//适中
            } else if (leftnum > 0 && leftnum < 0.3) {
                return 3;//拥挤
            } else {
                return 1;
            }
        }
    }

    /**
     * 返回实际精度显示价格.不会显示多余的精度小数点
     *
     * @param price
     * @return
     */
    public static String getShowPrice(BigDecimal price) {
        return price.setScale(2, RoundingMode.HALF_EVEN).stripTrailingZeros().toPlainString();
    }

    /**
     * 将伴手礼产品的所有规格代码展开
     *
     * @param codes
     * @param signal
     * @return
     */
    public static List<String> fissionSpec(List<String> codes, String signal) {//codes :  传递每一层的结构
        List<String> result = Lists.newArrayList();
        for (String currentdiv : codes) {//循环每一层
            if (currentdiv.isEmpty()) { //发现空格层就中断循环
                continue;
            }
            if (result.isEmpty()) {//初始化第一层开头
                result.add(currentdiv);
                continue;
            }
            List<String> newdiv = Lists.newArrayList();
            for (String currentNode : currentdiv.split(",")) { //分割数据
                for (String preRow : result) {
                    newdiv.add(preRow + signal + currentNode);
                }
            }
            result = newdiv;//传递最新分割
        }
        return result;
    }

    /**
     * 将二维码串转成base64 二维码
     *
     * @param scene
     * @param orderid
     * @param projectId
     * @return
     */
    public static String generateEncodeQr(String productType, String orderid, String projectId) {
        QrConfig config = new QrConfig(280, 280);
        config.setRatio(4);
        String base64qr = QrCodeUtil.generateAsBase64(productType + orderid, config, ImgUtil.IMAGE_TYPE_PNG);
        base64qr = base64qr.replaceAll("data:image/png;base64,", "");
        return base64qr;
    }

    public static String generateCouponEncodeQr(String couponid, String projectId) {
        String qrdata = SysFuncLibTool.encodeAesContent(couponid, projectId);
        QrConfig config = new QrConfig(280, 280);
        config.setRatio(4);
        String base64qr = QrCodeUtil.generateAsBase64(qrdata, config, ImgUtil.IMAGE_TYPE_PNG);
        base64qr = base64qr.replaceAll("data:image/png;base64,", "");
        return base64qr;
    }

    public static boolean lInputMatch(String dbcol, String input) {
        Set<String> dbSet = Sets.newHashSet(dbcol.split(","));
        Set<String> inputs = Sets.newHashSet(input.split(","));
        ;
        for (String s : dbSet) {
            if (inputs.contains(s)) {
                return true;
            }
        }
        return false;
    }

    public static String transRichText2PureText(String richText) {
        if (StrUtil.isNotBlank(richText)) {
            Document doc = Jsoup.parse(richText);
            return doc.text().replaceAll("：", ":");  //降空格.跟中文冒号,中文逗号全部替换掉
        }
        return StrUtil.EMPTY;
    }

    /**
     * 获取对象数据大小
     *
     * @param object
     * @param unit   数据单位
     * @return 占用的字节大小
     * @throws Exception
     */
    public static Long getObjectSize(Object object, DataUnit unit) {
        if (object == null)
            return 0L;
        ByteArrayOutputStream bStream = new ByteArrayOutputStream();
        ObjectOutputStream oStream = null;
        try {
            oStream = new ObjectOutputStream(bStream);
            oStream.writeObject(object);
            oStream.flush();
        } catch (IOException e) {
            return 0L;
        } finally {
            if (bStream != null) {
                try {
                    bStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (oStream != null) {
                try {
                    oStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        DataSize bytesize = DataSize.of(Long.valueOf(bStream.size()), DataUnit.BYTES);
        switch (unit) {
            case KILOBYTES:
                return bytesize.toKilobytes();
            case MEGABYTES:
                return bytesize.toMegabytes();
            case GIGABYTES:
                return bytesize.toGigabytes();
            case TERABYTES:
                return bytesize.toTerabytes();
            default:
                return bytesize.toBytes();
        }
    }

    public static JSON converString2Json(String str) {
        if (JSONUtil.isJsonObj(str)) {
            return JSONObject.parseObject(str);
        } else if (JSONUtil.isJsonArray(str)) {
            return JSONArray.parseArray(str);
        }
        return null;
    }

    public static String converJson2String(JSON json) {
        if (json instanceof JSONObject) {
            return JSONObject.toJSONString(json);
        } else if (json instanceof JSONArray) {
            return JSONArray.toJSONString(json);
        }
        return "{}";
    }

    public static Date getIdCardBirthday(String idnum) {
        String regex = "^(\\d{17})|^(\\d{15})$"; // 身份证号码正则表达式
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(idnum);
        if (matcher.find()) {
            String idNumber = matcher.group();
            return CalculateDate.stringToDate(StrUtil.format("{}-{}-{}", idNumber.substring(6, 10), idNumber.substring(10, 12), idNumber.substring(12, 14)));
        } else {
            return CalculateDate.NULLDATE;
        }
    }

    public static Integer getIdCardAge(String idnum) {
        String regex = "^(\\d{17})|^(\\d{15})$"; // 身份证号码正则表达式
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(idnum);

        if (matcher.find()) {
            String idNumber = matcher.group();
            return DateUtil.ageOfNow(StrUtil.format("{}-{}-{}", idNumber.substring(6, 10), idNumber.substring(10, 12), idNumber.substring(12, 14)));
        } else {
            //throw new IllegalArgumentException("Invalid ID format");
            return 999999999;
        }
    }

    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    public static void sortTimePeriods(List<String> queryPeriods) {
        queryPeriods.sort(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                String[] times1 = o1.split(" ~ ");
                String[] times2 = o2.split(" ~ ");

                LocalTime start1 = LocalTime.parse(times1[0]);
                LocalTime end1 = LocalTime.parse(times1[1]);
                LocalTime start2 = LocalTime.parse(times2[0]);
                LocalTime end2 = LocalTime.parse(times2[1]);

                int startComparison = start1.compareTo(start2);
                if (startComparison != 0) {
                    return startComparison;
                } else {
                    return end1.compareTo(end2);
                }
            }
        });
    }

}
