package com.cw.arithmetic;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.cw.arithmetic.func.Var;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.arithmetic.lang.R;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.*;
import com.cw.config.OSSConfig;
import com.cw.config.confyaml.node.Conf_Invoicenode;
import com.cw.config.exception.CustomException;
import com.cw.core.CoreCache;
import com.cw.core.CorePrice;
import com.cw.core.CoreRs;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.*;
import com.cw.outsys.stdop.request.StdInvoiceCreateRequest;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.req.node.RefundInfo;
import com.cw.pojo.dto.app.res.AppPostageDetail;
import com.cw.pojo.dto.app.res.AppPostageInfo;
import com.cw.pojo.dto.app.res.AppProdSku;
import com.cw.pojo.dto.app.res.LinkData;
import com.cw.pojo.dto.conf.req.rules.StrategyJson;
import com.cw.pojo.dto.conf.res.cms.Agreements;
import com.cw.pojo.dto.conf.res.cms.BusinessBanner;
import com.cw.pojo.dto.conf.res.cms.PageFoot;
import com.cw.pojo.dto.conf.res.cms.PageHeader;
import com.cw.pojo.dto.conf.res.gift.GiftitemParamDetail;
import com.cw.pojo.dto.conf.res.meeting.MeetingStyleJson;
import com.cw.pojo.dto.conf.res.perform.PerformSchedule;
import com.cw.pojo.dto.order.res.OrderKitItemItem;
import com.cw.pojo.entity.Perform_Entity;
import com.cw.service.order.impl.OrderServiceImpl;
import com.cw.utils.*;
import com.cw.utils.enums.*;
import com.cw.utils.enums.meeting.MeetingStyleEnum;
import com.cw.utils.enums.menus.MenuPlatformTypeEnums;
import com.cw.utils.enums.menus.MenuReserveId;
import com.cw.utils.enums.menus.PCPageTypeEnum;
import com.cw.utils.invoice.InvoiceUtil;
import com.cw.utils.pagedata.SelectDataNode;
import com.cw.utils.province.ProvinceUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 获取存缓存的一切内容工具类
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/10 11:04
 **/
public class ContentCacheTool {
    /**
     * @return
     */
    public static int getSysOrderExpireMinutue(String projectId) {
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf sysConf = sysConfCache.getOne(projectId);
        if (sysConf != null) {
            return sysConf.getAutocancel() <= 0 ? 15 : sysConf.getAutocancel();//如果没有设置自动取消时间，默认15分钟
        }
        return 15;
    }

    public static String getFIrstPic(String picColumnVal) {
        if (StringUtils.isNotBlank(picColumnVal)) {
            String[] array = picColumnVal.split(",");
            return array[0];
        }
        return StringUtils.EMPTY;
    }

    public static String getTicketOutCode(String localCode, String projectId, ProdType prodType) {
        if (ProdType.TICKET.equals(prodType)) {
            TicketCache ticketCache = (TicketCache) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
            Ticket ticket = ticketCache.getRecord(projectId, localCode);
            if (ticket != null) {
                return ticket.getOutcode();
            }
        }

        if (ProdType.ACTGROUP.equals(prodType)) {
            Actsite actsite = getActsite(projectId, localCode);
            if (actsite != null) {
                return actsite.getOutcode();
            }
        }
        return localCode;
    }

    public static boolean isRequireIdTicket(String localCode, String projectId) {
        TicketCache ticketCache = (TicketCache) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
        Ticket ticket = ticketCache.getRecord(projectId, localCode);
        if (ticket != null) {
            return ticket.getLreal();
        } else {
            return false;
        }
    }


    public static String getPayOrderDesc(List<Booking_rs> rsList) {
        String desc = "";
        for (Booking_rs bookingRs : rsList) {
            ProdType type = ProdType.getProdType(bookingRs.getPtype());
            String groupDesc = type.equals(ProdType.ROOM) ? getProductGroupInfo(bookingRs.getPtype(), bookingRs.getProduct(), bookingRs.getProjectid(), false) : "";
            String myDesc = groupDesc + "" + getProductDesc(bookingRs.getPtype(), bookingRs.getProduct(), bookingRs.getProjectid());
            desc += desc.isEmpty() ? myDesc : "," + myDesc;
        }
        return desc;
    }

    public static BigDecimal getPayOrderAmount(List<Booking_rs> rsList) {
        BigDecimal amount = BigDecimal.ZERO;
        for (Booking_rs bookingRs : rsList) {
            amount = amount.add(bookingRs.getAmount());
        }
        return amount;
    }

    /**
     * 获取产品轮播图片
     *
     * @param productType 产品类型.一般从数据库中取
     * @param productCode 产品代码
     * @param projectId   项目 ID
     * @return
     */
    public static List<String> getProductSlidPics(String productType, String productCode, String projectId, boolean lmobile) {
        return ProdFactory.getProd(ProdType.getProdType(productType)).getSlidPics(productCode, projectId, lmobile);
    }

    /**
     * @param str
     * @return 内容为null转换空字符串
     */
    public static String getContentStrValue(String str) {
        if (str == null) {
            return "";
        } else {
            return str;
        }
    }

    /**
     * @param bookingid   主订单号
     * @param productType 产品类型
     * @param productCode 产品代码
     * @param projectId   项目ID
     * @param arrDate     到店日期
     * @param deptDate
     * @return 免费取消订单时间
     */
    public static String getProductCancelFreeTime(String bookingid, String productType, String productCode, String projectId, Date arrDate, Date deptDate) {
        Date nowTime = new Date();
        String ruleCode = null;
        ProdType type = ProdType.getProdType(productType);

        ruleCode = ProdFactory.getProd(type).getProdRuleCode(productCode, projectId);


        if (type.equals(ProdType.ITEMS)) {//伴手礼不显示
            return "";

        }
        Date arrEnd = DateUtil.endOfDay(arrDate);
        //Date arrEnd = DateUtil.offsetHour(arrDate, 24);
        //未到店时间
        if (DateUtil.compare(arrEnd, nowTime) >= 0) {
            if (StringUtils.isNotBlank(ruleCode)) {
                Date cancelFreeTime = calProductCodeRuleCancelFreeTime(bookingid, ruleCode, projectId, arrDate, deptDate, nowTime);
                //可免费取消时间小于当前时间不显示
                if (DateUtil.compare(cancelFreeTime, nowTime) >= 0) {
                    return CalculateDate.timeToString(cancelFreeTime, "yyyy-MM-dd HH:mm") + "前可免费取消";
                } else {
                    return "";
                }
            } else {
                return SystemUtil.RULECANCELFREE;//未到到店当天结束没有规则免费全额取消
            }
        } else {
            return "";//超过到店时间不显示
        }
    }

    /**
     * @param bookingid 主订单号
     * @param ruleCode  规则代码
     * @param projectId 项目代码
     * @param arrDate   到店日期
     * @param deptDate
     * @return 计算规则提前退款免费时间
     */
    private static Date calProductCodeRuleCancelFreeTime(String bookingid, String ruleCode, String projectId, Date arrDate, Date deptDate, Date postDate) {
        Date cancelTime = null;
        Rules rules = (Rules) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RULES).getRecord(projectId, ruleCode);
        if (rules != null) {
            //需要校验新增策略字段是否非空
            if (StringUtils.isNotBlank(rules.getPolicys())) {
                List<String> policys = Arrays.asList(rules.getPolicys().split(","));
                RulespolicyCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RULESPOLICY);
                //获取取消校验的策略集合 校验日期和当前时间判断
                List<Rulespolicy> list = cache.getDataListWithCondition(rules.getProjectid(), p -> policys.contains(p.getCode()) && p.getPolicytype() == 0);
                if (CollectionUtil.isNotEmpty(list)) {
                    for (Rulespolicy node : list) {
                        Date startDate = node.getStartdate();
                        Date endDate = node.getEnddate();
                        //预定时段不可取消
                        if (ProdType.ROOM.val().equals(rules.getType()) || ProdType.TAOCAN.val().equals(rules.getType())) {//房型订单
                            if (DateUtil.isIn(arrDate, startDate, endDate)) {
                                return checkCanCancelRulePolicyPeriod(node, postDate, arrDate, deptDate);

                            }
                        } else if (node.getControltype() == 0 &&
                                (DateUtil.isIn(arrDate, startDate, endDate) || DateUtil.isIn(deptDate, startDate, endDate))) { //其他类型订单 判断使用时间
                            return checkCanCancelRulePolicyPeriod(node, postDate, arrDate, deptDate);

                        } else if (node.getControltype() == 1 && DateUtil.isIn(arrDate, startDate, endDate)) {
                            return checkCanCancelRulePolicyPeriod(node, postDate, arrDate, deptDate);

                        }
                    }
                }
            }
            //如果设置了下单后可免费取消分钟数 优先判断
            if (rules.getRefundfreetime() != 0) {
                PrepayMapper prepayMapper = SpringUtil.getBean(PrepayMapper.class);
                Prepay prepay = prepayMapper.findPrepayByBookingid(bookingid);
                if (prepay != null) {
                    int refundFreeTime = rules.getRefundfreetime();
                    Date prepayTime = Date.from(prepay.getCreatetime().atZone(ZoneId.systemDefault()).toInstant());
                    int postAdvMin = (int) ((postDate.getTime() - prepayTime.getTime()) / 60 / 1000);//日期差分钟数
                    if (postAdvMin < refundFreeTime) {
                        return DateUtil.offsetMinute(prepayTime, refundFreeTime);//支付下单后规定时间内可免费取消
                    }
                }
            }
            if (rules.getIsrefund() == 1) {
                int ruleAdvRefundHour = NumberUtil.parseInt(rules.getRefundday());
                Date arrEnd = DateUtil.endOfDay(arrDate);//到店当天晚
                //Date arrEnd = DateUtil.offsetHour(arrDate, 24);//到店当天晚
                //判断是否开启退款规则 找出免费提前取消时间
                if (rules.getStrategystaus() && StringUtils.isNotBlank(rules.getStrategyjson())) {
                    List<StrategyJson> strategyJsonList = JSON.parseArray(rules.getStrategyjson(), StrategyJson.class);
                    StrategyJson.sort(strategyJsonList);
                    int advMaxHour = 0;//查看可免费取消提前最大时间
                    for (StrategyJson strategyJson : strategyJsonList) {
                        if (StringUtils.isNotBlank(strategyJson.getStrategy())) {
                            String[] strategyArray = strategyJson.getStrategy().split("-");
                            int maxHour = Integer.parseInt(strategyArray[1]);
                            if (maxHour > advMaxHour) {
                                advMaxHour = maxHour;//赋值区间最大值
                            }
                        }
                    }
                    if (advMaxHour > 0) { //扣款策略设置小时区间不为空
                        cancelTime = DateUtil.offsetHour(arrEnd, -advMaxHour);
                    } else {//扣款策略设置小时区间为空
                        cancelTime = DateUtil.offsetHour(arrEnd, -ruleAdvRefundHour);
                    }
                } else {
                    cancelTime = DateUtil.offsetHour(arrEnd, -ruleAdvRefundHour); //提前可取消小时数
                }
            } else if (rules.getIsrefund() == 0) {
                //不限制退款 设置个默认时间 当天
                cancelTime = DateUtil.endOfDay(arrDate);//到店当天晚之前
                //cancelTime = DateUtil.offsetHour(arrDate, 24);
            }

        }
        if (cancelTime != null) {
            return cancelTime;
        } else {
            return null;
        }
    }

    /**
     * @param node
     * @param postDate
     * @param arrDate
     * @param deptDate
     * @return 判断是否可以免费提前可退时间
     */
    private static Date checkCanCancelRulePolicyPeriod(Rulespolicy node, Date postDate, Date arrDate, Date deptDate) {
        if (StringUtils.isNotBlank(node.getScript())) {
            //获取参数控制提前取消分钟数
            String minusStr = SpelFomulaFactory.getStringFomulaResult(node.getScript(), node);
            if (StringUtils.isNotBlank(minusStr)) {
                int minus = Integer.parseInt(minusStr);//参数控制提前分钟可免费取消
                int seconds = (minus * 60) - 1;//换算秒数 -1取边界值整数分钟
                long advSeconds = DateUtil.between(postDate, DateUtil.endOfDay(arrDate), DateUnit.SECOND, false);//提前秒数，
                // 为正数标识posDate还未到arrDate，数字为剩余秒数
                if (advSeconds < 0 || advSeconds < seconds) { //超过到店时间 或者 未在提前时间范围内
                    return null;
                } else {
                    return DateUtil.offsetSecond(DateUtil.endOfDay(arrDate), -seconds);
                }
            }
        } else {
            return null;
        }
        return null;
    }

    /**
     * @param productkits 套餐详情
     * @param arrDate     到店时间
     * @return 套餐免费取消提前时间
     */
    private static String getProductKitCancelFreeTime(String bookingid, List<Productkit> productkits, Date arrDate, Date deptDate, Date postDate) {
        //套餐统一 查询第一个产品即可
        Date cancelFreeTime = null;
        if (CollectionUtil.isNotEmpty(productkits)) {
            cancelFreeTime = calProductCodeRuleCancelFreeTime(bookingid, productkits.get(0).getRulecode(), productkits.get(0).getProjectid(), arrDate, deptDate, postDate);
        }
        if (cancelFreeTime == null) {
            return "";
        } else {
            return CalculateDate.timeToString(cancelFreeTime, CalculateDate.spd_time.toString());
        }
    }

    /**
     * 获取产品订单展示图片
     *
     * @param productType 产品类型.一般从数据库中取
     * @param productCode 产品代码
     * @param projectId   项目 ID
     * @return
     */
    public static String getProductOrderShowPic(String productType, String productCode, String projectId, boolean lmobile) {
        ProdType type = ProdType.getProdType(productType);
        return ProdFactory.getProd(type).getProductShowPic(productCode, projectId, lmobile);

    }

    /**
     * @param productCode 伴手礼产品代码
     * @param specs1code  规格1代码
     * @param projectId   项目ID
     * @return 返回伴手礼商品对应规格代码图片
     */
    public static String getGiftitemShowPic(String productCode, String specs1code, String projectId) {
        Giftitem giftitem = (Giftitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM).getRecord(projectId, productCode);
        List<String> picList = Arrays.asList(giftitem.getSpecs1pic().split(","));//规格1对应图片列表
        List<String> specs1codeList = Arrays.asList(giftitem.getSpecs1code().split(","));
        int index = specs1codeList.indexOf(specs1code);
        if (index != -1) {
            return picList.get(index);
        } else {

            return picList.get(0);
        }
    }

    public static boolean isNeedIdCard(String ticketCode, String projectId) {
        Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                getRecord(projectId, ticketCode);
        if (ticket != null) {
            return ticket.getLreal();
        }
        return false;
    }

    public static boolean isNeedShowTime(String ticketCode, String projectId) {
        Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                getRecord(projectId, ticketCode);
        if (ticket != null) {
            return ticket.getLshowtime();
        }
        return false;
    }

    public static boolean isOneIdCard2ManyPerson(String ticketCode, String projectId) {
        Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                getRecord(projectId, ticketCode);
        if (ticket != null) {
            return ticket.getLo2m();
        }
        return false;
    }


    /**
     * @param periods
     * @param porjectId
     * @return 预约时段代码转换具体时段区间
     */
    public static String getPeriodsDesc(String periods, String porjectId) {
        StringBuffer stringBuffer = new StringBuffer();
        List<String> list = Arrays.asList(periods.split(","));
        if (CollectionUtil.isNotEmpty(list)) {
            ActperiodCache actperiodCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTPERIOD);
            for (int i = 0; i < list.size(); i++) {
                Actperiod actperiod = actperiodCache.getRecord(porjectId, list.get(i));
                if (actperiod != null) {
                    if (i != 0) {
                        stringBuffer.append(",");//多个时段代码转换拼接
                    }
                    stringBuffer.append(actperiod.getTime());
                }
            }
        }
        return stringBuffer.toString();
    }


    /**
     * 获取产品描述
     *
     * @param productType 产品类型.一般从数据库中取
     * @param productCode 产品代码
     * @param projectId   项目 ID
     * @return
     */
    public static String getProductDesc(String productType, String productCode, String projectId) {
        ProdType type = ProdType.getProdType(productType);
        return ProdFactory.getProd(type).getProductDesc(productCode, projectId);

    }

    /**
     * 获取产品所属的大组属性
     *
     * @param productType       产品类型.一般从数据库中取
     * @param productCode       产品代码
     * @param projectId         项目 ID
     * @param codeOrDescription 取组的 code 传 true ,描述传 false
     * @return
     */
    public static String getProductGroupInfo(String productType, String productCode, String projectId, Boolean codeOrDescription) {
        ProdType type = ProdType.getProdType(productType);

        return codeOrDescription ? ProdFactory.getProd(type).getProdGroupCode(productCode, projectId)
                : ProdFactory.getProd(type).getProdGroupDesc(productCode, projectId);
    }

    /**
     * 获取缓存产品的价格
     *
     * @param productType
     * @param queryDate
     * @param productCode
     * @param projectId
     * @return
     */
    public static BigDecimal getProductCachedPrice(ProdType productType, Date queryDate, String productCode, String projectId) {
        CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
        return corePrice.getShowPrice(productType, queryDate, productCode, projectId);
//        return BigDecimal.valueOf((RandomUtil.randomInt(400, 1000) / 10 * 10));
    }


    /**
     * 获取子产品的最低起步价
     *
     * @param productType
     * @param queryDate
     * @param groupCode
     * @param projectId
     * @param var
     * @param prices      填充所有子产品的价格LIST  用于外部过滤
     * @return
     */
    public static BigDecimal getChildProductCachedMinPrice(ProdType productType, Date queryDate, String groupCode, String projectId, Var<String> var, List<BigDecimal> prices) {
        CoreCache coreCache = SpringUtil.getBean(CoreCache.class);
        String ratecode = CorePrice.getRateCode(projectId, "");
        BigDecimal minPrice = null;

        if (ProdType.TAOCAN.equals(productType)) {
            ProductKitCache kitCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT);
            List<Productkit> kits = kitCache.getDataListWithCondition(projectId, r -> r.getGroupid().equals(groupCode) && r.getLsell());// ticketCache.getDataList(projectId).stream().filter(r -> r.getGroupid().equals(groupCode) && r.getLsell()).collect(Collectors.toList());
            for (Productkit productkit : kits) {
                BigDecimal showPrice = productkit.getLowest();  //coreCache.getCacheShowPrice(projectId, ratecode, ProdType.TAOCAN, productkit.getCode(), queryDate);
                if (CalculateNumber.isGreaterThanZero(showPrice)) {
                    if (minPrice == null) {
                        minPrice = showPrice;
                    } else {
                        minPrice = CalculateNumber.min(minPrice, showPrice);
                    }
                    if (minPrice.doubleValue() == showPrice.doubleValue()) {
                        var.setValue(productkit.getCode());
                    }
                }
            }
        } else if (ProdType.TICKET.equals(productType)) {
            TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
            List<Ticket> tickets = ticketCache.getDataListWithCondition(projectId, r -> r.getGroupid().equals(groupCode) && r.getLsell());// ticketCache.getDataList(projectId).stream().filter(r -> r.getGroupid().equals(groupCode) && r.getLsell()).collect(Collectors.toList());
            for (Ticket ticket : tickets) {
                BigDecimal showPrice = coreCache.getCacheShowPrice(projectId, ratecode, ProdType.TICKET, ticket.getCode(), queryDate);
                if (CalculateNumber.isGreaterThanZero(showPrice)) {
                    if (minPrice == null) {
                        minPrice = showPrice;
                    } else {
                        minPrice = CalculateNumber.min(minPrice, showPrice);
                    }
                    if (minPrice.doubleValue() == showPrice.doubleValue()) {
                        var.setValue(ticket.getCode());
                    }
                }
            }
        } else if (ProdType.ROOM.equals(productType)) {
            RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
            List<Roomtype> roomtypes = roomTypeCache.getDataListWithCondition(projectId, r -> r.getHotelcode().equals(groupCode) && r.getLsell());// ticketCache.getDataList(projectId).stream().filter(r -> r.getGroupid().equals(groupCode) && r.getLsell()).collect(Collectors.toList());
            for (Roomtype roomtype : roomtypes) {
                BigDecimal showPrice = coreCache.getCacheShowPrice(projectId, ratecode, ProdType.ROOM, roomtype.getCode(), queryDate);
                if (CalculateNumber.isGreaterThanZero(showPrice)) {
                    if (prices != null) {
                        prices.add(showPrice);
                    }
                    if (minPrice == null) {
                        minPrice = showPrice;
                    } else {
                        minPrice = CalculateNumber.min(minPrice, showPrice);
                    }
                    if (minPrice.doubleValue() == showPrice.doubleValue()) {
                        var.setValue(roomtype.getCode());
                    }
                }
            }
        } else if (ProdType.WARES.equals(productType)) { //景区商品
            SpusitemCache spusitemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM);
            List<Spusitem> spusitems = spusitemCache.getDataListWithCondition(projectId, r -> r.getGroupid().equals(groupCode) && r.getLsell());
            for (Spusitem spusitem : spusitems) {
                BigDecimal showPrice = coreCache.getCacheShowPrice(projectId, ratecode, ProdType.WARES, spusitem.getCode(), queryDate);
                if (CalculateNumber.isGreaterThanZero(showPrice)) {
                    if (prices != null) {
                        prices.add(showPrice);
                    }
                    if (minPrice == null) {
                        minPrice = showPrice;
                    } else {
                        minPrice = CalculateNumber.min(minPrice, showPrice);
                    }
                    if (minPrice.doubleValue() == showPrice.doubleValue()) {
                        var.setValue(spusitem.getCode());
                    }
                }
            }
        } else if (ProdType.ITEMS.equals(productType)) { //伴手礼商品
            GiftItemCache giftItemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
            List<Giftitem> giftitems = giftItemCache.getDataListWithCondition(projectId, r -> r.getCode().equals(groupCode) && r.getLsell());//这里传的是伴手礼商品代码
            for (Giftitem giftitem : giftitems) {
                BigDecimal showPrice = coreCache.getCacheShowPrice(projectId, ratecode, ProdType.ITEMS, giftitem.getCode(), queryDate);
                if (CalculateNumber.isGreaterThanZero(showPrice)) {
                    if (prices != null) {
                        prices.add(showPrice);
                    }
                    if (minPrice == null) {
                        minPrice = showPrice;
                    } else {
                        minPrice = CalculateNumber.min(minPrice, showPrice);
                    }
                    if (minPrice.doubleValue() == showPrice.doubleValue()) {
                        var.setValue(giftitem.getCode());
                    }
                }
            }
        } else if (ProdType.MEETING.equals(productType)) { //会议室
            MeetingCache meetingCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETING);
            List<Meeting> meetings = meetingCache.getDataListWithCondition(projectId, r -> r.getGroupid().equals(groupCode) && r.getLsell());
            for (Meeting meeting : meetings) {
                BigDecimal showPrice = meeting.getPrice();
                if (CalculateNumber.isGreaterThanZero(showPrice)) {
                    if (prices != null) {
                        prices.add(showPrice);
                    }
                    if (minPrice == null) {
                        minPrice = showPrice;
                    } else {
                        minPrice = CalculateNumber.min(minPrice, showPrice);
                    }
                    if (minPrice.doubleValue() == showPrice.doubleValue()) {
                        var.setValue(meeting.getCode());
                    }
                }
            }
        }

        return minPrice == null ? BigDecimal.ZERO : NumberUtil.max(minPrice, BigDecimal.ZERO);
    }


    /**
     * @param projectId
     * @param packageCode
     * @return 返回会议室风格描述
     */
    public static List<MeetingStyleJson> getMeetingStyleDesc(String projectId, String packageCode) {
        List<MeetingStyleJson> meetingStyleJson = new ArrayList<>();
        if (StringUtils.isNotBlank(packageCode)) {
            meetingStyleJson = JSON.parseArray(packageCode, MeetingStyleJson.class);
            if (CollectionUtil.isNotEmpty(meetingStyleJson)) {
                meetingStyleJson = meetingStyleJson.stream().map(object -> {
                    MeetingStyleJson mt = new MeetingStyleJson();
                    BeanUtils.copyPropertiesIgnoreNull(object, mt);
                    mt.setDescription(MeetingStyleEnum.getMeetingDesc(mt.getCode()));
                    mt.setPeople(mt.getPeople() + "人");
                    return mt;
                }).collect(Collectors.toList());
            }
        }
        return meetingStyleJson;
    }

    /**
     * @param projectId
     * @param packageCode
     * @return 返回配套代码描述
     */
    public static String getSupportDescString(String projectId, String packageCode, boolean lmobile) {
        StringBuffer stringBuffer = new StringBuffer();
        if (StringUtils.isNotBlank(packageCode)) {
            List<String> codeList = Arrays.asList(packageCode.split(","));
            FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
            for (int i = 0; i < codeList.size(); i++) {
                Factor factor = cache.getRecord(projectId, codeList.get(i));
                if (factor != null && factor.getStatus() == 1) {
                    if (i != 0) {
                        if (lmobile) {
                            stringBuffer.append("/");//移动端分割符号
                        } else {
                            stringBuffer.append(",");//PC端分割符号
                        }
                    }
                    stringBuffer.append(R.lang(factor.getDescription()));
                }
            }
        }
        return stringBuffer.toString();
    }

    /**
     * @param projectId   项目id
     * @param packageCode 房型配套代码
     * @return 获取配套代码节点信息
     */
    public static List<SelectDataNode> getPackageCodeNodeLst(String projectId, String packageCode) {
        List<SelectDataNode> parentList = new ArrayList<>();
        List<Factor> factorList = new ArrayList<>();
        //Map<sqlid,SelectDataNode>
        Map<Long, List<Factor>> childrenMap = new HashMap<>();
        if (StringUtils.isNotBlank(packageCode)) {
            List<String> codeList = Arrays.asList(packageCode.split(","));
            //遍历查找配套代码
            for (String code : codeList) {
                getFactorAndChildrenList(projectId, code, factorList, childrenMap);
            }

        }

        if (CollectionUtil.isNotEmpty(factorList)) {
            //多选代码去除重复项factorList
            Set<Factor> factorSet = new TreeSet<Factor>((o1, o2) -> o1.getCode().compareTo(o2.getCode()));
            factorSet.addAll(factorList);
            factorList = new ArrayList<Factor>(factorSet);
            //排序节点信息 按sqlid header seq 升序
            factorList.sort(Comparator.comparing(Factor::getHeader).thenComparing(Factor::getSeq));
            for (Factor factor : factorList) {
                SelectDataNode node = new SelectDataNode();
                node.setCode(factor.getCode());
                node.setDesc(R.lang(factor.getDescription()));
                node.setGroup(factor.getHeader());
                if (!childrenMap.isEmpty() && childrenMap.containsKey(factor.getSqlid())) {
                    //子项集合填充
                    node.setChildren(getChildrenSelectNodeList(childrenMap.get(factor.getSqlid())));
                }
                parentList.add(node);
            }
        }


        return parentList;
    }

    /**
     * @param factors
     * @return 获取有序子项集合
     */
    private static List<SelectDataNode> getChildrenSelectNodeList(List<Factor> factors) {
        List<SelectDataNode> childrenNodeList = new ArrayList<>();
        //排序 按seq升序
        factors.sort(Comparator.comparing(Factor::getSeq));
        for (Factor children : factors) {
            SelectDataNode childrenNode = new SelectDataNode();
            childrenNode.setCode(children.getCode());
            childrenNode.setDesc(R.lang(children.getDescription()));
            childrenNode.setGroup(children.getHeader());
            childrenNodeList.add(childrenNode);
        }
        return childrenNodeList;
    }


    /**
     * 填充根节点factorList和children Factor
     *
     * @param projectId   项目ID
     * @param code        配套代码
     * @param factorList  根节点集合
     * @param childrenMap 子节点信息
     */
    private static void getFactorAndChildrenList(String projectId, String code, List<Factor> factorList, Map<Long, List<Factor>> childrenMap) {
        SelectDataNode node = new SelectDataNode();
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor factor = cache.getRecord(projectId, code);
        if (factor != null && factor.getStatus() == 1) {//启动状态
            node.setCode(factor.getCode());
            node.setDesc(R.lang(factor.getDescription()));
            node.setGroup(factor.getHeader());
            String header = factor.getHeader();
            String root = factor.getRoot();
            //如果如果header不等root则是子项数据 则父节点是root
            if (StringUtils.isNotBlank(header) && StringUtils.isNotBlank(root) &&
                    !header.equals(root)) {
                Factor parentFactor = cache.getRecord(projectId, header);
                if (parentFactor != null && parentFactor.getStatus() == 1) {
                    //添加节点集合信息
                    factorList.add(parentFactor);
                    //添加子节点信息
                    if (childrenMap.containsKey(parentFactor.getSqlid())) {
                        childrenMap.get(parentFactor.getSqlid()).add(factor);
                    } else {
                        List<Factor> childrenList = new ArrayList<>();
                        childrenList.add(factor);
                        childrenMap.put(parentFactor.getSqlid(), childrenList);
                    }
                    return;
                }
            }
            //添加节点集合信息
            factorList.add(factor);
        }
    }


    /**
     * 条件获取
     *
     * @param projectId
     * @param condition
     * @return
     */
    public static <T, R> R getProductProp(String productType, String productCode, String projectId, Function<T, R> condition) {
        ProdType type = ProdType.getProdType(productType);
        if (type.equals(ProdType.ROOM)) {
            Roomtype roomtype = (Roomtype) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).
                    getRecord(projectId, productCode);
            if (roomtype != null) {
                return condition.apply((T) roomtype);
            }
        }
        if (type.equals(ProdType.TICKET)) {
            Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                    getRecord(projectId, productCode);
            if (ticket != null) {
                return condition.apply((T) ticket);
            }
        }
        if (type.equals(ProdType.TAOCAN)) {
            Productkit productkit = (Productkit) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT).
                    getRecord(projectId, productCode);
            if (productkit != null) {
                String groupid = productkit.getGroupid();
                Kitgroup kitgroup = (Kitgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP).getRecord(projectId, groupid);
                if (kitgroup != null) {
                    return condition.apply((T) kitgroup);
                }
            }
        }
        return null;
    }


    /**
     * 获取系统保留栏目的第一条审批内容
     *
     * @param projectId
     * @param menuid
     * @param lgetRichText
     * @return
     */
    public static String getShowNotice(String projectId, String menuid, boolean lgetRichText) {
        MenuContentCache menuContentCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        List<Menu_content> contents = menuContentCache.getGroupList(projectId, menuid);
        for (Menu_content content : contents) {
            if (content.getOstatus() == 1) {
                return lgetRichText ? content.getRichtext() : content.getContentinfo();
            }
        }
        return "";
    }

    /**
     * 获取系统保留栏目中的图片
     *
     * @param projectId
     * @param contentid
     * @return
     */
    public static String getShowPic(String projectId, String contentid) {
        MenuContentCache menuContentCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        Menu_content menuContent = menuContentCache.getRecord(projectId, contentid);
        if (menuContent != null) {
            return menuContent.getImgurl();
        }
//        List<Menu_content> contents = menuContentCache.getGroupList(projectId, menuid);
//        for (Menu_content content : contents) {
//            if (content.getOstatus() == 1) {
//                return content.getImgurl();
//            }
//        }
        return "";
    }

    /**
     * @param bookingid 主订单号
     * @param projectid 项目id
     * @param prodType  产品类型，伴手礼流程需要单独处理
     * @return 获取产品退款信息 审核订单，历史审核订单 直连订单
     */
    public static RefundInfo getProductRefundInfo(String bookingid, String projectid, Integer scene, String prodType) {
        //添加
        RefundInfo info = new RefundInfo();
        List<RefundInfo.RefundDetail> refundDetails = new ArrayList<>();
        //查询数据问题 先查退款申请列表再查询历史表
        Auditing auditing = SpringUtil.getBean(AuditingMapper.class).findTopByBookingidAndProjectidAndSceneOrderByCreatetimeDesc(bookingid, projectid, scene);
        if (auditing != null) {//退款申请表
            info.setRefund(auditing.getRefund());
            RefundInfo.RefundDetail refundDetail = new RefundInfo.RefundDetail();
            //refundDetail.setProgress("退款申请");
            //refundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.INIT.getCode());
            //refundDetail.setTime(auditing.getCreatetime());
            //refundDetails.add(refundDetail);

            if (ProdType.ITEMS.val().equals(prodType) && auditing.getLreceived()) {  //判断产品类型，如果是伴手礼类型切需要退货，根据条件添加退货物流和签收退货节点
                if (auditing.getHandletime().isAfter(SystemUtil.EMPTY_LOCALTIME)) {//已经受理
                    RefundInfo.RefundDetail refundHandle = new RefundInfo.RefundDetail();
                    refundHandle.setProgress("退款申请已受理");
                    refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.CONFIRM.getCode());
                    refundDetails.add(refundHandle);
                } else {
                    RefundInfo.RefundDetail refundHandle = new RefundInfo.RefundDetail();
                    refundHandle.setProgress("退款申请待受理");
                    refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.INIT.getCode());
                    refundHandle.setTime(auditing.getCreatetime());
                    refundDetails.add(refundHandle);
                }
                if (StringUtils.isNotBlank(auditing.getCompany())) {//客人没填写物流信息寄回货物
                    RefundInfo.RefundDetail postage = new RefundInfo.RefundDetail();
                    postage.setProgress("退货接收中");
                    postage.setProgressNo(StatusUtil.GiftBackStatusEnum.RECEIVING.getCode());
                    postage.setCompany(CustomData.getDesc(projectid, auditing.getCompany(), SystemUtil.CustomDataKey.expresscompany));
                    postage.setDeliveryno(auditing.getDeliveryno());
                    refundDetails.add(postage);
                }
                if (auditing.getReceived()) {//后台确认收到货物
                    RefundInfo.RefundDetail received = new RefundInfo.RefundDetail();
                    received.setProgress("退货已接收");
                    received.setProgressNo(StatusUtil.GiftBackStatusEnum.RECEIVED.getCode());
                    received.setTime(auditing.getReceivedtime());
                    refundDetails.add(received);
                }
                //判断审核状态是否退款成功
                if (auditing.getAstatus().equals(StatusUtil.AuditingStatus.CONFIRM)) {
                    Prepay prepay = SpringUtil.getBean(PrepayMapper.class).findPrepayByBookingid(bookingid);
                    if (prepay != null) {
                        RefundInfo.RefundDetail prepayRefundDetail = new RefundInfo.RefundDetail();
                        if (prepay.getRefundtime().isAfter(SystemUtil.EMPTY_LOCALTIME)) {
                            prepayRefundDetail.setProgress("退款完成");
                            prepayRefundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUNDED.getCode());
                            prepayRefundDetail.setTime(prepay.getRefundtime());
                        } else {
                            prepayRefundDetail.setProgress("退款中");
                            prepayRefundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUNDED.getCode());
                        }
                        refundDetails.add(prepayRefundDetail);
                    }
                } else if (auditing.getAstatus().equals(StatusUtil.AuditingStatus.REFUSE)) {
                    RefundInfo.RefundDetail refuse = new RefundInfo.RefundDetail();
                    refuse.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUSE.getCode());
                    refuse.setProgress("退款失败");
                    refundDetails.add(refuse);
                }
            } else { //其他产品退款
                if (auditing.getHandletime().isAfter(SystemUtil.EMPTY_LOCALTIME)) { //已经受理
                    RefundInfo.RefundDetail refundHandle = new RefundInfo.RefundDetail();
                    if (auditing.getAstatus().equals(StatusUtil.AuditingStatus.CONFIRM)) {
                        refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.CONFIRM.getCode());
                        refundHandle.setProgress("退款申请已受理");
                    } else if (auditing.getAstatus().equals(StatusUtil.AuditingStatus.REFUSE)) {
                        refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUSE.getCode());
                        refundHandle.setProgress("退款申请已拒绝");
                    }
                    if (auditing.getHandletime().isAfter(SystemUtil.EMPTY_LOCALTIME)) {
                        refundHandle.setTime(auditing.getHandletime());
                    }
                    refundDetails.add(refundHandle);

                    //查询预付款表
                    Prepay prepay = SpringUtil.getBean(PrepayMapper.class).findPrepayByBookingid(bookingid);
                    if (prepay != null && auditing.getAstatus().equals(StatusUtil.AuditingStatus.CONFIRM)) {
                        RefundInfo.RefundDetail prepayRefundDetail = new RefundInfo.RefundDetail();
                        if (prepay.getRefundtime().isAfter(SystemUtil.EMPTY_LOCALTIME)) {
                            prepayRefundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUNDED.getCode());
                            prepayRefundDetail.setProgress("退款完成");
                            prepayRefundDetail.setTime(prepay.getRefundtime());
                        } else {
                            prepayRefundDetail.setProgress("退款中");
                            prepayRefundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUNDED.getCode());
                        }
                        refundDetails.add(prepayRefundDetail);
                    }
                } else {
                    RefundInfo.RefundDetail refundHandle = new RefundInfo.RefundDetail();
                    refundHandle.setProgress("退款申请待受理");
                    refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.INIT.getCode());
                    refundHandle.setTime(auditing.getCreatetime());
                    refundDetails.add(refundHandle);
                }

            }


        } else { //已经过夜审在退款申请历史表
            Auditing_his auditing_his = SpringUtil.getBean(AuditingHisMapper.class).findTopByBookingidAndProjectidOrderByCreatetimeDesc(bookingid, projectid);
            if (auditing_his != null) {
                info.setRefund(auditing_his.getRefund());
                RefundInfo.RefundDetail refundDetail = new RefundInfo.RefundDetail();
                //refundDetail.setProgress("退款申请");
                //refundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.INIT.getCode());
                //refundDetail.setTime(auditing_his.getCreatetime());
                //refundDetails.add(refundDetail);


                if (ProdType.ITEMS.val().equals(prodType) && auditing_his.getLreceived()) {  //判断产品类型，如果是伴手礼类型切需要退货，根据条件添加退货物流和签收退货节点
                    if (auditing_his.getHandletime().isAfter(SystemUtil.EMPTY_LOCALTIME)) {//已经受理
                        RefundInfo.RefundDetail refundHandle = new RefundInfo.RefundDetail();
                        refundHandle.setProgress("退款申请已受理");
                        refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.CONFIRM.getCode());
                        refundDetails.add(refundHandle);
                    } else {
                        RefundInfo.RefundDetail refundHandle = new RefundInfo.RefundDetail();
                        refundHandle.setProgress("退款申请待受理");
                        refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.INIT.getCode());
                        refundHandle.setTime(auditing_his.getCreatetime());
                        refundDetails.add(refundHandle);
                    }
                    if (StringUtils.isNotBlank(auditing_his.getCompany())) {
                        RefundInfo.RefundDetail postage = new RefundInfo.RefundDetail();
                        postage.setProgress("退货接收中");
                        postage.setProgressNo(StatusUtil.GiftBackStatusEnum.RECEIVING.getCode());
                        postage.setCompany(CustomData.getDesc(projectid, auditing_his.getCompany(), SystemUtil.CustomDataKey.expresscompany));
                        postage.setDeliveryno(auditing_his.getDeliveryno());
                        refundDetails.add(postage);
                    }
                    if (auditing_his.getReceived()) {
                        RefundInfo.RefundDetail received = new RefundInfo.RefundDetail();
                        received.setProgress("退货已接收");
                        received.setProgressNo(StatusUtil.GiftBackStatusEnum.RECEIVED.getCode());
                        received.setTime(auditing_his.getReceivedtime());
                        refundDetails.add(received);
                    }
                    //判断审核状态是否退款成功
                    if (auditing_his.getAstatus().equals(StatusUtil.AuditingStatus.CONFIRM)) {
                        Prepay_his prepayHis = SpringUtil.getBean(PrepayHisMapper.class).findPrepay_hisByBookingid(bookingid);
                        if (prepayHis != null) {
                            RefundInfo.RefundDetail prepayRefundDetail = new RefundInfo.RefundDetail();
                            if (prepayHis.getRefundtime().isAfter(SystemUtil.EMPTY_LOCALTIME)) {
                                prepayRefundDetail.setProgress("退款完成");
                                prepayRefundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUNDED.getCode());
                                prepayRefundDetail.setTime(prepayHis.getRefundtime());
                            } else {
                                prepayRefundDetail.setProgress("退款中");
                                prepayRefundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUNDED.getCode());
                            }
                            refundDetails.add(prepayRefundDetail);
                        }
                    } else if (auditing_his.getAstatus().equals(StatusUtil.AuditingStatus.REFUSE)) {
                        RefundInfo.RefundDetail refuse = new RefundInfo.RefundDetail();
                        refuse.setProgress("退款失败");
                        refuse.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUSE.getCode());
                        refundDetails.add(refuse);
                    }
                } else { //其他退货情况
                    if (auditing_his.getHandletime().isAfter(SystemUtil.EMPTY_LOCALTIME)) { //已经受理
                        RefundInfo.RefundDetail refundHandle = new RefundInfo.RefundDetail();
                        if (auditing_his.getAstatus().equals(StatusUtil.AuditingStatus.CONFIRM)) {
                            refundHandle.setProgress("退款申请已受理");
                            refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.INIT.getCode());
                        } else if (auditing_his.getAstatus().equals(StatusUtil.AuditingStatus.REFUSE)) {
                            refundHandle.setProgress("退款申请已拒绝");
                            refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUSE.getCode());
                        }
                        if (auditing_his.getHandletime().isAfter(SystemUtil.EMPTY_LOCALTIME)) {
                            refundHandle.setTime(auditing_his.getHandletime());
                        }
                        refundDetails.add(refundHandle);

                        //查询预付款表
                        Prepay_his prepayHis = SpringUtil.getBean(PrepayHisMapper.class).findPrepay_hisByBookingid(bookingid);
                        if (prepayHis != null && auditing_his.getAstatus().equals(StatusUtil.AuditingStatus.CONFIRM)) {
                            RefundInfo.RefundDetail prepayRefundDetail = new RefundInfo.RefundDetail();
                            if (prepayHis.getRefundtime().isAfter(SystemUtil.EMPTY_LOCALTIME)) {
                                prepayRefundDetail.setProgress("退款完成");
                                prepayRefundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUNDED.getCode());
                                prepayRefundDetail.setTime(prepayHis.getRefundtime());
                            } else {
                                prepayRefundDetail.setProgress("退款中");
                                prepayRefundDetail.setProgressNo(StatusUtil.GiftBackStatusEnum.REFUNDED.getCode());
                            }
                            refundDetails.add(prepayRefundDetail);
                        }
                    } else {
                        RefundInfo.RefundDetail refundHandle = new RefundInfo.RefundDetail();
                        refundHandle.setProgress("退款申请待受理");
                        refundHandle.setProgressNo(StatusUtil.GiftBackStatusEnum.INIT.getCode());
                        refundHandle.setTime(auditing_his.getCreatetime());
                        refundDetails.add(refundHandle);
                    }

                }
            }
        }

        if (CollectionUtil.isNotEmpty(refundDetails)) {
            info.setDetail(refundDetails);
        }
        return info;
    }

    /**
     * @param stdOrderData
     * @return 获取账单明细
     */
    public static List<OrderKitItemItem> getProductInfo(StdOrderData stdOrderData) {
        List<OrderKitItemItem> infoList = new ArrayList<>();
        //StdOrderData stdOrderData = SpringUtil.getBean(CoreRs.class).getOrderDetail(bookingid, projectId);
        if (stdOrderData != null) {
            OrderServiceImpl orderService = SpringUtil.getBean(OrderServiceImpl.class);
            infoList = orderService.fillItemData(stdOrderData);
        }
        return infoList;

    }

    /**
     * @param ptype     产品类型
     * @param product   产品代码
     * @param projectId 项目代码
     * @return 是否含房产品
     */
    public static boolean orderHasRoomProd(String ptype, String product, String projectId) {
        boolean hasRoom = false;
        if (ProdType.getProdType(ptype).equals(ProdType.ROOM)) {//订单包含房型产品
            hasRoom = true;
        } else if (ProdType.getProdType(ptype).equals(ProdType.TAOCAN)) {//订单是套餐 查询套餐下有无房型产品
            Productkit productkit = (Productkit) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT).getRecord(projectId, product);
            if (productkit != null) {
                KititemCache kititemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITITEM);
                //套餐明细分组 /kitcode+productcode 餐饮代码可能为空则取kitcode+productgroup
                List<Kititem> kititems = kititemCache.getGroupList(projectId, productkit.getCode() + product);
                if (CollectionUtil.isNotEmpty(kititems)) {
                    for (Kititem kititem : kititems) {
                        if (kititem.getProducttype().equals(ProdType.ROOM.val())) {
                            return true;
                        }
                    }
                }
            }

        }
        return hasRoom;
    }

    public static boolean orderHasTicketProd(String ptype, String product, String projectId) {
        boolean hasTicket = false;
        if (ProdType.getProdType(ptype).equals(ProdType.TICKET)) {//订单包含房型产品
            hasTicket = true;
        } else if (ProdType.getProdType(ptype).equals(ProdType.TAOCAN)) {//订单是套餐 查询套餐下有无房型产品
            Productkit productkit = (Productkit) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT).getRecord(projectId, product);
            if (productkit != null) {
                KititemCache kititemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITITEM);
                //套餐明细分组 /kitcode+productcode 餐饮代码可能为空则取kitcode+productgroup
                List<Kititem> kititems = kititemCache.getGroupList(projectId, productkit + product);
                if (CollectionUtil.isNotEmpty(kititems)) {
                    for (Kititem kititem : kititems) {
                        if (kititem.getProducttype().equals(ProdType.TICKET.val())) {
                            return true;
                        }
                    }
                }
            }

        }
        return hasTicket;
    }

    public static String getAudioUrl(String product, String projectId) {
        Spusitem spusitem = (Spusitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM).
                getRecord(projectId, product);
        if (spusitem != null) {
            String groupid = spusitem.getGroupid();
            Spugroup spugroup = (Spugroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP).getRecord(projectId, groupid);
            if (spugroup != null && spugroup.getType().equals(SpusType.AUDIO.getVal())) {
                return spusitem.getAudiourl();
            }
        }
        return "";
    }

   /* public static Spugroup getNeedSendMsgSpu(String product, String projectId) {
        Spusitem spusitem = (Spusitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM).
                getRecord(projectId, product);
        if (spusitem != null) {
            String groupid = spusitem.getGroupid();
            Spugroup spugroup = (Spugroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP).getRecord(projectId, groupid);
            if (spugroup.getCheckmode() != 0) {
                return spugroup;
            }
        }
        return null;
    }*/

    public static Integer getAudioExpireLen(String product, String projectId) {
        Spusitem spusitem = (Spusitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM).
                getRecord(projectId, product);
        if (spusitem != null) {
            String groupid = spusitem.getGroupid();
            Spugroup spugroup = (Spugroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP).getRecord(projectId, groupid);
            if (spugroup != null && spugroup.getType().equals(SpusType.AUDIO.getVal())) {
                return spusitem.getExpireday();
            }
        }
        return 0;
    }

    public static Integer getTicketExpireLen(String product, Date useDate, String projectId) {
        Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                getRecord(projectId, product);
        if (ticket != null) {
            Ticketgroup ticketgroup = (Ticketgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP).getRecord(projectId, ticket.getGroupid());
            Date expireDate = CalculateDate.reckonDay(useDate, 5, ticket.getExpireday());
            if (ticketgroup != null && !CalculateDate.emptyDate(ticketgroup.getEnddate())) {
                expireDate = CalculateDate.minDate(expireDate, ticketgroup.getEnddate());
                return Math.max(0, CalculateDate.compareDates(expireDate, useDate).intValue());//比如暑假游泳票.设置为7-1.到8-31有效. 有效期设置为60.但是7.1号购买.有效期最大还是到8-31
            }
            return ticket.getExpireday();
        }
        return 0;
    }

    public static Integer getAudioExpireHour(String product, String projectId) {
        Integer day = getAudioExpireLen(product, projectId);
        return day == 0 || day == 1 ? 24 : 24 * day;
    }

    /**
     * @param projectid 项目ID
     * @param lmobile   平台类型是否手机端
     * @return 获取订房须知文本
     */
    public static String getCheckinNoticeStr(String projectid, boolean lmobile) {
        String str = "";
        MenuContentCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        String menuid = lmobile ? MenuReserveId.checkinnotice.name() : "checkinnote";//根据平台类型取入住须知文本
        List<Menu_content> menuContents = cache.getGroupList(projectid, MenuReserveId.checkinnotice.name());
        if (CollectionUtil.isNotEmpty(menuContents)) {
            str = menuContents.get(0).getContentinfo();//todo 细分平台类型 menuid的mtype
        }

        return str;

    }

    /**
     * @param bookingid 主订单号
     * @param projectId 项目ID
     * @return 获取套餐明细包含产品类型字符串
     */
    public static String getKitItemTypeStr(String bookingid, String projectId) {
        List<String> typeList = new ArrayList<>();
        StdOrderData stdOrderData = SpringUtil.getBean(CoreRs.class).getOrderDetail(bookingid, projectId);
        if (stdOrderData != null) {
            if (CollectionUtil.isNotEmpty(stdOrderData.getRooms())) {
                typeList.add(ProdType.ROOM.val());
            }
            if (CollectionUtil.isNotEmpty(stdOrderData.getTickets())) {
                typeList.add(ProdType.TICKET.val());
            }
            if (CollectionUtil.isNotEmpty(stdOrderData.getCaters())) {
                typeList.add(ProdType.CANYIN.val());
            }
            if (CollectionUtil.isNotEmpty(stdOrderData.getKitfixcharges())) {
                typeList.add(ProdType.KITITEM.val());
            }
            if (CollectionUtil.isNotEmpty(stdOrderData.getMeetings())) {
                typeList.add(ProdType.MEETING.val());
            }
            if (CollectionUtil.isNotEmpty(stdOrderData.getSpus())) {
                typeList.add(ProdType.ITEMS.val());
            }

        }
        return CollectionUtil.isNotEmpty(typeList) ? StringUtils.join(typeList, ",") : "";
    }

    /**
     * @param projectid 项目ID
     * @return 获取PC官网页眉信息
     */
    public static List<PageHeader> getPageHeaderInfo(String projectid) {
        List<PageHeader> list = new ArrayList<>();
        Map<String, String> map = new HashMap<>();//卡片内容map<mname,menuid>
        MenuContentCache menuContentCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        MenusCache menusCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUS);
        List<Menus> menusList = menusCache.getDataList(projectid);
        Map<String, List<Menus>> menusOneMap = menusList.stream().filter(m -> MenuPlatformTypeEnums.PC.getCode().equals(m.getMtype()) &&
                "".equals(m.getOrgmenuid())).collect(Collectors.groupingBy(Menus::getMname));//获取PC端一级目录栏目名称
        menusList = menusList.stream().filter(m -> MenuPlatformTypeEnums.PC.getCode().equals(m.getMtype()) && m.getShow()).collect(Collectors.toList());//过滤开关
        if (CollectionUtil.isNotEmpty(menusList)) {
            for (Menus item : menusList) {
                //获取PC端上级节点为页眉的数据
                if (MenuPlatformTypeEnums.PC.getCode().equals(item.getMtype()) && PCPageTypeEnum.pcheader.name().equals(item.getOrgmenuid())) {
                    //这里检查一级栏目menuid的show是否关闭
                    if (menusOneMap.size() > 0 && menusOneMap.containsKey(item.getMname())) {
                        List<Menus> menusOneData = menusOneMap.get(item.getMname());
                        if (!menusOneData.get(0).getShow()) {//如果一级栏目关闭，对应页眉内容页应该不显示
                            continue;
                        }
                    }
                    PageHeader pageHeader = new PageHeader();
                    pageHeader.setTitle(item.getMname());
                    //map添加记录对应menuid
                    map.put(item.getMname(), item.getMenuid());
                    list.add(pageHeader);
                }
            }
        }

        List<Menu_content> contents = menuContentCache.getGroupList(projectid, PCPageTypeEnum.pcheader.name());
        //检查menuid是否关闭 添加子选项卡
        checkContentMenuIdIsOpen(projectid, contents);
        if (CollectionUtil.isNotEmpty(contents)) {
            for (Menu_content item : contents) {
                for (PageHeader pageHeader : list) {
                    //已经审核的内容
                    if (item.getTitle().equals(pageHeader.getTitle()) && item.getOstatus() == 1) {
                        pageHeader.setPath(item.getClickurl());//设置点击路径
                        pageHeader.setId(item.getContentid());
                        //查找内容下有无子节点内容 查找栏目有无下级节点，
                        List<Menu_content> childContent = menuContentCache.getGroupList(projectid, map.get(item.getTitle()));
                        checkContentMenuIdIsOpen(projectid, childContent);//检查子项数据是否打开
                        if (CollectionUtil.isNotEmpty(childContent)) {
                            pageHeader.setChildren(getChildrenHeader(childContent, menuContentCache, map));
                        } else {
                            pageHeader.setChildren(new ArrayList<>());
                        }
                    }
                }
            }
        }
        return list;
    }

    /**
     * 检查menuid是否关闭
     *
     * @param projectid
     * @param contents
     */
    private static void checkContentMenuIdIsOpen(String projectid, List<Menu_content> contents) {
        //查询menuid分组数据，PC端页眉标签栏目对应的菜单栏目是否打开
        Map<String, List<Menu_content>> groupMap = contents.stream().collect(Collectors.groupingBy(Menu_content::getMenuid));
        MenusCache menusCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUS);
        List<Menus> menusList = menusCache.getDataListWithCondition(projectid, m -> MenuPlatformTypeEnums.PC.getCode().equals(m.getMtype()) &&
                !PCPageTypeEnum.pcheader.name().equals(m.getOrgmenuid()));//PC端非页眉父节点栏目
        for (Map.Entry<String, List<Menu_content>> entry : groupMap.entrySet()) {
            Menus menus = menusCache.getRecord(projectid, entry.getKey());
            if (menus != null && !menus.getShow()) { //如果页眉菜单栏目关闭
                contents.removeAll(entry.getValue());
            } else {
                for (Menu_content menuContent : entry.getValue()) {//查看对应的栏目有无关闭
                    String mname = menuContent.getTitle();
                    List<Menus> menusDataList = menusList.stream().filter(m -> m.getMname().equals(mname)).collect(Collectors.toList());
                    menusDataList.sort(Comparator.comparing(Menus::getMenugrade));//按照层级节点排序，遍历节点开关
                    if (menusDataList.size() > 0) {
                        if (menusDataList.size() > 1) {   //不同节点包含同名菜单栏目，按照栏目等级
                            for (Menus dbMenus : menusDataList) {
                                if (!dbMenus.getShow()) {
                                    contents.remove(menuContent);//页眉选项卡不显示关闭菜单栏目
                                    break;
                                }
                            }
                        } else { //只包含一个
                            Menus menusData = menusList.stream().filter(m -> m.getMname().equals(mname)).collect(Collectors.toList()).get(0);
                            if (!menusData.getShow()) { //如果同名的栏目状态关闭，则不显示
                                contents.remove(menuContent);//页眉选项卡不显示关闭菜单栏目
                            }
                        }
                    }

                }
            }
        }
    }

    /**
     * @param childContent
     * @param map
     * @return 获取子项节点数据集合
     */
    private static List<PageHeader> getChildrenHeader(List<Menu_content> childContent, MenuContentCache menuContentCache, Map<String, String> map) {
        List<PageHeader> list = new ArrayList<>();
        for (Menu_content content : childContent) {
            if (content.getOstatus() != 1) {
                continue; //过滤不审核内容
            }
            //设置子项数据填充集合
            PageHeader childPageHeader = new PageHeader();
            childPageHeader.setId(content.getContentid());
            childPageHeader.setTitle(content.getTitle());
            childPageHeader.setPath(content.getClickurl());
            List<Menu_content> childList = menuContentCache.getGroupList(content.getProjectid(), map.get(content.getTitle()));
            if (CollectionUtil.isNotEmpty(childList)) {
                childPageHeader.setChildren(getChildrenHeader(childList, menuContentCache, map));
            } else {
                childPageHeader.setChildren(new ArrayList<>());
            }
            list.add(childPageHeader);
        }
        return list;
    }

    /**
     * 网站页脚表信息转换实体
     *
     * @param webconf
     * @return PageFoot 网页页脚实体
     */
    public static PageFoot transWebConfToFootInfo(Webconf webconf) {
        PageFoot footInfo = new PageFoot();
        footInfo.setCopyright(webconf.getCopyright());
        footInfo.setTel(webconf.getTel());
        footInfo.setFriendlinks(tranWebConfJsonToList(webconf.getFriendlinks()));
        footInfo.setRecords(tranWebConfJsonToList(webconf.getRecords()));
        footInfo.setTextlinks(tranWebConfTextJsonToList(webconf.getTextlinks()));
        footInfo.setImagelinks(tranWebConfImageJsonToList(webconf.getImagelinks()));
        return footInfo;
    }

    /**
     * PageFoot 转化Webconf
     *
     * @param footInfo
     * @return Webconf 网页页脚配置表
     */
    public static Webconf transFootInfoToWebConf(PageFoot footInfo) {
        Webconf webconf = new Webconf();
        webconf.setCopyright(footInfo.getCopyright());
        webconf.setTel(footInfo.getTel());
        if (CollectionUtil.isNotEmpty(footInfo.getFriendlinks())) {
            webconf.setFriendlinks(JSON.toJSONString(footInfo.getFriendlinks()));
        }
        if (CollectionUtil.isNotEmpty(footInfo.getImagelinks())) {
            webconf.setImagelinks(JSON.toJSONString(footInfo.getImagelinks()));
        }
        if (CollectionUtil.isNotEmpty(footInfo.getRecords())) {
            webconf.setRecords(JSON.toJSONString(footInfo.getRecords()));
        }
        if (CollectionUtil.isNotEmpty(footInfo.getTextlinks())) {
            webconf.setTextlinks(JSON.toJSONString(footInfo.getTextlinks()));
        }
        return webconf;
    }

    /**
     * @param json 文本描述和地址JSON
     * @return json解析返回list数据
     */
    private static List<LinkData> tranWebConfJsonToList(String json) {
        return JSON.parseObject(json, new TypeReference<List<LinkData>>() {
        });

    }

    private static List<PageFoot.ImageLinks> tranWebConfImageJsonToList(String json) {
        return JSON.parseObject(json, new TypeReference<List<PageFoot.ImageLinks>>() {
        });

    }

    private static List<PageFoot.TextLinks> tranWebConfTextJsonToList(String json) {
        return JSON.parseObject(json, new TypeReference<List<PageFoot.TextLinks>>() {
        });

    }

    public static String getContentPicUrl(String contentid, String projectid) {
        MenuContentCache menusCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        Menu_content content = menusCache.getRecord(projectid, contentid);
        return content == null ? "" : content.getImgurl();
    }


    public static String getProductNoticeText(String productType, String productCode, String projectId) {
        ProdType type = ProdType.getProdType(productType);
        return "";
     /*   MenuContentCache menusCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        if (type.equals(ProdType.ROOM)) {
            Roomtype roomtype = (Roomtype) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).
                    getRecord(projectId, productCode);
            if (roomtype != null) {
                Hotel hotel = (Hotel) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).
                        getRecord(projectId, roomtype.getHotelcode());
                if (hotel != null && !hotel.getNoticetext().isEmpty()) {
                    return hotel.getNoticetext();
                }
            }
            Menu_content content = getMenuFirstContent(projectId, MenuReserveId.roomnote.name());
            return content != null ? content.getRichtext() : "";
        }
        if (type.equals(ProdType.TICKET)) {
            Menu_content content = getMenuFirstContent(projectId, MenuReserveId.ticketnote.name());
            String note = content != null ? content.getRichtext() : "";
            Ticket ticket = (Ticket) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).
                    getRecord(projectId, productCode);
            if (ticket != null) {
                Ticketgroup ticketgroup = (Ticketgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP).
                        getRecord(projectId, ticket.getGroupid());
                if (ticketgroup != null) {
                    if (!ticketgroup.getIntrotext().isEmpty()) {
                        note = ticketgroup.getIntrotext();
                    }
//                    ticketgroup.geto
                }
            }
            return note;
        }
        if (type.equals(ProdType.TAOCAN)) {
            Productkit productkit = (Productkit) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT).
                    getRecord(projectId, productCode);
            if (productkit != null) {
                String groupid = productkit.getGroupid();
                Kitgroup kitgroup = (Kitgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP).getRecord(projectId, groupid);
                return kitgroup != null ? kitgroup.getNoticetext() : "";
            }
        }
        if (type.equals(ProdType.WARES)) {
            Spusitem spusitem = (Spusitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM).getRecord(projectId, productCode);
            if (spusitem != null) {
                Spugroup spugroup = (Spugroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP).
                        getRecord(projectId, spusitem.getGroupid());
                return spugroup != null ? spugroup.getNoticetext() : "";
            }
        }
        if (type.equals(ProdType.PASSBY)) {
            Menu_content content = getMenuFirstContent(projectId, MenuReserveId.passnote.name());
            return content != null ? content.getRichtext() : "";
        }
        return "";*/
    }


    public static String getProductGroupNoticeText(String productType, String groupId, String projectId) {
        ProdType type = ProdType.getProdType(productType);
      /*  if (type.equals(ProdType.ROOM)) {
            Hotel hotel = (Hotel) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL).
                    getRecord(projectId, groupId);
            if (hotel != null && !hotel.getNoticetext().isEmpty()) {
                return hotel.getNoticetext();
            }
            Menu_content content = getMenuFirstContent(projectId, MenuReserveId.roomnote.name());
            return content != null ? content.getRichtext() : "";
        }
        if (type.equals(ProdType.TICKET)) {
            Menu_content content = getMenuFirstContent(projectId, MenuReserveId.ticketnote.name());
            String note = content != null ? content.getRichtext() : "";
            Ticketgroup ticketgroup = (Ticketgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP).
                    getRecord(projectId, groupId);
            if (ticketgroup != null) {
                if (!ticketgroup.getIntrotext().isEmpty()) {
                    note = ticketgroup.getIntrotext();
                }
//                    ticketgroup.geto
            }
            return note;
        }
        if (type.equals(ProdType.TAOCAN)) {
            Kitgroup kitgroup = (Kitgroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP).getRecord(projectId, groupId);
            return kitgroup != null ? kitgroup.getNoticetext() : "";
        }
        if (type.equals(ProdType.WARES)) {
            Spugroup spugroup = (Spugroup) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP).
                    getRecord(projectId, groupId);
            return spugroup != null ? spugroup.getNoticetext() : "";
        }
        if (type.equals(ProdType.PASSBY)) {
            Menu_content content = getMenuFirstContent(projectId, MenuReserveId.passnote.name());
            return content != null ? content.getRichtext() : "";
        }*/
        return "";
    }

    public static Menu_content getMenuFirstContent(String projectId, String menuid) {
        MenuContentCache menusCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        List<Menu_content> menu_contents = menusCache.getGroupList(projectId, menuid);
        for (Menu_content r : menu_contents) {
            if (r.getOstatus() == 1) {
                return r;
            }
        }
        return null;
    }

    /**
     * @param projectId
     * @param maxMgPeople 会场最大容纳人数
     * @param peopleReq   前端传的人数区间
     * @return
     */
    public static boolean getMeetingGroupPeople(String projectId, Integer maxMgPeople, String peopleReq) {
        FactorCache factorCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        List<Factor> meetingPeopleList = factorCache.getGroupList(projectId, SystemUtil.FactoryType.MEETINGPEOPLE.name());
        if (CollectionUtil.isNotEmpty(meetingPeopleList)) {
            for (Factor mgPeople : meetingPeopleList) {
                if (mgPeople.getCode().equals(peopleReq)) {
                    String peopleStr = mgPeople.getDescription();//分割字符串取出价格区间
                    Integer arrow = 0; //0-区间，1-小于或等于，2-大于或等于，
                    if (peopleStr.contains("以上") || peopleStr.contains("+")) {
                        arrow = 2;
                    } else if (peopleStr.contains("内") || peopleStr.contains("以内") || peopleStr.contains("以下")) {
                        arrow = 1;
                    }
                    List<String> peopleStrList = new ArrayList<>();
                    if (peopleStr.contains("~")) {
                        peopleStrList = Arrays.asList(peopleStr.split("~"));
                    } else if (peopleStr.contains("-")) {
                        peopleStrList = Arrays.asList(peopleStr.split("-"));
                    } else {
                        peopleStrList = Collections.singletonList(peopleStr);
                    }
                    List<Integer> list = new ArrayList<>();
                    for (String str : peopleStrList) {
                        Pattern p = Pattern.compile("[^0-9]");//过滤字符串只取数字部分获取人数
                        Matcher m = p.matcher(str);
                        Integer num = Integer.parseInt(m.replaceAll("").trim());
                        list.add(num);
                    }
                    if (list.size() > 1 && maxMgPeople >= list.get(0) && maxMgPeople <= list.get(1)) { //区间值比较
                        return true;
                    } else if (list.size() == 1) {
                        if (arrow == 1 && maxMgPeople <= list.get(0)) {
                            return true;
                        } else if (arrow == 2 && maxMgPeople >= list.get(0)) {
                            return true;
                        }
                    }
                }
            }
        } else {
            return true;
        }
        return false;
    }

    /**
     * @param projectId
     * @param bedType   床型代码，逗号分割
     * @param hotelList 酒店列表
     * @return 筛选出满足房型条件的酒店列表
     */
    public static List<Hotel> getHotelCodeByBedTye(String projectId, String bedType, List<Hotel> hotelList) {
        if (CollectionUtil.isNotEmpty(hotelList) && StringUtils.isNotBlank(bedType)) {
            List<String> bedTypeCodeList = Arrays.asList(bedType.split(","));
            RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
            List<Hotel> list = new ArrayList<>();
            for (Hotel item : hotelList) {
                List<Roomtype> roomtypeList = roomTypeCache.getGroupList(projectId, item.getCode());
                if (CollectionUtil.isNotEmpty(roomtypeList)) {
                    //查找酒店下所有的床型代码集合，如果包含则添加
                    List<String> itemBedTypeList = roomtypeList.stream().map(Roomtype::getBedenum).collect(Collectors.toList());
                    if (itemBedTypeList.containsAll(bedTypeCodeList)) {
                        list.add(item);
                    }
                }
            }
            return list;
        }
        return hotelList;
    }

    /**
     * @param num      数字字符串
     * @param timeType D-天数，H-小时数，M-分钟数
     * @return 获取可提前取消分钟数, 按type返回
     */
    public static String canAdvCancelByRulePolicy(String num, String timeType) {
        Integer n = Integer.parseInt(num);
        if ("H".equals(timeType)) {
            return (n * 60) + "";
        } else if ("D".equals(timeType)) {
            return (n * 60 * 24) + "";
        } else if ("M".equals(timeType)) {
            return n + "";
        }
        return "";
    }


    public static String getProductUnit(String productType, String productCode, String projectId) {
        String unit = "间";
        ProdType type = ProdType.getProdType(productType);
        if (type.equals(ProdType.ROOM)) {
            Roomtype roomtype = (Roomtype) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).
                    getRecord(projectId, productCode);
            if (roomtype != null) {
                if (roomtype.getBedenum().equals(BedType.BedType_12.getVal())) {
                    return "床";
                }
            }
        }
        return unit;
    }

    public static Actgroup getActgroup(Actsite actsite) {
        if (actsite != null) {
            ActgroupCache actgroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTGROUP);
            Actgroup actgroup = actgroupCache.getRecord(actsite.getProjectid(), actsite.getGroupid());
            return actgroup;
        }
        return null;
    }

    public static Actperiod getActeriod(String projectid, String periodCode) {
        ActperiodCache actperiodCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTPERIOD);
        return actperiodCache.getRecord(projectid, periodCode);
    }

    public static Actsite getActsite(String projectid, String siteCode) {
        ActsiteCache actsiteCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTSITE);
        return actsiteCache.getRecord(projectid, siteCode);
    }

    public static Boolean isFreeAct(String projectid, String siteCode) {
        Actsite actsite = getActsite(projectid, siteCode);
        Actgroup actgroup = getActgroup(actsite);
        if (actgroup != null) {
            return actgroup.getLfree();
        }
        return true;
    }

    /**
     * @param usedDate  订单使用日期
     * @param sitecode  预约场所代码
     * @param projectId 项目ID
     * @param period    时段
     * @return 获取提前取消小时数的预约订单日期
     */
    public static Date getCancelActOrderDate(Date usedDate, String sitecode, String projectId, String period) throws DefinedException {
        LocalTime now = LocalTime.now();
        Actsite actsite = getActsite(projectId, sitecode);
        if (actsite != null) {
            Actgroup actgroup = getActgroup(actsite);
            if (actgroup != null) {
                ActperiodCache actperiodCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTPERIOD);
                Actperiod actperiod = actperiodCache.getRecord(projectId, period);
                //获取时段区间
                String[] periods = actperiod.getTime().replaceAll(" ", "").split("~");
                String startPeriod = periods[0];//HH:mm
                String endPeriod = periods[1];//HH:mm
                String[] parts = startPeriod.split(":");
                int hour = Integer.parseInt(parts[0]);
                int minute = Integer.parseInt(parts[1]);
                usedDate = DateUtil.offsetHour(usedDate, hour);//使用日期对应分钟
                usedDate = DateUtil.offsetMinute(usedDate, minute);//使用日期对应分钟  对应开始时段时间
                Integer mincancel = actgroup.getMincancel();
                if (DateUtil.isSameDay(usedDate, new Date())) { //如果是当天取消 判断时段是否满足 否则直接返回可提前取消日期
                    LocalTime startTime = LocalTime.parse(startPeriod, DateTimeFormatter.ofPattern("HH:mm"));
                    LocalTime endTime = LocalTime.parse(endPeriod, DateTimeFormatter.ofPattern("HH:mm"));
                    if (!now.isBefore(startTime.minusHours(mincancel))) { //需要对应预约取消时间提前小时数
                        String advHoursMsg = "";
                        if (mincancel > 0) {
                            advHoursMsg = "必须提前" + mincancel + "小时取消";
                            throw new DefinedException(advHoursMsg, SystemUtil.SystemerrorCode.ERR015_FORMERR);
                        } 

                    } else if (now.isAfter(endTime)) {
                        throw new DefinedException("过期项目不可取消", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                    }
                }
                //直接返回可提前取消时间
                return DateUtil.offsetHour(usedDate, -(actgroup.getMincancel()));
            }
        }
        return usedDate;

    }

    /**
     * @param projectId
     * @param groups
     * @return 预约组合项目二维码大组代码转换中文，代码用逗号分割，如果为空则显示全部
     */
    public static String getActqrGroupDesc(String projectId, String groups, String type) {
        StringBuffer stringBuffer = new StringBuffer();

        if (StringUtils.isBlank(groups)) {//如果为空则关联所有项目
            Joiner joiner = Joiner.on(", ");
            if (type.equals(ProdType.TICKET.name())) {
                TicketgroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP);
                List<Ticketgroup> datas = cache.getDataList(projectId);
                return joiner.join(datas.stream().map(Ticketgroup::getDescription).collect(Collectors.toList()));
            } else if (type.equals(ProdType.ACTGROUP.name())) {
                ActgroupCache actgroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTGROUP);
                List<Actgroup> datas = actgroupCache.getDataList(projectId);
                return joiner.join(datas.stream().map(Actgroup::getDescription).collect(Collectors.toList()));
            }
            //stringBuffer.append(list.get(i).getDescription());
        } else {
            List<String> codeList = Arrays.asList(groups.split(","));
            for (int i = 0; i < codeList.size(); i++) {
                if (i > 0) {
                    stringBuffer.append(",");
                }
                if (type.equals(ProdType.TICKET.name())) {
                    stringBuffer.append(ProdFactory.getProd(ProdType.TICKET).getGroupDesc(codeList.get(i), projectId));
                } else if (type.equals(ProdType.ACTGROUP.name())) {
                    stringBuffer.append(ProdFactory.getProd(ProdType.ACTGROUP).getGroupDesc(codeList.get(i), projectId));
                }
            }
        }
        return stringBuffer.toString();

    }

    /**
     * @param projectId 项目ID
     * @param groups    景区商品代码，逗号分割
     * @param groupid   产品大组代码
     * @return 景区商品项目二维码大组代码转换中文，代码用逗号分割，如果为空则显示全部
     */
    public static String getSpuqrGroupDesc(String projectId, String groups, String groupid) {
        StringBuffer stringBuffer = new StringBuffer();
        SpugroupCache spugroupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP);
        List<Spugroup> list = spugroupCache.getGroupList(projectId, groupid);
        if (StringUtils.isBlank(groups)) {//如果为空则关联所有项目
            for (int i = 0; i < list.size(); i++) {
                if (i > 0) {
                    stringBuffer.append(",");
                }
                stringBuffer.append(list.get(i).getDescription());
            }
        } else {
            List<String> codeList = Arrays.asList(groups.split(","));
            for (int i = 0; i < codeList.size(); i++) {
                if (i > 0) {
                    stringBuffer.append(",");
                }
                Spugroup spugroup = spugroupCache.getRecord(projectId, codeList.get(i));
                if (spugroup != null) {
                    stringBuffer.append(spugroup.getDescription());
                } else {
                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("关联项目代码不存在，" + codeList.get(i)));
                }
            }
        }
        return stringBuffer.toString();

    }

    /**
     * @param address
     * @param projectId
     * @return 获取省份描述，用逗号分割
     */
    public static String getPostageProvinceDesc(String address, String projectId) {
        StringBuffer buffer = new StringBuffer();
        if (StringUtils.isNotBlank(address)) {
            List<String> provinces = Arrays.asList(address.split(","));
            for (int i = 0; i < provinces.size(); i++) {
                if (i != 0) {
                    buffer.append(",");
                }
                buffer.append(ProvinceUtil.transCodeToDesc(provinces.get(i)));
            }

        }
        return buffer.toString();
    }

    /**
     * @param product
     * @param projectId
     * @return 获取伴手礼含规格商品描述,，giftitem:specs1/giftitem:specs1:specs2m
     */
    public static String getGifRsProductDesc(String product, String projectId, Var<String> varSpecDesc) {
        StringBuffer buffer = new StringBuffer();
        if (StringUtils.isNotBlank(product)) {
            List<String> list = Arrays.asList(product.split(":"));
            GiftItemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
            Giftitem giftitem = cache.getRecord(projectId, list.get(0));
            if (giftitem != null) {
                buffer.append(giftitem.getDescription());//G000001:trr2vv:lq9do5
                if (list.size() > 2) {
                    buffer.append("(");
                    String spec1desc = ContentCacheTool.giftItemSpecsCodeTransToDesc(list.get(0), list.get(1), null, projectId);
                    buffer.append(spec1desc);
                    buffer.append(" ");
                    String spec2desc = ContentCacheTool.giftItemSpecsCodeTransToDesc(list.get(0), null, list.get(2), projectId);
                    buffer.append(spec2desc);
                    buffer.append(")");
                    if (varSpecDesc != null) {
                        varSpecDesc.setValue(spec1desc + "|" + spec2desc);
                    }
                } else if (list.size() == 2) {
                    buffer.append("(");
                    String spec1desc = ContentCacheTool.giftItemSpecsCodeTransToDesc(list.get(0), list.get(1), null, projectId);
                    buffer.append(spec1desc);
                    if (varSpecDesc != null) {
                        varSpecDesc.setValue(spec1desc);
                    }
                }
            }

        }
        return buffer.toString();
    }

    public static Giftitem getGiftItemByProductStr(String product, String projectId) {
        GiftItemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
        List<String> list = Arrays.asList(product.split(":"));
        if (list.size() > 0) {
            Giftitem giftitem = cache.getRecord(projectId, list.get(0));
            if (giftitem != null) {
                return giftitem;
            }
        }
        return null;
    }

    /**
     * @param projectId
     * @param giftitem
     * @return 获取伴手礼商品标签
     */
    public static List<String> getGiftItemTags(String projectId, Giftitem giftitem) {
        List<String> list = new ArrayList<>();
        if (giftitem != null) {
            list.add(CustomData.getDesc(projectId, giftitem.getGroupid(), SystemUtil.CustomDataKey.gift)); //添加类目名称
            if (giftitem.getPosttype() == 0) {
                list.add("包邮");
            }
        }
        return list;
    }

    /**
     * @param projectId
     * @param giftitem
     * @return 获取伴手礼商品参数名称和参数描述集合数据
     */
    public static List<GiftitemParamDetail> getGiftItemProductDetailList(String projectId, Giftitem giftitem) {
        List<GiftitemParamDetail> list = new ArrayList<>();
        if (giftitem != null) {
            Gift gift = (Gift) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT).getRecord(projectId, giftitem.getGroupid());
            List<String> paramKey = Arrays.asList(gift.getTags().split(","));
            List<String> paramValue = Arrays.asList(giftitem.getParam().split(","));
            return IntStream.range(0, paramKey.size()).mapToObj(index -> {
                GiftitemParamDetail detail = new GiftitemParamDetail(paramKey.get(index), index < paramValue.size() ? paramValue.get(index) : "");
                return detail;
            }).collect(Collectors.toList());

        }
        return list;
    }

    /**
     * @param projectId
     * @param giftitem
     * @return 伴手礼商品参数名称集合
     */
    public static List<String> getGiftItemProductDetailString(String projectId, Giftitem giftitem) {
        List<String> list = new ArrayList<>();
        if (giftitem != null) {
            Gift gift = (Gift) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT).getRecord(projectId, giftitem.getGroupid());
            if (gift != null) {
                return Arrays.asList(gift.getTags().split(","));//类目模板参数名集合
            }
        }
        return list;
    }

    /**
     * @param projectId
     * @param giftitem
     * @return 伴手礼商品规格集合
     */
    public static List<String> getgiftItemSpesList(String projectId, Giftitem giftitem) {
        List<String> list = new ArrayList<>();
        if (giftitem != null) {
            Gift gift = (Gift) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT).getRecord(projectId, giftitem.getGroupid());
            if (gift != null) {
                list.add(gift.getSpecs1());
                if (StringUtils.isNotBlank(gift.getSpecs2())) {
                    list.add(gift.getSpecs2());
                }
            }
        }
        return list;
    }

    /**
     * @param projectId
     * @param productcode
     * @return 返回伴手礼商品规格信息
     */
    public static List<AppProdSku> getGiftitemSkuInfo(String projectId, String productcode) {
        List<AppProdSku> skuList = new ArrayList<>();
        Giftitem giftitem = (Giftitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM).getRecord(projectId, productcode);
        if (giftitem != null) {
            Gift gift = (Gift) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT).getRecord(projectId, giftitem.getGroupid());
            List<String> specs1PicList = Arrays.asList(giftitem.getSpecs1pic().split(","));
            //填充规格1信息
            AppProdSku sku = new AppProdSku();
            sku.setSkuName(gift.getSpecs1());
            List<String> specs1List = Arrays.asList(giftitem.getSpecs1().split(","));
            List<String> specs1codeList = Arrays.asList(giftitem.getSpecs1code().split(","));

            List<AppProdSku.ProdSkuItem> itemList = IntStream.range(0, specs1List.size()).mapToObj(index -> {
                AppProdSku.ProdSkuItem item = new AppProdSku.ProdSkuItem(specs1List.get(index), specs1codeList.get(index));
                return item;
            }).collect(Collectors.toList());
            for (int i = 0; i < itemList.size(); i++) { //填充图片信息
                itemList.get(i).setPicurl(specs1PicList.get(i));
            }
            sku.setItems(itemList);
            skuList.add(sku);

            if (StringUtils.isNotBlank(gift.getSpecs2())) { //如果规格2不为空填充多个信息
                AppProdSku sku2 = new AppProdSku();
                sku2.setSkuName(gift.getSpecs2());
                List<String> specs2List = Arrays.asList(giftitem.getSpecs2().split(","));
                List<String> specs2codeList = Arrays.asList(giftitem.getSpecs2code().split(","));

                List<AppProdSku.ProdSkuItem> item2List = IntStream.range(0, specs2List.size()).mapToObj(index -> {
                    AppProdSku.ProdSkuItem item = new AppProdSku.ProdSkuItem(specs2List.get(index), specs2codeList.get(index));
                    return item;
                }).collect(Collectors.toList());
                sku2.setItems(item2List);
                skuList.add(sku2);
            }

        }
        return skuList;
    }

    /**
     * @param projectId
     * @param provinceCode 省份代码
     * @return 查看有自定义区域信息，否则返回系统默认运费信息
     */
    public static AppPostageDetail getPostageDetail(String projectId, String provinceCode) {
        AppPostageDetail detail = new AppPostageDetail();
        List<AppPostageInfo> list = new ArrayList<>();
        List<Postage> postageList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.POSTAGE).getDataList(projectId);
        if (CollectionUtil.isNotEmpty(postageList)) {
            if (postageList.size() > 1) {
                List<Postage> sysPostage = postageList.stream().filter(p -> p.getName().equals(SystemUtil.DEFAULTUSERID)).collect(Collectors.toList());//获取系统运费
                //添加默认运费配置
                AppPostageInfo defaultPostage = new AppPostageInfo();
                if (CollectionUtil.isNotEmpty(sysPostage)) {
                    Postage dbPostage = sysPostage.get(0);
                    String type = sysPostage.get(0).getType();
                    defaultPostage.setAmount(sysPostage.get(0).getAmount());
                    defaultPostage.setCode(dbPostage.getName());
                    if (StringUtils.isNotBlank(type)) {
                        if (type.equals("0")) {
                            defaultPostage.setDescription("不包邮");
                        } else if (type.equals("1")) {
                            defaultPostage.setDescription("实付金额大于" + dbPostage.getPprice() + "时免邮费");
                            defaultPostage.setOveramount(dbPostage.getPprice());
                        } else if (type.equals("2")) {
                            defaultPostage.setDescription("原价金额大于" + dbPostage.getOprice() + "时免邮费");
                            defaultPostage.setOveramount(dbPostage.getOprice());
                        }
                    }
                }
                detail.setDefualt(defaultPostage);
                //获取自定义地区邮费集合
                postageList = postageList.stream().filter(p -> !p.getName().equals(SystemUtil.DEFAULTUSERID)).collect(Collectors.toList());
            }
            if (StringUtils.isBlank(provinceCode)) { //所有省份
                for (Postage item : postageList) {
                    AppPostageInfo info = new AppPostageInfo();
                    info.setName(item.getName());
                    info.setCode(item.getAddress());
                    info.setAddress(ContentCacheTool.getPostageProvinceDesc(item.getAddress(), projectId));
                    info.setAmount(item.getAmount());
                    if (StringUtils.isNotBlank(item.getType())) {
                        if (item.getType().equals("0")) {
                            info.setDescription("不包邮");
                        } else if (item.getType().equals("1")) {
                            info.setDescription("实付金额大于" + item.getPprice() + "时部分地区免邮费");
                            info.setOveramount(item.getPprice());
                        } else if (item.getType().equals("2")) {
                            info.setDescription("原价金额大于" + item.getOprice() + "时部分地区免邮费");
                            info.setOveramount(item.getOprice());
                        }
                    }
                    list.add(info);
                }
            } else { //指定省份代码
                for (Postage item : postageList) {
                    String[] addressList = item.getAddress().split(",");
                    for (String address : addressList) {
                        if (address.equals(provinceCode)) { //如果配送区域包含省份代码 判断类型返回对应价格
                            AppPostageInfo info = new AppPostageInfo();
                            info.setName(item.getName());
                            info.setCode(item.getAddress());
                            info.setAddress(ContentCacheTool.getPostageProvinceDesc(item.getAddress(), projectId));
                            info.setAmount(item.getAmount());
                            if (StringUtils.isNotBlank(item.getType())) {
                                if (item.getType().equals("0")) {
                                    info.setDescription("不包邮");
                                } else if (item.getType().equals("1")) {
                                    info.setDescription("实付金额大于" + item.getPprice() + "时部分地区免邮费");
                                    info.setOveramount(item.getPprice());
                                } else if (item.getType().equals("2")) {
                                    info.setDescription("原价金额大于" + item.getOprice() + "时部分地区免邮费");
                                    info.setOveramount(item.getOprice());
                                }
                            }
                            list.add(info);
                        }
                    }
                }

            }
            detail.setPostages(list);//添加自定义区域数据
        }
        return detail;
    }

    /**
     * @param projectId
     * @param giftitem
     * @return 获取伴手礼商品规格图片
     */
    public static List<String> getgiftItemSpesPicList(String projectId, Giftitem giftitem) {
        List<String> list = new ArrayList<>();
        if (giftitem != null) {
            list.add(giftitem.getSpecs1pic());
        }
        return list;
    }

    /**
     * @param dbconf
     * @return 获取协议集合
     */
    public static List<Agreements> getSysconfigAgreements(Sysconf dbconf) {
        List<Agreements> list = new ArrayList<>();
        if (StringUtils.isNotBlank(dbconf.getUseragr())) {
            list.add(JSON.parseObject(dbconf.getUseragr(), new TypeReference<Agreements>() {
            }));
        } else {
            Agreements user = new Agreements();
            user.setAgrKey(AgreementEnum.USERAGR.getVal());
            user.setAgrName(AgreementEnum.USERAGR.getDesc());
            list.add(user);
        }
        if (StringUtils.isNotBlank(dbconf.getPrivacyagr())) {
            list.add(JSON.parseObject(dbconf.getPrivacyagr(), new TypeReference<Agreements>() {
            }));
        } else {
            Agreements privacyAgr = new Agreements();
            privacyAgr.setAgrKey(AgreementEnum.PRIVACYAGR.getVal());
            privacyAgr.setAgrName(AgreementEnum.PRIVACYAGR.getDesc());
            list.add(privacyAgr);
        }
        if (StringUtils.isNotBlank(dbconf.getTerms())) {
            list.add(JSON.parseObject(dbconf.getTerms(), new TypeReference<Agreements>() {
            }));
        } else {
            Agreements terms = new Agreements();
            terms.setAgrKey(AgreementEnum.TERMS.getVal());
            terms.setAgrName(AgreementEnum.TERMS.getDesc());
            list.add(terms);
        }
        return list;
    }

    /**
     * @return 获取景区业态banner
     */
    public static List<BusinessBanner> getSysconfigProdBanner() {
        List<BusinessBanner> list = new ArrayList<>();
        ProdType[] prodTypes = ProdType.values();
        List<String> prodList = Arrays.asList("R", "T", "Z", "W", "I", "M");
        for (ProdType prodType : prodTypes) {
            BusinessBanner node = new BusinessBanner();
            if (StringUtils.isNotBlank(prodType.getDesc()) && prodList.contains(prodType.val())) {
                node.setBusiness(prodType.getDesc());
                if (prodType.val().equals("R")) {
                    node.setBusiness("酒店预定");
                } else if (prodType.val().equals("T")) {
                    node.setBusiness("票务预定");
                } else if (prodType.val().equals("Z")) {
                    node.setBusiness("套餐预定");
                } else if (prodType.val().equals("W")) {
                    node.setBusiness("美食餐饮");
                } else if (prodType.val().equals("I")) {
                    node.setBusiness("伴手礼");
                }
                node.setType(prodType.val());
                list.add(node);
            }

        }
        return list;

    }

    /**
     * @param projectId
     * @param prodType
     * @return 查找业态banner图
     */
    public static String getProdBannerPic(String projectId, ProdType prodType) {
        String bannerUrl = "";
        SysConfCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf sysconf = cache.getOne(projectId);
        if (sysconf != null && StringUtils.isNotBlank(sysconf.getBizbanners())) {
            List<BusinessBanner> bannerList = JSON.parseObject(sysconf.getBizbanners(), new TypeReference<List<BusinessBanner>>() {
            });
            //找出匹配类型的banner图
            bannerUrl = bannerList.stream().filter(b -> prodType.val().equals(b.getType()))
                    .map(BusinessBanner::getBannerUrl).collect(Collectors.joining());
        }
        return bannerUrl;

    }

    /**
     * @param projectid 项目代码
     * @return 获取预约活动大组代码下所有的产品
     */
    public static String getActQrGroupidAllString(String projectid) {
        ActgroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTGROUP);
        List<Actgroup> list = cache.getDataList(projectid);
        if (CollectionUtil.isNotEmpty(list)) {
            String groups = list.stream().map(Actgroup::getCode).collect(Collectors.joining(","));
            return groups;
        }
        return "";
    }

    /**
     * @param projectid 项目代码
     * @param groupid   产品分组代码
     * @return 获取产品分组代码下所有的产品
     */
    public static String getSpuQrGroupidAllString(String projectid, String groupid) {
        SpugroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP);
        List<Spugroup> list = cache.getGroupList(projectid, groupid);
        if (CollectionUtil.isNotEmpty(list)) {
            String groups = list.stream().map(Spugroup::getCode).collect(Collectors.joining(","));
            return groups;
        }
        return "";
    }

    /**
     * @param giftitem  伴手礼商品代码
     * @param specs1    规格1名称
     * @param specs2    规格2名称
     * @param projectId 项目Id
     * @return 返回伴手礼商品对应规格名称代码，specs1和specs2有一个为空
     */
    public static String getGiftItemSpecsCode(String giftitem, String specs1, String specs2, String projectId) {
        GiftItemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
        Giftitem dbGiftitem = cache.getRecord(projectId, giftitem);
        if (dbGiftitem != null) {
            if (StringUtils.isNotBlank(specs1)) {
                List<String> specs1s = Arrays.asList(dbGiftitem.getSpecs1().split(",")); //规格名称
                List<String> specs1codes = Arrays.asList(dbGiftitem.getSpecs1code().split(","));//规格代码
                int index = specs1s.indexOf(specs1);
                if (index != -1) {
                    return specs1codes.get(index);
                } else {
                    return specs1;
                }
            } else if (StringUtils.isNotBlank(specs2)) {
                List<String> specs2s = Arrays.asList(dbGiftitem.getSpecs2().split(",")); //规格名称
                List<String> specs2codes = Arrays.asList(dbGiftitem.getSpecs2code().split(","));//规格代码
                int index = specs2s.indexOf(specs2);
                if (index != -1) {
                    return specs2codes.get(index);
                } else {
                    return specs2;
                }
            }
        }
        return "";
    }

    /**
     * @param specString   完整的sku 规格代码
     * @param concatSignal 规格描述分隔符
     * @param needItemDesc 是否需要商品头描述
     * @return
     */
    public static String getGiftDescFast(String specString, String concatSignal, boolean needItemDesc) {
        GiftItemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
        return cache.quickTrans(specString, concatSignal, needItemDesc);
    }

    /**
     * @param giftitem   商品代码
     * @param specs1code 规格1代码
     * @param specs2code 规格2代码
     * @param projectId  项目ID
     * @return
     */
    public static String giftItemSpecsCodeTransToDesc(String giftitem, String specs1code, String specs2code, String projectId) {
        GiftItemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
        Giftitem dbGiftitem = cache.getRecord(projectId, giftitem);
        if (dbGiftitem != null) {
            if (StringUtils.isNotBlank(specs1code)) {
                List<String> specs1s = Arrays.asList(dbGiftitem.getSpecs1().split(",")); //规格名称
                List<String> specs1codes = Arrays.asList(dbGiftitem.getSpecs1code().split(","));//规格代码
                int index = specs1codes.indexOf(specs1code);
                if (index != -1) {
                    return specs1s.get(index);
                } else {
                    return specs1code;
                }
            } else if (StringUtils.isNotBlank(specs2code)) {
                List<String> specs2s = Arrays.asList(dbGiftitem.getSpecs2().split(",")); //规格名称
                List<String> specs2codes = Arrays.asList(dbGiftitem.getSpecs2code().split(","));//规格代码
                int index = specs2codes.indexOf(specs2code);
                if (index != -1) {
                    return specs2s.get(index);
                } else {
                    return specs2code;
                }
            }
        }
        return "";
    }

    /**
     * 判断会场是否包含对应风格的会议室
     *
     * @param projectId 项目ID
     * @param mg        会场
     * @param styles    风格代码，用逗号风格
     * @return
     */
    public static boolean hasMeetingStyle(String projectId, Meetinggroup mg, String styles) {
        List<String> styleCodeList = Arrays.asList(styles.split(","));
        MeetingCache meetingCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETING);
        List<Meeting> meetingList = meetingCache.getGroupList(projectId, mg.getCode());//获取会场下会议室
        if (CollectionUtil.isNotEmpty(meetingList)) {
            List<String> meetingStyleCodes = new ArrayList<>();
            meetingList.stream().filter(m -> StringUtils.isNotBlank(m.getStyle())).map(meeting -> {
                        List<MeetingStyleJson> styleJsonList = JSONArray.parseArray(meeting.getStyle(), MeetingStyleJson.class);
                        for (MeetingStyleJson styleJson : styleJsonList) {
                            if (!meetingStyleCodes.contains(styleJson.getCode())) {
                                meetingStyleCodes.add(styleJson.getCode());//添加风格代码
                            }
                        }
                        return styleJsonList;
                    }
            ).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(meetingStyleCodes)) {
                if (meetingStyleCodes.containsAll(styleCodeList)) { //如果包含所有查询风格代码 则
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * @param itemcode  商品代码
     * @param projectId 项目ID
     * @return
     */
    public static List<String> getGiftAllSpec(String itemcode, String projectId) {
        GiftItemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
        if (itemcode.contains(":")) { //如果传过来的是包含规格代码 只取第一部分
            itemcode = Arrays.asList(itemcode.split(":")).get(0);//产品代码
        }
        Giftitem dbGiftitem = cache.getRecord(projectId, itemcode);
        if (dbGiftitem != null) {
            //List<String> specs1s = Arrays.asList(dbGiftitem.getSpecs1().split(",")); //规格名称
            //List<String> specs2s = Arrays.asList(dbGiftitem.getSpecs2().split(",")); //规格名称
            List<String> specs = new ArrayList<>();
            specs.add(itemcode);
            specs.add(dbGiftitem.getSpecs1code());
            specs.add(dbGiftitem.getSpecs2code());
            return SysFuncLibTool.fissionSpec(specs, ":");
        }
        return Lists.newArrayList();
    }

    /**
     * @param projectId 项目Id
     * @param bookingid 主订单号
     * @param lred      是否整个订单红冲
     * @return
     */
    public static List<StdInvoiceCreateRequest.InvoiceInfoDetail> transInvoiceInfoDetail(String projectId, String bookingid, boolean lred) {
        List<StdInvoiceCreateRequest.InvoiceInfoDetail> detailList = new ArrayList<>();
        //红冲商品数量为负数，商品价格为正数
        if (lred) { //整个订单红冲，查询已开票发票明细，更改数量
            List<Invoice> invoiceList = SpringUtil.getBean(InvoiceMapper.class).findAllByBookingidAndProjectidOrderByCreatetimeDesc(bookingid, projectId);
            if (CollectionUtil.isNotEmpty(invoiceList)) {
                String detailInfo = invoiceList.get(0).getMemo();//获取开票明细记录
                List<StdInvoiceCreateRequest.InvoiceInfoDetail> dbDetailInfo = JSON.parseArray(detailInfo, StdInvoiceCreateRequest.InvoiceInfoDetail.class);
                for (StdInvoiceCreateRequest.InvoiceInfoDetail item : dbDetailInfo) { //数量 总金额，税额修改为负数
                    if (item.getNum() != null && item.getNum() > 0) {
                        item.setNum(item.getNum() * -1);
                    }
                    if (item.getAmount() != null && item.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal redAmount = item.getAmount().multiply(new BigDecimal(-1));
                        item.setAmount(redAmount);
                    }
                    if (item.getTax() != null && item.getTax().compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal redTax = item.getTax().multiply(new BigDecimal(-1));
                        item.setTax(redTax);
                    }
                }
                return dbDetailInfo;//返回红冲发票明细
            } else {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("发票记录不存在"));
            }

        } else {//正常开票申请发票明细
            Conf_Invoicenode invoiceConf = InvoiceUtil.getNNInvoiceConfig(projectId);
            List<Conf_Invoicenode.TaxDetailBean> detailBeans = invoiceConf.getTaxDetail();
            //判断是否预约订单，进行预约开票
            if (bookingid.startsWith("Y")) { //判断是否预约
                Act_rs actRs = SpringUtil.getBean(ActrsMapper.class).findAct_rsByBookingid(bookingid);
                if (actRs == null) {
                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("订单记录不存在"));
                }
                //统一按旅游服务税收编码，按照后台配置餐饮
                List<Conf_Invoicenode.TaxDetailBean> itemConf = detailBeans.stream().filter(t -> ProdType.ACTGROUP.val().equals(t.getType())).collect(Collectors.toList());
                StdInvoiceCreateRequest.InvoiceInfoDetail item = new StdInvoiceCreateRequest.InvoiceInfoDetail();
                item.setType(ProdType.ACTGROUP.val());
                //todo 是否补充发票配置参数 商品简称，额外加rs.groupid中文转换
                //String sitecode = CustomData.getDesc(actRs.getProjectid(), actRs.getSitecode(), SystemUtil.CustomDataKey.actsite);//站点名称
                item.setName("*餐饮服务*餐费");

                item.setAmount(actRs.getAmount());
                item.setTaxrate(itemConf.get(0).getTaxrate());//税率
                BigDecimal tax = actRs.getAmount().multiply(new BigDecimal(item.getTaxrate())).setScale(2, RoundingMode.HALF_UP);//乘法算出税额
                item.setTax(tax);//税额
                detailList.add(item);

            } else { //正常主单开票
                StdOrderData stdOrderData = SpringUtil.getBean(CoreRs.class).getOrderDetail(bookingid, projectId);
                //todo 查询开票记录 扣除红冲金额
                int lineNo = 1;
                if (CollectionUtil.isNotEmpty(stdOrderData.getRooms())) { //一般就一笔订单
                    List<Conf_Invoicenode.TaxDetailBean> itemConf = detailBeans.stream().filter(t -> ProdType.ROOM.val().equals(t.getType())).collect(Collectors.toList());
                    for (Room_rs rs : stdOrderData.getRooms()) {
                        StdInvoiceCreateRequest.InvoiceInfoDetail item = new StdInvoiceCreateRequest.InvoiceInfoDetail();
                        item.setLineNo(lineNo);
                        lineNo = lineNo + 1;
                        item.setType(ProdType.ROOM.val());
                        String roomtype = CustomData.getDesc(rs.getProjectid(), rs.getRmtype(), SystemUtil.CustomDataKey.roomtype);
                        if (rs.getRmtype().equals(roomtype)) { //如果找不到产品名称
                            roomtype = "住宿服务";
                        }
                        item.setName("*住宿服务*" + roomtype);
                        item.setNum(rs.getAnz());//商品数量
                        BigDecimal price = rs.getAmount().divide(BigDecimal.valueOf(rs.getAnz()), 2, BigDecimal.ROUND_HALF_UP);//商品单价
                        item.setPrice(price);
                        item.setAmount(rs.getAmount());
                        item.setTaxrate(itemConf.get(0).getTaxrate());//税率
                        BigDecimal tax = rs.getAmount().multiply(new BigDecimal(item.getTaxrate())).setScale(2, RoundingMode.HALF_UP);//乘法算出税额
                        item.setTax(tax);//税额
                        detailList.add(item);
                    }
                }
                //门票订单
                if (CollectionUtil.isNotEmpty(stdOrderData.getTickets())) {
                    List<Conf_Invoicenode.TaxDetailBean> itemConf = detailBeans.stream().filter(t -> ProdType.TICKET.val().equals(t.getType())).collect(Collectors.toList());
                    for (Ticket_rs rs : stdOrderData.getTickets()) {
                        StdInvoiceCreateRequest.InvoiceInfoDetail item = new StdInvoiceCreateRequest.InvoiceInfoDetail();
                        item.setType(ProdType.TICKET.val());
                        item.setLineNo(lineNo);
                        lineNo = lineNo + 1;
                        String ticket = CustomData.getDesc(rs.getProjectid(), rs.getTcode(), SystemUtil.CustomDataKey.ticket);
                        if (rs.getTcode().equals(ticket)) { //如果找不到门票产品名称 默认门票
                            ticket = "门票";
                        }
                        item.setName("*旅游服务*" + ticket);
                        item.setNum(rs.getAnz());//商品数量
                        item.setPrice(rs.getPrice());
                        item.setAmount(rs.getAmount());
                        item.setTaxrate(itemConf.get(0).getTaxrate());//税率
                        BigDecimal tax = rs.getAmount().multiply(new BigDecimal(item.getTaxrate())).setScale(2, RoundingMode.HALF_UP);//乘法算出税额
                        item.setTax(tax);//税额
                        detailList.add(item);
                    }

                }
                //自定义套餐
                if (CollectionUtil.isNotEmpty(stdOrderData.getKitfixcharges())) {
                    for (Kitfixcharge rs : stdOrderData.getKitfixcharges()) {
                        StdInvoiceCreateRequest.InvoiceInfoDetail item = new StdInvoiceCreateRequest.InvoiceInfoDetail();
                        //KititemCache kititemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITITEM);
                        //Kititem kititem = kititemCache.getRecord(rs.getProjectid(), stdOrderData.getKitCode() + rs.getCode());
                        item.setType(ProdType.KITITEM.val());
                        item.setLineNo(lineNo);
                        lineNo = lineNo + 1;
                        item.setName("*套餐服务*套餐服务");//自定义产品取套餐明细缓存
                        item.setNum(Integer.parseInt(rs.getAnz().toString()));
                        BigDecimal price = rs.getAmount().divide(rs.getAnz(), 2, BigDecimal.ROUND_HALF_UP);
                        item.setPrice(price);
                        item.setAmount(rs.getAmount());
                        detailList.add(item);
                    }

                }
                //餐饮
                if (CollectionUtil.isNotEmpty(stdOrderData.getCaters())) {
                    for (Cater_rs rs : stdOrderData.getCaters()) {
                        StdInvoiceCreateRequest.InvoiceInfoDetail item = new StdInvoiceCreateRequest.InvoiceInfoDetail();
                        item.setType(ProdType.CANYIN.val());
                        item.setLineNo(lineNo);
                        lineNo = lineNo + 1;
                        item.setName("*餐饮服务*餐饮服务");
                        item.setAmount(rs.getAmount());
                        item.setNum(rs.getAnz().intValue());
                        //BigDecimal price = rs.getAmount().divide(rs.getAnz(), 2, BigDecimal.ROUND_HALF_UP);
                        item.setPrice(rs.getPrice());
                        detailList.add(item);
                    }

                }
                //景区商品
                if (CollectionUtil.isNotEmpty(stdOrderData.getSpus())) {
                    for (Spu_rs rs : stdOrderData.getSpus()) {
                        StdInvoiceCreateRequest.InvoiceInfoDetail item = new StdInvoiceCreateRequest.InvoiceInfoDetail();
                        item.setType(ProdType.WARES.val());
                        item.setLineNo(lineNo);
                        lineNo = lineNo + 1;
                        item.setName(CustomData.getDesc(rs.getProjectid(), rs.getCode(), SystemUtil.CustomDataKey.spusitem));
                        item.setNum(Integer.parseInt(rs.getAnz().toString()));
                        item.setPrice(rs.getPrice());
                        item.setAmount(rs.getAmount());
                        detailList.add(item);
                    }
                }
                //伴手礼
                if (CollectionUtil.isNotEmpty(stdOrderData.getGifts())) {
                    for (Gift_rs rs : stdOrderData.getGifts()) {
                        StdInvoiceCreateRequest.InvoiceInfoDetail item = new StdInvoiceCreateRequest.InvoiceInfoDetail();
                        item.setType(ProdType.ITEMS.val());
                        item.setLineNo(lineNo);
                        lineNo = lineNo + 1;
                        item.setName(ContentCacheTool.getProductDesc(ProdType.ITEMS.val(), rs.getCode(), rs.getProjectid()));
                        item.setNum(Integer.parseInt(rs.getAnz().toString()));
                        item.setPrice(rs.getPrice());
                        item.setAmount(rs.getAmount());
                        detailList.add(item);
                    }
                }
            }
        }
        return detailList;
    }

    /**
     * @param videourl
     * @param slidepics
     * @return 获取视频封面图片地址
     */
    public static String getOSSVideoPicUrl(String videourl, String slidepics) {
        OSSConfig ossConfig = SpringUtil.getBean(OSSConfig.class);
        if ("oss".equals(ossConfig.getType()) || ossConfig.getType().isEmpty()) {
            return videourl + "?x-oss-process=video/snapshot,t_1000,f_jpg,m_fast";
        } else if ("xos".equals(ossConfig.getType())) {//资源池必须支持视频截帧功能 目前不支持，传第五帧图片作为封面图片
            //String url = videourl.substring(0, videourl.lastIndexOf(".")) + "snapshot.png";//固定文件名添加后缀
            //return url;
            //return videourl + "?x-amz-process=video/snapshot,t_1000,m_fast";
            //return StringUtils.isBlank(slidepics) ? "" : Arrays.asList(slidepics.split(",")).get(0);
            return "";
        } else if ("cos".equals(ossConfig.getType())) {
            return videourl + "?x-cos-process=video/snapshot,t_1000,m_fast";
        } else {
            return videourl;
        }

    }

    /**
     * @param list
     * @param entity
     * @return 对演出排期按重新排序并合并同类项
     */
    public static List<Integer> sortPerformScheduleList(List<PerformSchedule> list, Perform_Entity entity) {
        List<PerformSchedule> sortList = new ArrayList<>();
        List<Integer> weekList = new ArrayList<>();
        Map<String, PerformSchedule> map = new HashMap<>();
        //判断list week是否逗号分割进行
        for (PerformSchedule node : list) {
            List<String> periodList = node.getPerformPeriod();
            periodList = periodList.stream().filter(StringUtils::isNotBlank).distinct() //去重
                    .sorted(Comparator.comparing(period -> period.split("~")[0])) // 按照时段开始时间排序
                    .collect(Collectors.toList());// 转换为List
            node.setPerformPeriod(periodList);
            //如果是包含逗号则需要拆分
            if (node.getWeek().contains(",")) {
                List<String> weeks = Arrays.asList(node.getWeek().split(","));
                for (String week : weeks) {
                    if (map.containsKey(week)) {
                        throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("演出排期星期数不能重复"));
                    } else {
                        map.put(week, node);
                    }
                }
                sortList.add(node);
            } else {
                if (map.containsKey(node.getWeek())) {
                    throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("演出排期星期数不能重复"));
                } else {
                    map.put(node.getWeek(), node);
                }
                sortList.add(node);
            }
        }
        //todo 是否按顺序
        //排期包含星期数
        map.forEach((week, period) -> {
            weekList.add(Integer.valueOf(week));
        });
        //新初始化进行同类项合并
        //Map<String, List<String>> mergedAndSortedSchedules = initList.stream()
        //        .collect(Collectors.groupingBy(
        //                PerformSchedule::getWeek,
        //                Collectors.mapping(
        //                        PerformSchedule::getPerformPeriod,
        //                        Collectors.collectingAndThen(
        //                                Collectors.toSet(), // 使用Set去重
        //                                set -> set.stream()
        //                                        .flatMap(Collection::stream) // 扁平化处理
        //                                        .distinct() // 去重
        //                                        .sorted(Comparator.comparing(period -> period.split("~")[0])) // 按照时段开始时间排序
        //                                        .collect(Collectors.toList()) // 转换为List
        //                        )
        //                )
        //        ));
        //重新添加list
        //mergedAndSortedSchedules.forEach((week, period) -> {
        //    PerformSchedule schedule = new PerformSchedule();
        //    schedule.setWeek(week);
        //    schedule.setPerformPeriod(period);
        //    weekList.add(Integer.valueOf(week));
        //    sortList.add(schedule);
        //});
        //重新设置
        entity.setSchedule(sortList);
        return weekList;
    }

    /**
     * 获取微票密钥
     *
     * @return
     */
    public static String getMiniTicketKey() throws DefinedException {
        VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        List<Vendorconfig> vendorconfigList = vendorConfigCache.getVendorTypeConfig(VendorType.MINI_TICKET);
        if (CollectionUtil.isNotEmpty(vendorconfigList)) {
            return vendorconfigList.get(0).getAppsecrect();
        } else {
            throw new DefinedException("微票配置不存在");
        }

    }

    /**
     * 检查产品是否是李庄福利票务大组  然后再发送短信通知后台人员
     *
     * @param projectid
     * @param product
     * @return
     */
    public static boolean checkTicketNotifyConsole(String projectid, String product) {
        TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
        Ticket ticket = ticketCache.getRecord(projectid, product);
        if (ticket != null) {
            TicketgroupCache groupCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP);
            Ticketgroup ticketgroup = groupCache.getRecord(projectid, ticket.getGroupid());
            //如果是李庄福利票务大组 发送消息 给后台用户
            if (ticketgroup != null && ("GTFL1".equals(ticketgroup.getCode()) || "GTFL2".equals(ticketgroup.getCode()))) {
                return true;
            }
        }

        return false;
    }
}
