package com.cw.arithmetic.events;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/8/1 18:28
 **/
@Data
@ApiModel(value = "订阅消息内容节点")
public class WxMsgTemplateNode {

    @ApiModelProperty(value = "参数名")
    String param;

    @ApiModelProperty(value = " 公式")
    String formula;

    @ApiModelProperty(value = "公式值")
    String value;
}
