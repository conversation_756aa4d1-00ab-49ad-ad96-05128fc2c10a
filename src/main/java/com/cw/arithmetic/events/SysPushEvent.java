package com.cw.arithmetic.events;

import com.cw.utils.enums.sms.MsgTriggerEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/8/01 16:08
 **/
@Getter
@Setter
public class SysPushEvent extends ApplicationEvent {
    private PushData data;

    public SysPushEvent(Object source, MsgTriggerEnum triggerEnum, String mobile,
                        String content, String regno, String projectid) {
        super(source);
        data = new PushData();
        data.setData(source);
        data.setEvent(triggerEnum);
        data.setMobile(mobile);
        data.setContent(content);
        data.setRegno(regno);
        data.setProjectid(projectid);
    }


}
