package com.cw.arithmetic.sku;


import com.cw.utils.enums.ProdType;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/9/15 14:35
 **/
public class ToccItem {
    private HashMap<String, Integer> roominfo;
    private HashMap<String, Integer> ticketinfo;
    private HashMap<String, Integer> taocanifno;
    private HashMap<String, Integer> spuinfo;
    private HashMap<String, Integer> giftinfo;
    private HashMap<String, Integer> actinfo;

    public HashMap<String, Integer> getRoominfo() {
        return roominfo;
    }

    public HashMap<String, Integer> getTicketinfo() {
        return ticketinfo;
    }

    public HashMap<String, Integer> getTaocanifno() {
        return taocanifno;
    }

    public HashMap<String, Integer> getSpuinfo() {
        return spuinfo;
    }

    public HashMap<String, Integer> getGiftinfo() {
        return giftinfo;
    }

    public HashMap<String, Integer> getActinfo() {
        return actinfo;
    }

    public Integer getRoomOcc(String roomtype) {
        if (roominfo == null) {
            return 0;
        }
        return roominfo.getOrDefault(roomtype, 0);
    }

    public Integer getTicketOcc(String ticketcode) {
        if (ticketinfo == null) {
            return 0;
        }
        return ticketinfo.getOrDefault(ticketcode, 0);
    }

    public Integer getTaocanOcc(String kitcode) {
        if (taocanifno == null) {
            return 0;
        }
        return taocanifno.getOrDefault(kitcode, 0);
    }

    public Integer getSpuOcc(String kitcode) {
        if (spuinfo == null) {
            return 0;
        }
        return spuinfo.getOrDefault(kitcode, 0);
    }

    public Integer getGiftOcc(String giftcode) {
        if (giftcode == null) {
            return 0;
        }
        return giftinfo.getOrDefault(giftcode, 0);
    }

    public void updOcc(ProdType prodType, String skuid, Integer num) {
        HashMap<String, Integer> occmap = null;
        if (prodType.equals(ProdType.ROOM)) {
            if (roominfo == null) {
                roominfo = new HashMap<>();
            }
            occmap = roominfo;
        }
        if (prodType.equals(ProdType.TICKET)) {
            if (ticketinfo == null) {
                ticketinfo = new HashMap<>();
            }
            occmap = ticketinfo;
        }
        if (prodType.equals(ProdType.TAOCAN)) {
            if (taocanifno == null) {
                taocanifno = new HashMap<>();
            }
            occmap = taocanifno;
        }
        if (prodType.equals(ProdType.WARES)) {
            if (spuinfo == null) {
                spuinfo = new HashMap<>();
            }
            occmap = spuinfo;
        }

        if (prodType.equals(ProdType.ITEMS)) {
            if (giftinfo == null) {
                giftinfo = new HashMap<>();
            }
            occmap = giftinfo;
        }

        if (prodType.equals(ProdType.ACTGROUP)) {
            if (actinfo == null) {
                actinfo = new HashMap<>();
            }
            occmap = actinfo;
        }

        if (occmap != null) {
            occmap.put(skuid, num);
        }
    }


}
