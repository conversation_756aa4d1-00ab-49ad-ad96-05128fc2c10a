package com.cw.arithmetic.sku;


import com.cw.utils.enums.ProdType;

import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/9/15 15:42
 **/
public class TSkuUpd {
    private ProdType prodType;
    private String skuid = "";
    private String channel = "";
    private Date startdate = null;
    private Date enddate = null;
    private Integer changeNum = 0;

    public ProdType getProdType() {
        return prodType;
    }

    public void setProdType(ProdType prodType) {
        this.prodType = prodType;
    }

    public String getSkuid() {
        return skuid;
    }

    public void setSkuid(String skuid) {
        this.skuid = skuid;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }

    public Integer getChangeNum() {
        return changeNum;
    }

    public void setChangeNum(Integer changeNum) {
        this.changeNum = changeNum;
    }
}
