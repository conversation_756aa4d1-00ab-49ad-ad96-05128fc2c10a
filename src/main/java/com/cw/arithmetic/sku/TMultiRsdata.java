package com.cw.arithmetic.sku;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.PojoUtils;
import com.cw.entity.Gift_rs;
import com.cw.entity.Room_rs;
import com.cw.entity.Spu_rs;
import com.cw.entity.Ticket_rs;
import com.cw.utils.CalculateDate;
import com.cw.utils.enums.ProdType;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 针对一个list 的订单做处理 .
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/9/15 10:31
 **/
public class TMultiRsdata {

    private boolean emp = false; //空数组 定义.  用来方便的直接加减.例如新建时. 或者取消订单时

    private String channel = "";

    private List<Room_rs> rsList = null;

    private List<Ticket_rs> ticketList = null;

    //************后续如果需要增加其他订单的缓存. 就从这里添加***********/

    private HashMap<Date, ToccItem> items; //存放每个订单的占用

    public TMultiRsdata() {
        this(true, "", null, null, null, null, "", "", null, 0);
    }

    public TMultiRsdata(boolean emp, String channel, List<Room_rs> rooms, List<Ticket_rs> tickets) {
        this(emp, channel, rooms, tickets, null, null, "", "", null, 0);
    }


    /**
     * @param emp     新建时第一次传true
     * @param channel 当emp传false时. channel传实际要扣减的渠道
     * @param rooms
     * @param tickets
     */
    public TMultiRsdata(boolean emp, String channel, List<Room_rs> rooms, List<Ticket_rs> tickets,
                        List<Spu_rs> spus, List<Gift_rs> gifts, String kitcode, String actcode, Date useDate, Integer anz) {
        items = new HashMap<Date, ToccItem>();
        if (!emp) {
            this.channel = channel;
            fillArray(rooms, tickets, spus, gifts, kitcode, actcode, useDate, anz);
        }
    }

    public HashMap<Date, ToccItem> getOccItems() {
        return items;
    }


    private void fillArray(List<Room_rs> rooms, List<Ticket_rs> tickets, List<Spu_rs> spus, List<Gift_rs> gifts, String kitcode, String actcode, Date useDate, Integer anz) {
        if (rooms != null) {
            rsList = new ArrayList<>();
            for (Room_rs rs : rooms) {
                rsList.add(PojoUtils.cloneEntity(rs));
                int count = CalculateDate.compareDates(rs.getDeptdate(), rs.getArrdate()).intValue();
                for (int i = 0; i < count; i++) {
                    Date d = CalculateDate.reckonDay(rs.getArrdate(), 5, i);
                    updDateItem(d, ProdType.ROOM, rs.getRmtype(), rs.getAnz());
                }
            }
        }
        if (tickets != null) {
            ticketList = new ArrayList<>();
            for (Ticket_rs ticket : tickets) {
                ticketList.add(PojoUtils.cloneEntity(ticket));
                updDateItem(ticket.getUsedate(), ProdType.TICKET, ticket.getTcode(), ticket.getAnz());
            }
        }
        if (spus != null) {
            for (Spu_rs spu_rs : spus) {
                updDateItem(spu_rs.getStartdate(), ProdType.WARES, spu_rs.getCode(), spu_rs.getAnz(0));
            }
        }
        if (gifts != null) {
            for (Gift_rs gift : gifts) {
                updDateItem(CalculateDate.NULLDATE, ProdType.ITEMS, gift.getCode(), gift.getAnz(0));
            }
        }
        if (StrUtil.isNotBlank(kitcode)) {
            updDateItem(useDate, ProdType.TAOCAN, kitcode, anz);
        }

    }

    private void updDateItem(Date d, ProdType productType, String code, int num) {
        items.putIfAbsent(d, new ToccItem());
        items.get(d).updOcc(productType, code, num);
    }

    public int getOcc(Date date, ProdType productType, String product) {
        if (emp) {
            return 0;
        }
        if (!(date instanceof Date)) {
            date = (Date) date;
        }
        if (!items.containsKey(date)) {
            return 0;
        }
        ToccItem toccItem = items.get(date);
        if (productType.equals(ProdType.ROOM)) {
            return toccItem.getRoomOcc(product);
        }
        if (productType.equals(ProdType.TICKET)) {
            return toccItem.getTicketOcc(product);
        }
        return 0;
    }

    public String getChannel() {
        return channel;
    }


}
