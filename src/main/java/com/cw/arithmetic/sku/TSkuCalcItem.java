package com.cw.arithmetic.sku;


import com.cw.utils.enums.ProdType;

import java.util.*;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/9/15 17:31
 **/
public class TSkuCalcItem {
    private Date date;

    //存更新前结果
    private HashMap<String, HashMap<String, Integer>> orgRoomMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> orgTicketMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> orgTaocanMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> orgSpuMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> orgActMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> orgGiftMap = new HashMap<>();
//    private HashMap<String, HashMap<String, Integer>> orgActMap = new HashMap<>();

    //存更新后的结果
    private HashMap<String, HashMap<String, Integer>> currentRoomMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> currentTicketMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> currentTaocanMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> currentSpuMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> currentActMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> currentGiftMap = new HashMap<>();
//    private HashMap<String, HashMap<String, Integer>> currentActMap = new HashMap<>();


    //存比较后的结果
    private HashMap<String, HashMap<String, Integer>> updRoomMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> updTicketMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> updTaocanMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> updSpuMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> updActMap = new HashMap<>();
    private HashMap<String, HashMap<String, Integer>> updGiftMap = new HashMap<>();
//    private HashMap<String, HashMap<String, Integer>> updActMap = new HashMap<>();


    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public void fillOrgInfo(String channel, ToccItem toccItem) {
        if (channel.isEmpty() || toccItem == null) {
            return;
        }
        orgRoomMap.put(channel, toccItem.getRoominfo()); //PUT 进去的map 有可能是Null
        orgTicketMap.put(channel, toccItem.getTicketinfo());
        orgTaocanMap.put(channel, toccItem.getTaocanifno());
        orgSpuMap.put(channel, toccItem.getSpuinfo());
        orgGiftMap.put(channel, toccItem.getGiftinfo());


        fillUpdInfo(channel, toccItem);

    }

    public void fillCurrentInfo(String channel, ToccItem toccItem) {
        if (channel.isEmpty() || toccItem == null) {
            return;
        }
        currentRoomMap.put(channel, toccItem.getRoominfo()); //PUT 进去的map 有可能是Null
        currentTicketMap.put(channel, toccItem.getTicketinfo());
        currentTaocanMap.put(channel, toccItem.getTaocanifno());
        currentSpuMap.put(channel, toccItem.getSpuinfo());
        currentGiftMap.put(channel, toccItem.getGiftinfo());

        fillUpdInfo(channel, toccItem);
    }

    /**
     * 填充.拓充更新矩阵
     *
     * @param channel
     * @param toccItem
     */
    private void fillUpdInfo(String channel, ToccItem toccItem) {
        if (channel.isEmpty() || toccItem == null) {
            return;
        }
        if (toccItem.getRoominfo() != null) {
            if (!updRoomMap.containsKey(channel)) {
                updRoomMap.put(channel, new HashMap<>());  //如果没有.先填充
            }
            updRoomMap.get(channel).putAll(toccItem.getRoominfo());
        }
        if (toccItem.getTicketinfo() != null) {
            if (!updTicketMap.containsKey(channel)) {
                updTicketMap.put(channel, new HashMap<>());
            }
            updTicketMap.get(channel).putAll(toccItem.getTicketinfo());
        }
        if (toccItem.getTaocanifno() != null) {
            if (!updTaocanMap.containsKey(channel)) {
                updTaocanMap.put(channel, new HashMap<>());
            }
            updTaocanMap.get(channel).putAll(toccItem.getTaocanifno());
        }

        if (toccItem.getSpuinfo() != null) {
            if (!updSpuMap.containsKey(channel)) {
                updSpuMap.put(channel, new HashMap<>());
            }
            updSpuMap.get(channel).putAll(toccItem.getSpuinfo());
        }
        if (toccItem.getGiftinfo() != null) {
            if (!updGiftMap.containsKey(channel)) {
                updGiftMap.put(channel, new HashMap<>());
            }
            updGiftMap.get(channel).putAll(toccItem.getGiftinfo());
        }

    }


    /**
     * 累加比较.有差异的.就存入updMap
     */
    public void sumPickupChange() {
        //要不要先把upd**Map的值都先全部设为0呢?
        ProdType[] types = new ProdType[]{ProdType.ROOM, ProdType.TICKET, ProdType.TAOCAN, ProdType.WARES, ProdType.ITEMS};
        for (ProdType type : types) {
            HashMap<String, HashMap<String, Integer>> updmap = getUpdMap(type);
            for (Map.Entry<String, HashMap<String, Integer>> entry : updmap.entrySet()) {
                String channel = entry.getKey();
                HashMap<String, Integer> valmap = entry.getValue();
                for (String skuid : entry.getValue().keySet()) {
                    int changeNum = getUpdnum(channel, skuid, type);
                    valmap.put(skuid, changeNum);
                }
                Iterator<Map.Entry<String, Integer>> it = valmap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, Integer> valentry = it.next();
                    if (valentry.getValue() == 0) {
                        it.remove();   //移除upd为0 的值. 0 的不需要做更新
                    }
                }
            }
        }
    }

    public List<String> getUpdInfo() {
        List<String> reult = new ArrayList<>();
        ProdType[] types = new ProdType[]{ProdType.ROOM, ProdType.TICKET, ProdType.TAOCAN, ProdType.WARES, ProdType.ITEMS};
        for (ProdType type : types) {
            HashMap<String, HashMap<String, Integer>> updmap = getUpdMap(type);
            for (Map.Entry<String, HashMap<String, Integer>> entry : updmap.entrySet()) {
                String channel = entry.getKey();
                HashMap<String, Integer> valmap = entry.getValue();
                for (String skuid : entry.getValue().keySet()) {
                    int changeNum = getUpdnum(channel, skuid, type);
                    reult.add(channel + "," + type + "," + skuid + "," + changeNum);
                }
            }
        }
        return reult;
    }


    private int getUpdnum(String channel, String skuid, ProdType type) {
        int orgnum = 0;
        int currnum = 0;

        HashMap<String, HashMap<String, Integer>> orgmap = getOrgMap(type);
        HashMap<String, HashMap<String, Integer>> currmap = getCurrMap(type);
        if (orgmap.containsKey(channel)) {
            orgnum = orgmap.get(channel).getOrDefault(skuid, 0);
        }
        if (currmap.containsKey(channel)) {
            currnum = currmap.get(channel).getOrDefault(skuid, 0);
        }
        return currnum - orgnum;
    }

    private HashMap<String, HashMap<String, Integer>> getOrgMap(ProdType type) {
        switch (type) {
            case ROOM:
                return orgRoomMap;
            case TICKET:
                return orgTicketMap;
            case TAOCAN:
                return orgTaocanMap;
            case WARES:
                return orgSpuMap;
            case ITEMS:
                return orgGiftMap;
            default:
                return new HashMap<String, HashMap<String, Integer>>();
        }
    }

    private HashMap<String, HashMap<String, Integer>> getCurrMap(ProdType type) {
        switch (type) {
            case ROOM:
                return currentRoomMap;
            case TICKET:
                return currentTicketMap;
            case TAOCAN:
                return currentTaocanMap;
            case WARES:
                return currentSpuMap;
            case ITEMS:
                return currentGiftMap;
            default:
                return new HashMap<String, HashMap<String, Integer>>();
        }
    }

    private HashMap<String, HashMap<String, Integer>> getUpdMap(ProdType type) {
        switch (type) {
            case ROOM:
                return updRoomMap;
            case TICKET:
                return updTicketMap;
            case TAOCAN:
                return updTaocanMap;
            case WARES:
                return updSpuMap;
            case ITEMS:
                return updGiftMap;
            default:
                return new HashMap<String, HashMap<String, Integer>>();
        }
    }


}
