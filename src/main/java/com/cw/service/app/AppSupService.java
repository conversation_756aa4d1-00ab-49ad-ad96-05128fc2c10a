package com.cw.service.app;

import com.cw.pojo.dto.app.req.AppSubscribeNotifyMsgReq;
import com.cw.pojo.dto.app.req.AppSupUrlReq;
import com.cw.pojo.dto.app.res.AppUrlRes;
import com.cw.pojo.dto.common.res.Common_response;

/**
 * 客服服务
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/4/7 16:18
 **/
public interface AppSupService {
    AppUrlRes getAppSupUrl(AppSupUrlReq req);


    Common_response createWxSubMsg(AppSubscribeNotifyMsgReq req);
}
