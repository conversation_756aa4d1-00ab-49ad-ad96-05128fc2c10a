package com.cw.service.app.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.pay.OrderSenseNotifyDataHandler;
import com.cw.arithmetic.pay.PassByPayAttachInfo;
import com.cw.config.Cwconfig;
import com.cw.core.CorePay;
import com.cw.core.SeqNoService;
import com.cw.core.orderhandler.OrderVendorSwitcher;
import com.cw.core.orderhandler.PayVendorSwitcher;
import com.cw.core.platform.wechat.wxpay.WxNotifyResult;
import com.cw.core.vendor.pay.PayVendorHandler;
import com.cw.entity.Booking_rs;
import com.cw.exception.DefinedException;
import com.cw.mapper.BookingrsMapper;
import com.cw.mapper.PrepayMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.outsys.pay.PassOrderPayParams;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdPayQueryParams;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.dto.app.req.AppOnlinePayReq;
import com.cw.pojo.dto.app.req.AppOrderPayQueryReq;
import com.cw.pojo.dto.app.req.AppPassPayReq;
import com.cw.pojo.dto.app.res.*;
import com.cw.service.app.AppPayService;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.CalculateNumber;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.AgentType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.pay.OnlinePayMethod;
import com.cw.utils.pay.PayUtil;
import com.cw.utils.pay.wx.WxPayUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion 封装所有用户支付业务相关内容
 * @Create 2021/11/4 15:56
 **/
@Slf4j
@Service
public class AppPayServiceImpl implements AppPayService {

    @Autowired
    SeqNoService seqNoService;

    @Autowired
    BookingrsMapper bookingrsMapper;

    @Autowired
    PrepayMapper prepayMapper;

    @Autowired
    CorePay corePay;

    @Autowired
    DaoLocal<?> daoLocal;

//    @Value("${cwconfig.paymode:true}")
//    Boolean payMode = true;

    @Autowired
    Cwconfig cwconfig;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    PayVendorSwitcher payVendorSwitcher;


    private String getPayExpireTime(List<Booking_rs> bookingRsList, String projectId) throws DefinedException {
        int expireMin = ContentCacheTool.getSysOrderExpireMinutue(projectId);
        long now = DateTime.now().getTime();
        List<Long> expires = bookingRsList.stream().map(r -> {
            return CalculateDate.asUtilDate(r.getCreatedate()).getTime() + expireMin * 60 * 1000;
        }).collect(Collectors.toList());
        Long minExpire = Collections.min(expires);

        if (minExpire < now) {
            throw new DefinedException("订单支付时间已经过期", ResultCode.PAYFAIL.code());
        }
        return DateUtil.format(DateTime.of(minExpire), WxPayUtil.WXDATETIMEZONEFORMAT);
    }

    private Long getPayExpireTimeStamp(List<Booking_rs> bookingRsList, String projectId) throws DefinedException {
        int expireMin = ContentCacheTool.getSysOrderExpireMinutue(projectId);
        long now = DateTime.now().getTime();
        List<Long> expires = bookingRsList.stream().map(r -> {
            return CalculateDate.asUtilDate(r.getCreatedate()).getTime() + expireMin * 60 * 1000;
        }).collect(Collectors.toList());
        Long minExpire = Collections.min(expires);

        if (minExpire < now) {
            throw new DefinedException("订单支付时间已经过期", ResultCode.PAYFAIL.code());
        }
        return minExpire;
    }

    private StdPayQueryParams createStdQueryPayParams(AppOrderPayQueryReq req, String projectId) {
        StdPayQueryParams params = new StdPayQueryParams();
        params.setProjectId(projectId);
        params.setOutTradeNo(req.getOutTradeNo());
        return params;
    }

    private StdPayParams createStdPayParams(List<String> orderids, String openid, String projectId, int onlinePayMethod, Integer payscene) throws DefinedException {
        StdPayParams stdPayParams = new StdPayParams();

        OrderSenseNotifyDataHandler<?> senseHandler = OrderVendorSwitcher.getSenseHandler(payscene, "");
        senseHandler.initOrders(orderids);

        if (senseHandler.getOrdersSize() == 0 || CalculateNumber.isZero(senseHandler.getPayOrderAmount())) {
            throw new DefinedException("订单状态差异.请退出后重新发起支付", SystemUtil.SystemerrorCode.ERR013_PAYFAIL);
        }

//        List<Booking_rs> booking_rsList = bookingrsMapper.findNeedPayBookingids(orderids);
//        if (booking_rsList.size() == 0) {
//            throw new DefinedException("订单状态差异.请退出后重新发起支付", SystemUtil.SystemerrorCode.ERR013_PAYFAIL);
//        }
        String orderDesc = senseHandler.getPayOrderDesc(); // ContentCacheTool.getPayOrderDesc(booking_rsList);
        String prepeySeqno = senseHandler.getPrepaySeqNo(seqNoService, onlinePayMethod);
//                booking_rsList.size() == 1 ?//单独订单支付时.使用订单号作为支付 ID .防止重复支付
//                seqNoService.getSequenceID_withOrderId(SystemUtil.SequenceKey.PREPAY, booking_rsList.get(0).getBookingid() + onlinePayMethod)
//                : seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);
        BigDecimal totalPay = senseHandler.getPayOrderAmount();// ContentCacheTool.getPayOrderAmount(booking_rsList);
        Long payexpireTime = senseHandler.getPayExpireTimeStamp(projectId);

        stdPayParams.setProjectId(projectId);
        stdPayParams.setOrderids(orderids);
        stdPayParams.setOutTradeNo(prepeySeqno);
        stdPayParams.setOrderDesc(orderDesc);
        stdPayParams.setTotalPay(totalPay);
        stdPayParams.setPayerId(openid);
        stdPayParams.setExpireTime(payexpireTime);//getPayExpireTimeStamp(booking_rsList, projectId)
        stdPayParams.setNotifyDomain(cwconfig.getDomain());
        stdPayParams.setPaymode(onlinePayMethod);
        stdPayParams.setPayscene(payscene);

        return stdPayParams;
    }


    /**
     * 小程序微信支付预请求
     *
     * @param req
     * @return
     */
    @Override
    public AppWxJsApiPayRes createWxJsApiOrder(AppOnlinePayReq req) throws DefinedException {
        String appid = WebAppGlobalContext.getCurrentAppid();
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();

        boolean lwxapp = WebAppGlobalContext.getCurrentAppAgentType().equals(AgentType.WXAPP);


        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(), projectid,
                lwxapp ? OnlinePayMethod.WX_JSAPI : OnlinePayMethod.WX_H5, req.getPayscene());
        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        AppWxJsApiPayRes appWxJsApiPayRes = wxpayHandler.wxJsapiPay(stdPayParams);
        return appWxJsApiPayRes;
/****
 List<Booking_rs> booking_rsList = bookingrsMapper.findNeedPayBookingids(req.getOrderid());
 if (booking_rsList.size() == 0) {
 throw new DefinedException("订单状态差异.请退出后重新发起支付", SystemUtil.SystemerrorCode.ERR013_PAYFAIL);
 }

 String orderDesc = ContentCacheTool.getPayOrderDesc(booking_rsList);
 String prepeySeqno = booking_rsList.size() == 1 ?//单独订单支付时.使用订单号作为支付 ID .防止重复支付
 seqNoService.getSequenceID_withOrderId(SystemUtil.SequenceKey.PREPAY, booking_rsList.get(0).getBookingid())
 : seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);
 BigDecimal totalPay = ContentCacheTool.getPayOrderAmount(booking_rsList);

 //TODO 第一步:找出所有要支付的订单.计算出要支付的总金额.产生一个支付请求

 WxPayService wxPayService = WxPayConfiguration.getProjectPayService(projectid);
 WxPayUnifiedOrderV3Request v3PayRequest = new WxPayUnifiedOrderV3Request();
 v3PayRequest.setAppid(appid);//APPid
 v3PayRequest.setMchid(wxGlobalProperties.getWxPayConfig(appid).getMchId());//直连商户号
 WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
 amount.setTotal(PayUtil.Yuan2Fen(totalPay.doubleValue()));//支付金额.对应分
 v3PayRequest.setAmount(amount);

 WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
 payer.setOpenid(req.getOpenid());  //支付用户
 v3PayRequest.setPayer(payer);

 PayAttachInfo payAttachInfo = new PayAttachInfo();
 payAttachInfo.setBookingids(req.getOrderid()); //要批量创建预付款的订单号
 payAttachInfo.setProejectid(projectid);
 payAttachInfo.setTotalPay(totalPay);
 //        payAttachInfo.setUid(WebAppGlobalContext.getCurrentAppUserId());
 payAttachInfo.setOutTradeNo(prepeySeqno);

 RMapCache<String, PayAttachInfo> attachInfoRMapCache = redissonClient.getMapCache(RedisKey.PAY_ATTACHINFO);
 String rskey = payAttachInfo.getOutTradeNo();
 attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);
 //        colrscache.put(rskey, col_rs, CalculateDate.compareDates(col_rs.getEnddate(), CalculateDate.getSystemDate()) + 1,
 //                TimeUnit.DAYS);//缓存1个月. 1个月内的查询.直接缓存命中.返回结果


 String time = getPayExpireTime(booking_rsList, projectid);

 log.info(" 当前订单支付截止时间:{}", time);

 v3PayRequest.setTimeExpire(time);//TODO 交易结束时间 根据订单创建时间来反算
 //        v3PayRequest.setAttach(JSON.toJSONString(payAttachInfo));//自定义附加数据.会在查询以及通知回调中返回 可以返回要关联支付的订单号
 v3PayRequest.setDescription(StrUtil.sub(orderDesc, 0, 36));//购买支付的商品描述 微信最多128位
 v3PayRequest.setOutTradeNo(prepeySeqno);//TODO 这种模式不适合多个订单合并支付

 v3PayRequest.setNotifyUrl(cwconfig.getDomain() + "/pay/notify/jsapi/order/" + appid); //支付成功回调地址

 log.info("小程序发起支付请求:{}", JSON.toJSONString(v3PayRequest));

 WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = null;
 AppWxJsApiPayRes res = new AppWxJsApiPayRes();
 try {
 jsapiResult = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, v3PayRequest);
 } catch (WxPayException e) {
 e.printStackTrace();
 log.error("{}  请求支付错误  流水号:{}", booking_rsList.size() == 1 ? "单订单" : "购物车提交", prepeySeqno);
 throw new DefinedException(e.getMessage(), SystemUtil.SystemerrorCode.ERR013_PAYFAIL);
 }
 if (jsapiResult != null) {
 log.info("支付获取到微信返回:{}", jsapiResult);
 BeanUtil.copyProperties(jsapiResult, res);
 }
 return res;

 */

    }

    @Override
    public AppQrcodePayRes createWxNativeOrder(AppOnlinePayReq req) throws DefinedException {
        String appid = WebAppGlobalContext.getCurrentAppid();
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();

        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(),
                projectid, OnlinePayMethod.WX_NATIVE, req.getPayscene());
        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        AppQrcodePayRes appQrcodePayRes = wxpayHandler.wxNativeQrcodePay(stdPayParams);
        return appQrcodePayRes;

       /* List<Booking_rs> booking_rsList = bookingrsMapper.findNeedPayBookingids(req.getOrderid());
        if (booking_rsList.size() == 0) {
            throw new DefinedException("订单状态差异.请退出后重新发起支付", SystemUtil.SystemerrorCode.ERR013_PAYFAIL);
        }

        String orderDesc = ContentCacheTool.getPayOrderDesc(booking_rsList);
        String prepeySeqno = booking_rsList.size() == 1 ?//单独订单支付时.使用订单号作为支付 ID .防止重复支付
                seqNoService.getSequenceID_withOrderId(SystemUtil.SequenceKey.PREPAY, booking_rsList.get(0).getBookingid())
                : seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);
        BigDecimal totalPay = ContentCacheTool.getPayOrderAmount(booking_rsList);


        //TODO 第一步:找出所有要支付的订单.计算出要支付的总金额.产生一个支付请求
        WxPayService wxPayService = WxPayConfiguration.getProjectPayService(projectid);
        WxPayUnifiedOrderV3Request v3PayRequest = new WxPayUnifiedOrderV3Request();
        v3PayRequest.setAppid(appid);//APPid
        v3PayRequest.setMchid(wxGlobalProperties.getWxPayConfig(appid).getMchId());//直连商户号
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(PayUtil.Yuan2Fen(totalPay.doubleValue()));//支付金额.对应分
        v3PayRequest.setAmount(amount);

        PayAttachInfo payAttachInfo = new PayAttachInfo();
        payAttachInfo.setBookingids(req.getOrderid()); //要批量创建预付款的订单号
        payAttachInfo.setProejectid(projectid);
        payAttachInfo.setTotalPay(totalPay);
        payAttachInfo.setOutTradeNo(prepeySeqno);

        RMapCache<String, PayAttachInfo> attachInfoRMapCache = redissonClient.getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = payAttachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);

//        v3PayRequest.setAttach(JSON.toJSONString(payAttachInfo));//自定义附加数据.会在查询以及通知回调中返回 可以返回要关联支付的订单号
        String time = getPayExpireTime(booking_rsList, projectid);
        v3PayRequest.setTimeExpire(time);
        v3PayRequest.setDescription(StrUtil.sub(orderDesc, 0, 36));//购买支付的商品描述
        v3PayRequest.setOutTradeNo(prepeySeqno);//TODO 改成一个随机的流水号.同一个订单.重复调用会失败
        v3PayRequest.setNotifyUrl(cwconfig.getDomain() + "/pay/notify/jsapi/order/" + appid); //支付成功回调地址

        log.info("PC 端发起扫码支付请求:{}", JSON.toJSONString(v3PayRequest));
        WxPayUnifiedOrderV3Result wxPayResult = null;
        AppQrcodePayRes res = new AppQrcodePayRes();
        try {
            String s = wxPayService.createOrderV3(TradeTypeEnum.NATIVE, v3PayRequest);
            res.setQrcode(s);
        } catch (WxPayException e) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("支付发起异常"));
        }
        return res;*/
    }

    @Override
    public AppH5payRes createWxH5Order(AppOnlinePayReq req) throws DefinedException {
        String projectid = WebAppGlobalContext.getCurrentAppProjectId();


        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(), projectid, OnlinePayMethod.WX_H5, req.getPayscene());
        stdPayParams.setReturn_url(req.getReturn_url());//SysFuncLibTool.getWapPayReturnUrl(projectid)  2022.10.19 H5支付修改.以前端提交的为准

        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        AppH5payRes h5payRes = wxpayHandler.wxH5Pay(stdPayParams);
        return h5payRes;
    }


    /**
     * @param appid
     * @param body
     * @return
     */
    @Override
    public WxNotifyResult handleWxJsApiOrderPayNotify(String appid, String body, HttpServletRequest request) {
        boolean newmode = true;

        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        WxNotifyResult wxNotifyResult = wxpayHandler.payCallBack(appid, body, request);
        return wxNotifyResult;
       /* VendorConfigCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig vendorconfig = cache.getVendorConfigByAppId(appid);
        WxPayService wxPayService = WxPayConfiguration.getProjectPayService(vendorconfig.getProjectid());
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {//解密 确认是否为支付成功
            WxPayOrderNotifyV3Result v3Result = wxPayService.parseOrderNotifyV3Result(body, signatureHeader);
            log.info("解析微信支付通知成功:" + v3Result);
//            String transactionId = v3Result.getResult().getTransactionId();

            //给每一笔订单创建支付信息.并且产生下发消息

            corePay.createPrepayWithNotify(OnlinePayType.WX, v3Result.getResult().getOutTradeNo(), v3Result.getResult().getTransactionId());

        } catch (WxPayException e) {
            log.error(e.getMessage());
//            e.printStackTrace();
            return WxNotifyResult.fail("验签失败");
        }
        return WxNotifyResult.ok();*/
    }


    @Override
    public WxNotifyResult handleWxJsApiRefundNotify(String appid, String body, HttpServletRequest request) {
        boolean newmode = true;
        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        WxNotifyResult wxNotifyResult = wxpayHandler.refundCallBack(appid, body, request);
        return wxNotifyResult;

     /*   VendorConfigCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig vendorconfig = cache.getVendorConfigByAppId(appid);
        WxPayService wxPayService = WxPayConfiguration.getProjectPayService(vendorconfig.getProjectid());
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {
            WxPayRefundNotifyV3Result v3Result = wxPayService.parseRefundNotifyV3Result(body, signatureHeader);
            log.info("预付款单号{} 的退款成功通知已收到.收到微信退款成功通知: " + v3Result, v3Result.getResult().getOutTradeNo());

            corePay.writeRefundInfoWithNotify(OnlinePayType.WX, v3Result.getResult().getOutRefundNo());
            *//*RMapCache<String, RefundAttachInfo> attachInfoRMapCache = redissonClient.getMapCache(RedisKey.REFUND_ATTACHINFO);

            RefundAttachInfo attachInfo = attachInfoRMapCache.get(v3Result.getResult().getOutRefundNo());
            if (attachInfo != null) {
                attachInfoRMapCache.remove(v3Result.getResult().getOutRefundNo());
                if (!attachInfo.isLduplicate()) {
                    Prepay prepay = prepayMapper.findByBookingid(attachInfo.getBookingid(), attachInfo.getProejectid());
                    prepay.setRefundtime(LocalDateTime.now());
                    prepay.setRefund(attachInfo.getRefundAmount());
                    prepayMapper.save(prepay);
                    daoLocal.batchOption("update Booking_rs set refundtime=?1 where bookingid=?2 and projectid=?3", LocalDateTime.now(),
                            prepay.getBookingid(), prepay.getProjectid()); //更新订单的退款时间

                    log.info("收到微信退款通知 并更新退款时间");
                } else {
                    log.info("收到微信退款通知 {} 处理订单多余支付退款成功", attachInfo.getBookingid());
                }

            }*//*
        } catch (WxPayException e) {
            log.error(e.getMessage());
//            e.printStackTrace();
            return WxNotifyResult.fail("验签失败");
        }
        return WxNotifyResult.ok();*/
    }

    @Override
    public WxNotifyResult handleWxPassByOrderPayNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        WxNotifyResult wxNotifyResult = wxpayHandler.payAnonymousCallBack(appid, body, request);
        return wxNotifyResult;
    }

    @Override
    public WxNotifyResult handleWxPassByRefundNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler wxpayHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
        WxNotifyResult wxNotifyResult = wxpayHandler.refundAnonymousCallBack(appid, body, request);
        return wxNotifyResult;
    }

    @Override
    public String handleAliPayRefundNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler alipayHandler = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        String str = alipayHandler.refundCallBack(appid, body, request);
        return str;
    }

    @Override
    public String handleAliPassByRefundNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler alipayHandler = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        return alipayHandler.refundAnonymousCallBack(appid, body, request);
    }

    /**
     * @param req
     * @return
     * @throws DefinedException
     */
    @Override
    public AppQueryPayRes queryOrderPayStatus(AppOrderPayQueryReq req) throws DefinedException {
        AppQueryPayRes result = new AppQueryPayRes();
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();

        //if (!SysFuncLibTool.isVailidReq(req.getOutTradeNo(), req.getSign(), req.getTimestamp(), projectId)) {
        //    throw new DefinedException("请求参数非法", SystemUtil.SystemerrorCode.ERR015_FORMERR);
        //}
        StdPayQueryParams payQueryParams = createStdQueryPayParams(req, projectId);
        if (5 == req.getPayMode() || 3 == req.getPayMode()) {//支付宝
            PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
            result = aliPayVendor.queryPay(payQueryParams);
        } else {
            PayVendorHandler wxPayVendor = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
            result = wxPayVendor.queryPay(payQueryParams);
        }
        if (req.getPayscene() == PayUtil.PASSBY_SCENE) {//如果已经支付成功.返回具体的支付信息
            //根据外部交易单号.反查支付信息
            fillAppQueryPayResOrderInfo(result, projectId, req.getOutTradeNo(), req.getPayscene());

        }
        return result;
    }

    private void fillAppQueryPayResOrderInfo(AppQueryPayRes result, String projectId, String outTradeNo, Integer payscene) {
        if (payscene == PayUtil.PASSBY_SCENE) {
            //Pass_rs rs=daoLocal.getObject("from Pass_rs where projectid=?1 and payno=?2",outTradeNo);
            RMapCache<String, PassByPayAttachInfo> attachInfoRMapCache = redissonClient.getMapCache(RedisKey.PASSBY_PAY_ATTACHINFO);
            PassByPayAttachInfo attachInfo = attachInfoRMapCache.get(outTradeNo);

            if (attachInfo != null) {
                result.setPaymethod(attachInfo.getPayment());//支付方式
                result.setOrderid(attachInfo.getOutid());//线下支付单号
                result.setGuestname(attachInfo.getGuestname());//客人姓名
                result.setMobileno(attachInfo.getTel());//留下的电话号码
                result.setTotalAmount(SysFuncLibTool.getShowPrice(attachInfo.getAmount()));
                result.setCreatepaytime(attachInfo.getCreatedate());
            }
        }
        if (result.getPayStatus() == 0) {
            log.info("查询到未支付订单信息内容:{}", JSON.toJSONString(result));
        }
    }


    @Override
    public String createAliQrcodePayOrder(AppOnlinePayReq req) throws DefinedException {
        PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(),
                WebAppGlobalContext.getCurrentAppProjectId(), OnlinePayMethod.ALI_QRCODE, req.getPayscene());
        String qrcode = aliPayVendor.aliQrcodePay(stdPayParams);
        return qrcode;
    }

    @Override
    public String createAliH5PayOrder(AppOnlinePayReq req) throws DefinedException {
        PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(),
                WebAppGlobalContext.getCurrentAppProjectId(), OnlinePayMethod.ALI_WAPPAY, req.getPayscene());
        stdPayParams.setReturn_url(req.getReturn_url());
        String payform = aliPayVendor.aliWapPay(stdPayParams);
        return payform;
    }

    @Override
    public String createUnionPayOrder(AppOnlinePayReq req) throws DefinedException {
        PayVendorHandler unionPayVendor = payVendorSwitcher.getVendorHandler(VendorType.YL_PAY);
        StdPayParams stdPayParams = createStdPayParams(req.getOrderid(), req.getOpenid(),
                WebAppGlobalContext.getCurrentAppProjectId(), OnlinePayMethod.UNION_QR, req.getPayscene());
        return unionPayVendor.unionQrcodePay(stdPayParams);
    }

    @Override
    public String handlerAliPayNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        return aliPayVendor.payCallBack(appid, body, request);
    }

    @Override
    public String handlerPassbyAliPayNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
        return aliPayVendor.payAnonymousCallBack(appid, body, request);
    }

    @Override
    public String handlerUnionPayNotify(String appid, String body, HttpServletRequest request) {
        PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.YL_PAY);
        return aliPayVendor.payCallBack(appid, body, request);
    }


    @Override
    public AppCommonPayRes createPassOrderPay(AppPassPayReq req) throws DefinedException {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        req.setProjectId(projectId);
        PassOrderPayParams payParams = createPassOrderPayParams(req);
        AppCommonPayRes res = null;
        if (req.getPayMode() == OnlinePayMethod.WX_JSAPI || req.getPayMode() == OnlinePayMethod.WX_H5) {
            PayVendorHandler wxPayVendor = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
            res = wxPayVendor.anonymousPay(payParams);
        }
        if (req.getPayMode() == OnlinePayMethod.ALI_QRCODE || req.getPayMode() == OnlinePayMethod.ALI_WAPPAY) {
            PayVendorHandler aliPayVendor = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
            res = aliPayVendor.anonymousPay(payParams);
        }

        res.setOuttradeno(payParams.getOutTradeNo());
        return res;
    }


    private PassOrderPayParams createPassOrderPayParams(AppPassPayReq payReq) throws DefinedException {
        PassOrderPayParams payParams = new PassOrderPayParams();
        payParams.setProjectId(payReq.getProjectId());
        payParams.setPaymode(payReq.getPayMode());

        String orderid = seqNoService.getSequenceID(SystemUtil.SequenceKey.SUBORDER);
        String payid = seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);

        payParams.setBookingid(orderid);
        payParams.setOutTradeNo(payid);
        Long exlong = CalculateDate.asUtilDate(LocalDateTime.now()).getTime() + 15 * 60 * 1000;//暂定15分钟内需要支付
        payParams.setExpireTime(exlong);

        payParams.setOutid(payReq.getOutid());//短信中的订单号
        payParams.setMemo(payReq.getMemo());
        payParams.setPtype("");
        payParams.setOrderDesc("景区订单支付");
        payParams.setPayerId(payReq.getOpenid());
        payParams.setTel(payReq.getTel());
        payParams.setGuestname(payReq.getGuestname());
        payParams.setCreatedate(LocalDateTime.now());
        payParams.setAmount(payReq.getAmount());

        payParams.setOnlinePayMethod(payReq.getPayMode());

        payParams.setReturn_url(payReq.getReturn_url());
        payParams.setNotifyDomain(cwconfig.getDomain());
        return payParams;
    }


}
