package com.cw.service.app.impl;

import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.arithmetic.func.prodfactory.impl.BaseProdInfo;
import com.cw.arithmetic.func.prodfactory.impl.ProdTicketInfo;
import com.cw.core.CorePrice;
import com.cw.entity.Ticket;
import com.cw.entity.Ticketgroup;
import com.cw.pojo.dto.app.req.AppProdInfoReq;
import com.cw.pojo.dto.app.res.AppProdInfoRes;
import com.cw.service.app.AppResource2Service;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/7/12 09:57
 **/
@Service
public class AppReource2Service2Impl implements AppResource2Service {

    @Override
    public AppProdInfoRes getProdInfo(AppProdInfoReq req) {
        AppProdInfoRes res = new AppProdInfoRes();
        CorePrice corePrice = SpringUtil.getBean(CorePrice.class);
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        ProdType prodType = ProdType.getProdType(req.getPtype());
        BaseProdInfo prodInfo = ProdFactory.getProd(prodType);
        res.setTitle(prodInfo.getProdGroupDesc(req.getProductcode(), projectId));
        res.setSubtitle(prodInfo.getProductDesc(req.getProductcode(), projectId));
        res.setImg(prodInfo.getProductShowPic(req.getProductcode(), projectId, true));

        Date calcDate = CalculateDate.returnDate_ZeroTime(new Date());
        res.setStartdate(calcDate);
        res.setEnddate(CalculateDate.reckonDay(calcDate, 5, 30));
        if (ProdType.TICKET.equals(prodType)) {
            ProdTicketInfo prodTicketInfo = (ProdTicketInfo) prodInfo;
            Ticket ticket = prodTicketInfo.getProdRecord(req.getProductcode(), projectId, "");
            if (ticket != null) {
                res.setLoneToMany(ticket.getLo2m());
                res.setLneedIdcard(ticket.getLreal());
            }
            Ticketgroup ticketgroup = prodTicketInfo.getProdGroup(req.getProductcode(), projectId, "");
            ;
            if (ticketgroup != null && !CalculateDate.emptyDate(ticketgroup.getStartdate())) {
                calcDate = CalculateDate.maxDate(new Date(), ticketgroup.getStartdate());
                res.setStartdate(calcDate);
                res.setEnddate(CalculateDate.maxDate(new Date(), ticketgroup.getEnddate()));
            }
        }
     /*   if(ProdType.WARES.equals(prodType)){
            ProdWareInfo prodWareInfo=(ProdWareInfo) prodInfo;
            Spugroup spugroup=prodWareInfo.getProdGroup(req.getProductcode(),projectId,"");
            if(spugroup.getType().equals(SpusType.AUDIO.getVal())){
                res.setLshownumber(false);
            }
            res.setLshowday(false);
        }*/

        BigDecimal price = corePrice.getShowPrice(prodType, CalculateDate.returnDate_ZeroTime(calcDate), req.getProductcode(), projectId);
        res.setPrice(SysFuncLibTool.getShowPrice(price));

        return res;
    }
}
