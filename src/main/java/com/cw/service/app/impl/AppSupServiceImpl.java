package com.cw.service.app.impl;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedissonTool;
import com.cw.cache.customs.WxTemplateDataHandler;
import com.cw.cache.impl.PerFormCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.core.orderhandler.SupportHandler;
import com.cw.core.vendor.support.SupVendorHandler;
import com.cw.entity.Perform;
import com.cw.entity.Sub_usermsg;
import com.cw.entity.Wxtemplate;
import com.cw.mapper.SubusermsgMapper;
import com.cw.outsys.stdop.request.StdSupCallInRequest;
import com.cw.pojo.dto.app.req.AppSubscribeNotifyMsgReq;
import com.cw.pojo.dto.app.req.AppSupUrlReq;
import com.cw.pojo.dto.app.res.AppUrlRes;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.service.app.AppSupService;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.service.mq.DelayQueueService;
import com.cw.service.mq.msgmodel.bussiness.AppNotifyMsg;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.AgentType;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/4/7 16:19
 **/
@Slf4j
@Service
public class AppSupServiceImpl implements AppSupService {

    @Autowired
    SupportHandler supportHandler;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    DelayQueueService delayQueueService;

    @Autowired
    SubusermsgMapper subusermsgMapper;

    private final String userSubScribeMapKey = "USERSUBSCRIBEMSGMAP";

    @Override
    public AppUrlRes getAppSupUrl(AppSupUrlReq req) {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        boolean lmobile = AgentType.isMobile(WebAppGlobalContext.getCurrentAppAgentType());

        SupVendorHandler handler = supportHandler.getVendorHandler(projectId);
        StdSupCallInRequest request = new StdSupCallInRequest();
        request.setName(req.getName());//客户姓名
        request.setPhoneNum(req.getPhoneNum());//电话号码
        request.setAvatarUrl(req.getAvatarUrl());//头像链接
        request.setLmobile(lmobile);
        request.setProjectId(projectId);

        AppUrlRes res = new AppUrlRes();
        if (handler != null) {
            String url = handler.getCallInUrl(request);
            res.setUrl(url);
        }
        return res;
    }

    @Override
    public Common_response createWxSubMsg(AppSubscribeNotifyMsgReq req) {

        String notifyTime = req.getNotifyTime();

        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        MsgTriggerEnum trigger = EnumUtil.fromString(MsgTriggerEnum.class, req.getTriggerType(), MsgTriggerEnum.VALIDCODE);

        WxTemplateDataHandler handler = (WxTemplateDataHandler) CustomData.getFactory().getHandler(SystemUtil.CustomDataKey.wxtemplate);
        Wxtemplate template = handler.getTemplate("", trigger, projectId);

        if (template == null || !template.getStatus()) {
            return new Common_response();
        }

        SysConfCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        long advanceMinute = cache.getConfYaml(projectId).getSys().getShow_alerttime();

        if (!notifyTime.isEmpty()) {//不为空的判断是否为当前时间之后.
            RedissonClient redissonClient = RedissonTool.getInstance();
            String userSubScribeKey = req.getOpenid() + req.getTemplateId() + req.getKeyId() + req.getNotifyTime();

            if (redissonClient.getMapCache(userSubScribeMapKey).containsKey(userSubScribeKey)) {
                log.info("该节目已经订阅.请勿重复来搞");
                return new Common_response();
            }
            //解析想要订阅的时间
            LocalTime showTime = LocalTime.parse(notifyTime);
            LocalTime postTime = LocalTime.now();

            long minutes2Show = postTime.until(showTime, ChronoUnit.MINUTES);
            long second2Show = postTime.until(showTime, ChronoUnit.SECONDS);

            LocalTime pushTime = null;
            if (second2Show < 10) { //距离演出时间还有10秒.或者post 的时候刚好到点.就不推送了
                return new Common_response();
            } else if (minutes2Show > advanceMinute) {// 演出时间
                pushTime = showTime.minusMinutes(advanceMinute);//减5分钟
            } else {   //演出前5分钟的订阅.就是下一分钟的整点开始推.例如12:00的演出. 11:57分点击订阅.11:58分推送
                pushTime = showTime.minusMinutes(second2Show / 60);
            }
            long sec = Duration.between(LocalTime.now(), pushTime).getSeconds();

            AppNotifyMsg msg = transNotifyMsg(req, template, trigger, projectId);

            //String delaySignal = MqNameUtils.getDelaySignal(MqNameUtils.DirectExchange.MALL, MqNameUtils.BussinessTask.APPNOTIFY.name());
            //ExpireMessagePostProcessor expire = new ExpireMessagePostProcessor(sec, TimeUnit.SECONDS);//延迟个5秒.防止跟关闭交易时间有冲突
            //rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.DELAY.name(), delaySignal,
            //        JSON.toJSONString(msg), expire);

            //rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.DELAY.name(), delaySignal,
            //        JSON.toJSONString(msg),message -> {
            //            message.getMessageProperties().setExpiration(String.valueOf(TimeUnit.SECONDS.toMillis(sec)));
            //            return message;
            //        });

            delayQueueService.addDelayNotify(msg, sec, TimeUnit.SECONDS);//2024.09.04 修改为延迟队列 redisson 实现. 死信队列第一个消息如果延迟比较长.会造成消息堆积

            //log.info("节目{} 的订阅通知.还有 {} 秒发送 发送时间 {}", notifyTime, sec,pushTime);

            redissonClient.getMapCache(userSubScribeMapKey).put(userSubScribeKey, msg, sec, TimeUnit.SECONDS);
            Common_response response = new Common_response();
            response.setMsg(StrUtil.format("{} 将发送订阅通知.还有 {} 秒 演出时间: {}", pushTime, sec, notifyTime));
            return response;

        } else {  //不传时间的做入库处理.
            //AppNotifyMsg msg=transNotifyMsg(req,template,trigger,projectId);
            saveTriggerMsg(req, template, trigger, projectId);
        }
        return new Common_response();
    }

    private AppNotifyMsg transNotifyMsg(AppSubscribeNotifyMsgReq req, Wxtemplate template, MsgTriggerEnum triggerEnum, String projectId) {
        AppNotifyMsg msg = new AppNotifyMsg();
        if (MsgTriggerEnum.SHOWREADY.equals(triggerEnum)) {
            PerFormCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PERFORM);
            Perform perform = cache.getRecord(projectId, req.getKeyId());
            msg.setProjectId(projectId);
            msg.setKeyId(req.getKeyId()); //节目id
            msg.setWxOpenId(req.getOpenid());
            msg.setTemplateId(template.getOutid());
            msg.setP1(perform.getDescription());//演出名称
            msg.setP2(req.getPerformTime().replaceAll("-", "~"));//演出时间
            msg.setP3(perform.getAddress());//演出地点
            msg.setTriggerType(triggerEnum.name());

        }
        return msg;
    }

    private void saveTriggerMsg(AppSubscribeNotifyMsgReq req, Wxtemplate template, MsgTriggerEnum triggerEnum, String projectId) {
        Sub_usermsg usermsg = new Sub_usermsg();//生成一条数据库记录.发送的时候把这个转成appnotifymsg 进行发送
        usermsg.setProjectid(projectId);
        usermsg.setTargetid(req.getOpenid());//需要发送的用户ID
        usermsg.setKeyid(req.getKeyId());  //订单号
        usermsg.setTriggertype(triggerEnum.name());
        usermsg.setApptype(WebAppGlobalContext.getCurrentAppAgentType().val());
        usermsg.setTemplateid(template.getOutid());
        usermsg.setStatus(0);
        subusermsgMapper.save(usermsg);


    }


}
