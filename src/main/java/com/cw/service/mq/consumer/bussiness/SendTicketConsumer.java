package com.cw.service.mq.consumer.bussiness;

import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.events.SysPushEvent;
import com.cw.arithmetic.pay.OrderSenseNotifyDataHandler;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.config.Cwconfig;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.OrderVendorSwitcher;
import com.cw.core.vendor.order.VendorHandler;
import com.cw.entity.Booking_rs;
import com.cw.entity.Sysconf;
import com.cw.entity.Ticket_rs;
import com.cw.exception.DefinedException;
import com.cw.mapper.BookingrsMapper;
import com.cw.mapper.Ticket_rsMapper;
import com.cw.outsys.stdop.request.StdTicketQueryQrPicRequest;
import com.cw.outsys.stdop.request.StdTicketSendMsgRequest;
import com.cw.outsys.stdop.response.StdOrderResponse;
import com.cw.outsys.stdop.response.StdTicketQueryQrPicResponse;
import com.cw.outsys.stdop.response.StdTicketSendMsgResponse;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.service.mq.ExpireMessagePostProcessor;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushTicketMsg;
import com.cw.service.mq.msgmodel.bussiness.Bussness_QrCodeWebSocketMsg;
import com.cw.utils.*;
import com.cw.utils.enums.LoggerType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.rabbitmq.client.Channel;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class SendTicketConsumer implements ChannelAwareMessageListener {

    private Logger logger = LoggerFactory.getLogger(LoggerType.sys.name());
    private Logger resendLogger = LoggerUtil.getLogger(LoggerUtil.LoggerType.resendlog);
    ;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {

        try {
            Bussness_PushTicketMsg msg = JSON.parseObject(new String(message.getBody()), Bussness_PushTicketMsg.class);
            createTicketOrder(msg);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        }

    }

    /**
     * @param msg
     */
    private void createTicketOrder(Bussness_PushTicketMsg msg) {
        //创建门票订单
        OrderVendorSwitcher switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getConfCache();
        Sysconf sysconf = sysConfCache.getOne(msg.getProjectId());
        Cwconfig cwconfig = SpringUtil.getBean(Cwconfig.class);

        VendorType vendorType = EnumUtil.fromString(VendorType.class, sysconf.getTicketvendor(), VendorType.LOCAL);
        if (vendorType.equals(VendorType.LOCAL)) {
            logger.info("发码模式已关闭.直接返回.如果需要发二维码.请开启配置参数");
            return;
        }
        VendorHandler vendorHandler = switcher.getVendorHandler(vendorType);


        OrderSenseNotifyDataHandler<?> notifyDataHandler = OrderVendorSwitcher.getSenseHandler(msg.getScene(), msg.getBookingId());
        StdOrderData stdOrderData = notifyDataHandler.getStdOrerData(msg.getBookingId(), msg.getProjectId());    //getStdOrerData(msg.getBookingId(), msg.getProjectId());
        String assistCode = "";
        String qrCode = "";
        String outid = "";
        boolean lok = true;
        if (stdOrderData != null) {
            try {
                StdOrderResponse result = vendorHandler.createOrder(stdOrderData);
                assistCode = result.getTcheckNo();
                outid = result.getOutid();
            } catch (Exception e) {
                e.printStackTrace();
                lok = false;
            }
        }
        if (lok) {
            StdTicketQueryQrPicRequest qrPicRequest = new StdTicketQueryQrPicRequest();
            qrPicRequest.setColno(stdOrderData.getBookingRs().getBookingid());
            qrPicRequest.setProjectId(stdOrderData.getBookingRs().getProjectid());
            qrPicRequest.setProdType(stdOrderData.getBookingRs().getPtype());
            qrPicRequest.setAssistCode(assistCode);
            try {
                StdTicketQueryQrPicResponse response = vendorHandler.queryTicketQrPic(qrPicRequest);
                qrCode = response.getImg();//把二维码查回来 展示给用户
            } catch (Exception e) {
                e.printStackTrace();
                resendLogger.info("查询门票二维码返回异常");
                lok = false;
            }
        }
        if (lok && !qrCode.isEmpty()) {
            notifyDataHandler.writeQrCodeAndSave(stdOrderData, qrCode, assistCode, outid);
//            writeQrCodeAndSave(stdOrderData, qrCode, assistCode);
            logger.info("门票发码成功：" + msg.toString());

            //深大发送短信
            StdTicketSendMsgRequest smsRequest = new StdTicketSendMsgRequest();
            smsRequest.setColno(stdOrderData.getBookingRs().getBookingid());
            smsRequest.setProjectId(stdOrderData.getBookingRs().getProjectid());
            smsRequest.setOutid(outid);
            if (StringUtils.isNotBlank(stdOrderData.getBookingRs().getTel())) {
                smsRequest.setMobileno(stdOrderData.getBookingRs().getTel());

            }
            try {
                StdTicketSendMsgResponse response = vendorHandler.sendTicketMsg(smsRequest);//发送门票短信
                //todo  发码成功通知李庄后台用户  判断projectid ptype
                if ("005".equals(stdOrderData.getBookingRs().getProjectid()) && ProdType.TICKET.val().equals(stdOrderData.getBookingRs().getPtype())) {
                    if (ContentCacheTool.checkTicketNotifyConsole(stdOrderData.getBookingRs().getProjectid(),
                            stdOrderData.getBookingRs().getProduct())) {
                        //发送短信
                        SysPushEvent sysPushEvent = new SysPushEvent(stdOrderData.getBookingRs(), MsgTriggerEnum.TICKETQRNOTIFY, stdOrderData.getBookingRs().getTel(),
                                "", stdOrderData.getBookingRs().getBookingid(), stdOrderData.getBookingRs().getProjectid());
                        SpringUtil.getApplicationContext().publishEvent(sysPushEvent);//异步发送发码成功短信
                        logger.info("门票短信发送通知后台人员成功");
                    }

                }

                logger.info("门票短信发送成功：" + response.toString());
            } catch (DefinedException e) {
                e.printStackTrace();
            }

            //通知小程序.有变动.需要刷新
            try {
                Bussness_QrCodeWebSocketMsg socketMsg = new Bussness_QrCodeWebSocketMsg();
                socketMsg.setQrcode(stdOrderData.getBookingRs().getBookingid());
                socketMsg.setProjectid(stdOrderData.getBookingRs().getProjectid());
                //发送队列消息
                RabbitTemplate rabbitTemplate = SpringUtil.getBean("rabbitTemplate", RabbitTemplate.class);
                rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.QRCODEWEB),
                        JSON.toJSONString(socketMsg));
            } catch (AmqpException e) {
                logger.error("通知门票websocket  {}失败", stdOrderData.getBookingRs().getBookingid());
            }

        } else {
            logger.info("门票发码失败：" + msg.toString());
            handlerError(JSON.toJSONString(msg));
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLINFO,
                    RobotUtils.transRobotTicketMsg("门票发码失败", msg.getBookingId(), RobotUtils.RobotGroup.DSMALLINFO, JSON.toJSONString(msg)));

        }
    }

    /**
     * 获取要发送二维码的票务数据
     *
     * @param bookingId
     * @param projectId
     * @return
     */
    private StdOrderData getStdOrerData(String bookingId, String projectId) {
        StdOrderData stdOrderData = new StdOrderData();
        BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
        Ticket_rsMapper ticket_rsMapper = SpringUtil.getBean(Ticket_rsMapper.class);
        Booking_rs bookingRs = bookingrsMapper.findBooking_rsByBookingid(bookingId);
        List<Ticket_rs> ticketRsList = ticket_rsMapper.findTicket_rsByBookingidAndProjectid(bookingId, projectId);
        stdOrderData.setBookingRs(bookingRs);
        stdOrderData.getTickets().addAll(ticketRsList);

        if (!bookingRs.getMainstatus().equals(StatusUtil.BookingRsStatus.PAY) || ticketRsList.size() == 0) {
            return null;
        }
        return stdOrderData;
    }

    private void writeQrCodeAndSave(StdOrderData orderData, String qrCode, String assistCode) {
        Ticket_rsMapper ticket_rsMapper = SpringUtil.getBean(Ticket_rsMapper.class);
        for (Ticket_rs ticket : orderData.getTickets()) {
            ticket.setQrcode(qrCode);
            ticket.setAssistcode(assistCode);
        }
        ticket_rsMapper.saveAll(orderData.getTickets());
    }

    protected void handlerError(String msg) {
        RabbitTemplate rabbitTemplate = SpringUtil.getBean("rabbitTemplate", RabbitTemplate.class);
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        String mapKey = MqNameUtils.BussinessTask.SENDTICKETQR.name();
        RMapCache<String, Integer> map = redissonClient.getMapCache(mapKey);
        Integer currNum = map.get(msg);
        currNum = currNum == null ? 1 : currNum + 1;

        int trycount = 10; //默认重试10次
        boolean lfail = false;
        if (currNum > trycount) { //先定义.重试10次重发为失败
            lfail = true;
        }
        if (lfail) {
            map.remove(msg);
            logger.error("[PUSH]  发码失败:{}", msg);
            //TODO  报警.记录.增加手动重发?
            Bussness_PushTicketMsg msgObj = JSON.parseObject(msg, Bussness_PushTicketMsg.class);
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLINFO,
                    RobotUtils.transRobotTicketMsg("门票发码失败", msgObj.getBookingId(), RobotUtils.RobotGroup.DSMALLINFO, msg));
            //发码失败保存日志
            UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
            SystemUtil.UserLogType userLogType = msgObj.getScene() == 0 ? SystemUtil.UserLogType.COL : SystemUtil.UserLogType.ACTGROUP;
            userLogService.writeLog(userLogType, SystemUtil.UserLogOpType.MODIFY, SystemUtil.DEFAULTUSERID,
                    msgObj.getBookingId(), "门票发码失败", msgObj.getProjectId());

        } else {
            map.put(msg, currNum, 6, TimeUnit.HOURS); //6小时
            String delaySignal = MqNameUtils.getDelaySignal(MqNameUtils.DirectExchange.MALL,
                    MqNameUtils.BussinessTask.SENDTICKETQR.name());//CrsMqNameUtils.getGroupDelayQueueSignal(op);
            int interval = currNum <= 3 ? 5 : 20;  //5秒后重新入队
            ExpireMessagePostProcessor expire = new ExpireMessagePostProcessor(interval, TimeUnit.SECONDS);
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.DELAY.name(), delaySignal, msg, expire);
            logger.error("[PUSH]  发码重试:{}", msg);
            resendLogger.error("[PUSH]  发码重试:{}", msg);
        }
    }


}
