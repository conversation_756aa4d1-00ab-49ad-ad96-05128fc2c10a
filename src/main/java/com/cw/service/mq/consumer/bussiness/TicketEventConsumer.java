package com.cw.service.mq.consumer.bussiness;

import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.pay.OrderSenseNotifyDataHandler;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.config.Cwconfig;
import com.cw.core.CorePay;
import com.cw.core.CoreSync3;
import com.cw.core.orderhandler.OrderVendorSwitcher;
import com.cw.core.vendor.order.VendorHandler;
import com.cw.entity.Act_rs;
import com.cw.entity.Prepay;
import com.cw.entity.Sysconf;
import com.cw.entity.Ticket_rs;
import com.cw.exception.DefinedException;
import com.cw.mapper.ActrsMapper;
import com.cw.mapper.PrepayMapper;
import com.cw.mapper.Ticket_rsMapper;
import com.cw.outsys.stdop.common.array.StdTicketSubOrderNode;
import com.cw.outsys.stdop.request.StdTicketQueryRequest;
import com.cw.outsys.stdop.response.StdTicketQueryResponse;
import com.cw.pojo.common.rule.CancelOrderRuleValidData;
import com.cw.pojo.notify.sd.SDTicketNotifyData;
import com.cw.pojo.notify.sd.TicketNotifyEventModel;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.bussiness.Bussness_QrCodeWebSocketMsg;
import com.cw.utils.SpringUtil;
import com.cw.utils.StatusUtil;
import com.cw.utils.enums.LoggerType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.ticket.TicketNotifyEventType;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 处理票务系统订单状态更新消息
 */
@Slf4j
@Component
public class TicketEventConsumer implements ChannelAwareMessageListener {

    @Autowired
    CoreSync3 coreSync3;

    @Autowired
    CorePay corePay;

    @Autowired
    PrepayMapper prepayMapper;

    @Autowired
    Ticket_rsMapper ticketRsMapper;

    @Autowired
    ActrsMapper actrsMapper;


    private Logger logger = LoggerFactory.getLogger(LoggerType.sys.name());

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {

        try {
            TicketNotifyEventModel msg = JSON.parseObject(new String(message.getBody()), TicketNotifyEventModel.class);
            SDTicketNotifyData data = msg.getData();
            if (data != null) {
                syncTicketStatus(msg.getProjectId(), data, msg.getSense());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        }

    }

    /**
     * @param projectId
     * @param notifyData
     */
    private void syncTicketStatus(String projectId, SDTicketNotifyData notifyData, Integer sense) {
        if (notifyData.getType().equals(TicketNotifyEventType.FINISH)) {
//            if (sense == 0) {
//                coreSync3.updProductMainRsStatus(projectId, notifyData.getOrderid(), StatusUtil.BookingRsStatus.FINISH, ProdType.TICKET);
//
//            } else if (sense.equals(PayUtil.ACT_SCENE) ) {//针对活动预约
//
//
//            }
            OrderSenseNotifyDataHandler<?> handler = OrderVendorSwitcher.getSenseHandler(sense, notifyData.getOrderid());
            handler.updateTicketOrderStatus(projectId, notifyData.getOrderid(), StatusUtil.ActRsStatus.FINISH);
            //更新门票订单为完成状态
        }
        if (notifyData.getType().equals(TicketNotifyEventType.CANCEL)) {
            //查询
            refundTicket(projectId, notifyData.getOrderid());

        }
        if (notifyData.getType().equals(TicketNotifyEventType.CHECKTICEKT)) {

            boolean lact = notifyData.getOrderid().startsWith(ProdType.ACTGROUP.val());
            //微票回调不返回regno 直接查询主订单号
            if (lact) {  //活动预约 .只要检一张.都算是完成
                Act_rs rs = actrsMapper.findAct_rsByBookingid(notifyData.getOrderid());
                if (rs.getPersons() == notifyData.getChecknum()) {
                    SDTicketNotifyData data = new SDTicketNotifyData();
                    data.setType(TicketNotifyEventType.FINISH);
                    data.setOrderid(notifyData.getOrderid());
                    OrderSenseNotifyDataHandler<?> handler = OrderVendorSwitcher.getSenseHandler(1, notifyData.getOrderid());
                    handler.updateTicketOrderStatus(projectId, notifyData.getOrderid(), StatusUtil.ActRsStatus.FINISH);
                }
            } else {
                Ticket_rs ticketRs = null;
                if (StringUtils.isNotBlank(notifyData.getSuborderid())) {
                    ticketRs = ticketRsMapper.findTicket_rsByRegno(notifyData.getSuborderid());

                } else {
                    ticketRs = ticketRsMapper.findTicket_rsByBookingid(notifyData.getOrderid());
                }
                if (ticketRs != null) {
                    ticketRs.setChecknum(notifyData.getChecknum());
                    log.info("更新检票数字");
                    ticketRsMapper.save(ticketRs);
                    //如果是微票传过来 核销完毕完结订单
                    if (StringUtils.isBlank(notifyData.getSuborderid()) && ticketRs.getAnz().equals(notifyData.getChecknum())) {
                        SDTicketNotifyData data = new SDTicketNotifyData();
                        data.setType(TicketNotifyEventType.FINISH);
                        data.setOrderid(notifyData.getOrderid());
                        syncTicketStatus(projectId, data, notifyData.getOrderid().startsWith(ProdType.ACTGROUP.val()) ? 1 : 0);
                    }
                }
            }

            /***************通知小程序.有变动.需要刷新***************/
            //通知小程序.有变动.需要刷新
            try {
                Bussness_QrCodeWebSocketMsg msg = new Bussness_QrCodeWebSocketMsg();
                msg.setQrcode(notifyData.getOrderid());
                msg.setProjectid(projectId);
                //发送队列消息
                SpringUtil.getBean(RabbitTemplate.class).convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.QRCODEWEB),
                        JSON.toJSONString(msg));
            } catch (AmqpException e) {
                logger.error("通知门票websocket  {}失败", notifyData.getOrderid());
            }

        }
        log.info("同步门票通知成功");
    }

    private void updateTicketCheck() {

    }

    /**
     * @param projectId
     * @param bookingId
     */
    private void refundTicket(String projectId, String bookingId) {
        //创建门票订单
        OrderVendorSwitcher switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getConfCache();
        Sysconf sysconf = sysConfCache.getOne(projectId);
        Cwconfig cwconfig = SpringUtil.getBean(Cwconfig.class);

        VendorType vendorType = EnumUtil.fromString(VendorType.class, sysconf.getTicketvendor(), VendorType.LOCAL);
        if (vendorType.equals(VendorType.LOCAL) || !cwconfig.getTicketmode()) {
            return;
        }
        VendorHandler vendorHandler = switcher.getVendorHandler(vendorType);

        StdTicketQueryRequest request = new StdTicketQueryRequest();
        request.setColno(bookingId);

        boolean lexecRefund = true;

        try {
            StdTicketQueryResponse response = vendorHandler.queryTicket(request);
            if (response.getStd_flag() && response.getOrders() != null && response.getOrders().size() > 0) {
                for (StdTicketSubOrderNode node : response.getOrders()) {
                    if (node.getChecknum() > 0) {
                        lexecRefund = false;
                    }
                }
            }
        } catch (DefinedException e) {
            e.printStackTrace();
            lexecRefund = false;
        }

        if (lexecRefund) {
            Prepay prepay = prepayMapper.findPrepayByBookingid(bookingId);
            if (prepay != null) {
                CancelOrderRuleValidData cancelOrderRuleValidData = new CancelOrderRuleValidData();
                cancelOrderRuleValidData.setRefund(prepay.getAmount());//该订单的综合支付金额
                corePay.refundPay(prepay, cancelOrderRuleValidData);
            }
        }

    }

}
