package com.cw.core;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.others.CalcRoomsPickup;
import com.cw.arithmetic.others.SkuDetail;
import com.cw.arithmetic.others.TRoomsUpdate;
import com.cw.arithmetic.others.TRsdata;
import com.cw.arithmetic.sku.OpskuPickup;
import com.cw.arithmetic.sku.TMultiRsdata;
import com.cw.arithmetic.sku.TSkuUpd;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.cache.impl.ActsiteCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.cache.impl.SpusitemCache;
import com.cw.cache.impl.TicketCache;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.*;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.sqlresult.Produce_avlPo;
import com.cw.pojo.sqlresult.Produce_minAvlPo;
import com.cw.pojo.sqlresult.Product_maxDatePo;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.CalculateString;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存校验,写入扣减逻辑服务
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/20 10:10
 **/
@Slf4j
@Service
public class CoreAvl {

    @Autowired
    RroomsMapper rroomsMapper;

    @Autowired
    RticketsMapper rticketsMapper;

    @Autowired
    RacttimeMapper racttimeMapper;

    @Autowired
    RspuitemsMapper rspuitemsMapper;

    @Autowired
    RcouponMapper rcouponMapper;

    @Autowired
    RgiftitemsMapper rgiftitemsMapper;

    @Autowired
    RkitsMapper rkitsMapper;

    @Autowired
    RedisTemplate redisTemplate;


    @Autowired
    DaoLocal daoLocal;


    public List<TSkuUpd> getOrderSkuUpd(StdOrderData orgData, StdOrderData postData) {
        TMultiRsdata oldRsdata;
        TMultiRsdata newRsdata;
        if (orgData == null || orgData.getChannel().isEmpty()) {
            oldRsdata = new TMultiRsdata(true, "", null, null);
        } else {
            oldRsdata = new TMultiRsdata(false, orgData.getChannel(), orgData.getRooms(),//套餐售卖时.不校验票型库存
                    orgData.getKitCode().isEmpty() ? orgData.getTickets() : Lists.newArrayList(), orgData.getSpus(), orgData.getGifts(), orgData.getKitCode(), "", orgData.getUseDate(), orgData.getBookingRs().getAnz());
        }
        if (postData == null || postData.getChannel().isEmpty()) {
            newRsdata = new TMultiRsdata(true, "", null, null);
        } else {
            newRsdata = new TMultiRsdata(false, postData.getChannel(), postData.getRooms(),//套餐售卖时.不校验票型库存
                    postData.getKitCode().isEmpty() ? postData.getTickets() : Lists.newArrayList(), postData.getSpus(), postData.getGifts(), postData.getKitCode(), "", postData.getUseDate(), postData.getBookingRs().getAnz());
        }
        return OpskuPickup.calcSkuPickup(oldRsdata, newRsdata);
    }

    public boolean checkCacheSku(String projectId, List<TSkuUpd> skuUpds) throws DefinedException {
        if (skuUpds == null || skuUpds.isEmpty()) {
            return true;
        }


        for (TSkuUpd skuUpd : skuUpds) {
            if (!CoreCache.isCacheProductType(skuUpd.getProdType())) { // 目前只检查房的库存redis缓存
                continue;
            }
            String skuMapkey = OpskuPickup.getSkuMap(projectId, skuUpd);
            List<String> skukeys = CoreCache.getProdFetchMapKeys(skuUpd.getSkuid(), skuUpd.getProdType(), skuUpd.getStartdate(), skuUpd.getEnddate());
            //.getDateKeys(skuUpd.getStartdate(), skuUpd.getEnddate());
            //判断缓存类型取KEY

            List redisResult = RedisTool.getInstance().opsForHash().multiGet(skuMapkey, skukeys);


            for (int i = 0; i < redisResult.size(); i++) {
                Integer cachenum = redisResult.get(i) == null ? 0 : Integer.parseInt(redisResult.get(i).toString());
                int postnum = skuUpd.getChangeNum();
                if (cachenum - postnum < 0) {
                    String productDesc = ContentCacheTool.getProductDesc(skuUpd.getProdType().val(), skuUpd.getSkuid(), projectId);

                    throw new DefinedException(StrUtil.format("{}在{}库存不足", productDesc, skukeys.get(i)),
                            ResultCode.FORMERR.code(), 0, WebAppGlobalContext.getCurrentAppUserId());
                }
            }
        }
        return true;
    }

    public void updateSkuCache(String projectId, List<TSkuUpd> skuUpds) throws DefinedException {
        for (TSkuUpd skuUpd : skuUpds) {
            if (!CoreCache.isCacheProductType(skuUpd.getProdType())) { // 目前只检查房的库存redis缓存
                continue;
            }
            String skuMapkey = OpskuPickup.getSkuMap(projectId, skuUpd);
            boolean lspecSku = CoreCache.isSpecProdType(skuUpd.getProdType());
            List<String> gridKeys = CoreCache.getProdFetchMapKeys(skuUpd.getSkuid(), skuUpd.getProdType(), skuUpd.getStartdate(), skuUpd.getEnddate());
            List redisResult = RedisTool.getInstance().opsForHash().multiGet(skuMapkey, gridKeys);
            for (int i = 0; i < redisResult.size(); i++) {
                Integer cachenum = redisResult.get(i) == null ? 0 : Integer.parseInt(redisResult.get(i).toString());
                int postnum = skuUpd.getChangeNum();
                if (cachenum - postnum < 0) {
                    if (!lspecSku) {
                        throw new DefinedException(MessageFormat.format("{0}在{1}库存不足", skuUpd.getSkuid(), gridKeys.get(i)), SystemUtil.SystemerrorCode.ERR001_OVERBOOKROOM, 0, WebAppGlobalContext.getCurrentAppUserId());
                    } else {
                        throw new DefinedException("库存不足", SystemUtil.SystemerrorCode.ERR001_OVERBOOKROOM, 0, WebAppGlobalContext.getCurrentAppUserId());
                    }
                }
                RedisTool.getInstance().opsForHash().put(skuMapkey, gridKeys.get(i), (cachenum - postnum) + ""); //更新redis 缓存
            }
        }
    }

    public void updateAvailability(TRsdata old, Room_rs newrs) throws DefinedException {
        List<TRoomsUpdate> lis = CalcRoomsPickup.calcAvailability(old, newrs);
        if (!lis.isEmpty()) {
            for (TRoomsUpdate rUpd : lis) {
                excuteUpdateRoomGrid(rUpd, newrs.getProjectid());
            }
        }
    }

    public void updateTicketAvailability(List<TSkuUpd> updList, String projectId) {
        for (TSkuUpd tSkuUpd : updList) {
            rticketsMapper.updateRticketsPickup(tSkuUpd.getChangeNum(), tSkuUpd.getSkuid(), tSkuUpd.getStartdate(), tSkuUpd.getEnddate(), projectId);
        }
    }

    public void updateSpuitemAvailability(List<TSkuUpd> updList, String projectId) {
        RspuitemsMapper rspuitemsMapper = SpringUtil.getBean(RspuitemsMapper.class);
        for (TSkuUpd tSkuUpd : updList) {
            rspuitemsMapper.updateRspuitemPickup(tSkuUpd.getChangeNum(), tSkuUpd.getSkuid(), projectId);
        }
    }

    public void updateGiftAvailability(List<TSkuUpd> updList, String projectId) {
        for (TSkuUpd tSkuUpd : updList) {
            rgiftitemsMapper.updateRgiftitemPickup(tSkuUpd.getChangeNum(), tSkuUpd.getSkuid(), projectId);
        }
    }


    private boolean isActBlock(String block) {
        return StringUtils.isNotBlank(block);
    }

    /**
     * 更新可卖房表
     *
     * @param rUpd 房表变化的数据结构
     */
    public void excuteUpdateRoomGrid(TRoomsUpdate rUpd, String projectId) {
        boolean lactBlock = isActBlock(rUpd.getBlock());// isFreeSell(rUpd.getChannel());
        if (lactBlock) {//更新活动产品库存
            //TODO  MAPPER
//            rrooms_pmsMapper.updateRrooms_pmsPickup(rUpd.getChangeNum(),rUpd.getRmtype(),rUpd.getStartdate(),rUpd.getEnddate());
        } else {
//            RroomsMapper rroomsMapper = SpringUtil.getBean(RroomsMapper.class);
//            UpdateWrapper<Rrooms> updateWrapper = new UpdateWrapper<>();
//            updateWrapper.setSql("pickup=pickup+" + rUpd.getChangeNum());
//            updateWrapper.eq("rmtype", rUpd.getRmtype());
//            updateWrapper.between("datum", rUpd.getStartdate(), rUpd.getEnddate());
//            rroomsMapper.update(null, updateWrapper);
//            log.info("更新可卖房表{} {} {} 变化数:{}", rUpd.getRmtype(),
//                    DateUtil.formatDate(rUpd.getStartdate()), DateUtil.formatDate(rUpd.getEnddate()), rUpd.getChangeNum());
            rroomsMapper.updateRroomsPickup(rUpd.getChangeNum(), rUpd.getRmtype(), rUpd.getStartdate(), rUpd.getEnddate(), projectId);
        }
    }

    public void excuteUpdteTicketGrid(List<TSkuUpd> updList) {
//        for (TSkuUpd skuUpd : updList) {
//            if (skuUpd.getChangeNum() == 0) {
//                continue;
//            }
//            if (skuUpd.getChangeNum() > 0) {
//                ticketMapper.updateTicketPickup(skuUpd.getChangeNum(), skuUpd.getSkuid(), skuUpd.getStartdate(), skuUpd.getEnddate());
//            } else {
//                ticketMapper.updateTicketPickup(skuUpd.getChangeNum(), skuUpd.getSkuid(), skuUpd.getStartdate(), skuUpd.getEnddate());
//            }
//        }

    }

    /**
     * @param projectid
     * @return 获取房量表最大日期
     */
    public Date getRroomsMaxDate(String projectid) {
        Date maxDate = rroomsMapper.findRmtypeAvlMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    /**
     * @param projectid
     * @return 获取票型库存表最大日期
     */
    public Date getRticketMaxDate(String projectid) {
        Date maxDate = rticketsMapper.findTicketAvlMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    /**
     * @param projectid
     * @return 获取预约场所库存表最大日期
     */
    public Date getRacttimeMaxDate(String projectid) {
        Date maxDate = racttimeMapper.findActsiteAvlMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    /**
     * @param projectid
     * @return 景区商品库存表最大日期
     */
    public Date getRspuitemsMaxDate(String projectid) {
        Date maxDate = rspuitemsMapper.findSpuitemsAvlMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    /**
     * @param projectid
     * @return 获取伴手礼库存表最大日期
     */
    public Date getRgiftitemsMaxDate(String projectid) {
        Date maxDate = rgiftitemsMapper.findGiftitemsAvlMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    /**
     * 获取套餐表库存最大日期
     *
     * @param projectid
     * @return
     */
    public Date getProductkitMaxDate(String projectid) {
        Date maxDate = rkitsMapper.findProductkitAvlMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    /**
     * 景区产品库存生成
     *
     * @param projectId
     * @param spuitems
     * @param startDate
     * @param endDate
     */
    public void generateRspuitems(String projectId, String[] spuitems, Date startDate, Date endDate) throws SQLException {
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        if (startDate == null) {
            startDate = CalculateDate.getSystemDate();
        }
        if (endDate == null) {
            Date maxdate = CalculateDate.reckonDay(startDate, 5, 90);
            List ls = daoLocal.getObjectList("select max(datum) from Rspuitems where projectid=" + projectId);
            if (!ls.isEmpty()) {
                maxdate = (Date) ls.get(0);
                maxdate = CalculateDate.reckonDay(maxdate, 5, 1);// 将数据里的最大日期往后加1
            }
            endDate = maxdate;
        }
        String sdate = CalculateDate.dateToString(startDate);
        String edate = CalculateDate.dateToString(endDate);
        String deleteSql = "delete from Rspuitems where  spuitem=? and datum between '" + sdate + "' and '" + edate + "' and projectid=?";
        //按照日期区间插入门票资源数据
        String insertSql = "insert into Rspuitems(spuitem,datum,pickup,avl,projectid) select ?,date_add('"
                + sdate + "',interval number day),0,?,? from sptvalue where  date_add('" + sdate + "',interval number day)<='" + edate + "'";
        DataSource source = daoLocal.getDataSource();
        PreparedStatement pst = null;
        PreparedStatement pst2 = null;
        Connection connection = null;
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            pst = connection.prepareStatement(deleteSql);
            for (String spusitem : spuitems) {
                pst.setString(1, spusitem);
                pst.setString(2, projectId);
                pst.addBatch();
            }
            pst.executeBatch();
            connection.commit();
            //删除结束.新插入开始

            pst2 = connection.prepareStatement(insertSql);
            SpusitemCache spusitemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM);
            for (String spusitem : spuitems) {
                Spusitem rt = spusitemCache.getRecord(projectId, spusitem);
                if (rt != null) {
                    pst2.setString(1, spusitem);
                    pst2.setInt(2, 0);//先设置0
                    pst2.setString(3, projectId);

                    pst2.addBatch();
                }
            }
            pst2.executeBatch();
            connection.commit();
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        } finally {
            if (pst != null) {
                pst.close();
            }
            if (pst2 != null) {
                pst2.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    ;


    /**
     * 批量产品库存自动延长最大日期库存+1天
     *
     * @param projectId 项目日期
     * @param products  产品库存日期集合
     * @param endDate   最大日期库存+1天
     * @param pType
     * @throws DefinedException
     * @throws Exception
     */
    public void generateProductResource(String projectId, List<Product_maxDatePo> products, Date endDate, ProdType pType) throws DefinedException, Exception {
        if (!EnumUtils.isValidEnum(ProdType.class, pType.name())) {
            throw new DefinedException("夜审产品库存自动延长的产品类型不存在");
        }
        String deleteSql = "";
        String insertSql = "";
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        String edate = CalculateDate.dateToString(endDate);
        DataSource source = daoLocal.getDataSource();
        PreparedStatement pst = null;
        PreparedStatement pst2 = null;
        Connection connection = null;
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);
            switch (pType) {
                case ROOM:
                    deleteSql = "delete from Rrooms where  rmtype=? and datum between ? and '" + edate + "' and projectid=?";
                    //按照日期区间插入房量数据
                    insertSql = "insert into Rrooms(rmtype,datum,pickup,avl,projectid) select ?,date_add(?,interval number day),0,?,? from sptvalue" +
                            " where  date_add(?,interval number day)<='" + edate + "'";
                    pst2 = connection.prepareStatement(insertSql);
                    RoomTypeCache roomTypeCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
                    for (Product_maxDatePo rmtype : products) {
                        Roomtype rt = roomTypeCache.getRecord(projectId, rmtype.getCode());
                        if (rt != null) {
                            Date startDate = CalculateDate.reckonDay(rmtype.getDatum(), 5, 1);
                            pst2.setString(1, rmtype.getCode());
                            pst2.setDate(2, DateUtil.date(startDate).toSqlDate());
                            pst2.setInt(3, rt.getNum(0));
                            pst2.setString(4, projectId);
                            pst2.setDate(5, DateUtil.date(startDate).toSqlDate());
                            pst2.addBatch();
                        }
                    }
                    break;
                case TICKET:
                    deleteSql = "delete from Rtickets where  ticket=? and datum between ? and '" + edate + "' and projectid=?";
                    //按照日期区间插入门票资源数据
                    insertSql = "insert into Rtickets(ticket,datum,pickup,avl,projectid) select ?,date_add(?,interval number day),0,?,? from sptvalue " +
                            "where  date_add(?,interval number day)<='" + edate + "'";
                    pst2 = connection.prepareStatement(insertSql);
                    TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
                    for (Product_maxDatePo ticket : products) {
                        Ticket rt = ticketCache.getRecord(projectId, ticket.getCode());
                        if (rt != null) {
                            Date startDate = CalculateDate.reckonDay(ticket.getDatum(), 5, 1);
                            pst2.setString(1, ticket.getCode());
                            pst2.setDate(2, DateUtil.date(startDate).toSqlDate());
                            pst2.setInt(3, 0); //门票先设置0库存
                            pst2.setString(4, projectId);
                            pst2.setDate(5, DateUtil.date(startDate).toSqlDate());
                            pst2.addBatch();
                        }
                    }
                    break;
                case ACTGROUP:
                    deleteSql = "delete from Racttime where  sitecode=? and `period`=?  and datum between ? and '" + edate + "' and projectid=?";
                    //按照日期区间插入预约场所资源数据
                    insertSql = "insert into Racttime(sitecode,`period`,datum,pickup,avl,projectid) select ?,?,date_add(?,interval number day),0,?,? from sptvalue " +
                            "where  date_add(?,interval number day)<='" + edate + "'";
                    pst2 = connection.prepareStatement(insertSql);
                    ActsiteCache actsiteCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTSITE);
                    for (Product_maxDatePo actsite : products) {
                        Actsite rt = actsiteCache.getRecord(projectId, actsite.getCode());
                        if (rt != null) {
                            Date startDate = CalculateDate.reckonDay(actsite.getDatum(), 5, 1);
                            pst2.setString(1, actsite.getCode());
                            pst2.setString(2, actsite.getPeriod());
                            pst2.setDate(3, DateUtil.date(startDate).toSqlDate());
                            pst2.setInt(4, rt.getDfallownum()); //先设置库存
                            pst2.setString(5, projectId);
                            pst2.setDate(6, DateUtil.date(startDate).toSqlDate());
                            pst2.addBatch();
                        }
                    }
                    break;
            }

            pst = connection.prepareStatement(deleteSql);
            if (pType.equals(ProdType.ACTGROUP)) { //预约活动多一个时段
                for (Product_maxDatePo product : products) {
                    Date startDate = CalculateDate.reckonDay(product.getDatum(), 5, 1);//数据库内日期+1
                    pst.setString(1, product.getCode());
                    pst.setString(2, product.getPeriod());
                    pst.setDate(3, DateUtil.date(startDate).toSqlDate());
                    pst.setString(4, projectId);
                    pst.addBatch();
                }
            } else {
                for (Product_maxDatePo product : products) {
                    Date startDate = CalculateDate.reckonDay(product.getDatum(), 5, 1);//数据库内日期+1
                    pst.setString(1, product.getCode());
                    pst.setDate(2, DateUtil.date(startDate).toSqlDate());
                    pst.setString(3, projectId);
                    pst.addBatch();
                }
            }

            pst.executeBatch();
            connection.commit();
            //删除结束.新插入开始
            pst2.executeBatch();
            connection.commit();
        } finally {
            if (pst != null) {
                pst.close();
            }
            if (pst2 != null) {
                pst2.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }


    /**
     * 按 start 和 end 返回一段有效的库存
     *
     * @param productcode
     * @param startdate
     * @param enddate
     * @param productType
     * @return
     * @throws DefinedException
     */
    public LinkedHashMap<String, Integer> queryProductAvailGrid(String projectId, String productcode,
                                                                Date startdate, Date enddate,
                                                                ProdType productType, String specode) {
        LinkedHashMap<String, Integer> maps = Maps.newLinkedHashMap();
        int len = CalculateDate.compareDates(enddate, startdate).intValue();
        if (len == 0) {
            len++;
        }
        for (int i = 0; i < len; i++) {
            maps.put(CalculateDate.dateToString(CalculateDate.reckonDay(startdate, 5, i)), productType.equals(ProdType.ROOM) ? 0 : 9999);//其他产品暂时不限数量
        }

        List<Produce_avlPo> list = null;
        switch (productType) {
            case ROOM:
                list = rroomsMapper.queryAvailNum(startdate, enddate, productcode, projectId);
                break;
            case TICKET:
                list = rticketsMapper.queryAvailNum(startdate, enddate, productcode, projectId);
                break;
            case WARES:
                list = rspuitemsMapper.queryAvailNum(startdate, enddate, productcode, projectId);
                break;
            case TAOCAN:
                list = rkitsMapper.queryAvailNum(startdate, enddate, productcode, projectId);
                break;
            case ITEMS:
                if (StringUtils.isNotBlank(specode)) {//规格代码不为空 ,拼接规格代码查询
                    productcode = productcode + ":" + specode.replaceAll(",", ":") + "%";
                } else {
                    productcode = productcode + "%";
                }
                list = rgiftitemsMapper.queryAvailNum(productcode, projectId);
                break;
            default:
                list = new ArrayList<>();
        }

        for (Produce_avlPo row : list) {
            maps.put(CalculateDate.dateToString(row.getDate()), row.getAvl());
        }
        return maps;
    }

    /**
     * 返回一段时间内的最小库存
     *
     * @param startdate
     * @param enddate
     * @param prods
     * @param projectId
     * @param requireNum
     * @return
     */
    public SkuDetail queryMinAvailNum(Date startdate, Date enddate, List<String> prods, String projectId, Integer requireNum) {
        List<Produce_minAvlPo> pos = prods.size() > 0 ? rroomsMapper.queryMinAvlNum(startdate, enddate, prods, projectId, requireNum) : Lists.newArrayList();
        SkuDetail skuDetail = new SkuDetail();
        for (Produce_minAvlPo po : pos) {
            skuDetail.putProductAvl(po.getSku(), po.getAvl());
        }
        return skuDetail;
    }

    public void calc2Cache(String projectId, String productCode, Date startdate, Date enddate, ProdType prodType, String specode) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        LinkedHashMap<String, Integer> grids = queryProductAvailGrid(projectId, productCode, CalculateDate.returnDate_ZeroTime(startdate),
                CalculateDate.returnDate_ZeroTime(enddate), prodType, specode);
        for (Map.Entry<String, Integer> entry : grids.entrySet()) {
            map.put(entry.getKey(), entry.getValue() + "");
        }
        String key = CoreCache.getGridCacheKey(projectId, SystemUtil.DEFAULT_CHANNELID, prodType.val(), productCode);
        redisTemplate.opsForHash().putAll(key, map);
    }

    public void calcGiftItemCache(String projectId, String itemcode, List<String> specs) {
        String key = CoreCache.getGridCacheKey(projectId, SystemUtil.DEFAULT_CHANNELID, ProdType.ITEMS.val(), itemcode);
        List<Produce_avlPo> list = rgiftitemsMapper.queryAllSpecsAvl(projectId, itemcode, specs);
        LinkedHashMap<String, String> map = list.stream().collect(Collectors.toMap(
                row -> row.getSpecode(),
                row -> row.getAvl() + "",
                (v1, v2) -> v1,
                LinkedHashMap::new
        ));
        redisTemplate.opsForHash().putAll(key, map);
    }

    public void calcActCache(String projectId, String sitecode, Date startdate, Date enddate, List<String> times) {
        String key = CoreCache.getGridCacheKey(projectId, SystemUtil.DEFAULT_CHANNELID, ProdType.ACTGROUP.val(), sitecode);
        List<Produce_avlPo> list = racttimeMapper.queryAvlNumPeriod(sitecode, startdate, enddate, projectId);
        LinkedHashMap<String, String> map = list.stream().collect(Collectors.toMap(
                row -> row.getSpecode() + SystemUtil.KEYSPLITSIGNAL + CalculateDate.dateToString(row.getDate()),
                row -> row.getAvl() + "",
                (v1, v2) -> v1,
                LinkedHashMap::new
        ));
        redisTemplate.opsForHash().putAll(key, map);
    }

    public HashMap<String, Object> checkBatchCreateAvailability(List<Room_rs> rlis, String block, String projectId) {
        HashMap<Date, HashMap<String, Integer>> katChnageMap = new HashMap<Date, HashMap<String, Integer>>(); //存放每种房型要多订多少或者是少订多少
        HashSet<String> seekRoomTypes = new HashSet<String>();
        HashSet<Date> seekDates = new HashSet<Date>();
        Date hoteldate = new Date();
//        String msg = "";

        boolean lfreesell = block.isEmpty();//isFreeSell(channel);

        HashMap<Room_rs, String> rsMsgMap = new HashMap<Room_rs, String>();
        for (Room_rs g : rlis) {
            if (!CalculateDate.isEqual(g.getArrdate(), g.getDeptdate())) {
                Date startDate = CalculateDate.maxDate(hoteldate, g.getArrdate());
                int len = CalculateDate.compareDates(g.getDeptdate(), startDate).intValue();
                rsMsgMap.put(g, "");
                for (int i = 0; i < len; i++) {
                    Date d = CalculateDate.reckonDay(startDate, 5, i);
                    sumRoomTypePickup(g.getRmtype(), g.getAnz(), d, katChnageMap,
                            seekRoomTypes, seekDates);
                }
            }
        }
        if (katChnageMap.size() > 0 && !seekDates.isEmpty() && !seekRoomTypes.isEmpty()) {
            HashMap<Date, HashMap<String, Integer>> katLeftMap = new HashMap<Date, HashMap<String, Integer>>();
            //组装数据。
            List<String> rms = Arrays.asList(seekRoomTypes.toArray(new String[]{}));
            List<Date> checkdates = Arrays.asList(seekDates.toArray(new Date[]{}));
            String roomLeftStr = lfreesell ? "avl" : "avl";
            String rmTypeFilter = CalculateString.transFilter(rms, "'");


            List<Date[]> dateBetween = CalculateDate.getDateStartEnd2(checkdates);
            if (!rmTypeFilter.isEmpty() && !dateBetween.isEmpty()) {
                String datefilter = "";
//                int i = 0;
//                int j = 1;
                List<Date> params = new ArrayList<Date>();

//                String resourceJpql = "select g.datum,g.rmtype,sum(" + roomLeftStr + ") from Rrooms g where  ("
//                        + datefilter + ") and g.rmtype in (" + rmTypeFilter + ") and channelid='" + block + "' group by g.rmtype,g.datum order by datum";
//
//                if (lfreesell) {
//                    resourceJpql = "select g.datum,g.rmtype,sum(" + roomLeftStr + ") from Rroomsms g where  ("
//                            + datefilter + ") and g.rmtype in (" + rmTypeFilter + ") and ' group by g.rmtype,g.datum order by datum";
//                }


                for (Date[] dates : dateBetween) {
//                    i++;
//                    j++;
//                    datefilter += datefilter.isEmpty() ? "(datum between ?" + i + " and ?" + j + ")" : " or (datum between ?" + i + " and ?" + j + ")";//BUG6054
                    String dbegin = "'" + CalculateDate.dateToString(dates[0]) + "'";
                    String dend = "'" + CalculateDate.dateToString(dates[1]) + "'";


                    datefilter += datefilter.isEmpty() ? "(datum between " + dbegin + " and " + dend + ")" : " or (datum between " + dbegin + " and " + dend + ")";//BUG6054
                    params.add(dates[0]);
                    params.add(dates[1]);
//                    i++;
//                    j++;
                }
                String resourceJpql = "select g.datum,g.rmtype,sum(" + roomLeftStr + ") from Rrooms as g where  ("
                        + datefilter + ") and g.rmtype in (" + rmTypeFilter + ") and channelid='" + block + "' and g.projectid='" + projectId + "'  group by g.rmtype,g.datum order by datum";

                if (lfreesell) {
                    resourceJpql = "select g.datum,g.rmtype,sum(" + roomLeftStr + ") from Rrooms as  g where  ("
                            + datefilter + ") and g.rmtype in (" + rmTypeFilter + ") and g.projectid='" + projectId + "'  group by g.rmtype,g.datum order by datum";
                }
//                List<LinkedHashMap<String, Object>> dblist = sqlMapper.selectList(resourceJpql);
//                List<LinkedHashMap<String, Object>> dblist = SpringUtil.getBean(DefinedSqlService.class).selectList(resourceJpql,null);//TODO 这里可能有问题
//                List<Object[]> data = Db.castMapList(dblist);
                List<Object[]> data = SpringUtil.getBean(DaoLocal.class).getNativeObjectList(resourceJpql);


                for (int a = 0; a < data.size(); a++) {
                    Object[] os = data.get(a);
                    Date date = CalculateDate.stringToDate(os[0].toString());
                    String kat = os[1].toString();
                    Integer left = Integer.parseInt(os[2].toString());
                    sumRoomTypeLeft(kat, left, date, katLeftMap);
                }

                for (Date date : checkdates) {
                    if (!katChnageMap.containsKey(date) || !rsMsgMap.containsValue("")) {//如果这天不需要检查或者都校验完了
                        continue;
                    }
                    if (katLeftMap.containsKey(date)) {
                        HashMap<String, Integer> changeMap = katChnageMap.get(date);
                        HashMap<String, Integer> leftMap = katLeftMap.get(date);
                        for (Map.Entry<String, Integer> entry : changeMap.entrySet()) {
                            if (leftMap.containsKey(entry.getKey())) {
                                int left = leftMap.get(entry.getKey());
                                int change = entry.getValue();//
                                String rmtdesc = ContentCacheTool.getProductDesc(ProdType.ROOM.val(), entry.getKey(), projectId);
                                if (left - change < 0) {// 如果做进来会房型超预定的话
                                    //这天房间不够了
                                    String errmsg = MessageFormat.format("{2}在{0}仅剩余{1}间!", CalculateDate.dateToString(date), left, rmtdesc);
                                    if (left <= 0) {
                                        errmsg = MessageFormat.format("{1}在{0}已售罄!", CalculateDate.dateToString(date), rmtdesc);
                                    }
                                    putErrMsg(date, entry.getKey(), errmsg, rsMsgMap);
                                }
                            } else {
                                String errmsg = MessageFormat.format("{1}在{0}未设置预留", CalculateDate.dateToString(date), entry.getKey());
                                putErrMsg(date, entry.getKey(), errmsg, rsMsgMap);
                            }
                        }
                    } else {
                        //这天房间不够了
                        HashMap<String, Integer> changeMap = katChnageMap.get(date);
                        for (String rmtype : changeMap.keySet()) {
                            String errmsg = MessageFormat.format("房型{1}在{0}未设置留房", CalculateDate.dateToString(date), rmtype);
                            putErrMsg(date, rmtype, errmsg, rsMsgMap);
                        }
                    }
                }
            }
        }

        HashMap<String, Object> result = new HashMap<String, Object>();

        for (Map.Entry<Room_rs, String> entry : rsMsgMap.entrySet()) {
            if (!entry.getValue().isEmpty()) {
                result.put(entry.getKey().getRegno(), entry.getValue());
            }
        }
        return result;
    }

    private void sumRoomTypeLeft(String kat, Integer left, Date date,
                                 HashMap<Date, HashMap<String, Integer>> roomLeftMap) {
        date = CalculateDate.getDateKey(date);
        if (!roomLeftMap.containsKey(date))
            roomLeftMap.put(date, new HashMap<String, Integer>());
        roomLeftMap.get(date).put(kat, left);
    }

    private void sumRoomTypePickup(String kat, Integer change, Date date,
                                   HashMap<Date, HashMap<String, Integer>> katChnageMap,
                                   HashSet<String> seekRoomTypes, HashSet<Date> seekDates) {
        date = CalculateDate.getDateKey(date);
        if (change > 0) {
            seekRoomTypes.add(kat);
            seekDates.add(date);
            if (!katChnageMap.containsKey(date))
                katChnageMap.put(date, new HashMap<String, Integer>());

            if (!katChnageMap.get(date).containsKey(kat))
                katChnageMap.get(date).put(kat, change);
            else
                katChnageMap.get(date).put(kat, katChnageMap.get(date).get(kat) + change);
        }
    }

    /**
     * 存放错误信息
     *
     * @param d
     * @param msg
     * @param rsMsgMap
     */
    private void putErrMsg(Date d, String rmtype, String msg, HashMap<Room_rs, String> rsMsgMap) {
        for (Map.Entry<Room_rs, String> entry : rsMsgMap.entrySet()) {
            Room_rs r = entry.getKey();
            if (entry.getValue().isEmpty()
                    && CalculateDate.isInRange(d, r.getArrdate(), CalculateDate.reckonDay(r.getDeptdate(), 5, -1))
                    && r.getRmtype().equals(rmtype)) {
                rsMsgMap.put(r, msg);//
            }
        }
    }

    public boolean checkSiteSkuAvl(Integer pickup, String sitecode, String periodcode, Date date, String projectId) throws DefinedException {
        String jpql = " select  sum(avl) from Racttime  where sitecode=?1 and period=?2 and datum =?3 and projectid=?4";
        int count = daoLocal.getCountOption(jpql, sitecode, periodcode, date, projectId);
        if (count - pickup < 0) {
            throw new DefinedException("抱歉,该预约时段预约名额不足",
                    ResultCode.OVERBOOK_ERR.code());
        }
        return true;
    }

    /**
     * 更新伴手礼商品库存日期
     *
     * @param projectId
     * @param generateDate
     */
    public void generateRgiftitemResource(String projectId, Date generateDate) {
        rgiftitemsMapper.updateDate(projectId, generateDate);
    }

    /**
     * 更新景区商品库存日期
     *
     * @param projectId
     * @param generateDate
     */
    public void generateRspuitemResource(String projectId, Date generateDate) {
        rspuitemsMapper.updateDate(projectId, generateDate);
    }

    /**
     * 更新优惠券库存日期
     *
     * @param projectId
     * @param generateDate
     */
    public void generateRcouponResource(String projectId, Date generateDate) {
        rcouponMapper.updateDate(projectId, generateDate);
    }

    /**
     * 优惠券最大库存日期
     *
     * @param projectid
     * @return
     */
    public Date getRcouponMaxDate(String projectid) {
        Date maxDate = rcouponMapper.findRcouponAvlMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }
}
