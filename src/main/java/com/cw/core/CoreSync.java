package com.cw.core;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.core.func.order.OrderReqTransFactory;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.CrmHandler;
import com.cw.core.vendor.crm.CrmVendorHandler;
import com.cw.entity.App_user;
import com.cw.entity.Spu_rs;
import com.cw.entity.Vendorconfig;
import com.cw.entity.Waitlistcoupon;
import com.cw.exception.DefinedException;
import com.cw.mapper.common.DaoLocal;
import com.cw.outsys.stdop.request.crm.StdAddCustomerRequest;
import com.cw.outsys.stdop.request.crm.StdUpdCustomerRequest;
import com.cw.outsys.stdop.response.crm.StdAddCustomerResponse;
import com.cw.outsys.stdop.response.crm.StdUpdCustomerResponse;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.VendorType;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

/**
 * 订单逻辑处理
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/20 10:10
 **/
@Slf4j
@Service
public class CoreSync {

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    CrmHandler crmHandler;

    @Autowired
    OrderReqTransFactory orderReqTransFactory;

    @Autowired
    DaoLocal<?> daoLocal;

    @Async("commonPool")
    public void notifyOt(App_user appUser, String loginappid) {
        if (StrUtil.isBlank(loginappid)) {
            return;
        }
        VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig simpSupConfig = vendorConfigCache.getVendorConfig(appUser.getProjectid(), VendorType.SIMP_SUP);
        if (simpSupConfig != null && !simpSupConfig.getNotifyurl().isEmpty()) {
            sendSimpInfo(simpSupConfig.getNotifyurl(), loginappid, simpSupConfig.getAppid(), appUser);
        }


//        Vendorconfig crmConfig=
    }

    /**
     * @param appUser
     * @return
     */
    public String getCrmNo(App_user appUser) {
        CrmVendorHandler crmVendorHandler = crmHandler.getVendorHandler(appUser.getProjectid());
        if (crmVendorHandler == null) {
            return "";
        } else {
            StdAddCustomerRequest addCustomerRequest = new StdAddCustomerRequest();
            addCustomerRequest.setCustomer_name(appUser.getUsername().isEmpty() ? appUser.getNickname() : appUser.getUsername());
            addCustomerRequest.setPhone(appUser.getMobileno());
            addCustomerRequest.setProjectId(appUser.getProjectid());
            addCustomerRequest.setShop_no(SysFuncLibTool.getCrmShopNo(appUser.getProjectid()));
            addCustomerRequest.setUnionid(appUser.getWxunionid());
            addCustomerRequest.setGender(appUser.getGender());

            try {
                StdAddCustomerResponse response = crmVendorHandler.addCrmMember(addCustomerRequest);
                return response.getCrmno();
            } catch (DefinedException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    @Async("commonPool")
    public void updCrm(App_user appUser) {
        if (appUser.getMemberid().isEmpty()) {
            return;
        }
        CrmVendorHandler crmVendorHandler = crmHandler.getVendorHandler(appUser.getProjectid());
        try {
            StdUpdCustomerRequest updCustomerRequest = new StdUpdCustomerRequest();
            updCustomerRequest.setProjectId(appUser.getProjectid());
            updCustomerRequest.setBirthday(appUser.getBirthday());
            updCustomerRequest.setCustomer_name(appUser.getUsername());//暂时只更新两个用于演示的字段
            StdUpdCustomerResponse response = crmVendorHandler.updCrmMember(updCustomerRequest);

        } catch (Exception e) {
            e.printStackTrace();

        }
    }



    private void sendSimpInfo(String url, String appid, String supappid, App_user appUser) {
        HashMap<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("appId", appid + "@" + supappid);
        paramMap.put("openId", appUser.getWxopenid());
        paramMap.put("unionId", appUser.getWxunionid());
        paramMap.put("name", appUser.getNickname());
        paramMap.put("headImg", appUser.getLogourl());


        url = url + "?" + SysFuncLibTool.getPostUrlParamStr(paramMap);
//        url="http://ca.wujiangcun.com:22227/WebCall5//weixin/miniUserLogin.do?appId=wxf17f50c99bb1ec14@wx1d9eb9dcae3603d1&openId=oUmL65DO4xKQ-yyNqRGCV7Nq7AMw&unionId=o0A0i6WX1kyKaXs6g-gwdwks1-G0&name=H&headImg=" +
//                "https://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83eolzuFnUX8ibVg8XKibqxAedZHx40gcOibGBdOFhdu5zUOKnUrHRPnQBD3cWY83Y4Ab5SFjCvqJYianww/132";
        try {
            String result = HttpUtil.get(url);
            log.info("注册信息发送到信普飞科客服系统: {}", result);
        } catch (Exception e) {
            log.error("发送信息给信普飞科系统失败");
        }

    }

    //@Async("commonPool")
    public void createKitSpucoupon(Waitlistcoupon waitlistcoupon, String uid, String userId, String projectId) {
        StdOrderData stdOrderData = orderReqTransFactory.transSaveWaitListCoupon(waitlistcoupon, uid, userId, projectId);
        if (stdOrderData.getBookingRs() != null) {
            daoLocal.merge(stdOrderData.getBookingRs());
        }
        for (Spu_rs spus : stdOrderData.getSpus()) {
            daoLocal.merge(spus);
        }
        waitlistcoupon.setFinishtime(LocalDateTime.now());
        daoLocal.merge(waitlistcoupon);
        log.info("{}员工发送优惠券{},{}张成功", userId, stdOrderData.getBookingRs().getProduct(), stdOrderData.getSpus().size());
    }

    @Async("commonPool")
    public void regSendCoupon(App_user appUser) {
        if (appUser.getProjectid().equals("005") || appUser.getProjectid().equals("001")) {
            List<Waitlistcoupon> list = daoLocal.getObjectList("from Waitlistcoupon where projectid=?1 and targetmobile=?2 and finishtime <?3",
                    appUser.getProjectid(), appUser.getMobileno(), LocalDateTime.of(1900, 1, 2, 0, 0, 0));
            for (Waitlistcoupon w : list) {
                createKitSpucoupon(w, appUser.getUserid(), w.getOpuser(), appUser.getProjectid());
                log.info("用户延迟领取优惠券{}成功", w.getProdcode());
            }
        }
    }



}
