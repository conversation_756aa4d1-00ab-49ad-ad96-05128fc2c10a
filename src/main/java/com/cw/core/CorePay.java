package com.cw.core;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.pay.*;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.cache.impl.TicketCache;
import com.cw.config.Cwconfig;
import com.cw.config.exception.CustomException;
import com.cw.core.orderhandler.OrderHandler;
import com.cw.core.orderhandler.OrderVendorSwitcher;
import com.cw.core.orderhandler.PayVendorSwitcher;
import com.cw.core.vendor.pay.PayVendorHandler;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.BookingrsMapper;
import com.cw.mapper.PassrsMapper;
import com.cw.mapper.PrepayMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.common.rule.CancelOrderRuleValidData;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushPayPlatformMsg;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushPayWebSocketMsg;
import com.cw.service.mq.msgmodel.bussiness.Bussness_PushTicketMsg;
import com.cw.service.mq.msgmodel.bussiness.Bussness_ReundDuplicateMsg;
import com.cw.utils.*;
import com.cw.utils.enums.OnlinePayType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 订单支付逻辑处理
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/20 10:10
 **/
@Slf4j
@Service
public class CorePay {
    @Autowired
    DaoLocal<?> daoLocal;

    @Autowired
    BookingrsMapper bookingrsMapper;

    @Autowired
    PrepayMapper prepayMapper;

    @Autowired
    PassrsMapper passrsMapper;

    @Autowired
    OrderHandler orderHandler;

    @Autowired
    SeqNoService seqNoService;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    Cwconfig cwconfig;

    @Autowired
    PayVendorSwitcher payVendorSwitcher;

    public void createPassByOrderWithNotify(OnlinePayType onlinePayType, String outTradeNo, String transactionId) {
        RMapCache<String, PassByPayAttachInfo> attachInfoRMapCache = redissonClient.getMapCache(RedisKey.PASSBY_PAY_ATTACHINFO);
        String rskey = outTradeNo;// result.getResult().getOutTradeNo();
        PassByPayAttachInfo attachInfo = attachInfoRMapCache.get(rskey);
        if (attachInfo == null || attachInfo.getBookingid().isEmpty()) {
            log.error("支付回调，获取线下订单预付款信息失败，rskey:{}", rskey);
            //添加机器人报警
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg("支付回调，获取线下订单预付款信息失败，rskey:" + rskey));
            return;
        }

        RLock orderLock = redissonClient.getLock(RedisKey.getOrderLockKey(attachInfo.getProjectId(), attachInfo.getBookingid()));
        try {
            boolean lok = orderLock.tryLock(10, 10, TimeUnit.SECONDS);//等待5秒.
            if (lok) {
                SysConfCache sysConfCache = GlobalCache.getDataStructure().getConfCache();
                String otherId = sysConfCache.getConfYaml(attachInfo.getProjectId()).getSys().getPab_pid();
                String targetId = StrUtil.isEmpty(otherId) ? attachInfo.getProjectId() : otherId;//查看项目配置.空的话.按发起者写入.如果配置有值.就按配置写入.
                String prefix = sysConfCache.getConfYaml(attachInfo.getProjectId()).getSys().getPab_prefix();


                Pass_rs rs = attachInfo.getParseOrder();
                rs.setProjectid(targetId);
                if (StrUtil.isNotBlank(otherId)) {
                    rs.setPayprojectid(attachInfo.getProjectId()); //加了个配置在这里.记录原始支付的项目id
                }

                rs.setPayid(transactionId);//支付宝,微信支付流水号
                rs.setPayno(outTradeNo);//本地支付单号

                rs.setOutid(attachInfo.getOutid()); //线下订单号
                rs.setPayment(onlinePayType.name());
                rs.setCreatedate(LocalDateTime.now());
                rs.setPaytime(LocalDateTime.now());
                rs.setGuestname(attachInfo.getGuestname());
                rs.setMemo(prefix + attachInfo.getMemo());
                rs.setMainstatus(StatusUtil.PassRsStatus.WAIT);
                passrsMapper.saveAndFlush(rs);
                //attachInfoRMapCache.remove(attachInfo.getOutTradeNo()); //内存信息暂时不删除.一日后自动清理.为了方便查询

                log.info("线下订单支付创建成功: 客人姓名{},支付方式{},流水号{},本地支付单号{}", rs.getGuestname(), rs.getPayment(), rs.getPayid(), rs.getPayno());
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }


    }

    public void createPrepayWithNotify(OnlinePayType onlinePayType, String outTradeNo, String transactionId) {
//        PayAttachInfo attachInfo = JSON.parseObject(result.getResult().getAttach(), PayAttachInfo.class);

        RMapCache<String, PayAttachInfo> attachInfoRMapCache = redissonClient.getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = outTradeNo;// result.getResult().getOutTradeNo();
        PayAttachInfo attachInfo = attachInfoRMapCache.get(rskey);

        if (attachInfo == null || attachInfo.getBookingids().isEmpty()) {
            log.error("支付回调，获取预付款信息失败，rskey:{}", rskey);
            //添加机器人报警
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR,
                    RobotUtils.transRobotExceptionMsg("支付回调，获取预付款信息失败，rskey:" + rskey));
            return;
        }

        for (String bookingId : attachInfo.getBookingids()) {
            RLock orderLock = redissonClient.getLock(RedisKey.getOrderLockKey(attachInfo.getProejectid(), bookingId));
            try {
                boolean lok = orderLock.tryLock(10, 10, TimeUnit.SECONDS);//等待5秒.
                if (lok) { //拿到锁.才能创建预付款.避免重复收通知.

                    OrderSenseNotifyDataHandler<?> orderSenseNotifyDataHandler = OrderVendorSwitcher.getSenseHandler(attachInfo.getScene(), bookingId);
                    orderSenseNotifyDataHandler.initData();

                    if (CalculateNumber.isZero(orderSenseNotifyDataHandler.getOrderAmount())) {//如果是免费订单.不做预付款创建.这单多数属于购物车的合并支付.
                        continue;
                    }

                    if (orderSenseNotifyDataHandler.isCancelStatus()) {//bookingRs.getMainstatus().equals(StatusUtil.BookingRsStatus.CANCEL)
                        log.warn("订单已经被取消了,不能创建预付款,bookingid:{} 走自动取消", orderSenseNotifyDataHandler.getBookingId());
                    }
                    Prepay prepay = daoLocal.getObject("from Prepay where bookingid=?1 ", bookingId);
                    boolean dorefund = orderSenseNotifyDataHandler.isCancelStatus();//2022.5.5 已经支付成功生成预付款的.不再处理.不做自动退款
                    if (prepay != null) {
                        dorefund = !prepay.getSerialno().equals(transactionId);//接收到的支付流水号和预付款流水号不一致.说明是多余的支付.需要退款
                        if (!dorefund) {
                            log.info("预付款已经创建.跳过付款成功通知处理{}", bookingId);
                        }

                    }
                    if (dorefund) {//订单已经被超时取消.或者客户端产生了多余支付
                        //if (prepay != null) {
                        //    log.info("预付款已经创建.准备取消多余付款{}", bookingId);
                        //}
                        if (orderSenseNotifyDataHandler.isCancelStatus()) {
                            log.info("订单已经取消.准备自动退款");
                        } else {
                            log.info("预付款已经创建.准备取消多余付款{}-{}", onlinePayType.getDesc(), transactionId);
                        }

                        Bussness_ReundDuplicateMsg refundDuplicateMsg = new Bussness_ReundDuplicateMsg();
                        refundDuplicateMsg.setBookingId(bookingId);
                        refundDuplicateMsg.setProjectId(attachInfo.getProejectid());
                        refundDuplicateMsg.setUid(orderSenseNotifyDataHandler.getOrderUid());
                        refundDuplicateMsg.setOnlinePayType(onlinePayType.name());
                        refundDuplicateMsg.setTotal(attachInfo.getTotalPay());
                        refundDuplicateMsg.setPayAmount(orderSenseNotifyDataHandler.getOrderAmount());
                        refundDuplicateMsg.setOrgTransactionId(transactionId);//result.getResult().getTransactionId()
                        refundDuplicateMsg.setOutTradeNo(outTradeNo);//result.getResult().getOutTradeNo()
                        refundDuplicateMsg.setPayscene(attachInfo.getScene());

//                        refundDuplicateMsg.setTotal(result.getResult().getAmount().getTotal());
//                        refundDuplicateMsg.setOnlinePayType();

                        rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.REFUNDRETRY),
                                JSON.toJSONString(refundDuplicateMsg));

                        orderLock.unlock();
                        continue;
                    }

                    //判断当前还没支付成功.创建预付款
                    if (prepay == null) {
                        prepay = new Prepay();
                    }
                    prepay.setBookingid(orderSenseNotifyDataHandler.getBookingId());
                    prepay.setAmount(orderSenseNotifyDataHandler.getOrderAmount());
                    prepay.setProjectid(attachInfo.getProejectid());
                    prepay.setUid(orderSenseNotifyDataHandler.getOrderUid());
                    prepay.setCreatetime(LocalDateTime.now());
                    prepay.setPaymethod(onlinePayType.name());
                    prepay.setPaytime(LocalDateTime.now());
                    prepay.setOrgamount(attachInfo.getTotalPay());//合并支付的总金额
                    prepay.setSerialno(transactionId); //result.getResult().getTransactionId()厂商的支付流水号
                    prepay.setPayno(attachInfo.getOutTradeNo());//本地预付请求流水号 可能是多个订单的合并支付.合并支付的就是同一个

                    prepay.setScene(attachInfo.getScene());//支付场景.给对应的业务做支付
                    daoLocal.merge(prepay);

                    orderSenseNotifyDataHandler.saveOrderPayStatus(transactionId, attachInfo.getOutTradeNo(), onlinePayType.name());

                    log.info("支付成功 订单号{}金额 {}", orderSenseNotifyDataHandler.getBookingId(), orderSenseNotifyDataHandler.getOrderAmount());

                    try {//TODO 重复推送支付成功消息
                        orderSenseNotifyDataHandler.dispatchOrderPaySuccessEvent(rabbitTemplate, prepay, attachInfo.isLnotify());
//                        dispatchOrderPrePay(bookingRs, prepay, attachInfo.isLnotify());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    orderLock.unlock();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }

        attachInfoRMapCache.remove(attachInfo.getOutTradeNo());
    }


    public void writeRefundInfoWithNotify(OnlinePayType onlinePayType, String outReundNo) {

        RMapCache<String, RefundAttachInfo> attachInfoRMapCache = redissonClient.getMapCache(RedisKey.REFUND_ATTACHINFO);
        RefundAttachInfo attachInfo = attachInfoRMapCache.get(outReundNo);
        if (attachInfo != null) {
            attachInfoRMapCache.remove(outReundNo);
            if (!attachInfo.isLduplicate()) {
                Prepay prepay = prepayMapper.findByBookingid(attachInfo.getBookingid(), attachInfo.getProejectid());
                prepay.setRefundtime(LocalDateTime.now());
                prepay.setRefund(prepay.getRefund().add(attachInfo.getRefundAmount()));
                prepayMapper.save(prepay);
                if (attachInfo.getPayscene() == 0) {
                    daoLocal.batchOption("update Booking_rs set refundtime=?1 where bookingid=?2 and projectid=?3", LocalDateTime.now(),
                            prepay.getBookingid(), prepay.getProjectid()); //更新订单的退款时间
                } else {
                    daoLocal.batchOption("update Act_rs set refundtime=?1 where bookingid=?2 and projectid=?3", LocalDateTime.now(),
                            prepay.getBookingid(), prepay.getProjectid()); //更新预约订单的退款时间
                }
                log.info("收到" + onlinePayType.getDesc() + "处理退款并更新退款时间");
            } else {
                log.info("收到" + onlinePayType.getDesc() + "退款通知 {} 处理订单多余支付退款成功", attachInfo.getBookingid());
            }

        }

    }

    public void writePassByRefundInfoWithNotify(OnlinePayType onlinePayType, String outReundNo) {
        RMapCache<String, PassByPayRefundAttachInfo> attachInfoRMapCache = redissonClient.getMapCache(RedisKey.PASSBY_REFUND_ATTACHINFO);
        PassByPayRefundAttachInfo attachInfo = attachInfoRMapCache.get(outReundNo);
        if (attachInfo != null) {
            attachInfoRMapCache.remove(outReundNo);
            if (!attachInfo.isLduplicate()) {
                Pass_rs rs = passrsMapper.findByBookingid(attachInfo.getBookingid(), attachInfo.getProejectid());
                rs.setRefundtime(LocalDateTime.now());
                passrsMapper.saveAndFlush(rs);
                log.info("收到" + onlinePayType.getDesc() + "处理线下订单" + rs.getBookingid() + "的退款并更新退款时间");
            } else {
                log.info("收到" + onlinePayType.getDesc() + "退款通知 {} 处理订单多余支付退款成功", attachInfo.getBookingid());
            }

        }

    }


    /**
     * 将预付款信息发送到所有需要通知的子系统
     *
     * @param prepay
     */
    public void dispatchOrderPrePay(Booking_rs rs, Prepay prepay, boolean lnotifyWebsocket) throws DefinedException {
        if (rs.getPtype().equals(ProdType.ROOM.val()) || rs.getPtype().equals(ProdType.TAOCAN.val())) {//TODO 套餐里可能含有客房产品
            Bussness_PushPayPlatformMsg platformMsg = new Bussness_PushPayPlatformMsg();
            platformMsg.setProjectId(rs.getProjectid());
            platformMsg.setBookingId(rs.getBookingid());
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.PUSHPAYINFO),
                    JSON.toJSONString(platformMsg));
        }
        if (rs.getPtype().equals(ProdType.TICKET.val())) {
            //TODO 判断子订单包含有票务
            TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
            Ticket ticket = ticketCache.getRecord(rs.getProjectid(), rs.getProduct());

            if (!ticket.getOutcode().isEmpty()) {
                Bussness_PushTicketMsg ticketMsg = new Bussness_PushTicketMsg();
                ticketMsg.setBookingId(rs.getBookingid());
                ticketMsg.setProjectId(rs.getProjectid());
                rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.SENDTICKETQR),
                        JSON.toJSONString(ticketMsg));

            }
        }
        if (lnotifyWebsocket) {//二维码之类的websocket  才广播通知
            Bussness_PushPayWebSocketMsg msg = new Bussness_PushPayWebSocketMsg();
            msg.setProjectId(rs.getProjectid());
            msg.setBookingId(rs.getBookingid());
            //发送队列消息
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), MqNameUtils.getMallBussinessSignal(MqNameUtils.BussinessTask.PAYINFOWEB),
                    JSON.toJSONString(msg));
            log.info("发送websocket推送消息:{}", JSON.toJSONString(msg));
        }

//        StdPayRequest request = new StdPayRequest();
//        request.setBookingId(rs.getBookingid());
//        request.setProjectId(rs.getProjectid());
//
//        StdPay_payNode paynode = new StdPay_payNode();
//        paynode.setAmount(prepay.getAmount());
//        paynode.setPayid(prepay.getPayno());
//        request.getPayinfo().add(paynode);
//        orderHandler.payOrder(request);
    }

    public void refundPay(Prepay prepay, CancelOrderRuleValidData cancelOrderRuleValidData) {
        if (prepay == null) {
            return;
        }
        String seqNo = seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);//生成一个流水号
        StdRefundParams stdRefundParams =
                new StdRefundParams(prepay, cancelOrderRuleValidData, cwconfig.getDomain(), seqNo);
        stdRefundParams.setPayscene(prepay.getScene());

        OnlinePayType payType = EnumUtil.fromString(OnlinePayType.class, prepay.getPaymethod());
        VendorType vendorType = payType.getVendorType();
        PayVendorHandler payVendorHandler = payVendorSwitcher.getVendorHandler(vendorType);
        try {
            payVendorHandler.refundPay(stdRefundParams);
        } catch (DefinedException e) {
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("发起" + payType.getDesc() + "退款失败"));
        }


    }

    /**
     * 后台给线下支付单退款
     *
     * @param passRs
     */
    public void refundPassByPay(Pass_rs passRs) {
        OnlinePayType payType = EnumUtil.fromString(OnlinePayType.class, passRs.getPayment());
        VendorType vendorType = payType.getVendorType();
        PayVendorHandler payVendorHandler = payVendorSwitcher.getVendorHandler(vendorType);
        String seqNo = seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);//生成一个流水号
        StdRefundParams stdRefundParams = new StdRefundParams(passRs, cwconfig.getDomain(), seqNo);
        try {
            payVendorHandler.refundAnonymousPay(stdRefundParams);
        } catch (DefinedException e) {
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("发起" + payType.getDesc() + "退款失败"));

        }
    }

    @Data
    private class RsPayDataStructure {
        int mode = 0;
        private Booking_rs bookingRs;
        private Act_rs actRs;

        public RsPayDataStructure(int mode) {
            this.mode = mode;
        }

        public boolean isAlreadyCancel() {
            if (mode == 0) {
                return bookingRs.getMainstatus().equals(StatusUtil.BookingRsStatus.CANCEL);
            }
            if (mode == 1) {
                return actRs.getStatus().equals(StatusUtil.BookingRsStatus.CANCEL);
            }
            return false;
        }


    }



    /*@Deprecated
    public void refundWxJsApiOrderPay(Prepay prepay, CancelOrderRuleValidData cancelData) {
//        String appid = WebAppGlobalContext.getCurrentAppid();

        if (prepay == null) {
            return;
        }

        String projectid = prepay.getProjectid();

        WxPayService wxPayService = WxPayConfiguration.getProjectPayService(projectid);
        WxPayRefundV3Request refundV3Request = new WxPayRefundV3Request();
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        BigDecimal total = prepay.getOrgamount();  //prepay.getOrgamount();  发起支付/购物车合并支付时的总金额
        BigDecimal refund = cancelData.getRefund();//prepay.getAmount();

        amount.setTotal(PayUtil.Yuan2Fen(total.doubleValue()));//原始支付总金额
        amount.setRefund(PayUtil.Yuan2Fen(refund.doubleValue()));//退款金额
        amount.setCurrency(PayUtil.DEFAULT_CURRENCY);
        refundV3Request.setAmount(amount);

        log.info("即将对订单做退款 原支付金额 {} 现在退款 {}", amount.getTotal(), amount.getRefund());

        RefundAttachInfo refundAttachInfo = new RefundAttachInfo();
        refundAttachInfo.setBookingid(prepay.getBookingid());
        refundAttachInfo.setUid(prepay.getUid());
        refundAttachInfo.setProejectid(prepay.getProjectid());
        refundAttachInfo.setReqTime(LocalDateTime.now().toString());
        refundAttachInfo.setRefundAmount(refund);


        refundV3Request.setTransactionId(prepay.getSerialno());//原支付单号
        refundV3Request.setOutTradeNo(prepay.getPayno());//原商户订单号
        refundV3Request.setOutRefundNo(seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY));//生成一个本地退款流水号
        refundV3Request.setNotifyUrl(cwconfig.getDomain() + "/pay/notify/jsapi/refund/" + wxPayService.getConfig().getAppId());//TODO  通知地址改下前缀


        RMapCache<String, RefundAttachInfo> attachInfoRMapCache = redissonClient.getMapCache(RedisKey.REFUND_ATTACHINFO);
        String rskey = refundV3Request.getOutRefundNo();//退款信息 用单个订单号作为 KEY
        attachInfoRMapCache.put(rskey, refundAttachInfo, 1, TimeUnit.DAYS);

        WxPayRefundV3Result v3Result;
        try {
            v3Result = wxPayService.refundV3(refundV3Request);
        } catch (WxPayException e) {
            log.error("发起微信退款失败");
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("发起微信退款失败"));
        }
    }*/


}
