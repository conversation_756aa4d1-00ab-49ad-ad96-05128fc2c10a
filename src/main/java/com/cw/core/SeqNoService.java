package com.cw.core;

import com.cw.cache.RedisTool;
import com.cw.utils.CalculateDate;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 统一的获取订单号服务
 */
@Service
public class SeqNoService {

    @Autowired
    RedissonClient redissonClient;


    public String getSequenceID(SystemUtil.SequenceKey sequenceKey) {
        return getRedisNewValue(sequenceKey);
    }

    public String getSenseSequenceID(SystemUtil.SequenceKey sequenceKey, Integer sequence) {
        if (sequence == 1) {
            return ProdType.ACTGROUP.val() + getRedisNewValue(sequenceKey);
        } else {
            return getRedisNewValue(sequenceKey);
        }
    }

    public String getSequenceID_withOrderId(SystemUtil.SequenceKey sequenceKey, String orderNo) {
        //先用otaSeqNo 查找是否已经生成了序列号.如果有.不再生成一个新的.
        if (orderNo == null || orderNo.isEmpty()) {
            return getRedisNewValue(sequenceKey);
        }
        RMapCache<String, String> rMapCache = redissonClient.getMapCache(RedisKey.SeqNo_Map);
        String cacheKey = sequenceKey + orderNo;
        String cacheVal = rMapCache.getOrDefault(cacheKey, null);
        if (cacheVal != null) {
            return cacheVal;
        } else {
            cacheVal = getRedisNewValue(sequenceKey);
            rMapCache.put(cacheKey, cacheVal, 12, TimeUnit.HOURS);  //一般是用于支付流水号.不用太长
        }
        return cacheVal;
    }

    private String getRandomStr(int randomMax) {
        Random random = new Random();
        String result = String.valueOf(random.nextInt(randomMax));
        int length = String.valueOf(randomMax).length();
        int resultLen = result.length();
        for (int i = 0; i < length - resultLen; i++) {
            result = "0" + result;
        }
        return result;
    }

    public String getRedisNewValue(SystemUtil.SequenceKey sequenceKey) {
        String day = CalculateDate.dateToString(new Date(), false, CalculateDate.keyspd);
        String key = day + sequenceKey;
        if (Boolean.TRUE.equals(RedisTool.getInstance().opsForValue().setIfAbsent(key, "1"))) {//如果当天还没有值.就初始化为1,从1开始计数
            RedisTool.getInstance().expire(key, 25, TimeUnit.HOURS); //设置失效时间是25小时 .25小时之后就自动释放计数了
        }
        String value = RedisTool.getInstance().opsForValue().increment(key, 1).toString();
        int length = value.length();
        if (length < 9) {
            Random random = new Random();
            String zero = "";
            for (int i = 0; i < 9 - length; i++) {
                zero = "0" + zero;
            }
            int suijilength = zero.length() / 2;
            String suijistr = "";
            for (int i = 0; i < suijilength; i++) {
                suijistr = suijistr + random.nextInt(9);
            }
            if (suijilength < zero.length()) {
                zero = suijistr + zero.substring(suijilength);
            }
            value = zero + value;
        }
        return day + value;
    }


}
