package com.cw.core.orderhandler;

import cn.hutool.core.lang.ClassScanner;
import com.cw.config.exception.CustomException;
import com.cw.core.vendor.crm.CrmVendorHandler;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-28
 */
@Slf4j
@Component
public class CrmVendorSwitcher {
    public HashMap<VendorType, CrmVendorHandler> handlerFacotry = new HashMap();

    @PostConstruct
    protected void scanPackage() {
        //根据 包的路径.将请求类加载到map 缓存
        ClassScanner.scanAllPackageByAnnotation("com.cw.core.vendor.crm", VendorAdapter.class).forEach(clazz -> {
            VendorType vendorType = clazz.getAnnotation(VendorAdapter.class).vendorType();
            try {
                handlerFacotry.put(vendorType, (CrmVendorHandler) clazz.newInstance());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        log.info(" CRM适配厂商大小--> {}", handlerFacotry.size());
    }

    public void initHandlerClient() {
        handlerFacotry.forEach((k, v) -> {
            v.initClient();
        });
    }


    public CrmVendorHandler getVendorHandler(VendorType type) {
        if (type.equals(VendorType.LOCAL)) {
            return null;
        }
        if (handlerFacotry.get(type) == null) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("厂商还未开发适配"));
        }
        return handlerFacotry.get(type);
    }
}
