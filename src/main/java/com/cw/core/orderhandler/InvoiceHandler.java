package com.cw.core.orderhandler;

import cn.hutool.core.util.EnumUtil;
import com.cw.cache.GlobalCache;
import com.cw.core.vendor.invoice.InvoiceVendorHandler;
import com.cw.entity.Sysconf;
import com.cw.exception.DefinedException;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-02-28
 */
@Slf4j
@Component
public class InvoiceHandler {
    @Autowired
    InvoiceVendorSwitcher switcher;

    private VendorType getHandlerVendorType(String projectId) {
        Sysconf sysconf = GlobalCache.getDataStructure().getConfCache().getOne(projectId);
        if (sysconf == null) {
            return VendorType.LOCAL;
        }
        return EnumUtil.fromString(VendorType.class, sysconf.getInvoicevendor(), VendorType.LOCAL);
    }

    /**
     * 获取指定产品类型的厂商接口处理器实例.例如这个projectid 的票务厂商是深大.就返回该实例
     *
     * @param projectId 项目ID
     * @return 如果是LOCAL.回返回NULL .自行判断
     */
    public InvoiceVendorHandler getVendorHandler(String projectId) {
        VendorType type = getHandlerVendorType(projectId);
        InvoiceVendorHandler vendorHandler = switcher.getVendorHandler(type);
        return vendorHandler;
    }


    /**
     *  开具发票
     * @param invoiceInfo
     * @return
     * @throws DefinedException
     */
    //public StdInvoiceCreateResponse InvoiceCreate(InvoiceInfo invoiceInfo) throws DefinedException {
    //    String projectId = WebAppGlobalContext.getCurrentAppProjectId();
    //    InvoiceVendorHandler invoiceVendorHandler = getVendorHandler(projectId);
    //    if (invoiceVendorHandler != null) {
    //        StdInvoiceCreateRequest request = new StdInvoiceCreateRequest();
    //        //填充参数
    //        //request.setTitle();
    //        request.setProjectId(projectId);
    //        StdInvoiceCreateResponse stdInvoiceCreateResponse = invoiceVendorHandler.InvoiceCreate(request);
    //        return stdInvoiceCreateResponse;
    //    }
    //    return new StdInvoiceCreateResponse();
    //}


    /**
     * 查询发票
     * @param stdInvoiceQueryRequest
     * @return
     * @throws DefinedException
     */
    //public StdInvoiceQueryResponse InvoiceQuery(StdInvoiceQueryRequest stdInvoiceQueryRequest) throws DefinedException {
    //    GZInvoiceClient client = getClient(stdInvoiceQueryRequest.getProjectId());
    //    GZInvoiceQueryRequest request =  new GZInvoiceQueryRequest().transfer(stdInvoiceQueryRequest);
    //    GZInvoiceQueryResponse response = client.execute(request);
    //    StdInvoiceQueryResponse stdInvoiceQueryResponse = response.getSysStdResponse();
    //    return stdInvoiceQueryResponse;
    //}

    /**
     * 作废发票
     * @param invoiceCancelRequest
     * @return
     * @throws DefinedException
     */
    //public StdInvoiceCancelResponse InvoiceCancel(InvoiceCancelRequest invoiceCancelRequest) throws DefinedException {
    //    String projectId = WebAppGlobalContext.getCurrentAppProjectId();
    //    InvoiceVendorHandler invoiceVendorHandler = getVendorHandler(projectId);
    //    if (invoiceVendorHandler != null) {
    //        StdInvoiceCancelRequest request = new StdInvoiceCancelRequest();
    //        request.setInvoicecode(invoiceCancelRequest.getInvoicecode());
    //        request.setInvoiceid(invoiceCancelRequest.getInvoiceid());
    //        request.setInvoiceno(invoiceCancelRequest.getInvoiceno());
    //        request.setOperator(invoiceCancelRequest.getOperator());
    //        request.setProjectId(projectId);
    //        StdInvoiceCancelResponse stdInvoiceCancelResponse = invoiceVendorHandler.InvoiceCancel(request);
    //        return stdInvoiceCancelResponse;
    //    }
    //    return new StdInvoiceCancelResponse();
    //}


    /**
     * 发票推送
     * @param stdInvoicePushRequest
     * @return
     * @throws DefinedException
     */
    //public StdInvoicePushResponse InvoicePush(StdInvoicePushRequest stdInvoicePushRequest) throws DefinedException {
    //    GZInvoiceClient client = getClient(stdInvoicePushRequest.getProjectId());
    //    GZInvoicePushRequest request =  new GZInvoicePushRequest().transfer(stdInvoicePushRequest);
    //    GZInvoicePushResponse response = client.execute(request);
    //    StdInvoicePushResponse stdInvoicePushResponse = response.getSysStdResponse();
    //    return stdInvoicePushResponse;
    //}

}
