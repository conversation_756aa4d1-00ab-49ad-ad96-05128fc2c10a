package com.cw.core.orderhandler;

import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.GlobalCache;
import com.cw.core.vendor.sms.SmsVendorHandler;
import com.cw.entity.Sysconf;
import com.cw.exception.DefinedException;
import com.cw.outsys.stdop.request.StdSmsSendMsgRequest;
import com.cw.outsys.stdop.response.StdSmsSendMsgResponse;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:32
 **/
@Slf4j
@Component
public class SmsHandler {

    @Autowired
    SmsVendorSwitcher switcher;

    private VendorType getHandlerVendorType(String projectId) {
        Sysconf sysconf = GlobalCache.getDataStructure().getConfCache().getOne(projectId);
        if (sysconf == null) {
            return VendorType.LOCAL;
        }
        return EnumUtil.fromString(VendorType.class, sysconf.getSmsvendor(), VendorType.LOCAL);
    }

    /**
     * 获取指定产品类型的厂商接口处理器实例.例如这个projectid 的票务厂商是深大.就返回该实例
     *
     * @param projectId 项目ID
     * @return 如果是LOCAL.回返回NULL .自行判断
     */
    public SmsVendorHandler getVendorHandler(String projectId) {
        VendorType type = getHandlerVendorType(projectId);
        SmsVendorHandler vendorHandler = switcher.getVendorHandler(type);
        return vendorHandler;
    }


    public boolean sendSms(String projectid, String mobileNo, String content, String outModalId, Map<String, String> params) throws DefinedException {
        SmsVendorHandler smsVendorHandler = getVendorHandler(projectid);
        if (smsVendorHandler != null) {
            //发送短信
            StdSmsSendMsgRequest sendMsgRequest = new StdSmsSendMsgRequest();
            sendMsgRequest.setProjectId(projectid);
            sendMsgRequest.setMobile(mobileNo);
            sendMsgRequest.setContent(content);
            sendMsgRequest.setTemplate(outModalId);
            sendMsgRequest.setParams(JSON.toJSONString(params));
            StdSmsSendMsgResponse response = smsVendorHandler.sendSmsMsg(sendMsgRequest);
            if (response.isSuccess()) {
                return true;
            } else {
                return false;
            }
        } else {  //未配置短信发送的环境.设置为true
            return true;
        }

    }


}
