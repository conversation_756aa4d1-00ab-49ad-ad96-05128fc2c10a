package com.cw.core.orderhandler;

import cn.hutool.core.util.EnumUtil;
import com.cw.cache.GlobalCache;
import com.cw.config.Cwconfig;
import com.cw.entity.Sysconf;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:32
 **/
@Slf4j
@Component
public class PayHandler {

    @Autowired
    PayVendorSwitcher switcher;

    @Autowired
    Cwconfig cwconfig;


    private VendorType getHandlerVendorType(ProdType type, String projectId) {
        Sysconf sysconf = GlobalCache.getDataStructure().getConfCache().getOne(projectId);
        if (sysconf == null) {
            return VendorType.LOCAL;
        }
        switch (type) {
            case ROOM:
                return EnumUtil.fromString(VendorType.class, sysconf.getRoomvendor(), VendorType.LOCAL);
            case TICKET:
                return EnumUtil.fromString(VendorType.class, sysconf.getTicketvendor(), VendorType.LOCAL);
            case CANYIN:
                return EnumUtil.fromString(VendorType.class, sysconf.getCatervendor(), VendorType.LOCAL);
            case MAIN:
                return EnumUtil.fromString(VendorType.class, sysconf.getColvendor(), VendorType.LOCAL);
            default:
                return VendorType.LOCAL;
        }
    }


}
