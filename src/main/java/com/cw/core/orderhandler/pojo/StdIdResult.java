package com.cw.core.orderhandler.pojo;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.HashMap;

/**
 * 创建或者保存订单的返回结果集.就是一堆对应的 ID
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/10/13 03:15
 **/
@Data
public class StdIdResult {

    private HashMap<String, HashMap<String, String>> ids;
    private HashMap<String, HashMap<String, String>> subids;//这个用来放子系统id

    public StdIdResult() {
        ids = new HashMap<>();
        subids = new HashMap<>();
    }

    public void fillid(String productType, String id, String outid) {
        if (!ids.containsKey(productType)) {
            ids.put(productType, new HashMap<>());
        }
        ids.get(productType).put(id == null ? "" : id, outid);
    }

    public void fillsubid(String productType, String id, String outid) {
        if (!subids.containsKey(productType)) {
            subids.put(productType, new HashMap<>());
        }
        subids.get(productType).put(id == null ? "" : id, outid);
    }

    public String getMyOutId(String productType, String id, String orgoutid) {
        String outid = ids.getOrDefault(productType, Maps.newHashMap()).getOrDefault(id, orgoutid);
        return outid;
    }

    public String getSubOutId(String productType, String id, String orgoutid) {
        String outid = subids.getOrDefault(productType, Maps.newHashMap()).getOrDefault(id, orgoutid);
        return outid;
    }

}
