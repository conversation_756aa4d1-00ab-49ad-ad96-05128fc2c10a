package com.cw.core.orderhandler;

import cn.hutool.core.util.EnumUtil;
import com.cw.cache.GlobalCache;
import com.cw.config.Cwconfig;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.vendor.order.VendorHandler;
import com.cw.entity.Sysconf;
import com.cw.exception.DefinedException;
import com.cw.outsys.stdop.request.StdCancelOrderRequest;
import com.cw.outsys.stdop.request.StdPayRequest;
import com.cw.outsys.stdop.response.StdOrderResponse;
import com.cw.pojo.dto.app.res.AppSingleOrderResult;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.utils.CalculateNumber;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:32
 **/
@Slf4j
@Component
public class OrderHandler {

    @Autowired
    OrderVendorSwitcher switcher;

    @Autowired
    Cwconfig cwconfig;

    /**
     * 获取指定产品类型的厂商接口处理器实例.例如这个projectid 的票务厂商是深大.就返回该实例
     *
     * @param prodType  产品类型
     * @param projectId 项目ID
     * @return 如果是LOCAL.回返回NULL .自行判断
     */
    public VendorHandler getVendorHandler(ProdType prodType, String projectId) {
        VendorType type = getHandlerVendorType(prodType, projectId);
        VendorHandler vendorHandler = switcher.getVendorHandler(type);
        return vendorHandler;
    }


    /**
     * 调用各个厂商接口.创建付款前需要确认的预订
     *
     * @param stdOrderData
     * @return
     * @throws DefinedException
     */
    public AppSingleOrderResult createInitialOrder(StdOrderData stdOrderData) throws DefinedException {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        //解析出获取需要发送数据的第三方厂商..拿到他们的确认号.回填到我们的订单对象. 然后再存库
        List<VendorType> outVendors = getOrderNeedDispatchVendor(stdOrderData, projectId, true, false);
//        log.info("本次订单需要发送的 vendor: {} {}", outVendors, cwconfig.getOrdermode());
        for (VendorType vendorType : outVendors) {//获取所有需要在付款前调用第三方接口确认的接口
            StdOrderResponse stdOrderResponse = switcher.getVendorHandler(vendorType).createOrder(stdOrderData);
        }
        //目前一般只有房需要做马上确认处理
        return new AppSingleOrderResult();
    }

    /**
     * 调用各个厂商接口.创建付款后需要确认的预订.比如购买门票
     *
     * @param stdOrderData
     * @return
     * @throws DefinedException
     */
    public AppSingleOrderResult createPayedOrder(StdOrderData stdOrderData) throws DefinedException {
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        //解析出获取需要发送数据的第三方厂商..拿到他们的确认号.回填到我们的订单对象. 然后再存库
        List<VendorType> outVendors = getOrderNeedDispatchVendor(stdOrderData, projectId, false, false);
        log.info("本次订单需要发送的 vendor: {}", outVendors);

        for (VendorType vendorType : outVendors) {//获取所有需要在付款前调用第三方接口确认的接口
            StdOrderResponse stdOrderResponse = switcher.getVendorHandler(vendorType).createOrder(stdOrderData);
        }
        //目前一般只有房需要做马上确认处理
        return new AppSingleOrderResult();
    }

    public void cancelOrder(StdOrderData stdOrderData) throws DefinedException {
        String projectId = stdOrderData.getBookingRs().getProjectid();// WebAppGlobalContext.getCurrentAppProjectId();
        //解析出获取需要发送数据的第三方厂商..拿到他们的确认号.回填到我们的订单对象. 然后再存库
        List<VendorType> outVendors = getOrderNeedDispatchVendor(stdOrderData, projectId, true, true);
        log.info("本次订单需要发送的 vendor: {}", outVendors);

        for (VendorType vendorType : outVendors) {//获取所有需要在付款前调用第三方接口确认的接口
            StdCancelOrderRequest request = new StdCancelOrderRequest();
            request.setOtaorderid(stdOrderData.getBookingRs().getBookingid());
            if (StringUtils.isNotBlank(stdOrderData.getBookingRs().getOutid())) {
                request.setOutid(stdOrderData.getBookingRs().getOutid());//微票订单号  没有支付的没有
            }
            request.setProjectId(projectId);
            request.setOrderDataContext(stdOrderData);
            if (stdOrderData.getCancelOrderRuleValidData() != null) {
                request.setLincludeDebit(CalculateNumber.isGreaterThanZero(stdOrderData.getCancelOrderRuleValidData().getDebit()));
                request.setPercent(stdOrderData.getCancelOrderRuleValidData().getDebitpercent());
            }
            //TODO 先以中台为准.后面按厂商来填写参数
            switcher.getVendorHandler(vendorType).cancelOrder(request);
        }
    }


    public void payOrder(StdPayRequest request) throws DefinedException {
        //获取所有需要在付款成功后调用厂商付款接口的厂商
        List<VendorType> payNotifyVendors = Arrays.asList(VendorType.ZJPLPATFORM);
        for (VendorType payNotifyVendor : payNotifyVendors) {
            if (switcher != null) {
                switcher.getVendorHandler(payNotifyVendor).payOrder(request);
            }
        }
    }

    /**
     * 获取需要前置锁定资源或者是确认支付之后调用的的接口类型
     *
     * @param stdOrderData 订单数据集合结构
     * @param projectId    项目ID
     * @param lBeforePay   前置传 true  比如客房订单.后置传 false 比如票
     * @param getAll       获取所有订单的接口厂商接口. 一般只有取消订单时需要用到
     * @return 排除 LOCAL??
     */
    List<VendorType> getOrderNeedDispatchVendor(StdOrderData stdOrderData, String projectId, boolean lBeforePay, boolean getAll) {
        Set<VendorType> vendorTypes = new HashSet<>();
        VendorType maintype = getHandlerVendorType(ProdType.MAIN, projectId);// 判断是否需要发到中台
        if (maintype.getLbeforePay().equals(lBeforePay) && (stdOrderData.getRooms().size() > 0
                || stdOrderData.getBookingRs().getPtype().equals(ProdType.TAOCAN.val()))) { //中台目前只接受含房订单
            vendorTypes.add(maintype);
            if (!maintype.equals(VendorType.LOCAL)) {
                return vendorTypes.stream().collect(Collectors.toList());//中台处理模式下.统一全部由中台处理
            }
        }
        if (stdOrderData.getRooms().size() > 0) {
            VendorType type = getHandlerVendorType(ProdType.ROOM, projectId);
            if (type.getLbeforePay().equals(lBeforePay) || getAll) {
                vendorTypes.add(type);
            }
        }
        if (stdOrderData.getTickets().size() > 0) {
            VendorType type = getHandlerVendorType(ProdType.TICKET, projectId);
            if (type.getLbeforePay().equals(lBeforePay) || getAll) {
                vendorTypes.add(type);
            }
        }
        if (stdOrderData.getMeetings().size() > 0) {
            VendorType type = getHandlerVendorType(ProdType.MEETING, projectId);
            if (type.getLbeforePay().equals(lBeforePay) || getAll) {
                vendorTypes.add(type);
            }
        }
        if (stdOrderData.getCaters().size() > 0) {
            VendorType type = getHandlerVendorType(ProdType.CANYIN, projectId);
            if (type.getLbeforePay().equals(lBeforePay) || getAll) {
                vendorTypes.add(type);
            }
        }
        if (stdOrderData.getDiscountList().size() > 0) {
            VendorType type = getHandlerVendorType(ProdType.DISCOUNT, projectId);
            if (type.getLbeforePay().equals(lBeforePay) || getAll) {
                vendorTypes.add(type);
            }
        }
        return vendorTypes.stream().filter(t -> !t.equals(VendorType.LOCAL)).collect(Collectors.toList());
    }


    private VendorType getHandlerVendorType(ProdType type, String projectId) {
        Sysconf sysconf = GlobalCache.getDataStructure().getConfCache().getOne(projectId);
        if (sysconf == null) {
            return VendorType.LOCAL;
        }
        switch (type) {
            case ROOM:
                return EnumUtil.fromString(VendorType.class, sysconf.getRoomvendor(), VendorType.LOCAL);
            case TICKET:
                return EnumUtil.fromString(VendorType.class, sysconf.getTicketvendor(), VendorType.LOCAL);
            case CANYIN:
                return EnumUtil.fromString(VendorType.class, sysconf.getCatervendor(), VendorType.LOCAL);
            case MAIN:
                return EnumUtil.fromString(VendorType.class, sysconf.getColvendor(), VendorType.LOCAL);
            default:
                return VendorType.LOCAL;
        }
    }


}
