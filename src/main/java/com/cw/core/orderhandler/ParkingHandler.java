package com.cw.core.orderhandler;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.config.confyaml.ConfYaml;
import com.cw.core.vendor.parking.ParkingVendorHandler;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:32
 **/
@Slf4j
@Component
public class ParkingHandler {

    @Autowired
    ParkingVendorSwitcher switcher;

    private VendorType getHandlerVendorType(String projectId) {

        SysConfCache cache = GlobalCache.getDataStructure().getConfCache();
        ConfYaml confYaml = cache.getConfYaml(projectId);
        String type = confYaml.getSys().getParkingsys_type();

        if (StrUtil.isBlank(confYaml.getSys().getParkingsys_type())) {
            return VendorType.LOCAL;
        }
        return EnumUtil.fromString(VendorType.class, type, VendorType.LOCAL);
    }

    /**
     * 获取指定产品类型的厂商接口处理器实例.例如这个projectid 的票务厂商是深大.就返回该实例
     *
     * @param projectId 项目ID
     * @return 如果是LOCAL.回返回NULL .自行判断
     */
    public ParkingVendorHandler getVendorHandler(String projectId) {
        VendorType type = getHandlerVendorType(projectId);
        ParkingVendorHandler vendorHandler = switcher.getVendorHandler(type);
        return vendorHandler;
    }


}
