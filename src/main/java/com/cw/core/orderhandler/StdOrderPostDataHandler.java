package com.cw.core.orderhandler;

import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.pojo.StdIdResult;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/20 17:14
 **/
public class StdOrderPostDataHandler {
    private StdOrderData dataStructure;

    public StdOrderPostDataHandler(StdOrderData orderData) {
        this.dataStructure = orderData;
    }

    public void applyStdIdResult(StdIdResult stdIdResult) {
//        if (stdIdResult == null) { //如果关闭接口仅仅测试
//            return;
//        }
//        if (dataStructure.getBookingRs() != null) {
//            dataStructure.getBookingRs().set(stdIdResult.getMyOutId(SystemUtil.ProductType.COLRES,
//                    dataStructure.getCol_rs().getColno(), dataStructure.getCol_rs().getPmscolno()));
//        }
//        for (Room_rs room : dataStructure.getRooms()) {
//            room.setPmsno(stdIdResult.getMyOutId(SystemUtil.ProductType.ROOM, room.getRegno(), room.getPmsno()));
//        }
//        for (Cater_rs cater : dataStructure.getCaters().values()) {
//            cater.setPmsno(stdIdResult.getMyOutId(SystemUtil.ProductType.CANYIN, cater.getBookingid(), cater.getPmsno()));
//            //TODO interfaceid填pos号
////            cater.setInterfaceid(stdIdResult.getMyOutId(SystemUtil.ProductType.CANYIN, cater.getBookingid()));
//        }
//        for (Ticket_rs ticket : dataStructure.getTickets().values()) {
//            ticket.setPmsno(stdIdResult.getMyOutId(SystemUtil.ProductType.TICKET, ticket.getBookingid(), ticket.getPmsno()));
//            if(dataStructure.getCol_rs()!=null){
//                ticket.setInterfaceid(stdIdResult.getSubOutId(SystemUtil.ProductType.TICKET, dataStructure.getCol_rs().getColno(), ticket.getInterfaceid()));
//            }
//        }
    }


}
