package com.cw.core.orderhandler;


import com.cw.utils.enums.VendorType;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/9/6 11:25
 **/
@Target(ElementType.TYPE)  //应用于接口,类.枚举.注解
@Retention(RetentionPolicy.RUNTIME)  //反射的时候能够获取
@Documented              //允许 JAVA DOC 生成描述
@Inherited                    //允许子类继承使用
public @interface VendorAdapter {

    VendorType vendorType(); //加载厂商的适配器


}
