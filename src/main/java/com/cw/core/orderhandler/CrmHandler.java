package com.cw.core.orderhandler;

import cn.hutool.core.util.EnumUtil;
import com.cw.cache.GlobalCache;
import com.cw.core.vendor.crm.CrmVendorHandler;
import com.cw.entity.Sysconf;
import com.cw.exception.DefinedException;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:32
 **/
@Slf4j
@Component
public class CrmHandler {

    @Autowired
    CrmVendorSwitcher switcher;

    private VendorType getHandlerVendorType(String projectId) {
        Sysconf sysconf = GlobalCache.getDataStructure().getConfCache().getOne(projectId);
        if (sysconf == null) {
            return VendorType.LOCAL;
        }
        return EnumUtil.fromString(VendorType.class, sysconf.getCrmvendor(), VendorType.LOCAL);
    }

    /**
     * 获取指定产品类型的厂商接口处理器实例.例如这个projectid 的票务厂商是深大.就返回该实例
     *
     * @param projectId 项目ID
     * @return 如果是LOCAL.回返回NULL .自行判断
     */
    public CrmVendorHandler getVendorHandler(String projectId) {
        VendorType type = getHandlerVendorType(projectId);
        CrmVendorHandler vendorHandler = switcher.getVendorHandler(type);
        return vendorHandler;
    }


    public boolean regCrmUser(String projectid) throws DefinedException {
        CrmVendorHandler crmVendorHandler = getVendorHandler(projectid);

        return true;
    }


}
