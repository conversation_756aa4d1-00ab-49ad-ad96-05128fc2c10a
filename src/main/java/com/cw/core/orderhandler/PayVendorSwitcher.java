package com.cw.core.orderhandler;

import cn.hutool.core.lang.ClassScanner;
import com.cw.config.exception.CustomException;
import com.cw.core.vendor.pay.PayVendorHandler;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;

/**
 * 外部厂商路由器.提交业务给第三方确认处理
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:59
 **/
@Slf4j
@Component
public class PayVendorSwitcher {

    public HashMap<VendorType, PayVendorHandler> handlerFacotry = new HashMap();

    @PostConstruct
    protected void scanPackage() {
        //根据 包的路径.将请求类加载到map 缓存
        ClassScanner.scanAllPackageByAnnotation("com.cw.core.vendor.pay", VendorAdapter.class).forEach(clazz -> {
            VendorType vendorType = clazz.getAnnotation(VendorAdapter.class).vendorType();
            try {
                handlerFacotry.put(vendorType, (PayVendorHandler) clazz.newInstance());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        log.info(" 支付适配厂商大小--> {}", handlerFacotry.size());
    }

    public void initHandlerClient() {
        handlerFacotry.forEach((k, v) -> {
            v.initClient();
        });
    }


    public PayVendorHandler getVendorHandler(VendorType type) {
        if (type.equals(VendorType.LOCAL)) {
            return null;
        }
        if (handlerFacotry.get(type) == null) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("厂商还未开发适配"));
        }
        return handlerFacotry.get(type);
    }

//    public StdIdResult createColOrder(StdOrderData structure) throws DefinedException {
//        return getHandler().createColOrder(structure);
//    }
//
//    public StdIdResult createRoomOrder(StdOrderData structure) throws DefinedException {
//        return getHandler().createRoomOrder(structure);
//    }
//
//    public StdIdResult createTicketRs(StdOrderData structure) throws DefinedException {
//        return getHandler().createTicketOrder(structure);
//    }
//
//    public StdIdResult createCaterOrder(StdOrderData structure) throws DefinedException {
//        return getHandler().createCaterOrder(structure);
//    }
//
//    public void createPrepay(StdOrderData structure) throws DefinedException { //支付
//        getHandler().createPrepay(structure);
//    }
//
//    public void cancelPrepay(StdOrderData structure) throws DefinedException { //付款
//        getHandler().cancelPrepay(structure);
//    }
//
//    public void cancelRefund(StdOrderData structure) throws DefinedException { //撤销退款
//        getHandler().cancelRefund(structure);
//    }
//
//    public void cancelOrder(StdOrderData structure) throws DefinedException { //中台订单整单取消
//        getHandler().cancelColOrder(structure);
//    }
//
//    public void cancelRoomRs(StdOrderData structure) throws DefinedException {
//        getHandler().cancelRoomOrder(structure);//TODO
//    }
//
//    public void cancelTicketOrder(StdOrderData structure) throws DefinedException {
//        getHandler().cancelTicketOrder(structure);
//    }
//
//    public void cancelCaterRs(StdOrderData structure) throws DefinedException {
//        getHandler().cancelCaterOrder(structure);
//    }

}
