package com.cw.core;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.func.Var;
import com.cw.arithmetic.others.CodeDetail;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.*;
import com.cw.core.func.order.OrderReqTransFactory;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.*;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.dto.app.req.node.CombineInfo;
import com.cw.pojo.dto.app.res.node.KitProductItem;
import com.cw.pojo.sqlresult.*;
import com.cw.utils.CalculateDate;
import com.cw.utils.CalculateNumber;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.PostageRule;
import com.cw.utils.enums.ProdType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import static com.cw.utils.SystemUtil.DEFAULTRATECODE;
import static com.cw.utils.SystemUtil.DEFAULTUSERID;

/**
 * 产品价格获取
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/20 10:10
 **/
@Slf4j
@Service
public class CorePrice {

    @Autowired
    RratedetMapper rratedetMapper;
    @Autowired
    TratedetMapper tratedetMapper;
    @Autowired
    KitratedetMapper kitratedetMapper;
    @Autowired
    SpuratedetMapper spuratedetMapper;

    @Autowired
    ActratedetMapper actratedetMapper;

    @Autowired
    GiftratedetMapper giftratedetMapper;
    @Autowired
    OrderReqTransFactory transFactory;

    @Autowired
    RedisTemplate redisTemplate;

    /**
     * 返回当前产品使用的 RATECODE
     *
     * @param projectId
     * @return
     */
    public static String getRateCode(String projectId, String productCode) {
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        Sysconf sysConf = sysConfCache.getOne(projectId);
        if (sysConf != null && StringUtils.isNotBlank(sysConf.getRatecode())) {
            return sysConf.getRatecode();
        } else {
            return DEFAULTRATECODE;
        }
    }

    public void writeRoomPrice(StdOrderData stdOrderData, Room_rs rs, String kitcode, String actcode, boolean lcheckConfig)
            throws DefinedException {
//        int len=CalculateDate.compareDates(rs.getDeptdate(),rs.getArrdate()).intValue();
        Date calcEnd = CalculateDate.reckonDay(rs.getDeptdate(), 5, -1); //暂时不考虑日用房当天来当天走
        List<Produce_calcpricePo> prices = calcPrice_DB(rs.getProjectid(), ProdType.ROOM, rs.getRmtype(),
                rs.getArrdate(), calcEnd, 1, 0, 0, "", kitcode, actcode, "", lcheckConfig);
        BigDecimal total = BigDecimal.ZERO;
        for (Produce_calcpricePo po : prices) {
            total = po.getPrice().add(total);
            stdOrderData.getRoomPriceTable().put(rs.getRegno(), CalculateDate.dateToString(po.getDate()), po.getPrice());
        }
        rs.setAmount(total.multiply(BigDecimal.valueOf(rs.getAnz())));
    }

    public void writeTicketPrice(Ticket_rs rs, String kitcode, String actcode, boolean lcheckConfig)
            throws DefinedException {
        List<Produce_calcpricePo> prices = calcPrice_DB(rs.getProjectid(), ProdType.TICKET, rs.getTcode(),
                rs.getUsedate(), rs.getUsedate(), 1, 0, 0, "", kitcode, actcode, "", lcheckConfig);
        BigDecimal total = BigDecimal.ZERO;
        for (Produce_calcpricePo po : prices) {
            total = po.getPrice().add(total);
        }
        if (prices.size() > 0) {
            rs.setPrice(prices.get(0).getPrice());//单价
        }
        rs.setAmount(total.multiply(BigDecimal.valueOf(rs.getAnz())));
    }

    public void writeCaterPrice(Cater_rs rs, String kitcode, String actcode, boolean lcheckConfig) throws DefinedException {
        List<Produce_calcpricePo> prices = calcPrice_DB(rs.getProjectid(), ProdType.CANYIN, rs.getCcode(),
                rs.getUsedate(), rs.getUsedate(), 1, 0, 0, "", kitcode, actcode, "", lcheckConfig);
        BigDecimal total = BigDecimal.ZERO;
        for (Produce_calcpricePo po : prices) {
            total = po.getPrice().add(total);
        }
        if (prices.size() > 0) {
            rs.setPrice(prices.get(0).getPrice());//单价
        }
        rs.setAmount(total.multiply(BigDecimal.valueOf(rs.getAnz(0))));

    }

    public BigDecimal writeGiftPrice(StdOrderData stdOrderData, List<Gift_rs> rsList, String kitcode, String actcode, String projectId) throws DefinedException {
        //List<Produce_calcpricePo> prices = calcPrice_DB(rs.getProjectid(), ProdType.CANYIN, rs.getCcode(),
        //        rs.getUsedate(), rs.getUsedate(), 1, 0, 0, "", kitcode, actcode, "", lcheckConfig);
        String ratecode = getRateCode(projectId, "");
        List<Produce_ratesqueryPo> prices = null;
        List<String> querys = rsList.stream().map(r -> r.getCode()).collect(Collectors.toList());
        if (querys.size() == 0) {
            return BigDecimal.ZERO;
        }
        prices = giftratedetMapper.calcCombiePrice(projectId, ratecode, querys);
        BigDecimal totalprice = BigDecimal.ZERO;

        if (CollectionUtil.isNotEmpty(prices)) {
            Map<String, BigDecimal> priceInfo = prices.stream().collect(Collectors.toMap(Produce_ratesqueryPo::getProduct, Produce_ratesqueryPo::getPrice, (key1, key2) -> key1));

            for (Gift_rs giftRs : rsList) {
                if (priceInfo.containsKey(giftRs.getCode())) {
                    BigDecimal dbprice = priceInfo.get(giftRs.getCode());
                    giftRs.setPrice(dbprice);
                    giftRs.setAmount(dbprice.multiply(giftRs.getAnz()));
                    totalprice = totalprice.add(giftRs.getAmount());
                }
            }
            for (CombineInfo.CombineNode node : stdOrderData.getCombineInfo().getNodes()) {
                if (priceInfo.containsKey(node.getProductcode())) {
                    BigDecimal dbprice = priceInfo.get(node.getProductcode());
                    node.setPrice(dbprice);
                    node.setAmount(dbprice.multiply(new BigDecimal(node.getAnz())));
                }
            }
        }

        BigDecimal extra = calcExtraFee(stdOrderData);
        stdOrderData.getCombineInfo().setTotalprice(totalprice);
        stdOrderData.getCombineInfo().setPostage(extra);
        stdOrderData.getCombineInfo().setAmount(totalprice.add(extra));

        stdOrderData.setExtrafee(stdOrderData.getExtrafee().add(extra));

        totalprice = totalprice.add(extra);

        return totalprice;
    }

    public BigDecimal calcExtraFee(StdOrderData stdOrderData) {
        BigDecimal extra = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(stdOrderData.getGifts())) {
            String province = stdOrderData.getCombineInfo().getProvince();
            PostageCache postageCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.POSTAGE);
            Postage syspostage = postageCache.getRecord(stdOrderData.getBookingRs().getProjectid(), DEFAULTUSERID);

            boolean lneedarea = false;
            BigDecimal giftamount = BigDecimal.ZERO;
            for (CombineInfo.CombineNode node : stdOrderData.getCombineInfo().getNodes()) {
                if (node.getPosttype() == 1) {
                    giftamount = node.getAmount().add(giftamount);//不包邮的物品做运费累加
                    lneedarea = true;
                }
            }
            if (lneedarea) {
                extra = getThePostRuleFee(syspostage, giftamount, giftamount, province);//先算全局运费.全场满xx 元包邮
                List<Postage> postages = postageCache.getDataListWithCondition(stdOrderData.getBookingRs().getProjectid(),
                        r -> r.getAddress().contains(province));
                BigDecimal areafee = BigDecimal.ZERO;
                for (Postage postage : postages) {//这里的list 最多只会返回一条
                    areafee = getThePostRuleFee(postage, giftamount, giftamount, province);
                    if (!CalculateNumber.isZero(areafee)) {
                        extra = areafee;  //配置有地区运费的.地区比全局优先
                    }
                }
            }
        }

        stdOrderData.getCombineInfo().setPostage(extra);
        return extra;
    }

    private BigDecimal getThePostRuleFee(Postage postage, BigDecimal actualAmount, BigDecimal giftamount, String province) {
        if (!postage.getName().equals(DEFAULTUSERID) && !postage.getAddress().contains(province)) {
            return BigDecimal.ZERO;
        }
        if (postage.getType().equals(PostageRule.NOT_FREE)) {
            return postage.getAmount();
        }
        if (postage.getType().equals(PostageRule.ACTUAL_GREATER_FREE) && NumberUtil.isLessOrEqual(actualAmount, postage.getPprice())) {
            return postage.getAmount();
        }
        if (postage.getType().equals(PostageRule.ORG_GREATER_FREE) && NumberUtil.isLessOrEqual(giftamount, postage.getPprice())) {
            return postage.getAmount();
        }
        return BigDecimal.ZERO;
    }


    public List<KitProductItem> calcKitItemPrIce(String projectId, String kitcode, List<Kititem> kititems, Date calcDate, Integer num, Var<BigDecimal> totalAmount) {
        List<KitProductItem> items = Lists.newArrayList();
        if (kititems.isEmpty()) {
            return items;
        }
        StdOrderData stdOrderData = new StdOrderData();
        Booking_rs rs = new Booking_rs();
        rs.setProjectid(projectId);
        rs.setArrdate(calcDate);
        rs.setDeptdate(CalculateDate.reckonDay(calcDate, 5, getMinDay(kititems)));
        stdOrderData.setBookingRs(rs);
        stdOrderData.setKitCode(kitcode);

        HashMap<Kititem, Object> objs = transFactory.transKitItem2Order(kititems, calcDate, num, stdOrderData);
        BigDecimal total = overrideKitsPrice(calcDate, stdOrderData);
        totalAmount.setValue(total);
        for (Map.Entry<Kititem, Object> entry : objs.entrySet()) {
            KitProductItem productItem = new KitProductItem();
            items.add(productItem);
            productItem.setId(entry.getKey().getSqlid());
            productItem.setTitle(entry.getKey().getProductdesc());
            productItem.setOrgnum(entry.getKey().getNum(0));

            if (entry.getValue() instanceof Room_rs) {
                productItem.setPrice(((Room_rs) entry.getValue()).getAmount(0));
            }
            if (entry.getValue() instanceof Ticket_rs) {
                productItem.setPrice(((Ticket_rs) entry.getValue()).getAmount(0));
            }
            if (entry.getValue() instanceof Cater_rs) {
                productItem.setPrice(((Cater_rs) entry.getValue()).getAmount(0));
            }
            if (entry.getValue() instanceof Kitfixcharge) {
                productItem.setPrice(((Kitfixcharge) entry.getValue()).getAmount(0));
            }
        }
        return items;
    }

    private Integer getMinDay(List<Kititem> kititems) {
        int min = 0;
        for (Kititem kititem : kititems) {
            if (ProdType.ROOM.val().equals(kititem.getProducttype())) {
                min = Math.max(min, kititem.getDays(0));
            }
        }
        return min;
    }

    public BigDecimal overrideKitsPrice(Date calcdate, StdOrderData orderData) {
        HashMap<String, BigDecimal> priceinfo = getKitPriceInfo(orderData.getBookingRs().getProjectid(), orderData.getKitCode(),
                orderData.getBookingRs().getArrdate(), orderData.getBookingRs().getDeptdate());
        BigDecimal totalAmount = BigDecimal.ZERO;

        //暂定套餐接口只含有房.票.餐 固定项目

        for (Room_rs room : orderData.getRooms()) {
            Date s = room.getArrdate();
            int len = CalculateDate.compareDates(room.getDeptdate(), room.getArrdate()).intValue();
            BigDecimal roomtotal = BigDecimal.ZERO;
            String arrKey = CalculateDate.dateToString(s);
            for (int i = 0; i < len; i++) {
                String dateKey = CalculateDate.dateToString(CalculateDate.reckonDay(s, 5, i));  //要实现多日价格不一样.应该用datekey的.为了兼容抖音
                BigDecimal price = priceinfo.getOrDefault(
                        getKitPriceKey(ProdType.ROOM.val(), room.getRmtype(), dateKey), BigDecimal.ZERO);
                orderData.getRoomPriceTable().put(room.getRegno(), dateKey, price);  //有的酒店系统需要准确的每日价格明细

                roomtotal = price.add(roomtotal);
                room.setAmount(roomtotal.multiply(BigDecimal.valueOf(room.getAnz())));
            }
            totalAmount = totalAmount.add(room.getAmount());
        }

        for (Ticket_rs ticket : orderData.getTickets()) {
            String key = getKitPriceKey(ProdType.TICKET.val(), ticket.getTcode(), CalculateDate.dateToString(ticket.getUsedate()));
            BigDecimal price = priceinfo.getOrDefault(key, BigDecimal.ZERO);
            ticket.setPrice(price);
            ticket.setAmount(price.multiply(BigDecimal.valueOf(ticket.getAnz())));

            totalAmount = totalAmount.add(ticket.getAmount());
        }

        for (Cater_rs cater : orderData.getCaters()) {
            String key = getKitPriceKey(ProdType.CANYIN.val(), cater.getCcode().isEmpty() ? cater.getRestaurant() : cater.getCcode(),
                    CalculateDate.dateToString(cater.getUsedate()));
            BigDecimal price = priceinfo.getOrDefault(key, BigDecimal.ZERO);
            cater.setPrice(price);
            cater.setAmount(price.multiply(BigDecimal.valueOf(cater.getAnz(0))));

            totalAmount = totalAmount.add(cater.getAmount());
        }

        for (Kitfixcharge kitfixcharge : orderData.getKitfixcharges()) {
            String key = getKitPriceKey(ProdType.KITITEM.val(), kitfixcharge.getCode(), CalculateDate.dateToString(orderData.getBookingRs().getArrdate()));
            BigDecimal price = priceinfo.getOrDefault(key, BigDecimal.ZERO);
            kitfixcharge.setPrice(price);
            kitfixcharge.setAmount(price.multiply(kitfixcharge.getAnz()));

            totalAmount = totalAmount.add(kitfixcharge.getAmount());
        }

        return totalAmount;
    }

    /**
     * @param projectId
     * @param kitcode
     * @param arrdate
     * @param deptdate
     * @return
     */
    public HashMap<String, BigDecimal> getKitPriceInfo(String projectId, String kitcode, Date arrdate, Date deptdate) {
        List<Produce_kitpricePo> kitpricePos = kitratedetMapper.queryKitItemPrice(projectId, kitcode, arrdate, deptdate);
        HashMap<String, BigDecimal> priceInfos = new HashMap<>();
        for (Produce_kitpricePo po : kitpricePos) {
            priceInfos.put(getKitPriceKey(po.getProductType(), po.getProduct(), CalculateDate.dateToString(po.getDate())),
                    po.getPrice());
        }
        return priceInfos;
    }

    public String getKitPriceKey(String productType, String productCode, String date) {
        return productCode + SystemUtil.SPLITSIGNAL + productCode + SystemUtil.SPLITSIGNAL + date + productType;
    }


    public void writeSpuPrice(Spu_rs rs, String kitcode, String actcode, boolean lcheckConfig) throws DefinedException {
        BigDecimal total = BigDecimal.ZERO;
        SpusitemCache spusitemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM);
        Spusitem spusitem = spusitemCache.getRecord(rs.getProjectid(), rs.getCode());
        if (spusitem != null && !spusitem.getLavl()) {
            rs.setPrice(spusitem.getShowprice());
            total = total.add(spusitem.getShowprice());
        } else {
            List<Produce_calcpricePo> prices = calcPrice_DB(rs.getProjectid(), ProdType.WARES, rs.getCode(),
                    rs.getStartdate(), rs.getEnddate(), 1, 0, 0, "", kitcode, actcode, "", lcheckConfig);
            for (Produce_calcpricePo po : prices) {
                total = po.getPrice().add(total);
            }
            if (prices.size() > 0) {
                rs.setPrice(prices.get(0).getPrice());//单价
            }
        }


        rs.setAmount(total.multiply(rs.getAnz()));

    }

    public void writeActPrIce(Act_rs rs, String kitcode, String actcode, boolean lcheckConfig) throws DefinedException {
        BigDecimal total = BigDecimal.ZERO;

        Actsite actsite = ContentCacheTool.getActsite(rs.getProjectid(), rs.getSitecode());
        Actgroup actgroup = ContentCacheTool.getActgroup(actsite);

        if (actgroup != null && !actgroup.getLfree()) {//如果不是免费项目.才会计算价格
            List<Produce_calcpricePo> prices = calcPrice_DB(rs.getProjectid(), ProdType.ACTGROUP, rs.getSitecode(),
                    rs.getUsedate(), rs.getUsedate(), 1, 0, 0, "", kitcode, actcode, rs.getPeriod(), lcheckConfig);
            for (Produce_calcpricePo po : prices) {
                total = po.getPrice().add(total);
            }
        }
        rs.setAmount(total.multiply(BigDecimal.valueOf(rs.getPersons())));
    }

    /**
     * 数据库查询价格
     *
     * @param projectId   项目 ID
     * @param type        产品类型
     * @param productcode 产品代码
     * @param startdate   开始日期
     * @param enddate     结束日期 (如果是房的.自己先减掉.传有效使用的结束日期)
     * @param person      成人 (目前没有用预留)
     * @param child       小孩
     * @param extrabad    加床
     * @param pkgs        附加服务
     * @param kitcode     套餐代码
     * @param actcode     活动代码
     * @param specode     规格代码或者时段代码
     * @param checkConfig 价格出现0时是否抛出异常
     * @return
     * @throws DefinedException
     */
    public List<Produce_calcpricePo> calcPrice_DB(String projectId,
                                                  ProdType type, String productcode, Date startdate, Date enddate,
                                                  int person, int child, int extrabad, String pkgs,
                                                  String kitcode, String actcode, String specode, boolean checkConfig) throws DefinedException {
        String ratecode = getRateCode(projectId, productcode);
        List<Produce_calcpricePo> list = null;
        switch (type) {
            case ROOM:
                list = rratedetMapper.calcPrice(projectId, ratecode, productcode, startdate, enddate);
                break;
            case TICKET:
                list = tratedetMapper.calcPrice(projectId, ratecode, productcode, startdate, enddate);
                break;
            case WARES:
                Spusitem spusitem = (Spusitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM).getRecord(projectId, productcode);
                if (spusitem != null) {
                        list = produceRates(spusitem.getShowprice(), startdate, startdate);
                }
                break;
            case ACTGROUP:
                list = actratedetMapper.calcPrice(projectId, productcode, startdate, enddate, specode);
                break;
            case ITEMS:
                if (StringUtils.isNotBlank(specode)) {//规格代码不为空 ,拼接规格代码查询
                    productcode = productcode + ":" + specode.replaceAll(",", ":") + "%";
                } else {
                    productcode = productcode + "%";
                }
                list = giftratedetMapper.calcPrice(projectId, productcode);
                break;
            default:
                list = new ArrayList<>();
        }
        if (checkConfig) {
            checkQueryResult(productcode, startdate, enddate, list);
        }
        return list;
    }

    private List<Produce_calcpricePo> produceRates(BigDecimal price, Date startdate, Date enddate) {
        List<Produce_calcpricePo> produces = new ArrayList<>();
        int len = CalculateDate.compareDates(enddate, startdate).intValue();
        if (len == 0) {
            len++;
        }
        for (int i = 0; i < len; i++) {
            Produce_calcpricePo po = new Produce_calcpricePo(CalculateDate.reckonDay(startdate, 5, i), price);
            produces.add(po);
        }
        return produces;
    }

    public BigDecimal getShowPrice(ProdType productType, Date queryDate, String productCode, String projectId) {
        try {
            if (ProdType.ITEMS.equals(productType)) {
                GiftratedetMapper giftratedetMapper = SpringUtil.getBean(GiftratedetMapper.class);
                List<Produce_calcpricePo> prices = giftratedetMapper.calcPrice(projectId, productCode);
                if (CollectionUtil.isNotEmpty(prices)) {
                    return prices.get(0).getPrice();
                } else {
                    String code = productCode.contains(":") ? productCode.substring(0, productCode.indexOf(":")) : productCode;
                    Giftitem giftitem = (Giftitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM).getRecord(projectId, code);
                    if (giftitem != null) {
                        return giftitem.getShowprice();
                    }
                }
            }
            List<Produce_calcpricePo> calcpricePos = calcPrice_DB(projectId, productType, productCode,
                    queryDate,
                    queryDate, 1, 0, 0, "", null, null, "", false);
            if (calcpricePos.size() > 0) {
                return calcpricePos.get(0).getPrice();
            }
        } catch (DefinedException e) {
        }
        return BigDecimal.valueOf(-1);
    }

    public CodeDetail ratesQuery(String projectId,
                                 ProdType type, List<String> prods, Date showDate) {
        String ratecode = getRateCode(projectId, "");
        List<Produce_ratesqueryPo> list = null;
        if (prods.size() > 0) {
            switch (type) {
                case ROOM:
                    list = rratedetMapper.rateQuery(projectId, ratecode, prods, CalculateDate.returnDate_ZeroTime(showDate));
                    break;
                case TICKET:
                    list = tratedetMapper.rateQuery(projectId, ratecode, prods, CalculateDate.returnDate_ZeroTime(showDate));
                    break;
                default:
                    list = new ArrayList<>();
            }
        } else {
            list = new ArrayList<>();
        }
        CodeDetail codeDetail = new CodeDetail();
        for (Produce_ratesqueryPo po : list) {
            codeDetail.putProductPrice(po.getProduct(), po.getPrice());
        }
        return codeDetail;
    }

    private void checkQueryResult(String productcode, Date startdate, Date enddate, List<Produce_calcpricePo> list) throws DefinedException {
        int len = CalculateDate.compareDates(enddate, startdate).intValue();
        if (len == 0) {
            len++;
        }
        HashMap<String, Integer> maps = Maps.newHashMap();
        for (int i = 0; i < len; i++) {  //根据日期初始化出检查矩阵
            maps.put(CalculateDate.dateToString(CalculateDate.reckonDay(startdate, 5, i)), 0);
        }
        for (Produce_calcpricePo po : list) {
            maps.put(CalculateDate.dateToString(po.getDate()), po.getPrice().intValue());
        }
        for (Map.Entry<String, Integer> entry : maps.entrySet()) {
            if (entry.getValue() < 0) {
                throw new DefinedException(String.format("价格异常.产品代码%S在日期%S未上架价格! 暂不可购买", productcode, entry.getKey()));
            }
        }
    }

    public void calc2Cache(String projectId, String ratecode, ProdType prodType, String productCode, Date startdate, Date enddate) {
//        corePrice = SpringUtil.getBean(CorePrice.class);
//        RedisTemplate redisTemplate = SpringUtil.getBean("redisTemplate", RedisTemplate.class);
//        corePrice.calcPrice(rateMqMsg.getSection(),rateMqMsg.getRatecode(),rateMqMsg.getType(),rateMqMsg.getCode(),rateMqMsg.getStartdate(),rateMqMsg.getEnddate(),"",true);

        List<Produce_calcpricePo> prices = null;
        try {
            Date calcend = CalculateDate.reckonDay(enddate, 5, 1);
            prices = calcPrice_DB(projectId, prodType, productCode, startdate, calcend, 1, 0, 0, "", null, null, "", false);
        } catch (DefinedException e) {
            log.error("出错参数NULL:" + projectId + "," + ratecode + "," + prodType + "," + productCode + "," + startdate + "," + enddate);
            log.error("计算价格异常", e);
        }
        if (prices == null) {
            return;
        }

        Map<String, String> rmtinfo = new HashMap<>();
        for (Produce_calcpricePo row : prices) {
            String d = CalculateDate.dateToString(row.getDate());//.toString();
            String price = row.getPrice().toString();// row[1].toString();
            rmtinfo.put(d, price);
        }
        String key = CoreCache.getRateCacheKey(projectId, ratecode, prodType.val(), productCode);// RedisKey.YunRateCacheKey + rateMqMsg.getSection() + ":" + rateMqMsg.getRatecode() + ":" + rateMqMsg.getCode();

        redisTemplate.opsForHash().putAll(key, rmtinfo);
    }

    /**
     * 更新指定商品缓存
     *
     * @param projectId
     * @param ratecode
     * @param itemcode
     * @param specs
     */
    public void calcGiftItemCache(String projectId, String ratecode, String itemcode, List<String> specs) {
        //查询指定规格.更新缓存
        List<Produce_calcpricePo> prices = giftratedetMapper.calcSpecPrice(projectId, itemcode, specs);

        Map<String, String> dbresult = new HashMap<>();
        for (Produce_calcpricePo row : prices) {
            String d = row.getSpecode();
            String price = row.getPrice().toString();// row[1].toString();
            dbresult.put(d, price);
        }
        String key = CoreCache.getRateCacheKey(projectId, ratecode, ProdType.ITEMS.val(), itemcode);// RedisKey.YunRateCacheKey + rateMqMsg.getSection() + ":" + rateMqMsg.getRatecode() + ":" + rateMqMsg.getCode();
        //log.info("更新伴手礼价格缓存:{},{}" ,key,dbresult);
        redisTemplate.opsForHash().putAll(key, dbresult);
    }

    public void calcActCache(String projectId, String ratecode, String sitecode, Date startdate, Date enddate, List<String> times) {
        //查询指定规格.更新缓存
        List<Produce_calcpricePo> prices = actratedetMapper.calcPriceBatch(projectId, sitecode, startdate, enddate, times);

        Map<String, String> cacheupds = prices.stream().collect(Collectors.toMap(
                row -> row.getSpecode() + ":" + CalculateDate.dateToString(row.getDate()),
                row -> row.getPrice().toString(),
                (key1, key2) -> key1
        ));
        if (cacheupds.values().size() > 0) {
            String key = CoreCache.getRateCacheKey(projectId, ratecode, ProdType.ACTGROUP.val(), sitecode);// RedisKey.YunRateCacheKey + rateMqMsg.getSection() + ":" + rateMqMsg.getRatecode() + ":" + rateMqMsg.getCode();
            //log.info("更新活动价格缓存:{},{}" ,key,cacheupds);
            redisTemplate.opsForHash().putAll(key, cacheupds);
        }
    }


    public Date getRratedetMaxDate(String projectid) {
        Date maxDate = rratedetMapper.findRratedetMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }


    public Date getTratedetMaxDate(String projectid) {
        Date maxDate = tratedetMapper.findTratedetMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    public Date getActratedetMaxDate(String projectid) {
        Date maxDate = actratedetMapper.findActratedetMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    public Date getSpuratedetMaxDate(String projectid) {
        Date maxDate = spuratedetMapper.findSpuratedetMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    public Date getGiftratedetMaxDate(String projectid) {
        Date maxDate = giftratedetMapper.findGiftratedetMaxDate(projectid);
        if (maxDate == null) {
            maxDate = CalculateDate.reckonDay(DateUtil.beginOfDay(new Date()), 5, 90);
        }
        return maxDate;
    }

    /**
     * 自动延长门票价格日期
     *
     * @param projectId
     * @param tickets
     * @param endDate
     */
    public void generateTratedet(String projectId, List<Ticket_ratePo> tickets, Date endDate) throws SQLException {
        String deleteSql = "";
        String insertSql = "";
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        String edate = CalculateDate.dateToString(endDate);
        DataSource source = daoLocal.getDataSource();
        PreparedStatement pst = null;
        PreparedStatement pst2 = null;
        Connection connection = null;
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);

            deleteSql = "delete from Tratedet where `code`=? and  productcode=? and datum between ? and '" + edate + "' and projectid=?";
            //按照日期区间插入门票价格数据
            insertSql = "insert into Tratedet(code,productcode,datum,rate,projectid) select ?,?,date_add(?,interval number day),?,? from sptvalue " +
                    "where  date_add(?,interval number day)<='" + edate + "'";


            pst = connection.prepareStatement(deleteSql);
            pst2 = connection.prepareStatement(insertSql);
            for (Ticket_ratePo ticket : tickets) {
                Date startDate = CalculateDate.reckonDay(ticket.getDatum(), 5, 1);//数据库内日期+1
                pst.setString(1, ticket.getCode());
                pst.setString(2, ticket.getProductcode());
                pst.setDate(3, DateUtil.date(startDate).toSqlDate());
                pst.setString(4, projectId);
                pst.addBatch();
            }
            TicketCache ticketCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
            for (Ticket_ratePo tratedet : tickets) {
                Ticket rt = ticketCache.getRecord(projectId, tratedet.getProductcode());
                //if (rt != null && StringUtils.isNotBlank(rt.getOutcode())) { //outcode不为空标识售卖
                if (rt != null) { //outcode不为空标识售卖
                    Date startDate = CalculateDate.reckonDay(tratedet.getDatum(), 5, 1);
                    pst2.setString(1, tratedet.getCode());
                    pst2.setString(2, tratedet.getProductcode());
                    pst2.setDate(3, DateUtil.date(startDate).toSqlDate());
                    pst2.setBigDecimal(4, rt.getShowprice()); //门票设置价格
                    pst2.setString(5, projectId);
                    pst2.setDate(6, DateUtil.date(startDate).toSqlDate());
                    pst2.addBatch();
                }
            }

            pst.executeBatch();
            connection.commit();
            //删除结束.新插入开始
            pst2.executeBatch();
            connection.commit();
        } finally {
            if (pst != null) {
                pst.close();
            }
            if (pst2 != null) {
                pst2.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 自动延长景区场所价格日期
     *
     * @param projectId
     * @param actsites
     * @param endDate
     */
    public void generateActratedet(String projectId, List<Actsite_ratePo> actsites, Date endDate) throws SQLException {
        String deleteSql = "";
        String insertSql = "";
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        String edate = CalculateDate.dateToString(endDate);
        DataSource source = daoLocal.getDataSource();
        PreparedStatement pst = null;
        PreparedStatement pst2 = null;
        Connection connection = null;
        try {
            connection = source.getConnection();
            connection.setAutoCommit(false);

            deleteSql = "delete from Actratedet where `sitecode`=? and  `period`=? and datum between ? and '" + edate + "' and projectid=?";
            //按照日期区间插入预约场所价格数据
            insertSql = "insert into Actratedet(sitecode,`period`,datum,rate,projectid) select ?,?,date_add(?,interval number day),?,? from sptvalue " +
                    "where  date_add(?,interval number day)<='" + edate + "'";


            pst = connection.prepareStatement(deleteSql);
            pst2 = connection.prepareStatement(insertSql);
            for (Actsite_ratePo actsite_rate : actsites) {
                Date startDate = CalculateDate.reckonDay(actsite_rate.getDatum(), 5, 1);//数据库内日期+1
                pst.setString(1, actsite_rate.getSiteCode());
                pst.setString(2, actsite_rate.getPeriod());
                pst.setDate(3, DateUtil.date(startDate).toSqlDate());
                pst.setString(4, projectId);
                pst.addBatch();
            }
            ActsiteCache actsiteCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTSITE);
            for (Actsite_ratePo actsite_rate : actsites) {
                Actsite at = actsiteCache.getRecord(projectId, actsite_rate.getSiteCode());
                if (at != null) { //todo outcode不为空标识售卖
                    Date startDate = CalculateDate.reckonDay(actsite_rate.getDatum(), 5, 1);
                    pst2.setString(1, actsite_rate.getSiteCode());
                    pst2.setString(2, actsite_rate.getPeriod());
                    pst2.setDate(3, DateUtil.date(startDate).toSqlDate());
                    pst2.setBigDecimal(4, at.getDfprice()); //预约场所设置价格
                    pst2.setString(5, projectId);
                    pst2.setDate(6, DateUtil.date(startDate).toSqlDate());
                    pst2.addBatch();
                }
            }

            pst.executeBatch();
            connection.commit();
            //删除结束.新插入开始
            pst2.executeBatch();
            connection.commit();
        } finally {
            if (pst != null) {
                pst.close();
            }
            if (pst2 != null) {
                pst2.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 更新Giftratedet日期
     *
     * @param projectId
     * @param generateDate
     * @deprecated
     */
    @Deprecated
    public void generateGiftratedet(String projectId, Date generateDate) {
        giftratedetMapper.updateDate(projectId, generateDate);
        //todo 是否需要价格通知
    }
}
