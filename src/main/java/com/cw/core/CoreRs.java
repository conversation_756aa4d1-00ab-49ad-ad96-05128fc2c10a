package com.cw.core;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.sku.OpskuPickup;
import com.cw.arithmetic.sku.TSkuUpd;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.common.client.CwApiClient;
import com.cw.config.Cwconfig;
import com.cw.config.exception.CustomException;
import com.cw.core.func.order.OrderReqTransFactory;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.OrderHandler;
import com.cw.core.vendor.order.VendorHandler;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.*;
import com.cw.mapper.common.DaoLocal;
import com.cw.outsys.stdop.common.array.StdTicketSubStatusNode;
import com.cw.outsys.stdop.request.StdCancelOrderRequest;
import com.cw.outsys.stdop.request.StdTicketQueryCanelStatusRequest;
import com.cw.outsys.stdop.request.StdTicketQueryStatusRequest;
import com.cw.outsys.stdop.request.StdTicketReturnNumRequest;
import com.cw.outsys.stdop.response.StdTicketQueryCancelStatusResponse;
import com.cw.outsys.stdop.response.StdTicketQueryStatusResponse;
import com.cw.outsys.stdop.response.StdTicketReturnNumResponse;
import com.cw.pms.request.CwColRsReq;
import com.cw.pms.response.CwColRsRes;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.common.rule.CancelOrderRuleValidData;
import com.cw.pojo.dto.app.req.AppOrderReq;
import com.cw.pojo.dto.app.req.node.CombineInfo;
import com.cw.pojo.dto.app.res.AppCancelOrderResult;
import com.cw.pojo.dto.app.res.AppSingleOrderResult;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.utils.*;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.TicketUtil;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.RedissonMultiLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 订单逻辑处理
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/20 10:10
 **/
@Slf4j
@Service
public class CoreRs {
    @Autowired
    RedissonClient redissonClient;

    @Autowired
    OrderReqTransFactory transFactory;

    @Autowired
    CoreAvl coreAvl;

    @Autowired
    DaoLocal<?> daoLocal;


    @Lazy
    @Autowired
    CoreSync3 coreSync3;

    @Autowired
    Cwconfig cwconfig;


    /**
     * 创建订单成功后.返回订单号与支付金额给前端做支付
     *
     * @param orderReq
     * @throws DefinedException
     */
    public AppSingleOrderResult createNewRs_now(AppOrderReq orderReq) throws DefinedException {

        String projectId = WebAppGlobalContext.getCurrentAppProjectId();

        orderReq.setAgentTypeValue(WebAppGlobalContext.getCurrentAppAgentType().name());
        orderReq.setUid(WebAppGlobalContext.getCurrentAppUserId());

        checkForm(orderReq, projectId);//先检查表单提交

        //第一步:解析订单对象
        StdOrderData orderData = transFactory.parseOrderForm(orderReq);
        transFactory.writeMakerAndRegno(orderData);//写入订单号.更新日期
        transFactory.updateCombieInfo(orderData, orderReq.getAddressInfo());// 初始化伴手礼合单信息
        //订单加并发抢购锁.并且预扣 redis cache 库存

        coreSync3.checkProductRule(orderData);//检查产品规则是否可卖

        checkCache(projectId, orderData);//扣减缓存库存
        //创建订单

        sendPms(orderData);//TODO 删掉哦

        AppSingleOrderResult result = coreSync3.saveOrder_Now(orderData);


        return result;
    }

    private void sendPms(StdOrderData structure) {
        if (StrUtil.isBlank(cwconfig.getPmsdomain())) {
            return;
        }
        String hotelId = "bh02";
        //for (Room_rs room : structure.getRooms()) {
        //    hotelId=room.getHotelcode();
        //    break;
        //}
        CwApiClient client = new CwApiClient(
                cwconfig.getPmsdomain(), // 测试服务器地址
                hotelId,                 // 测试应用ID
                "5bcaqh19ks128q5auw1lo23v6fi7cjht",            // 测试私钥
                "RSA2"                         // 签名方式
        );


        CwColRsReq req = new CwColRsReq();
        req.setBooker(structure.getBookingRs().getGuestname());
        req.setPhone(structure.getBookingRs().getTel());
        req.setNetworkId(structure.getBookingRs().getBookingid());
        req.setOtaOrderId(structure.getBookingRs().getBookingid());
        req.setArrDate(structure.getBookingRs().getArrdate());
        req.setDeptDate(structure.getBookingRs().getDeptdate());

        List<CwColRsReq.Room> rooms = new ArrayList<>();
        for (Room_rs roomrs : structure.getRooms()) {
            CwColRsReq.Room room = new CwColRsReq.Room();
            room.setRoomType(roomrs.getRmtype());
            room.setNumberOfRooms(roomrs.getAnz());
            room.setRateCode("OTA");
            room.setCommon(structure.getBookingRs().getMemo());
            room.getStayDateRange().setStartDate(roomrs.getArrdate());
            room.getStayDateRange().setEndDate(roomrs.getDeptdate());
            room.setChannel("XCX");

            rooms.add(room);
            req.setRooms(rooms);
        }


        CwColRsRes response = client.execute(req);
        System.out.println("Group reservation response: " + JSON.toJSONString(response));
    }

    public void checkForm(AppOrderReq req, String projectId) throws DefinedException {

        if (!req.getKitCode().isEmpty()) {
            SysFuncLibTool.checkKitCanBuy(req.getKitCode(), req.getStartdate(), projectId);
        }
        if (req.getCheckinGuestInfo() != null) {
            if (StrUtil.isBlank(req.getCheckinGuestInfo().getGuestname()) || StrUtil.isBlank(req.getCheckinGuestInfo().getMobile())) {
                throw new DefinedException("请填写购买人姓名与手机", SystemUtil.SystemerrorCode.ERR015_FORMERR);
            }
        }
        if (CollectionUtil.isNotEmpty(req.getTicketGuestInfo()) && req.getItems().size() > 0) {
            String ticketcode = req.getItems().get(0).getProductcode();
            Integer ticketNum = req.getItems().get(0).getNum();
            SysFuncLibTool.checkTIcketForm(req.getTicketGuestInfo(), ticketcode, projectId, ticketNum, req.getCheckinGuestInfo());
        }
    }


    private void checkCache(String projectId, StdOrderData data) throws DefinedException {
        // 第二步 等待库存联锁加锁成功 .先在 redis 预扣库存
        List<TSkuUpd> updList = coreAvl.getOrderSkuUpd(null, data);//解析占用数
        List<String> lockKeys = OpskuPickup.getLockKeys(projectId, updList);

        boolean llock = false;
        RedissonMultiLock multiLock = null;
        RLock[] rLocks = getRLocks(lockKeys.toArray(new String[]{}));
        if (rLocks.length == 0) {
            return;
        }
        multiLock = new RedissonMultiLock(rLocks);
        try {
            llock = multiLock.tryLock(3, 2, TimeUnit.SECONDS); //等待5秒.锁有效期3秒.5秒之内足够扣减数据库库存.与redis库存
        } catch (InterruptedException e) {
            log.error(e.getMessage());
        }
        if (!llock) {
            throw new DefinedException("订单请求处理正忙,请重试", SystemUtil.SystemerrorCode.RETRY_ORDERERR);
        }
        //资源锁成功后.做资源库存扣减
        if (data.getRooms().size() > 0 || data.getTickets().size() > 0 || data.getGifts().size() > 0) {//TODO 是否所有产品都加入缓存??如果包含客房有限资源  检查。并马上对资源进行扣减
            try {
                coreAvl.checkCacheSku(projectId, updList);//再判断一次redis库存是否足够.因为有可能等待过程中.上一个锁用户已经消费了库存
            } catch (DefinedException e) {
                multiUnlock(multiLock);
                throw e;
            }
            coreAvl.updateSkuCache(projectId, updList); //马上扣减相关的缓存库存
        }
        if (llock) {
            multiUnlock(multiLock);
        }
    }

    private RLock[] getRLocks(String[] skuid) {
        RLock[] lockarray = new RLock[skuid.length];
        for (int i = 0; i < skuid.length; i++) {
            lockarray[i] = redissonClient.getLock(skuid[i]);
        }
        return lockarray;
    }

    private void multiUnlock(RedissonMultiLock multiLock) {
        if (multiLock != null) {
            multiLock.unlock();
        }
    }

    /**
     * 移动端取消订单
     *
     * @param stdOrderData
     * @return
     */
    public AppCancelOrderResult appCancelOrder_now(StdOrderData stdOrderData) {

        AppCancelOrderResult result = null;
        try {
            result = coreSync3.diapatchCancelOrderAction(stdOrderData);
        } catch (DefinedException e) {
            throw new CustomException(ResultJson.failure(ResultCode.CANCELFAIL_ERR));
        }
        return result;
    }

    public void cancelOrder_Now(String orderId, String projectId) throws DefinedException {
        coreSync3.diapatchCancelOrderAction(getOrderDetail(orderId, projectId));
    }

    /**
     * //仅本地取消订单,并退款.适用于一些跟第三方系统交互的订单
     * 一般是跟第三方系统交互失败.无法交付给用户完整状态的物品.本地直接取消并退款
     * 比如发码失败.CRM优惠券不合用
     *
     * @param bookingId
     * @param projectId
     * @throws DefinedException
     */
    public void cancelOrder_Local(String bookingId, String projectId) throws DefinedException {
        StdOrderData stdOrderData = getOrderDetail(bookingId, projectId);
        coreSync3.cancelLocalOrder(stdOrderData);

        Prepay prepay = SpringUtil.getBean(PrepayMapper.class).findPrepayByBookingid(bookingId);
        if (prepay != null) {
            CancelOrderRuleValidData cancelOrderRuleValidData = CancelOrderRuleValidData.getFullRefundData(prepay);
            try {
                SpringUtil.getBean(CorePay.class).refundPay(prepay, cancelOrderRuleValidData);  //尝试全额退款 如果失败 则新建退款审核订单
            } catch (Exception e) {
                //退款失败后 主单变成审核状态，新建退款审核订单
                Booking_rs bookingRs = stdOrderData.getBookingRs();
                AuditingMapper auditingMapper = SpringUtil.getBean(AuditingMapper.class);
                if (auditingMapper.countAuditing(prepay.getBookingid(), projectId) <= 0) {
                    Auditing auditing = new Auditing();
                    auditing.setBookingid(bookingRs.getBookingid());
                    auditing.setCreatetime(LocalDateTime.now());
                    auditing.setUid(bookingRs.getUid());
                    auditing.setAstatus(StatusUtil.AuditingStatus.EXPECTED);//初始化审核状态
                    //订单信息
                    auditing.setAmount(bookingRs.getAmount());
                    auditing.setBookername(bookingRs.getBookername());
                    auditing.setTel(bookingRs.getTel());
                    auditing.setMainstatus(bookingRs.getMainstatus());
                    auditing.setPtype(bookingRs.getPtype());
                    auditing.setRefund(bookingRs.getAmount());
                    auditing.setProjectid(projectId);
                    //支付信息
                    auditing.setStatus(prepay.getPstatus() + prepay.getStatus());//付款状态+预付款状态
                    auditing.setSerialno(prepay.getSerialno());
                    auditing.setPaymethod(prepay.getPaymethod());
                    auditing.setPayno(prepay.getPayno());
                    auditing.setPaytime(prepay.getCreatetime());
                    auditing.setReason("取消订单发起退款失败");
                    daoLocal.merge(auditing);
                    bookingRs.setMainstatus(StatusUtil.BookingRsStatus.AUDTING);//将订单状态更新为等待审核中
                    daoLocal.merge(bookingRs);
                } else {
                    return;//重复提交
                }
            }
        } else {
            //对应订单 bookingrs ticket_rs
            if (stdOrderData.getBookingRs() != null) {
                BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
                bookingrsMapper.delete(stdOrderData.getBookingRs());
            }
            if (CollectionUtil.isNotEmpty(stdOrderData.getTickets()) && stdOrderData.getTickets().get(0) != null) {
                Ticket_rsMapper ticketRsMapper = SpringUtil.getBean(Ticket_rsMapper.class);
                ticketRsMapper.deleteAll(stdOrderData.getTickets());
                RticketsMapper rticketsMapper = SpringUtil.getBean(RticketsMapper.class);
                for (Ticket_rs ticket : stdOrderData.getTickets()) {   //2024.6.14  just  避免票务系统拦截之后.产生资源不释放的占用问题
                    rticketsMapper.updateRticketsPickup(-ticket.getAnz(), ticket.getTcode(), ticket.getUsedate(), ticket.getUsedate(), ticket.getProjectid());
                }
            }
        }

    }

    public void checkOrderCanCancel(StdOrderData stdOrderData) throws DefinedException {
        OrderHandler orderHandler = SpringUtil.getBean(OrderHandler.class);

        Date systemDate = CalculateDate.getSystemDate();
        if (stdOrderData.getRooms().size() > 0) {
            for (Room_rs room : stdOrderData.getRooms()) {
                if (CalculateDate.isAfter(systemDate, room.getDeptdate())) {
                    throw new DefinedException("过期订单不允许取消");
                }
            }
        }


        if (stdOrderData.getTickets().size() > 0) {//取消前调用门票接口.查询
//            StdOrderCancelReq
            boolean lhasQrcode = false;
            for (Ticket_rs t : stdOrderData.getTickets()) {
                if (!t.getQrcode().isEmpty()) {
                    lhasQrcode = true;
                }
                //if (CalculateDate.isAfter(systemDate, t.getUsedate())) {  先屏蔽掉.因为票的有效期可能是两天或者多天
                //    throw new DefinedException("过期订单不允许取消");
                //}
            }
            VendorHandler vendorHandler = orderHandler.getVendorHandler(ProdType.TICKET, stdOrderData.getBookingRs().getProjectid());
            if (lhasQrcode && vendorHandler != null) {//有二维码的票.才去做取消前的判断
                StdTicketQueryStatusRequest ticketQueryRequest = new StdTicketQueryStatusRequest();
                ticketQueryRequest.setProjectId(stdOrderData.getBookingRs().getProjectid());
                ticketQueryRequest.setColno(stdOrderData.getBookingRs().getBookingid());
                if (StringUtils.isNotBlank(stdOrderData.getBookingRs().getOutid())) {
                    ticketQueryRequest.setOutId(stdOrderData.getBookingRs().getOutid());//微票订单号
                }
                StdTicketQueryStatusResponse response = vendorHandler.queryTicketStatus(ticketQueryRequest);
                if (response.getStd_flag()) {
                    if (CollUtil.isNotEmpty(response.getSubOrders())) {
                        for (StdTicketSubStatusNode subOrder : response.getSubOrders()) {
                            if (subOrder.getStatus().equals(TicketUtil.TicketCheckStatus.CHECKING.name()) || subOrder.getStatus().equals(TicketUtil.TicketCheckStatus.CHECKED.name())) {
                                throw new DefinedException("门票已检! 不允许取消", SystemUtil.SystemerrorCode.ERR009_CANCELFAIL);
                            }
                        }
                    }
                    log.info("{}门票取消.接口预检查通过", stdOrderData.getBookingRs().getBookingid());
                } else {
                    throw new DefinedException("查询门票接口失败.不允许取消", SystemUtil.SystemerrorCode.ERR009_CANCELFAIL);
                }
            }
        }

        if (stdOrderData.getGifts().size() > 0) { //取消前检查伴手礼订单是否已经发货，发货过程不可取消
            List<Gift_rs> gift_rsList = stdOrderData.getGifts();
            if (gift_rsList.get(0).getDeliverytime().isAfter(SystemUtil.EMPTY_LOCALTIME) &&
                    !gift_rsList.get(0).getReceivetime().isAfter(SystemUtil.EMPTY_LOCALTIME)) {//已经发货 未收货
                throw new DefinedException("伴手礼订单已经发货，不允许取消", SystemUtil.SystemerrorCode.ERR009_CANCELFAIL);
            }

        }

        if (stdOrderData.getSpus().size() > 0) {
            List<Spu_rs> rsList = stdOrderData.getSpus();
            for (Spu_rs spuRs : rsList) {
                if (!CalculateDate.emptyDate(spuRs.getCheckdate()) || spuRs.getLcheck()) {
                    throw new DefinedException("产品已经核验使用,不允许取消", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }
            }
        }
    }


    public void excuteLockBeforeAudit(StdOrderData stdOrderData) throws DefinedException {
        OrderHandler orderHandler = SpringUtil.getBean(OrderHandler.class);
        CoreOrderWriter writer = SpringUtil.getBean(CoreOrderWriter.class);
        boolean lhasQrcode = false;
        boolean lneedAudit = false;
        if (stdOrderData.getTickets().size() > 0) {
            for (Ticket_rs t : stdOrderData.getTickets()) {
                if (!t.getQrcode().isEmpty()) {
                    lhasQrcode = true;
                }
                Rules rules = SysFuncLibTool.getProductRule(ProdType.TICKET.val(), t.getTcode(), t.getProjectid());
                if (rules != null && rules.getLauditing()) {
                    lneedAudit = true;
                }
            }
        }
        VendorHandler vendorHandler = orderHandler.getVendorHandler(ProdType.TICKET, stdOrderData.getBookingRs().getProjectid());
        if (lhasQrcode && lneedAudit && vendorHandler != null) {//  对门票先发起退票.不是调用取消接口了
            for (Ticket_rs ticketRs : stdOrderData.getTickets()) {
                StdTicketReturnNumRequest returnNumRequest = new StdTicketReturnNumRequest();
                returnNumRequest.setReturnNum(ticketRs.getAnz() + "");
                returnNumRequest.setCancelno(ticketRs.getRegno());  //取消批次号.暂时先用个固定的
                returnNumRequest.setSubOrderno(ticketRs.getRegno());
                returnNumRequest.setProjectId(ticketRs.getProjectid());
                returnNumRequest.setBookingId(ticketRs.getBookingid());
                returnNumRequest.setOutId(stdOrderData.getBookingRs().getOutid());
                StdTicketReturnNumResponse response = vendorHandler.returnTicketNum(returnNumRequest);
                if (response.getStd_flag()) {
                    ticketRs.setCancelno(response.getOutCancelNo());
                    daoLocal.merge(ticketRs);
                    writer.updCancelData(stdOrderData, true);
                }
            }
        }

        SysConfCache sysConfCache = GlobalCache.getDataStructure().getConfCache();
        Sysconf sysconf = sysConfCache.getOne(stdOrderData.getBookingRs().getProjectid());
        VendorType vendorType = EnumUtil.fromString(VendorType.class, sysconf.getRoomvendor(), VendorType.LOCAL);
        if (!vendorType.equals(VendorType.LOCAL) && stdOrderData.isContainRoom()) {//调用客房接口.取消客房产品订单
            VendorHandler roomHandler = orderHandler.getVendorHandler(ProdType.ROOM, stdOrderData.getBookingRs().getProjectid());
            if (vendorHandler != null && !stdOrderData.getBookingRs().getOutid().isEmpty()) {
                StdCancelOrderRequest request = new StdCancelOrderRequest();
                request.setOtaorderid(stdOrderData.getBookingRs().getBookingid());
                request.setProjectId(stdOrderData.getBookingRs().getProjectid());
                request.setOrderDataContext(stdOrderData);
                if (stdOrderData.getCancelOrderRuleValidData() != null) {
                    request.setLincludeDebit(CalculateNumber.isGreaterThanZero(stdOrderData.getCancelOrderRuleValidData().getDebit()));
                    request.setPercent(stdOrderData.getCancelOrderRuleValidData().getDebitpercent());
                }

                roomHandler.cancelOrder(request);
                writer.updCancelData(stdOrderData, true);
            }
        }


    }


    public void checkAllTicketAudited(String projectId, StdOrderData stdOrderData, boolean needAudit) throws DefinedException {
        OrderHandler orderHandler = SpringUtil.getBean(OrderHandler.class);
        boolean lhasQrcode = false;
        boolean lneedAudit = false;
        if (stdOrderData.getTickets().size() > 0) {
            for (Ticket_rs t : stdOrderData.getTickets()) {
                if (!t.getQrcode().isEmpty()) {
                    lhasQrcode = true;
                }
                Rules rules = SysFuncLibTool.getProductRule(ProdType.TICKET.val(), t.getTcode(), t.getProjectid());
                if (rules != null && rules.getLauditing()) {
                    lneedAudit = true;
                }
            }
        }

        VendorHandler vendorHandler = orderHandler.getVendorHandler(ProdType.TICKET, projectId);
        if (lhasQrcode && lneedAudit && vendorHandler != null) {//  对门票先发起退票.不是调用取消接口了
            //调用退票方法
            for (Ticket_rs ticketRs : stdOrderData.getTickets()) {
                if (!ticketRs.getCancelno().isEmpty()) {
                    StdTicketQueryCanelStatusRequest queryCanelStatusRequest = new StdTicketQueryCanelStatusRequest();
                    queryCanelStatusRequest.setProjectId(projectId);
                    queryCanelStatusRequest.setCancelno(ticketRs.getCancelno());
                    queryCanelStatusRequest.setColno(stdOrderData.getBookingRs().getBookingid());
                    if (StringUtils.isNotBlank(stdOrderData.getBookingRs().getOutid())) {
                        queryCanelStatusRequest.setOutId(stdOrderData.getBookingRs().getOutid());
                    }
                    StdTicketQueryCancelStatusResponse response = vendorHandler.queryTicketCacnelStatus(queryCanelStatusRequest);

                    if (needAudit) {// 判断是否都审核通过退票了
                        if (!response.getStd_flag()) {//要求检查必须审核状态 如果没审核.就抛出异常
                            throw new DefinedException("票务系统退票未审核.请核查票务系统是否人工未审核或退款中", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                        }
                    } else {//判断是否还是未审核状态
                        if (response.getStd_flag()) {  //要求票务系统不能已经审核通过.如果审核了.就抛出异常
                            throw new DefinedException("票务系统已经退票", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                        }
                    }
                }
            }
        }

    }

    /**
     * 获取订单详情
     *
     * @param bookingid
     * @param projectId
     * @return
     */
    public StdOrderData getOrderDetail(String bookingid, String projectId) {
        BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
        StdOrderData stdOrderData = new StdOrderData();
        Booking_rs bookingRs = bookingrsMapper.findBooking_rsByBookingid(bookingid);
        if (bookingRs == null) {
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("订单记录不存在"));
        }
        stdOrderData.setBookingRs(bookingRs);
        //伴手礼信息
        if (StringUtils.isNotBlank(bookingRs.getCombineinfo())) {
            CombineInfo combineInfo = JSONObject.parseObject(bookingRs.getCombineinfo(), CombineInfo.class);
            stdOrderData.setCombineInfo(combineInfo);
        }
        //房型
        if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.ROOM)) {
            RoomrsMapper roomrsMapper = SpringUtil.getBean(RoomrsMapper.class);
            List<Room_rs> records = roomrsMapper.findRoom_rsByBookingidAndProjectid(bookingid, projectId);
            stdOrderData.getRooms().addAll(records);

        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.TICKET)) {//门票
            Ticket_rsMapper ticketRsMapper = SpringUtil.getBean(Ticket_rsMapper.class);
            List<Ticket_rs> records = ticketRsMapper.findTicket_rsByBookingidAndProjectid(bookingid, projectId);
            stdOrderData.getTickets().addAll(records);

        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.KITITEM)) {//自定义套餐
            KitfixchargeMapper kitfixchargeMapper = SpringUtil.getBean(KitfixchargeMapper.class);
            List<Kitfixcharge> records = kitfixchargeMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            stdOrderData.getKitfixcharges().addAll(records);
            stdOrderData.setKitCode(records.get(0).getCode());

        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.TAOCAN)) {    //组合套餐
            //查询套餐详情列表查找其他子订单
            stdOrderData.setKitCode(bookingRs.getProduct());
            KititemMapper kititemMapper = SpringUtil.getBean(KititemMapper.class);
            List<Kititem> kititemList = kititemMapper.finByKitCodeAndProjectId(bookingRs.getProduct(), projectId);
            //查询套餐种类 查找
            List<String> productTypeList = new ArrayList<>();
            for (Kititem kititem : kititemList) {
                if (productTypeList.contains(kititem.getProducttype())) {
                    continue;
                } else {
                    productTypeList.add(kititem.getProducttype());
                }
            }
            fillStdOrderData(productTypeList, bookingid, projectId, stdOrderData);

        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.MEETING)) {//会议
            //MeetingrsMapper meetingrsMapper = SpringUtil.getBean(MeetingrsMapper.class);
            //List<Meeting_rs> records = meetingrsMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            //stdOrderData.getMeetings().addAll(records);

        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.WARES)) {  //景区商品
            SpursMapper spursMapper = SpringUtil.getBean(SpursMapper.class);
            List<Spu_rs> records = spursMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            stdOrderData.getSpus().addAll(records);

        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.ITEMS)) {        //伴手礼
            GiftrsMapper giftrsMapper = SpringUtil.getBean(GiftrsMapper.class);
            List<Gift_rs> records = giftrsMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            stdOrderData.getGifts().addAll(records);

        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.DISCOUNT)) {    //折扣
            DiscountMapper discountMapper = SpringUtil.getBean(DiscountMapper.class);
            List<Discount> records = discountMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            stdOrderData.getDiscountList().addAll(records);

        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.CANYIN)) {        //餐饮
            CaterrsMapper caterrsMapper = SpringUtil.getBean(CaterrsMapper.class);
            List<Cater_rs> records = caterrsMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            stdOrderData.getCaters().addAll(records);

        }

        //TODO 还有其他订单
        return stdOrderData;
    }

    /**
     * @param bookingid 历史表主订单号
     * @param projectId 项目ID
     * @return 标准对象数据
     */
    public StdOrderData getOrderHisDetail(String bookingid, String projectId) {
        BookingrsHisMapper bookingrsHisMapper = SpringUtil.getBean(BookingrsHisMapper.class);
        StdOrderData stdOrderData = new StdOrderData();
        Booking_rs_his bookingRsHis = bookingrsHisMapper.findByBookingidAndProjectid(bookingid, projectId);
        if (bookingRsHis == null) {
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("订单记录不存在"));
        }
        Booking_rs bookingRs = new Booking_rs();
        BeanUtil.copyProperties(bookingRsHis, bookingRs);
        stdOrderData.setBookingRs(bookingRs);
        //房型
        if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.ROOM)) {
            RoomrsHisMapper roomrsHisMapper = SpringUtil.getBean(RoomrsHisMapper.class);
            List<Room_rs_his> records = roomrsHisMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            List<Room_rs> rsList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(records)) {
                for (Room_rs_his item : records) {
                    Room_rs room_rs = new Room_rs();
                    BeanUtil.copyProperties(item, room_rs);
                    rsList.add(room_rs);
                }
            }
            stdOrderData.getRooms().addAll(rsList);
        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.TICKET)) { //门票
            Ticket_rsHisMapper ticket_rsHisMapper = SpringUtil.getBean(Ticket_rsHisMapper.class);
            List<Ticket_rs_his> records = ticket_rsHisMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            List<Ticket_rs> ticket_rsList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(records)) {
                for (Ticket_rs_his item : records) {
                    Ticket_rs ticket_rs = new Ticket_rs();
                    BeanUtil.copyProperties(item, ticket_rs);
                    ticket_rsList.add(ticket_rs);
                }
            }
            stdOrderData.getTickets().addAll(ticket_rsList);
        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.KITITEM)) { //自定义套餐
            KitfixchargeMapper kitfixchargeMapper = SpringUtil.getBean(KitfixchargeMapper.class);
            List<Kitfixcharge> records = kitfixchargeMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            stdOrderData.getKitfixcharges().addAll(records);
            stdOrderData.setKitCode(records.get(0).getCode());
        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.TAOCAN)) { //组合套餐
            //查询套餐详情列表查找其他子订单
            stdOrderData.setKitCode(bookingRs.getProduct());
            KititemMapper kititemMapper = SpringUtil.getBean(KititemMapper.class);
            List<Kititem> kititemList = kititemMapper.finByKitCodeAndProjectId(bookingRs.getProduct(), projectId);
            //查询套餐种类 查找
            List<String> productTypeList = new ArrayList<>();
            for (Kititem kititem : kititemList) {
                if (productTypeList.contains(kititem.getProducttype())) {
                    continue;
                } else {
                    productTypeList.add(kititem.getProducttype());
                }
            }
            fillStdOrderHisData(productTypeList, bookingid, projectId, stdOrderData);//查询历史


        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.MEETING)) {//会议
            //MeetingrsHisMapper rsHisMapper = SpringUtil.getBean(MeetingrsHisMapper.class);
            //List<Meeting_rs_his> records = rsHisMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            //if (CollectionUtil.isNotEmpty(records)) {
            //    List<Meeting_rs> rsList = new ArrayList<>();
            //    for (Meeting_rs_his item : records) {
            //        Meeting_rs meeting_rs = new Meeting_rs();
            //        BeanUtil.copyProperties(item, meeting_rs);
            //        rsList.add(meeting_rs);
            //    }
            //    stdOrderData.getMeetings().addAll(rsList);
            //}
        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.WARES)) { //景区商品
            SpursHisMapper spursHisMapper = SpringUtil.getBean(SpursHisMapper.class);
            List<Spu_rs_his> records = spursHisMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            if (CollectionUtil.isNotEmpty(records)) {
                List<Spu_rs> spu_rsList = new ArrayList<>();
                for (Spu_rs_his item : records) {
                    Spu_rs spu_rs = new Spu_rs();
                    BeanUtil.copyProperties(item, spu_rs);
                    spu_rsList.add(spu_rs);
                }
                stdOrderData.getSpus().addAll(spu_rsList);
            }
        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.ITEMS)) {  //伴手礼
            GiftrsHisMapper giftrsHisMapper = SpringUtil.getBean(GiftrsHisMapper.class);
            List<Gift_rs_his> records = giftrsHisMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            if (CollectionUtil.isNotEmpty(records)) {
                List<Gift_rs> gift_rsList = new ArrayList<>();
                for (Gift_rs_his item : records) {
                    Gift_rs spu_rs = new Gift_rs();
                    BeanUtil.copyProperties(item, spu_rs);
                    gift_rsList.add(spu_rs);
                }
                stdOrderData.getGifts().addAll(gift_rsList);
            }

        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.DISCOUNT)) { //折扣
            DiscountMapper discountMapper = SpringUtil.getBean(DiscountMapper.class);
            List<Discount> records = discountMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            stdOrderData.getDiscountList().addAll(records);
        } else if (ProdType.getProdType(bookingRs.getPtype()).equals(ProdType.CANYIN)) {   //餐饮
            CaterrsHisMapper caterrsHisMapper = SpringUtil.getBean(CaterrsHisMapper.class);
            List<Cater_rs_his> records = caterrsHisMapper.findAllByBookingidAndProjectid(bookingid, projectId);
            if (CollectionUtil.isNotEmpty(records)) {
                List<Cater_rs> cater_rsList = new ArrayList<>();
                for (Cater_rs_his item : records) {
                    Cater_rs cater_rs = new Cater_rs();
                    BeanUtil.copyProperties(item, cater_rs);
                    cater_rsList.add(cater_rs);
                }
                stdOrderData.getCaters().addAll(cater_rsList);
            }
        }

        //TODO 还有其他订单
        return stdOrderData;
    }

    /**
     * 套餐查询填充标准数据对象
     *
     * @param productTypeList 订单种类
     * @param bookingid       主订单号
     * @param projectId
     * @param stdOrderData    标准数据对象
     */
    private void fillStdOrderData(List<String> productTypeList, String bookingid, String projectId, StdOrderData stdOrderData) {
        if (CollectionUtil.isNotEmpty(productTypeList)) {
            for (String type : productTypeList) {
                //获取房型订单
                if (ProdType.getProdType(type).equals(ProdType.ROOM)) {
                    List<Room_rs> rsList = SpringUtil.getBean(RoomrsMapper.class).findRoom_rsByBookingidAndProjectid(bookingid, projectId);
                    if (CollectionUtil.isNotEmpty(rsList)) {
                        stdOrderData.getRooms().addAll(rsList);
                    }

                } else if (ProdType.getProdType(type).equals(ProdType.TICKET)) {
                    //获取票型订单
                    List<Ticket_rs> rsList = SpringUtil.getBean(Ticket_rsMapper.class).findTicket_rsByBookingidAndProjectid(bookingid, projectId);
                    if (CollectionUtil.isNotEmpty(rsList)) {
                        if (CollectionUtil.isNotEmpty(rsList)) {
                            stdOrderData.getTickets().addAll(rsList);
                        }
                    }

                } else if (ProdType.getProdType(type).equals(ProdType.KITITEM)) { //中台自定义套餐
                    //自定义套餐
                    List<Kitfixcharge> rsList = SpringUtil.getBean(KitfixchargeMapper.class).findAllByBookingidAndProjectid(bookingid, projectId);
                    if (CollectionUtil.isNotEmpty(rsList)) {
                        if (CollectionUtil.isNotEmpty(rsList)) {
                            stdOrderData.getKitfixcharges().addAll(rsList);
                        }
                    }

                } else if (ProdType.getProdType(type).equals(ProdType.CANYIN)) {
                    //餐饮
                    List<Cater_rs> rsList = SpringUtil.getBean(CaterrsMapper.class).findAllByBookingidAndProjectid(bookingid, projectId);
                    if (CollectionUtil.isNotEmpty(rsList)) {
                        if (CollectionUtil.isNotEmpty(rsList)) {
                            stdOrderData.getCaters().addAll(rsList);
                        }
                    }

                }
            }
        }
    }


    /**
     * 套餐查询填充标准历史数据对象
     *
     * @param productTypeList 订单种类
     * @param bookingid       主订单号
     * @param projectId       项目Id
     * @param stdOrderData    标准数据对象
     */
    private void fillStdOrderHisData(List<String> productTypeList, String bookingid, String projectId, StdOrderData stdOrderData) {
        if (CollectionUtil.isNotEmpty(productTypeList)) {
            for (String type : productTypeList) {
                //获取房型订单
                if (ProdType.getProdType(type).equals(ProdType.ROOM)) {
                    List<Room_rs_his> rsHisList = SpringUtil.getBean(RoomrsHisMapper.class).findAllByBookingidAndProjectid(bookingid, projectId);
                    if (CollectionUtil.isNotEmpty(rsHisList)) {
                        List<Room_rs> rsList = new ArrayList<>();
                        for (Room_rs_his item : rsHisList) {
                            Room_rs room_rs = new Room_rs();
                            BeanUtil.copyProperties(item, room_rs);
                            rsList.add(room_rs);
                        }
                        stdOrderData.getRooms().addAll(rsList);
                    }


                } else if (ProdType.getProdType(type).equals(ProdType.TICKET)) {
                    //获取票型订单
                    List<Ticket_rs_his> rsHisList = SpringUtil.getBean(Ticket_rsHisMapper.class).findAllByBookingidAndProjectid(bookingid, projectId);
                    if (CollectionUtil.isNotEmpty(rsHisList)) {
                        List<Ticket_rs> rsList = new ArrayList<>();
                        for (Ticket_rs_his item : rsHisList) {
                            Ticket_rs ticket_rs = new Ticket_rs();
                            BeanUtil.copyProperties(item, ticket_rs);
                            rsList.add(ticket_rs);
                        }
                        stdOrderData.getTickets().addAll(rsList);
                    }

                } else if (ProdType.getProdType(type).equals(ProdType.KITITEM)) { //中台自定义套餐
                    //自定义套餐
                    List<Kitfixcharge> rsList = SpringUtil.getBean(KitfixchargeMapper.class).findAllByBookingidAndProjectid(bookingid, projectId);
                    if (CollectionUtil.isNotEmpty(rsList)) {
                        if (CollectionUtil.isNotEmpty(rsList)) {
                            stdOrderData.getKitfixcharges().addAll(rsList);
                        }
                    }

                } else if (ProdType.getProdType(type).equals(ProdType.CANYIN)) {
                    //餐饮
                    List<Cater_rs_his> rsHisList = SpringUtil.getBean(CaterrsHisMapper.class).findAllByBookingidAndProjectid(bookingid, projectId);
                    if (CollectionUtil.isNotEmpty(rsHisList)) {
                        if (CollectionUtil.isNotEmpty(rsHisList)) {
                            List<Cater_rs> rsList = new ArrayList<>();
                            for (Cater_rs_his item : rsHisList) {
                                Cater_rs cater_rs = new Cater_rs();
                                BeanUtil.copyProperties(item, cater_rs);
                                rsList.add(cater_rs);
                            }
                            stdOrderData.getCaters().addAll(rsList);
                        }
                    }

                }
            }
        }
    }


}
