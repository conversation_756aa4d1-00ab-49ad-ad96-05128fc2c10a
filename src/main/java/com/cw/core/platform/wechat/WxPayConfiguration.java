package com.cw.core.platform.wechat;

import com.cw.cache.GlobalCache;
import com.cw.entity.Sysconf;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Slf4j
@Configuration
//@EnableConfigurationProperties(WxMaProperties.class)  固定启用
public class WxPayConfiguration {
    private static Map<String, WxPayService> payServices = Maps.newConcurrentMap();//KEY: PROJECTID

    private final WxGlobalProperties properties;

    @Autowired
    public WxPayConfiguration(WxGlobalProperties properties) {
        this.properties = properties;
    }


//    public static WxPayService getPayService(String appid) {
//        WxPayService wxService = payServices.get(appid);
//        if (wxService == null) {
//            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
//        }
//        return wxService;
//    }

    public static WxPayService getProjectPayService(String projectId) {
        Sysconf sysconf = GlobalCache.getDataStructure().getConfCache().getOne(projectId);
        if (sysconf == null) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", projectId));
        }
        String appid = sysconf.getWxappkey();
        WxPayService wxService = payServices.get(projectId);
        if (wxService == null) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }
        return wxService;
    }


    /*public void init() {
        Map<String, WxPayService> tempService = new HashMap<>();
        for (WxPayConfig row : this.properties.getAppidWxPayConfigs().values()) {
            WxPayService wxPayService=new WxPayServiceImpl();
            wxPayService.setConfig(row);
            tempService.put(row.getAppId(),wxPayService);
        }
        payServices=tempService;
    }*/

    public void updService(String appid, WxPayConfig config) {//TODO  属性刷新之后.要刷新服务
        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(config);
        payServices.put(appid, wxPayService);


    }


}
