package com.cw.core.platform.wechat;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedissonConfigImpl;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 微信公众号服务配置
 */
@Slf4j
@Configuration
public class WxMpConfiguration {
    private static Map<String, WxMpService> mpServices = Maps.newConcurrentMap();
    @Autowired
    RedissonClient redissonClient;

    public static WxMpService getMpService(String appid) {
        WxMpService wxService = mpServices.get(appid);
        if (wxService == null) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }
        return wxService;
    }


    public void updService(WxGlobalProperties.WxAppConfig config) {//TODO  属性刷新之后.要刷新服务
        WxMpDefaultConfigImpl newConfig = new WxMpRedissonConfigImpl(redissonClient);
        newConfig.setAppId(config.getAppid());
        newConfig.setSecret(config.getSecret());
        newConfig.setToken(config.getToken());
        newConfig.setAesKey(config.getAesKey());

        WxMpService service = new WxMpServiceImpl();
        service.setWxMpConfigStorage(newConfig);
        mpServices.put(config.getAppid(), service);
    }


}
