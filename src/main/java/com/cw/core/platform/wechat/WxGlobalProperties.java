package com.cw.core.platform.wechat;

import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SysConfCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.entity.Sysconf;
import com.cw.entity.Vendorconfig;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.VendorType;
import com.google.common.collect.Maps;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 存放项目上所有景区的微信app 相关配置
 * 修改微信配置时.请在 MQ 广播收到后马上刷新
 */
@Data
@Component
public class WxGlobalProperties {

    /**
     * Key : projectID,
     */
    private Map<String, WxAppConfig> appidWxAppConfigs = Maps.newConcurrentMap();

    private Map<String, WxAppConfig> appidWxMpConfigs = Maps.newConcurrentMap();


    /**
     * 系统启动时.做初始化
     */
    public void refreshAll() {
        VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        SysConfCache sysConfCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SYSCONF);
        for (Sysconf sysconf : sysConfCache.getAllSysConf()) {
            Vendorconfig wxconfig = vendorConfigCache.getVendorConfig(sysconf.getProjectid(), VendorType.WX_APP);
            if (wxconfig != null && !wxconfig.getAppid().isEmpty()) {
                refreshSingle(wxconfig.getAppid(), wxconfig.getProjectid(), sysconf);
            }
            Vendorconfig wxmpconfig = vendorConfigCache.getVendorConfig(sysconf.getProjectid(), VendorType.WX_MP);
            if (wxmpconfig != null && !wxmpconfig.getAppid().isEmpty()) {
                refeshMpSingle(wxmpconfig.getAppid(), wxmpconfig.getProjectid(), sysconf);
            }
        }
    }

    public void refreshSingle(String appid, String projectid, Sysconf sysconf) {
        if (sysconf == null) {
            appidWxAppConfigs.remove(appid);
        }
        VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig appVendorConfig = configCache.getRecord(projectid, VendorType.WX_APP.name());
//        Vendorconfig payVendorConfig = configCache.getRecord(projectid, VendorType.WX_PAY.name());


        if (appVendorConfig != null) {
            WxAppConfig config = new WxAppConfig();  //加载商户小程序配置
            config.setProjectid(appVendorConfig.getProjectid());
            config.setAppid(appVendorConfig.getAppid());// config.setAppid(sysconf.getWxappkey());
            config.setSecret(appVendorConfig.getAppsecrect());  //config.setSecret(sysconf.getWxappsecrect());
            appidWxAppConfigs.put(appid, config);
            SpringUtil.getBean(WxMaConfiguration.class).updService(config);
        }
    }

    public void refeshMpSingle(String appid, String projectid, Sysconf sysconf) {
        if (sysconf == null) {
            appidWxMpConfigs.remove(appid);
        }
        VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig appVendorConfig = configCache.getRecord(projectid, VendorType.WX_MP.name());

        if (appVendorConfig != null) {
            WxAppConfig config = new WxAppConfig();  //加载微信公众号配置
            config.setProjectid(appVendorConfig.getProjectid());
            config.setAppid(appVendorConfig.getAppid());// config.setAppid(sysconf.getWxappkey());
            config.setSecret(appVendorConfig.getAppsecrect());  //config.setSecret(sysconf.getWxappsecrect());
            appidWxMpConfigs.put(appid, config);
            SpringUtil.getBean(WxMpConfiguration.class).updService(config);
        }
    }

/*    private String getPrivateKeyPath(String appid, String content) {
        String tmpDirsLocation = System.getProperty("java.io.tmpdir");
        File file = new File(StrUtil.format(tmpDirsLocation + "/{}key.pem", appid));
        file.deleteOnExit();
        FileUtil.touch(file);
        FileUtil.writeBytes(content.getBytes(), file);
        return file.getPath();
    }

    private String getPrivateCertPath(String appid, String content) {
        String tmpDirsLocation = System.getProperty("java.io.tmpdir");
        File file = new File(StrUtil.format(tmpDirsLocation + "/{}cert.pem", appid));
        file.deleteOnExit();
        FileUtil.touch(file);
        FileUtil.writeBytes(content.getBytes(), file);
        return file.getPath();
    }*/


    @Data
    public static class WxAppConfig {
        private String projectid;
        /**
         * 设置微信小程序的appid
         */
        private String appid;

        /**
         * 设置微信小程序的Secret
         */
        private String secret;

        /**
         * 设置微信小程序消息服务器配置的token
         */
        private String token;

        /**
         * 设置微信小程序消息服务器配置的EncodingAESKey
         */
        private String aesKey;

        /**
         * 消息格式，XML或者JSON
         */
        private String msgDataFormat;
    }

}
