package com.cw.core;


import com.cw.cache.GlobalCache;
import com.cw.entity.Naparam;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.CalculateDate;
import com.cw.utils.LoggerUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.na.NaParams;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/12/4 15:25
 **/
@Service
public class CoreLog {
    @Autowired
    DaoLocal daoLocal;
    private Logger nalogger = LoggerUtil.getLogger(LoggerUtil.LoggerType.nalog);

    //非交易日志
    public void clearNotOrderLog(Date naDate, String projectId) {
        //缓存配置一个指定天数
        Integer moveDay = 3;
        Naparam naparam = (Naparam) GlobalCache.getDataStructure()
                .getCache(SystemUtil.GlobalDataType.NA_PARAM).getRecord(projectId, NaParams.CL_NOTORDER_LOG_DAY.name());
        if (naparam != null) {
            moveDay = Integer.valueOf(naparam.getParamvalue());
        }
        Date fromDateObj = CalculateDate.reckonDay(naDate, 5, moveDay * -1);
        String fromDateStr = CalculateDate.dateToString(fromDateObj);
        nalogger.info("★清理{}天前非订单日志 {},开始", moveDay, fromDateStr);


        //短信日志
        Integer moveMsgDay = SystemUtil.DEFAULT_NA_MOVE_DAY;
        Date msgDate = CalculateDate.reckonDay(naDate, 5, moveMsgDay * -1);
        String msgDateStr = CalculateDate.dateToString(msgDate);
        Integer res2 = daoLocal.batchNativeOption("delete from Msglog where date<=?1 and projectid=?2", msgDate, projectId);
        nalogger.info("清理短信日志{}条 {}", res2, msgDateStr);

        //接口日志
        //Connection connection = null;
        //PreparedStatement pmspst = null;
        //int count = 0;
        //try {
        //    connection = daoLocal.getDataSource().getConnection();
        //    connection.setAutoCommit(false);
        //    String pmssql = "delete from pmslog where msgtype='syncRatedetail' and pushtime<='" + fromDateStr + "'";
        //    pmspst = connection.prepareStatement(pmssql);
        //    count = pmspst.executeUpdate();
        //    connection.commit();
        //    pmspst.close();
        //} catch (Exception e) {
        //    e.printStackTrace();
        //} finally {
        //    try {
        //        if(pmspst!=null){
        //            pmspst.close();
        //        }
        //        if (connection!=null) {
        //            connection.close();
        //        }
        //    } catch (SQLException e) {
        //        e.printStackTrace();
        //    }
        //    nalogger.info("清理Pms同步价格日志{}条",count);
        //}
        nalogger.info("★清理非订单日志,结束");
    }

    private int deleteOrderLogData(Date lastYear, String projectId) {
        try {
            String sql = "delete from Userlog where type not in('COL','REFUND','PREPAY','INVOICE') and date<=?1 and projectid=?2";
            int count = daoLocal.batchNativeOptionWithLimit(sql, 1000, lastYear, projectId);
            return count;
            //if (count > 0) {
            //    return count + deleteOrderLogData(lastYear, projectId);
            //} else {
            //    return 0;
            //}
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 清除用户订单日志记录
     *
     * @param naDate
     * @param projectId
     */
    public void clearOrderLog(Date naDate, String projectId) {
        //缓存配置一个指定天数
        Integer moveDay = SystemUtil.DEFAULT_NA_MOVE_DAY;

        Naparam naparam = (Naparam) GlobalCache.getDataStructure()
                .getCache(SystemUtil.GlobalDataType.NA_PARAM).getRecord(projectId, NaParams.CL_ORDER_LOG_DAY.name());
        if (naparam != null) {
            moveDay = Integer.valueOf(naparam.getParamvalue());
        }
        Date fromDateObj = CalculateDate.returnDate_ZeroTime(CalculateDate.reckonDay(naDate, 5, moveDay * -1));
        String fromDateStr = CalculateDate.dateToString(fromDateObj);

        //跟订单关联的要跟着订单历史的日期走
        Integer moveOrderDay = SystemUtil.DEFAULT_NA_MOVE_DAY;
        naparam = (Naparam) GlobalCache.getDataStructure()
                .getCache(SystemUtil.GlobalDataType.NA_PARAM).getRecord(projectId, NaParams.MOVE_HISTORY_DAY.name());
        if (naparam != null) {
            moveOrderDay = Integer.valueOf(naparam.getParamvalue());
        }
        Date moveOrderDate = CalculateDate.returnDate_ZeroTime(CalculateDate.reckonDay(naDate, 5, moveOrderDay * -1));
//        String moveOrderDateStr = CalculateDate.dateToString(moveOrderDate);

        nalogger.info("★清理{}天前订单日志 {},开始", moveDay, fromDateStr);

        //todo 是否删除订单日志
        int count = deleteOrderLogData(fromDateObj, projectId);
        nalogger.info("清理用户订单日志{}条", count);

        //接口日志
        //Connection connection = null;
        //PreparedStatement pmspst = null;
        //try {
        //    connection = daoLocal.getDataSource().getConnection();
        //    connection.setAutoCommit(false);
        //
        //    //除了col_rs的日志 其它都按时间删掉
        //    String pmssql = "delete from pmslog where msgtype not in('syncRatedetail','syncColOrder') and pushtime<='" + fromDateStr + "'";
        //    pmspst = connection.prepareStatement(pmssql);
        //    count = pmspst.executeUpdate();
        //    nalogger.info("清理Pms非综合预定日志{}条, {}",count,fromDateStr);
        //    connection.commit();

        //其它只能跟着订单走 这里先处理综合订单的 有其他订单多了再说
        //List<String> otaregno = new ArrayList<>();
        //List<String> crsregno = new ArrayList<>();
        //List<String> pmsregno = new ArrayList<>();
        //List<Object[]> colRsList = daoLocal.getNativeObjectList("select channelno,colno,pmscolno from col_rs where enddate<=?1", moveOrderDate);
        //if (colRsList!=null && !colRsList.isEmpty()) {
        //    for (Object[] row: colRsList) {
        //        if(StringUtils.isNotBlank((String)row[0])){
        //            otaregno.add(row[0].toString());
        //        }
        //        if(StringUtils.isNotBlank((String)row[1])){
        //            crsregno.add(row[1].toString());
        //        }
        //        if(StringUtils.isNotBlank((String)row[2])){
        //            pmsregno.add(row[2].toString());
        //        }
        //    }
        //    Map<IfcLogType,List<String>> dataMap = new HashMap<>();
        //    dataMap.put(IfcLogType.otalog,otaregno);
        //    dataMap.put(IfcLogType.crslog,crsregno);
        //    dataMap.put(IfcLogType.pmslog,pmsregno);
        //
        //    Iterator<IfcLogType> iterator = dataMap.keySet().iterator();
        //    IfcLogType logType;
        //    final int limitCount = 50;//分批处理 每批50条
        //    StringBuffer sb = new StringBuffer();
        //    for(int i=0; i<limitCount; i++){
        //        sb.append(",?");
        //    }
        //    while(iterator.hasNext()){
        //        logType = iterator.next();
        //        List<String> tempList = dataMap.get(logType);
        //        if(!tempList.isEmpty()){
        //            int batchCount = tempList.size()/limitCount+(tempList.size()%limitCount==0?0:1);
        //            nalogger.info("需要处理{}订单{}个,分{}批",logType.name(),tempList.size(),batchCount);
        //            List<List<String>> batchList = new ArrayList<>();
        //            Stream.iterate(0, n -> n + 1).limit(batchCount).forEach(i -> {
        //                List<String> ids = tempList.stream().skip(i * limitCount).limit(limitCount).collect(Collectors.toList());
        //                batchList.add(ids);
        //            });
        //            pmspst = connection.prepareStatement("delete from "+logType.name()+" where regno in ("+sb.toString().substring(1)+")");
        //            for(List<String> ids: batchList){
        //                for(int i=0; i<ids.size(); i++){
        //                    pmspst.setString(i+1,ids.get(i));
        //                }
        //                if(ids.size()!=limitCount){//不够的要补全
        //                    for(int i=ids.size(); i<limitCount; i++){
        //                        pmspst.setString(i+1,"");
        //                    }
        //                }
        //                pmspst.addBatch();
        //            }
        //            int[] counts = pmspst.executeBatch();
        //            connection.commit();
        //            nalogger.info("清理{}接口日志{}条",logType.name(),Arrays.stream(counts).sum());
        //        }
        //    }
        //}


//            String otalogsql = "delete from otalog where regno=?";
//            String crslogsql = "delete from crslog where regno=?";
//            String pmslogsql = "delete from pmslog where regno=?";
//
//            Map<IfcLogType, PreparedStatement> pstMap = Maps.newHashMap();
//            pstMap.put(IfcLogType.otalog, connection.prepareStatement(otalogsql));
//            pstMap.put(IfcLogType.crslog, connection.prepareStatement(crslogsql));
//            pstMap.put(IfcLogType.pmslog, connection.prepareStatement(pmslogsql));
//
//            Map<IfcLogType, List<String>> paramMap = Maps.newHashMap();
//            paramMap.put(IfcLogType.otalog, new ArrayList<>());
//            paramMap.put(IfcLogType.crslog, new ArrayList<>());
//            paramMap.put(IfcLogType.pmslog, new ArrayList<>());
//
//            List<String> otaregno = new ArrayList<>();
//            List<String> crsregno = new ArrayList<>();
//            List<String> pmsregno = new ArrayList<>();
//
//            //col_rs
//            List<Object[]> col_rs_his = daoLocal.getNativeObjectList("select channelno,colno,pmscolno from col_rs_his where enddate=?1", fromDate);
//            if (col_rs_his.size()>0) {
//                for (Object[] row: col_rs_his) {
//                    if (!row[0].toString().isEmpty()) {
//                        otaregno.add(row[0].toString());
//                    }
//                    if (!row[1].toString().isEmpty()) {
//                        crsregno.add(row[1].toString());
//                    }
//                    if (!row[2].toString().isEmpty()) {
//                        pmsregno.add(row[2].toString());
//                    }
//                }
//            }
//
//            //room_rs
//            List<Object[]> room_rs_his = daoLocal.getNativeObjectList("select regno,pmsno from room_rs_his where deptdate=?1", fromDate);
//            if (room_rs_his.size()>0) {
//                for (Object[] row: room_rs_his) {
//                    if (!row[0].toString().isEmpty()) {
//                        crsregno.add(row[0].toString());
//                    }
//                    if (!row[1].toString().isEmpty()) {
//                        pmsregno.add(row[1].toString());
//                    }
//                }
//            }
//
//            //ticket_rs
//            List<Object[]> ticket_rs_his = daoLocal.getNativeObjectList("select bookingid,pmsno from ticket_rs_his where `date`=?1", fromDate);
//            if (ticket_rs_his.size()>0) {
//                for (Object[] row: ticket_rs_his) {
//                    if (!row[0].toString().isEmpty()) {
//                        crsregno.add(row[0].toString());
//                    }
//                    if (!row[1].toString().isEmpty()) {
//                        pmsregno.add(row[1].toString());
//                    }
//                }
//            }
//
//            //cater_rs
//            List<Object[]> cater_rs_his = daoLocal.getNativeObjectList("select bookingid,pmsno from cater_rs_his where `date`=?1", fromDate);
//            if (cater_rs_his.size()>0) {
//                for (Object[] row: cater_rs_his) {
//                    if (!row[0].toString().isEmpty()) {
//                        crsregno.add(row[0].toString());
//                    }
//                    if (!row[1].toString().isEmpty()) {
//                        pmsregno.add(row[1].toString());
//                    }
//                }
//            }
//
//            //boat_rs
//            List<Object[]> boat_rs_his = daoLocal.getNativeObjectList("select bookingid,pmsno from boat_rs_his where `date`=?1", fromDate);
//            if (boat_rs_his.size()>0) {
//                for (Object[] row: boat_rs_his) {
//                    if (!row[0].toString().isEmpty()) {
//                        crsregno.add(row[0].toString());
//                    }
//                    if (!row[1].toString().isEmpty()) {
//                        pmsregno.add(row[1].toString());
//                    }
//                }
//            }
//
//            //spa_rs
//            List<Object[]> spa_rs_his = daoLocal.getNativeObjectList("select bookingid,pmsno from spa_rs_his where `date`=?1", fromDate);
//            if (spa_rs_his.size()>0) {
//                for (Object[] row: spa_rs_his) {
//                    if (!row[0].toString().isEmpty()) {
//                        crsregno.add(row[0].toString());
//                    }
//                    if (!row[1].toString().isEmpty()) {
//                        pmsregno.add(row[1].toString());
//                    }
//                }
//            }
//
//            //guide_rs
//            List<Object[]> guide_rs_his = daoLocal.getNativeObjectList("select bookingid,pmsno from guide_rs_his where departure=?1", fromDate);
//            if (guide_rs_his.size()>0) {
//                for (Object[] row: guide_rs_his) {
//                    if (!row[0].toString().isEmpty()) {
//                        crsregno.add(row[0].toString());
//                    }
//                    if (!row[1].toString().isEmpty()) {
//                        pmsregno.add(row[1].toString());
//                    }
//                }
//            }
//
//            paramMap.put(IfcLogType.otalog, otaregno);
//            paramMap.put(IfcLogType.crslog, crsregno);
//            paramMap.put(IfcLogType.pmslog, pmsregno);
//
//            Integer limitSize = 500;
//            for (Map.Entry<IfcLogType, PreparedStatement> entry : pstMap.entrySet()) {
//                List<String> paramList = paramMap.get(entry.getKey());
//                if (paramList.size()>0) {
//                    Integer limit = (paramList.size() + limitSize - 1) / limitSize;
//                    List<List<String>> strList = new ArrayList<>();
//                    Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
////                        String ids = paramList.stream().skip(i * limitSize).limit(limitSize).map(item->"'"+item+"'").collect(Collectors.joining(","));
//                        List<String> ids = paramList.stream().skip(i * limitSize).limit(limitSize).collect(Collectors.toList());
//                        strList.add(ids);
//                    });
//                    int totalValue = 0;
//                    for (List<String> row: strList) {
////                        entry.getValue().setString(1, row);
//                        String ids = row.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
//                        Integer res = daoLocal.batchNativeOption("delete from Userlog where regno in (" + ids + ")");
//                        for (String val: row) {
//                            entry.getValue().setString(1, val);
//                            entry.getValue().addBatch();
//                        }
//                        totalValue += res;
//                    }
//                    nalogger.info("清理{}用户日志,数量{}",entry.getKey().name(),totalValue);
//                    int[] result = entry.getValue().executeBatch();
//                    totalValue = 0;
//                    if(result!=null && result.length>0){
//                        for(int temp: result){
//                            totalValue += temp;
//                        }
//                    }
//                    nalogger.info("清理{}OTA日志,数量{}",entry.getKey().name(),totalValue);
//                }
//            }
//            connection.commit();
//            for (Map.Entry<IfcLogType, PreparedStatement> entry : pstMap.entrySet()) {
//                entry.getValue().close();
//            }
        nalogger.info("★清理订单日志,结束");
        //} catch (Exception e) {
        //    e.printStackTrace();
        //} finally {
        //    try {
        //        if(pmspst!=null){
        //            pmspst.close();
        //        }
        //        if (connection!=null) {
        //            connection.close();
        //        }
        //    } catch (SQLException e) {
        //        e.printStackTrace();
        //    }
        //}
    }


}
