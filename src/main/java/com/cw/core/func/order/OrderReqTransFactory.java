package com.cw.core.func.order;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.func.Var;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.arithmetic.func.prodfactory.impl.ProdTicketInfo;
import com.cw.arithmetic.func.prodfactory.impl.ProdWareInfo;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.GiftItemCache;
import com.cw.config.exception.CustomException;
import com.cw.core.SeqNoService;
import com.cw.entity.*;
import com.cw.mapper.KititemMapper;
import com.cw.mapper.ShoppingitemMapper;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.common.spuitem.SpuPackCheckInfo;
import com.cw.pojo.dto.app.req.node.AddressInfo;
import com.cw.pojo.dto.app.req.node.CombineInfo;
import com.cw.pojo.dto.conf.res.products.SpusitemPackInfo;
import com.cw.service.context.WebAppGlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 将购买请求转换成订单实体.并写入订单号.付款单号
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/13 11:56
 **/
@Service
public class OrderReqTransFactory {

    private final SeqNoService seqNoService;

    @Autowired
    public OrderReqTransFactory(SeqNoService seqNoService) {
        this.seqNoService = seqNoService;
    }


    public StdOrderData parseOrderForm(StdOrderReq req) {
        StdOrderData stdOrderData = new StdOrderData();
        stdOrderData.setBookingRs(req.getBookingRs());
        stdOrderData.setActCode(req.getActivityCode());
        stdOrderData.setKitCode(req.getKitCode());
        stdOrderData.setUid(req.getUid());
        stdOrderData.getBookingRs().setAgenttype(req.getAgentType());
        //读取数据库.  将固定项目转成订单. 并将购买数量做转换 套餐场景时.各个订单的购买数
        if (req.getBookingRs().getPtype().equals(ProdType.TAOCAN.val())) {
            transKitsProduct(stdOrderData, req);
        } else {  //普通单一产品购买场景
            stdOrderData.setRooms(req.getRoomRss());
            stdOrderData.setCaters(req.getCaterRss());
            stdOrderData.setTickets(req.getTicketRss());
            stdOrderData.setSpus(req.getSpuRss());
            stdOrderData.setGifts(req.getGiftRss());//伴手礼
            stdOrderData.setDiscountList(req.getDiscount());
            stdOrderData.setMeetings(req.getMeetingRs());
        }
        return stdOrderData;
    }


    private void transKitsProduct(StdOrderData orderData, StdOrderReq req) {
        KititemMapper kititemMapper = SpringUtil.getBean(KititemMapper.class);
        List<Kititem> kititems = kititemMapper.finByKitCodeAndProjectId(req.getKitCode(), WebAppGlobalContext.getCurrentAppProjectId());
        fillKitsItem2StdData(kititems, orderData, req);
    }

    public HashMap<Kititem, Object> transKitItem2Order(List<Kititem> kititems, Date startdate, Integer num, StdOrderData stdOrderData) {
        HashMap<Kititem, Object> itemMap = Maps.newHashMap();
        for (Kititem item : kititems) {
            if (ProdType.ROOM.val().equals(item.getProducttype())) {
                Room_rs rs = kitItem2Room(item, startdate, num);
                itemMap.put(item, rs);
                stdOrderData.getRooms().add(rs);
            }
            if (ProdType.TICKET.val().equals(item.getProducttype())) {
                Ticket_rs rs = kitItem2Ticket(item, startdate, num);
                itemMap.put(item, rs);
                stdOrderData.getTickets().add(rs);
//                itemMap.put(item,kitItem2Ticket(item, startdate,num));
            }
            if (ProdType.CANYIN.val().equals(item.getProducttype())) {
                Cater_rs rs = kitItem2Cater(item, startdate, num);
                itemMap.put(item, rs);
                stdOrderData.getCaters().add(rs);
            }
            if (ProdType.KITITEM.val().equals(item.getProducttype())) {
                Kitfixcharge kitfixcharge = kitItem2KitFixChargeItem(item, startdate, num);
                itemMap.put(item, kitfixcharge);
                stdOrderData.getKitfixcharges().add(kitfixcharge);
            }
        }
        return itemMap;
    }


    public List<StdOrderData> parseShopitemsCommit(List<Long> ids) {
        List<StdOrderData> list = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(ids)) {
            ShoppingitemMapper mapper = SpringUtil.getBean(ShoppingitemMapper.class);
            List<Shopping_item> items = mapper.findAllByIds(ids);
            //购物车批量提交
            if (CollectionUtil.isEmpty(items)) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("提交的购物车内容为空"));
            }
            fillItems2StdData(items, list);
        } else {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("提交的购物车内容为空"));
        }
        return list;
    }

    /**
     * 初始化订单字段.写入项目 ID, 创建时间,订单号.根据购买产品将产品类型写入订单
     *
     * @param data
     */
    public void writeMakerAndRegno(StdOrderData data) {
        String bookingId = seqNoService.getSequenceID(SystemUtil.SequenceKey.BOOKINGID);
        String projectId = WebAppGlobalContext.getCurrentAppProjectId();
        String userid = WebAppGlobalContext.getCurrentAppUserId();
        LocalDateTime now = LocalDateTime.now();

        data.getBookingRs().setProjectid(projectId);
        data.getBookingRs().setBookingid(bookingId);
        data.getBookingRs().setMainstatus(StatusUtil.BookingRsStatus.INITIAL);
        data.getBookingRs().setUid(userid);
        data.getBookingRs().setCreatedate(now);
        List<Long> dates = new ArrayList<>();


        for (Room_rs rs : data.getRooms()) {
            rs.setBookingid(bookingId);
            rs.setUid(userid);
            rs.setCreatedate(now);
            rs.setProjectid(projectId);
            rs.setRegno(seqNoService.getSequenceID(SystemUtil.SequenceKey.SUBORDER));
            if (data.getBookingRs().getProduct().isEmpty() && data.getBookingRs().getPtype().isEmpty()) {
                data.getBookingRs().setProduct(rs.getRmtype());
                data.getBookingRs().setPtype(ProdType.ROOM.val());
                data.getBookingRs().setAnz(rs.getAnz());
            }
            dates.add(rs.getArrdate().getTime());
            dates.add(rs.getDeptdate().getTime());
        }
        for (Cater_rs rs : data.getCaters()) {
            rs.setBookingid(bookingId);
            rs.setUid(userid);
            rs.setCreatetime(now);
            rs.setProjectid(projectId);
            rs.setRegno(seqNoService.getSequenceID(SystemUtil.SequenceKey.SUBORDER));
            if (data.getBookingRs().getProduct().isEmpty() && data.getBookingRs().getPtype().isEmpty()) {
                data.getBookingRs().setProduct(rs.getCcode().isEmpty() ? rs.getRestaurant() : rs.getCcode());
                data.getBookingRs().setPtype(ProdType.CANYIN.val());
                data.getBookingRs().setAnz(rs.getAnz(0));

            }
            dates.add(rs.getUsedate().getTime());
        }
        for (Ticket_rs rs : data.getTickets()) {
            rs.setBookingid(bookingId);
            rs.setUid(userid);
            rs.setCreatedate(now);
            rs.setProjectid(projectId);
            rs.setRegno(seqNoService.getSequenceID(SystemUtil.SequenceKey.SUBORDER));
            if (data.getBookingRs().getProduct().isEmpty() && data.getBookingRs().getPtype().isEmpty()) {
                data.getBookingRs().setProduct(rs.getTcode());
                data.getBookingRs().setPtype(ProdType.TICKET.val());
                data.getBookingRs().setAnz(rs.getAnz());
            }
            dates.add(rs.getUsedate().getTime());
            dates.add(CalculateDate.reckonDay(rs.getUsedate(), 5, ContentCacheTool.getTicketExpireLen(rs.getTcode(), rs.getUsedate(), rs.getProjectid())).getTime());
            ProdTicketInfo ticketInfo = ProdFactory.getProd(ProdType.TICKET);
            String groupCode = ticketInfo.getProdGroupCode(rs.getTcode(), projectId);
            rs.setGroupid(groupCode);
        }
        for (Spu_rs rs : data.getSpus()) {
            rs.setBookingid(bookingId);
            rs.setUid(userid);
            rs.setProjectid(projectId);
            rs.setCreatetime(now);
            rs.setRegno(seqNoService.getSequenceID(SystemUtil.SequenceKey.SUBORDER));

            //dates.add(CalculateDate.reckonDay(rs.getUsedate(), 5, ContentCacheTool.getTicketExpireLen(rs.getTcode(), rs.getUsedate(), rs.getProjectid())).getTime());

            ProdWareInfo prodWareInfo = ProdFactory.getProd(ProdType.WARES);
            Spusitem spusitem = prodWareInfo.getProdRecord(rs.getCode(), rs.getProjectid(), StrUtil.EMPTY);

            if (!spusitem.getLpack()) {
                rs.setEnddate(CalculateDate.reckonDay(rs.getStartdate(), 5, prodWareInfo.getExpireLen(rs.getCode(), rs.getStartdate(), rs.getProjectid())));//24.8.16 统一音频.券类的有效期
            } else {
                rs.setStartdate(CalculateDate.maxDate(spusitem.getStartsell(), CalculateDate.getSystemDate()));
                rs.setEnddate(spusitem.getEndsell());
            }

            dates.add(rs.getStartdate().getTime());
            dates.add(rs.getEnddate().getTime());

            if (data.getBookingRs().getProduct().isEmpty() && data.getBookingRs().getPtype().isEmpty()) {
                data.getBookingRs().setProduct(rs.getCode());
                data.getBookingRs().setPtype(ProdType.WARES.val());
                data.getBookingRs().setAnz(rs.getAnz(0));
            }
        }
        for (Gift_rs rs : data.getGifts()) {//todo 伴手礼 多个商品
            rs.setGuestname(data.getBookingRs().getGuestname());
            rs.setTel(data.getBookingRs().getTel());
            rs.setBookingid(bookingId);
            rs.setUid(userid);
            rs.setProjectid(projectId);
            rs.setRegno(seqNoService.getSequenceID(SystemUtil.SequenceKey.SUBORDER));
            rs.setCreatetime(now);
            if (data.getBookingRs().getProduct().isEmpty() && data.getBookingRs().getPtype().isEmpty()) { //todo 多个商品主单显示的产品代码和数量
                data.getBookingRs().setProduct(rs.getCode());//产品代码
                data.getBookingRs().setPtype(ProdType.ITEMS.val());
                data.getBookingRs().setAnz(rs.getAnz(0));
            }
            if (data.getCombineInfo() != null) {//获取收货地址
                rs.setArea(data.getCombineInfo().getProvince());//省份
                rs.setAddress(data.getCombineInfo().getAddress());//详细地址
                rs.setPostage(data.getCombineInfo().getPostage());//运费
            }
        }
        for (Kitfixcharge rs : data.getKitfixcharges()) {
            rs.setBookingid(bookingId);
            rs.setUid(userid);
            rs.setProjectid(projectId);
            rs.setRegno(seqNoService.getSequenceID(SystemUtil.SequenceKey.SUBORDER));
        }
        for (Discount rs : data.getDiscountList()) {
            rs.setBookingid(bookingId);
            rs.setProjectid(projectId);
        }
        Collections.sort(dates);
        if (!dates.isEmpty()) {
            Date startdate = new Date(dates.get(0));
            Date enddate = new Date(dates.get(dates.size() - 1));
            data.getBookingRs().setArrdate(startdate);
            data.getBookingRs().setDeptdate(enddate);
        } else {
            data.getBookingRs().setArrdate(new Date());
            data.getBookingRs().setDeptdate(new Date());
        }

    }


    /**
     * 套餐内容转订单数据
     *
     * @param kititems
     * @param stdOrderData
     * @param req
     */
    private void fillKitsItem2StdData(List<Kititem> kititems, StdOrderData stdOrderData, StdOrderReq req) {
        for (Kititem item : kititems) {
            if (!item.getItemtype().isEmpty() && !req.getKitSelectedItem().contains(item.getSqlid())) {
                continue;
            }
            if (ProdType.ROOM.val().equals(item.getProducttype())) {
                stdOrderData.getRooms().add(kitItem2Room(item, req.getOrderStartDate(), req.getKitNum()));
            }
            if (ProdType.TICKET.val().equals(item.getProducttype())) {
                stdOrderData.getTickets().add(kitItem2Ticket(item, req.getOrderStartDate(), req.getKitNum()));
            }
            if (ProdType.CANYIN.val().equals(item.getProducttype())) {
                stdOrderData.getCaters().add(kitItem2Cater(item, req.getOrderStartDate(), req.getKitNum()));
            }
            if (ProdType.KITITEM.val().equals(item.getProducttype())) {
                stdOrderData.getKitfixcharges().add(kitItem2KitFixChargeItem(item, req.getOrderStartDate(), req.getKitNum()));
            }
        }
    }

    /**
     * 购物车转订单数据
     *
     * @param items 购物车记录.
     * @param list  订单结构
     */
    private void fillItems2StdData(List<Shopping_item> items, List<StdOrderData> list) {
        for (Shopping_item item : items) {
            StdOrderData stdOrderData = new StdOrderData();
            Booking_rs bookingRs = new Booking_rs();
            bookingRs.setMainstatus(StatusUtil.BookingRsStatus.INITIAL);
            if (ProdType.ROOM.val().equals(item.getPtype())) {
                stdOrderData.getRooms().add(item2Room(item));
            }
            if (ProdType.TICKET.val().equals(item.getPtype())) {
                stdOrderData.getTickets().add(item2Ticket(item));
            }
            if (ProdType.CANYIN.val().equals(item.getPtype())) {
                stdOrderData.getCaters().add(item2Cater(item));
            }
            if (ProdType.ITEMS.val().equals(item.getPtype())) {
                stdOrderData.getSpus().add(item2SpuItem(item));
            }
            stdOrderData.setBookingRs(bookingRs);
            list.add(stdOrderData);
        }
    }

    private Room_rs item2Room(Shopping_item item) {
        Room_rs rs = new Room_rs();
        rs.setRmtype(item.getSkuid());
        rs.setArrdate(item.getStartdate());
        rs.setDeptdate(item.getEnddate());
        rs.setAnz(item.getNum());
        rs.setCreatedate(LocalDateTime.now());
        return rs;
    }

    private Ticket_rs item2Ticket(Shopping_item item) {
        Ticket_rs rs = new Ticket_rs();
        rs.setTcode(item.getSkuid());
        rs.setUsedate(item.getStartdate());
        rs.setAnz(item.getNum());
        rs.setCreatedate(LocalDateTime.now());
        return rs;
    }

    private Cater_rs item2Cater(Shopping_item item) {
        Cater_rs rs = new Cater_rs();
        rs.setCcode(item.getSkuid());
        rs.setUsedate(item.getStartdate());
        rs.setAnz(item.getNum());
        rs.setCreatetime(LocalDateTime.now());
        return rs;
    }

    private Kitfixcharge item2FixchargeItem(Shopping_item item) {
        Kitfixcharge rs = new Kitfixcharge();
        rs.setCode(item.getSkuid());
        rs.setAnz(item.getNum());
        return rs;
    }


    private Spu_rs item2SpuItem(Shopping_item item) {
        Spu_rs rs = new Spu_rs();
        rs.setCode(item.getSkuid());
        rs.setAnz(item.getNum());


        return rs;
    }

    private Room_rs kitItem2Room(Kititem item, Date startdate, Integer kitNum) {
        Room_rs rs = new Room_rs();
        rs.setRmtype(item.getProductcode());
        rs.setArrdate(startdate);
        rs.setDeptdate(CalculateDate.reckonDay(startdate, 5, item.getDays(0) == 0 ? 1 : item.getDays(0)));
        rs.setAnz(item.getNum(0) * kitNum);
        rs.setCreatedate(LocalDateTime.now());
        return rs;
    }

    private Ticket_rs kitItem2Ticket(Kititem item, Date startdate, Integer kitNum) {
        Ticket_rs rs = new Ticket_rs();
        rs.setTcode(item.getProductcode());
        rs.setUsedate(startdate);
        rs.setAnz(item.getNum(0) * kitNum);
        rs.setCreatedate(LocalDateTime.now());
        return rs;
    }

    private Cater_rs kitItem2Cater(Kititem item, Date startdate, Integer kitNum) {
        Cater_rs rs = new Cater_rs();
        rs.setCcode(item.getProductcode());
        rs.setUsedate(startdate);
        rs.setAnz(item.getNum(0) * kitNum);
        rs.setCreatetime(LocalDateTime.now());
        rs.setRestaurant(item.getProductgroup());

        List<String> times = StrUtil.isBlank(item.getTime()) ? Lists.newArrayList() : Lists.newArrayList(item.getTime().split(","));
        if (times.size() > 0) {
            rs.setTime(times.get(0));
        } else {
            rs.setTime("3");
        }
        return rs;
    }

    private Kitfixcharge kitItem2KitFixChargeItem(Kititem item, Date startdate, Integer kitNum) {
        Kitfixcharge rs = new Kitfixcharge();
        rs.setCode(item.getProductcode());
        rs.setAnz(item.getNum(0) * kitNum);
        return rs;
    }

    public void writeProjectId(String projectId, StdOrderData orderData) {
        orderData.getBookingRs().setProjectid(projectId);
        for (Room_rs room : orderData.getRooms()) {
            room.setProjectid(projectId);
        }
        for (Ticket_rs ticket : orderData.getTickets()) {
            ticket.setProjectid(projectId);
        }
        for (Cater_rs cater : orderData.getCaters()) {
            cater.setProjectid(projectId);
        }
        for (Spu_rs spus : orderData.getSpus()) {
            spus.setProjectid(projectId);
        }
        if (CollectionUtil.isNotEmpty(orderData.getGifts())) {
            for (Gift_rs gift : orderData.getGifts()) {
                gift.setProjectid(projectId);
            }
        }
    }

    public void updateCombieInfo(StdOrderData stdOrderData, AddressInfo addressInfo) {
        CombineInfo combineInfo = new CombineInfo();
        if (addressInfo != null) {
            combineInfo.setAddress(addressInfo.getAddressDesc());
            combineInfo.setProvince(addressInfo.getProvince());
        }
        if (CollectionUtil.isNotEmpty(stdOrderData.getGifts())) {

            GiftItemCache giftCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);

            String projectId = stdOrderData.getBookingRs().getProjectid();
            for (Gift_rs giftRs : stdOrderData.getGifts()) {
                CombineInfo.CombineNode node = new CombineInfo.CombineNode();
                node.setGroupid(giftRs.getCode().split(":")[0]);  //商品编码 比如皮鞋的编码
                node.setProductcode(giftRs.getCode());// SKU编码
                Var<String> specStr = new Var<String>();
                String fulldesc = ContentCacheTool.getGifRsProductDesc(giftRs.getCode(), projectId, specStr);
                node.setSpecdesc(specStr.getValue());
                node.setRegno(giftRs.getRegno());
                node.setAnz(giftRs.getAnz(0));
                combineInfo.getNodes().add(node);
                Giftitem giftitem = giftCache.getRecord(projectId, node.getGroupid());
                if (giftitem != null) {
                    node.setPosttype(giftitem.getPosttype());
                    node.setDesc(giftitem.getDescription());
                }
            }
            stdOrderData.setCombineInfo(combineInfo);
        }
        if (CollectionUtil.isNotEmpty(stdOrderData.getSpus())) {
            ProdWareInfo prodWareInfo = ProdFactory.getProd(ProdType.WARES);
            for (Spu_rs spuRs : stdOrderData.getSpus()) {
                Spusitem spusitem = prodWareInfo.getProdRecord(spuRs.getCode(), stdOrderData.getBookingRs().getProjectid(), null);
                if (spusitem.getLpack() && !spusitem.getPackinfo().isEmpty()) {
                    List<SpusitemPackInfo> packInfos = JSON.parseArray(spusitem.getPackinfo(), SpusitemPackInfo.class);
                    List<SpuPackCheckInfo> checkInfos = Lists.newArrayList();
                    for (SpusitemPackInfo packInfo : packInfos) {
                        SpuPackCheckInfo checkInfo = new SpuPackCheckInfo();
                        checkInfo.setCode(packInfo.getCode());
                        checkInfo.setProductName(packInfo.getProductName());
                        checkInfo.setTotal(1);
                        checkInfo.setCheck(0);
                        checkInfos.add(checkInfo);
                    }
                    spuRs.setPackinfo(JSON.toJSONString(checkInfos));
                }
            }
        }
    }

    public StdOrderData transSaveWaitListCoupon(Waitlistcoupon waitlistcoupon, String uid, String userId, String projectId) {
        Booking_rs bookingRs = new Booking_rs();
        StdOrderData stdOrderData = new StdOrderData();
        if (!CalculateDate.isInRange(CalculateDate.getSystemDate(), waitlistcoupon.getStartdate(), waitlistcoupon.getEnddate())) {//已经过期的就不发
            return stdOrderData;
        }
        bookingRs.setProjectid(projectId);
        bookingRs.setMainstatus(StatusUtil.BookingRsStatus.PAY);
        bookingRs.setGuestname(waitlistcoupon.getTargetmobile());
        bookingRs.setPtype(ProdType.WARES.val());
        bookingRs.setProduct(waitlistcoupon.getProdcode());
        bookingRs.setAnz(1);
        bookingRs.setUid(uid);
        bookingRs.setCreatedate(LocalDateTime.now());
        bookingRs.setBookingid(seqNoService.getSequenceID(SystemUtil.SequenceKey.BOOKINGID));
        bookingRs.setArrdate(waitlistcoupon.getStartdate());
        bookingRs.setDeptdate(waitlistcoupon.getEnddate());
        bookingRs.setBookername(waitlistcoupon.getTargetmobile());
        bookingRs.setTel(waitlistcoupon.getTargetmobile());
        bookingRs.setMemo("创建者:" + userId + "消费者Id:" + uid);
        bookingRs.setOutid(waitlistcoupon.getOutid());

        Spu_rs spuRs = new Spu_rs();
        spuRs.setCode(waitlistcoupon.getProdcode());
        spuRs.setAnz(1);
        spuRs.setStartdate(waitlistcoupon.getStartdate());
        spuRs.setEnddate(waitlistcoupon.getEnddate());
        spuRs.setBookingid(bookingRs.getBookingid());
        spuRs.setProjectid(projectId);


        stdOrderData.setBookingRs(bookingRs);
        stdOrderData.getSpus().add(spuRs);


        waitlistcoupon.setBookingid(bookingRs.getBookingid()); //为了给后续撤销操作使用
        updateCombieInfo(stdOrderData, null);

        return stdOrderData;
    }

}
