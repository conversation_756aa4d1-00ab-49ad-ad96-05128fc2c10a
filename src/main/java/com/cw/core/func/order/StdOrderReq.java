package com.cw.core.func.order;

import com.cw.entity.*;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 标准提交订单处理流对象
 * <p>
 * 订单请求应该实现次接口
 *
 * <AUTHOR>
 */
public interface StdOrderReq {

    Booking_rs getBookingRs();

    List<Room_rs> getRoomRss();

    List<Cater_rs> getCaterRss();

    List<Ticket_rs> getTicketRss();

    List<Spu_rs> getSpuRss();

    List<Gift_rs> getGiftRss();

    List<Discount> getDiscount();

    List<Meeting_rs> getMeetingRs();

    String getKitCode();

    String getUid();

    Integer getKitNum();

    String getActivityCode();

    String getAgentType();

    List<String> getKitSelectedGroup();

    Set<Long> getKitSelectedItem();

    Date getOrderStartDate();

    String getSalesid();



/*
    default List<Long>  getShopItemIds(){
        return Lists.newArrayList();
    }
*/

}
