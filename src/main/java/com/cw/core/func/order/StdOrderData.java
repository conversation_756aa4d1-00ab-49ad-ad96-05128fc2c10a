package com.cw.core.func.order;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.entity.*;
import com.cw.outsys.pojo.zjplatform.common.OtaOrder_guestNode;
import com.cw.outsys.stdop.common.*;
import com.cw.outsys.stdop.request.StdOrderRequest;
import com.cw.pojo.common.rule.CancelOrderRuleValidData;
import com.cw.pojo.dto.app.req.node.CombineInfo;
import com.cw.pojo.dto.app.req.node.OrderGuestInfo;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/18 14:49
 **/
@Data
public class StdOrderData {
    Booking_rs bookingRs;     //除了套餐订单场景.多数情况下一个主单对应一个子订单
    List<Meeting_rs> meetings = new ArrayList<>();
    List<Room_rs> rooms = new ArrayList<>();
    List<Ticket_rs> tickets = new ArrayList<>();
    List<Cater_rs> caters = new ArrayList<>();
    List<Kitfixcharge> kitfixcharges = new ArrayList<>();
    List<Spu_rs> spus = new ArrayList<>();
    List<Gift_rs> gifts = new ArrayList<>();
    List<Discount> discountList = new ArrayList<>();

    Table<String, String, BigDecimal> roomPriceTable = HashBasedTable.create();

    CombineInfo combineInfo;//伴手礼订单信息

    CancelOrderRuleValidData cancelOrderRuleValidData; //取消订单违约金扣款信息 给中台做扣款时使用
    String channel = SystemUtil.DEFAULT_CHANNELID;  // 产品指定的库存来源. 默认为空.使用的是产品库资源
    String actCode = "";  //先留个空.活动时可以使用 活动代码初始化
    String kitCode = "";//套餐代码
    String uid;

    BigDecimal extrafee = BigDecimal.ZERO;//额外费用,通常是运费
    BigDecimal totalAmount = BigDecimal.ZERO;//订单总金额
    BigDecimal discAmount = BigDecimal.ZERO;//折扣金额

    public Date getUseDate() {
        return bookingRs.getArrdate();
    }


    public boolean isOnlyTicket() {
        return tickets.size() > 0 && bookingRs.getPtype().equals(ProdType.TICKET.val());
    }

    public boolean isContainTicket() {
        return tickets.size() > 0;
    }

    public boolean isContainRoom() {
        return rooms.size() > 0;
    }


    /**
     * 判断提交订单的数据是否是昨天的.处理凌晨问题
     *
     * @return
     */
    public boolean isContianYesterDay() {
        return false;
    }

    public StdOrderRequest toStdOrderReq() {
        StdOrderRequest stdOrderReq = new StdOrderRequest();
        stdOrderReq.setOtaorderid(bookingRs.getBookingid());
        stdOrderReq.setProjectId(bookingRs.getProjectid());
        StdOrder_guestNode guestNode = new StdOrder_guestNode();
        guestNode.setGuestname(bookingRs.getBookername());
        guestNode.setMobile(bookingRs.getTel());
        stdOrderReq.setGuestinfo(guestNode);
        stdOrderReq.setProdType(ProdType.getProdType(bookingRs.getPtype()));
        stdOrderReq.setOrderDataContext(this);


        stdOrderReq.setTotalamount(bookingRs.getAmount());
        stdOrderReq.setRemark(bookingRs.getMemo());
        stdOrderReq.setActcode(actCode);
        if (bookingRs.getPtype().equals(ProdType.TAOCAN.val())) {
            stdOrderReq.setPackagecode(bookingRs.getProduct());
            stdOrderReq.setPackagenum(bookingRs.getAnz());
        }

        StdOrder_ordersNode ordersNode = new StdOrder_ordersNode();
        stdOrderReq.setOrderlist(ordersNode);
        ordersNode.setRoom(rooms.stream().map(r -> {
            StdOrder_roomNode roomNode = new StdOrder_roomNode();
            roomNode.setHotelid(ContentCacheTool.getProductGroupInfo(ProdType.ROOM.val(), r.getRmtype(), bookingRs.getProjectid(), true));
            roomNode.setStartdate(r.getArrdate());
            roomNode.setEnddate(r.getDeptdate());
            roomNode.setRoomtype(r.getRmtype());
            roomNode.setNum(r.getAnz());
            return roomNode;
        }).collect(Collectors.toList()));

        ordersNode.setTicket(tickets.stream().map(t -> {
            StdOrder_ticketNode ticketNode = new StdOrder_ticketNode();
            ticketNode.setNum(t.getAnz());
            ticketNode.setTicketcode(t.getTcode());
            ticketNode.setOutticketcode(ContentCacheTool.getTicketOutCode(t.getTcode(), t.getProjectid(), ProdType.getProdType(bookingRs.getPtype())));
            ticketNode.setUsedate(t.getUsedate());
            ticketNode.setPrice(t.getPrice());
            ticketNode.setAmount(t.getAmount());
            ticketNode.setRegno(t.getRegno());
            if (!t.getIdinfo().isEmpty()) {
                OtaOrder_guestNode guest = new OtaOrder_guestNode();
                String names = "";
                String ids = "";
                List<OrderGuestInfo> idinfos = JSON.parseArray(t.getIdinfo(), OrderGuestInfo.class);
                for (OrderGuestInfo idinfo : idinfos) {
                    names += names.isEmpty() ? idinfo.getGuestname() : "," + idinfo.getGuestname();
                    ids += ids.isEmpty() ? idinfo.getIdno() : "," + idinfo.getIdno();
                }
                guest.setGuestname(names);
                guest.setIdno(ids);
                if (StrUtil.isBlank(guestNode.getIdno())) {
                    guestNode.setIdno(ids);
                }
                if (StrUtil.isBlank(guestNode.getGuestname())) {
                    guestNode.setGuestname(names);
                }
                ticketNode.setIdinfos(idinfos);
                ticketNode.setGuestinfo(guest);
            }
//            ticketNode.setGuestinfo();
            return ticketNode;
        }).collect(Collectors.toList()));

        ordersNode.setCustomitem(kitfixcharges.stream().map(kitfixcharge -> {
            StdOrder_itemNode customitemNode = new StdOrder_itemNode();
            customitemNode.setCode(kitfixcharge.getCode());
            customitemNode.setNum(kitfixcharge.getAnz(0));
            return customitemNode;
        }).collect(Collectors.toList()));


        ordersNode.setSpus(spus.stream().map(spu -> {
            StdOrder_spusNode spusNode = new StdOrder_spusNode();
            spusNode.setSpuscode(spu.getCode());
            spusNode.setNum(spu.getAnz(0));
            spusNode.setStartdate(spu.getStartdate());
            spusNode.setEnddate(spu.getEnddate());
            return spusNode;
        }).collect(Collectors.toList()));

        ordersNode.setGift(gifts.stream().map(gift -> {
            StdOrder_giftNode giftNode = new StdOrder_giftNode();
            giftNode.setGiftcode(gift.getCode());
            giftNode.setNum(gift.getAnz(0));
            giftNode.setDeliverytime(gift.getDeliverytime());
            return giftNode;
        }).collect(Collectors.toList()));

        return stdOrderReq;
    }

}
