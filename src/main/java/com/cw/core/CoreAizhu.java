package com.cw.core;

import com.cw.common.client.CwApiClient;
import com.cw.common.exception.CwApiException;
import com.cw.config.Cwconfig;
import com.cw.pms.model.RoomOrderInfoNode;
import com.cw.pms.request.CwCheckInReq;
import com.cw.pms.request.CwQueryCheckinOrderReq;
import com.cw.pms.request.CwQueryHotelInfoReq;
import com.cw.pms.response.CwCheckInRes;
import com.cw.pms.response.CwQueryCheckinOrderRes;
import com.cw.pms.response.CwQueryHotelInfoRes;
import com.cw.pojo.dto.app.req.AppCheckinReq;
import com.cw.pojo.dto.app.req.AppQueryCheckinOrderReq;
import com.cw.pojo.dto.app.req.AppQueryHotelInfoReq;
import com.cw.pojo.dto.app.res.AppCheckinRes;
import com.cw.pojo.dto.app.res.AppQueryCheckinOrderRes;
import com.cw.pojo.dto.app.res.AppQueryHotelInfoRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 爱住核心服务类 - 负责调用 dspms 工程的 OTA 接口
 * 
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/7/16 11:33
 **/
@Slf4j
@Service
public class CoreAizhu {
    
    @Autowired
    Cwconfig cwconfig;

    /**
     * 查询入住订单信息
     * 
     * @param appReq APP端查询入住订单请求
     * @return APP端查询入住订单响应
     */
    public AppQueryCheckinOrderRes queryCheckinOrder(AppQueryCheckinOrderReq appReq) {
        try {
            // 1. 获取API客户端
            CwApiClient client = getExecuteClient(appReq.getHotelId());
            
            // 2. 转换请求参数
            CwQueryCheckinOrderReq pmsReq = new CwQueryCheckinOrderReq();
            pmsReq.setHotelId(appReq.getHotelId());
            pmsReq.setOtaorderId(appReq.getOtaorderId());
            pmsReq.setGuestName(appReq.getGuestName());
            pmsReq.setPhoneNumber(appReq.getPhoneNumber());
            
            // 3. 调用PMS接口
            CwQueryCheckinOrderRes pmsRes = client.execute(pmsReq);
            
            // 4. 转换响应结果
            return convertToAppQueryCheckinOrderRes(pmsRes);
            
        } catch (CwApiException e) {
            log.error("查询入住订单失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询入住订单失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("查询入住订单异常: {}", e.getMessage(), e);
            throw new RuntimeException("查询入住订单异常: " + e.getMessage(), e);
        }
    }

    /**
     * 办理入住
     * 
     * @param appReq APP端入住办理请求
     * @return APP端入住办理响应
     */
    public AppCheckinRes checkIn(AppCheckinReq appReq) {
        try {
            // 1. 获取API客户端 - 从第一个订单号中提取酒店ID（简化处理）
            CwApiClient client = getExecuteClient(appReq.getHotelId());
            
            // 2. 转换请求参数
            CwCheckInReq pmsReq = new CwCheckInReq();
            pmsReq.setRoomOrderIds(appReq.getRoomOrderIds());
            pmsReq.setContactPhone(appReq.getContactPhone());
            
            // 转换入住人员信息
            List<CwCheckInReq.CheckInGuest> pmsGuests = appReq.getGuests().stream()
                    .map(this::convertToCheckInGuest)
                    .collect(Collectors.toList());
            pmsReq.setGuests(pmsGuests);
            
            // 3. 调用PMS接口
            CwCheckInRes pmsRes = client.execute(pmsReq);
            
            // 4. 转换响应结果
            return convertToAppCheckinRes(pmsRes);
            
        } catch (CwApiException e) {
            log.error("办理入住失败: {}", e.getMessage(), e);
            throw new RuntimeException("办理入住失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("办理入住异常: {}", e.getMessage(), e);
            throw new RuntimeException("办理入住异常: " + e.getMessage(), e);
        }
    }

    /**
     * 查询酒店信息
     * 
     * @param appReq APP端查询酒店信息请求
     * @return APP端查询酒店信息响应
     */
    public AppQueryHotelInfoRes queryHotelInfo(AppQueryHotelInfoReq appReq) {
        try {
            // 1. 获取API客户端
            CwApiClient client = getExecuteClient(appReq.getHotelId());
            
            // 2. 转换请求参数
            CwQueryHotelInfoReq pmsReq = new CwQueryHotelInfoReq();
            pmsReq.setHotelId(appReq.getHotelId());
            
            // 3. 调用PMS接口
            CwQueryHotelInfoRes pmsRes = client.execute(pmsReq);
            
            // 4. 转换响应结果
            return convertToAppQueryHotelInfoRes(pmsRes);
            
        } catch (CwApiException e) {
            log.error("查询酒店信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询酒店信息失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("查询酒店信息异常: {}", e.getMessage(), e);
            throw new RuntimeException("查询酒店信息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 获取执行客户端
     * 
     * @param hotelId 酒店ID
     * @return CwApiClient实例
     */
    public CwApiClient getExecuteClient(String hotelId) {
        return new CwApiClient(
                cwconfig.getPmsdomain(), // PMS服务器地址
                hotelId,                 // 酒店ID作为应用ID
                "5bcaqh19ks128q5auw1lo23v6fi7cjht", // 私钥
                "RSA2"                   // 签名方式
        );
    }

    /**
     * 从订单号列表中提取酒店ID（简化处理，实际可能需要更复杂的逻辑）
     * 
     * @param roomOrderIds 订单号列表
     * @return 酒店ID
     */
    private String extractHotelIdFromOrderId(List<String> roomOrderIds) {
        // 简化处理：默认返回 "001"，实际项目中可能需要从订单号中解析或查询数据库
        return "001";
    }

    /**
     * 转换APP端入住人员信息为PMS端格式
     * 
     * @param appGuest APP端入住人员信息
     * @return PMS端入住人员信息
     */
    private CwCheckInReq.CheckInGuest convertToCheckInGuest(AppCheckinReq.CheckInGuest appGuest) {
        CwCheckInReq.CheckInGuest pmsGuest = new CwCheckInReq.CheckInGuest();
        pmsGuest.setGuestName(appGuest.getGuestName());
        pmsGuest.setIdNumber(appGuest.getIdNumber());
        pmsGuest.setIdPhoto(appGuest.getIdPhoto());
        return pmsGuest;
    }

    /**
     * 转换PMS端入住响应为APP端格式
     * 
     * @param pmsRes PMS端入住响应
     * @return APP端入住响应
     */
    private AppCheckinRes convertToAppCheckinRes(CwCheckInRes pmsRes) {
        AppCheckinRes appRes = new AppCheckinRes();
        
        if (pmsRes.getData() != null) {
            CwCheckInRes.BizModel bizModel = pmsRes.getData();
            appRes.setRoomCount(bizModel.getRoomCount());
            appRes.setContactPhone(bizModel.getContactPhone());
            
            // 转换房间信息
            if (bizModel.getRooms() != null) {
                List<AppCheckinRes.RoomInfo> appRooms = bizModel.getRooms().stream()
                        .map(this::convertToAppRoomInfo)
                        .collect(Collectors.toList());
                appRes.setRooms(appRooms);
            }
            
            // 转换入住人员信息
            if (bizModel.getGuests() != null) {
                List<AppCheckinRes.GuestInfo> appGuests = bizModel.getGuests().stream()
                        .map(this::convertToAppGuestInfo)
                        .collect(Collectors.toList());
                appRes.setGuests(appGuests);
            }
        }
        
        return appRes;
    }

    /**
     * 转换PMS端房间信息为APP端格式
     *
     * @param pmsRoom PMS端房间信息
     * @return APP端房间信息
     */
    private AppCheckinRes.RoomInfo convertToAppRoomInfo(CwCheckInRes.RoomInfo pmsRoom) {
        AppCheckinRes.RoomInfo appRoom = new AppCheckinRes.RoomInfo();
        appRoom.setRoomNo(pmsRoom.getRoomNo());
        appRoom.setRoomPassword(pmsRoom.getRoomPassword());
        appRoom.setRoomTypeDesc(pmsRoom.getRoomTypeDesc());
        return appRoom;
    }

    /**
     * 转换PMS端入住人员信息为APP端格式
     *
     * @param pmsGuest PMS端入住人员信息
     * @return APP端入住人员信息
     */
    private AppCheckinRes.GuestInfo convertToAppGuestInfo(CwCheckInRes.GuestInfo pmsGuest) {
        AppCheckinRes.GuestInfo appGuest = new AppCheckinRes.GuestInfo();
        appGuest.setGuestName(pmsGuest.getGuestName());
        appGuest.setIdNumber(pmsGuest.getIdNumber());
        appGuest.setCheckInStatus(pmsGuest.getCheckInStatus());
        return appGuest;
    }

    /**
     * 转换PMS端查询入住订单响应为APP端格式
     *
     * @param pmsRes PMS端查询入住订单响应
     * @return APP端查询入住订单响应
     */
    private AppQueryCheckinOrderRes convertToAppQueryCheckinOrderRes(CwQueryCheckinOrderRes pmsRes) {
        AppQueryCheckinOrderRes appRes = new AppQueryCheckinOrderRes();

        if (pmsRes.getData() != null) {
            CwQueryCheckinOrderRes.BizModel bizModel = pmsRes.getData();
            appRes.setLcanCheckin(bizModel.isLcanCheckin());

            // 转换房间订单信息
            if (bizModel.getRooms() != null) {
                List<AppQueryCheckinOrderRes.RoomOrderInfo> appRooms = bizModel.getRooms().stream()
                        .map(this::convertToAppRoomOrderInfo)
                        .collect(Collectors.toList());
                appRes.setRooms(appRooms);
            }
        }

        return appRes;
    }

    /**
     * 转换PMS端房间订单信息为APP端格式
     *
     * @param pmsRoom PMS端房间订单信息
     * @return APP端房间订单信息
     */
    private AppQueryCheckinOrderRes.RoomOrderInfo convertToAppRoomOrderInfo(RoomOrderInfoNode pmsRoom) {
        AppQueryCheckinOrderRes.RoomOrderInfo appRoom = new AppQueryCheckinOrderRes.RoomOrderInfo();
        appRoom.setRoomReservationNo(pmsRoom.getRoomOrderId());
        //appRoom.setGuestNames(pmsRoom.getGuestNames());
        appRoom.setArrDate(pmsRoom.getArrDate());
        appRoom.setDeptDate(pmsRoom.getDeptDate());
        appRoom.setOrderStatus(pmsRoom.getOrderStatus());
        appRoom.setOrderAmount(pmsRoom.getOrderAmount());
        appRoom.setOtano(pmsRoom.getOtano());
        appRoom.setGuestName(pmsRoom.getGuestName());
        appRoom.setPhoneNumber(pmsRoom.getPhoneNumber());
        appRoom.setRooms(pmsRoom.getRooms());
        appRoom.setRoomNo(pmsRoom.getRoomNo());
        appRoom.setRoomTypeDesc(pmsRoom.getRoomTypeDesc());
        appRoom.setTotalprice(pmsRoom.getTotalprice());
        appRoom.setChannel(pmsRoom.getChannel());
        appRoom.setDlpwd(pmsRoom.getDlpwd());
        return appRoom;
    }

    /**
     * 转换PMS端查询酒店信息响应为APP端格式
     *
     * @param pmsRes PMS端查询酒店信息响应
     * @return APP端查询酒店信息响应
     */
    private AppQueryHotelInfoRes convertToAppQueryHotelInfoRes(CwQueryHotelInfoRes pmsRes) {
        AppQueryHotelInfoRes appRes = new AppQueryHotelInfoRes();

        if (pmsRes.getData() != null) {
            CwQueryHotelInfoRes.BizModel bizModel = pmsRes.getData();
            appRes.setHotelName(bizModel.getHotelName());
            appRes.setPhone(bizModel.getPhone());
            appRes.setHotelAddress(bizModel.getHotelAddress());
        }

        return appRes;
    }
}
