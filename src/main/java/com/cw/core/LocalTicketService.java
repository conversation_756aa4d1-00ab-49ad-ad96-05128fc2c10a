package com.cw.core;

import com.cw.arithmetic.func.Var;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.cache.RedisTool;
import com.cw.entity.Booking_rs;
import com.cw.entity.Localqr;
import com.cw.exception.DefinedException;
import com.cw.mapper.BookingrsMapper;
import com.cw.mapper.LocalqrMapper;
import com.cw.outsys.stdop.common.array.StdTicketSubOrderNode;
import com.cw.outsys.stdop.common.array.StdTicketSubStatusNode;
import com.cw.outsys.stdop.request.StdCancelOrderRequest;
import com.cw.outsys.stdop.request.StdOrderRequest;
import com.cw.outsys.stdop.request.StdTicketQueryRequest;
import com.cw.outsys.stdop.request.StdTicketQueryStatusRequest;
import com.cw.outsys.stdop.response.StdCancelOrderResponse;
import com.cw.outsys.stdop.response.StdOrderResponse;
import com.cw.outsys.stdop.response.StdTicketQueryResponse;
import com.cw.outsys.stdop.response.StdTicketQueryStatusResponse;
import com.cw.service.context.GlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.TicketUtil;
import com.google.common.collect.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/7/14 20:26
 **/
@Service
public class LocalTicketService {

    @Autowired
    private LocalqrMapper localqrMapper;

    private String lockKey = "LOCALTICKET_LOCK";



    public StdOrderResponse createOrder(StdOrderRequest request) {
        Localqr localqr = new Localqr();
        Booking_rs booking_rs = request.getOrderDataContext().getBookingRs();

        localqr.setProjectid(request.getProjectId());
        localqr.setBookingid(request.getOtaorderid());
        localqr.setProdcode(booking_rs.getProduct());
        localqr.setPtype(booking_rs.getPtype());
        localqr.setBookingid(booking_rs.getBookingid());
        localqr.setUsedate(booking_rs.getArrdate());
        localqr.setExpiredate(booking_rs.getDeptdate());
        localqr.setQrid(booking_rs.getPtype() + booking_rs.getBookingid());
        localqr.setCreatedate(LocalDateTime.now());
        localqr.setTotalnum(booking_rs.getAnz());

        localqrMapper.save(localqr);

        //localqr.setExpiredate(booking_rs.getDeptdate());

        StdOrderResponse stdOrderResponse = new StdOrderResponse();
        stdOrderResponse.setBookingid(localqr.getBookingid());
        stdOrderResponse.setOutid(localqr.getQrid());
        stdOrderResponse.setTcheckNo(localqr.getQrid());


        return stdOrderResponse;
    }

    public StdCancelOrderResponse cancelOrder(StdCancelOrderRequest request) {
        StdCancelOrderResponse stdCancelOrderResponse = new StdCancelOrderResponse();
        RedissonClient redissonClient = RedisTool.getRedissonClient();

        RLock orderLock = redissonClient.getLock(lockKey + request.getOtaorderid());

        try {
            boolean llock = orderLock.tryLock(5, 10, TimeUnit.SECONDS);
            if (llock) {
                //如果已经检票.不允许进行取消
                Localqr localqr = localqrMapper.findByProjectidAndAndBookingid(request.getProjectId(), request.getOtaorderid());
                //localqr.setReturnnum();
                if (localqr == null || localqr.getChecknum() > 1) {
                    stdCancelOrderResponse.setStd_flag(false);
                    stdCancelOrderResponse.setStd_sub_msg("该订单已经检票,不允许取消");
                } else {
                    localqr.setReturnnum(localqr.getTotalnum());  //退票
                    localqr.setCanceldate(LocalDateTime.now());
                    localqrMapper.save(localqr);
                }
            } else {
                stdCancelOrderResponse.setStd_flag(false);
                stdCancelOrderResponse.setStd_sub_msg("请重试");
            }

        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        //解锁
        return stdCancelOrderResponse;
    }

    public void finishTicket(String projectId, String bookingId, Var<String> descVar) throws DefinedException {

        RedissonClient redissonClient = RedisTool.getRedissonClient();

        RLock orderLock = redissonClient.getLock(lockKey + bookingId);
        try {
            boolean llock = orderLock.tryLock(5, 10, TimeUnit.SECONDS);
            if (llock) {
                Localqr localqr = localqrMapper.findByProjectidAndAndBookingid(projectId, bookingId);
                if (!CalculateDate.isInRange(new Date(), localqr.getUsedate(), localqr.getExpiredate())) {//DateUtil.isSameDay(new Date(), localqr.getUsedate())
                    throw new DefinedException("该订单未到使用日期，核销失败", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }
                if (localqr.getReturnnum() > 0) {
                    orderLock.unlock();
                    throw new DefinedException("该订单已经取消,请勿重复核销", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }
                if (localqr.getChecknum() > 0) {
                    orderLock.unlock();
                    throw new DefinedException("该订单已经核销,请勿重复核销", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }

                localqr.setChecknum(localqr.getTotalnum());
                localqr.setCheckuser(GlobalContext.getCurrentUserId());
                localqr.setCheckdate(new Date());
                localqrMapper.save(localqr);
                //将关联的主预订状态改为完成
                if (localqr.getPtype().equals(ProdType.TICKET.val())) {
                    BookingrsMapper bookingrsMapper = SpringUtil.getBean(BookingrsMapper.class);
                    Booking_rs bookingRs = bookingrsMapper.findBooking_rsByBookingid(bookingId);
                    bookingRs.setMainstatus(StatusUtil.BookingRsStatus.FINISH);
                    bookingrsMapper.saveAndFlush(bookingRs);

                    String desc = ProdFactory.getProd(ProdType.TICKET).getProductDesc(bookingRs.getProduct(), bookingRs.getProjectid());
                    descVar.setValue(desc);
                }
                orderLock.unlock();
            }
        } catch (InterruptedException e) {
            throw new DefinedException("核销失败,请稍后再试", SystemUtil.SystemerrorCode.ERR015_FORMERR);
        }

    }

    public StdTicketQueryResponse queryTicket(StdTicketQueryRequest request) {
        StdTicketQueryResponse response = new StdTicketQueryResponse();
        Localqr localqr = localqrMapper.findByProjectidAndAndBookingid(request.getProjectId(), request.getColno());
        if (localqr != null) {
            response.setAssistCheckNo(localqr.getQrid());//
            response.setStatus(localqr.getChecknum() > 0 ? "C" : "N");
            List<StdTicketSubOrderNode> orders = Lists.newArrayList();
            StdTicketSubOrderNode subOrderNode = new StdTicketSubOrderNode();
            subOrderNode.setDate(localqr.getUsedate());
            subOrderNode.setNum(localqr.getTotalnum());
            subOrderNode.setChecknum(localqr.getChecknum());
            subOrderNode.setReturnnum(localqr.getReturnnum());
            orders.add(subOrderNode);
            response.setOrders(orders);
        } else {
            response.setStd_flag(false);
        }
        return response;
    }

    public StdTicketQueryStatusResponse queryTicketStatus(StdTicketQueryStatusRequest request) {
        StdTicketQueryStatusResponse response = new StdTicketQueryStatusResponse();
        Localqr localqr = localqrMapper.findByProjectidAndAndBookingid(request.getProjectId(), request.getColno());
        if (localqr == null) {
            response.setStd_flag(false);
            response.setStd_sub_msg("订单不存在");
            return response;
        }

        List<StdTicketSubStatusNode> subStatus = new ArrayList<StdTicketSubStatusNode>();
        StdTicketSubStatusNode statusNode = new StdTicketSubStatusNode();
        statusNode.setChecknum(localqr.getChecknum());
        statusNode.setReturnnum(localqr.getReturnnum());
        //0-成功 1-订单不存在 2-线下接口关 闭 3-查询失败 4-取消审核中 5-取消审核成功 6-取 消审核失败 7-已使用 8-已完成
        statusNode.setStatus(localqr.getChecknum() > 0 ? TicketUtil.TicketCheckStatus.CHECKED.name() : TicketUtil.TicketCheckStatus.UNCHECK.name());
        subStatus.add(statusNode);

        response.setSubOrders(subStatus);
        return response;
    }


}
