package com.cw.core;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.arithmetic.others.TRsdata;
import com.cw.arithmetic.sku.OpskuPickup;
import com.cw.arithmetic.sku.TSkuUpd;
import com.cw.cache.RedissonTool;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.*;
import com.cw.pojo.dto.app.req.node.OrderGuestInfo;
import com.cw.utils.CalculateNumber;
import com.cw.utils.RedisKey;
import com.cw.utils.StatusUtil;
import com.cw.utils.enums.ProdType;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 订单逻辑处理
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/20 10:10
 **/
@Slf4j
@Service
public class CoreOrderWriter {

    BookingrsMapper bookingrsMapper;
    RoomrsMapper roomrsMapper;
    RticketsMapper rticketsMapper;
    RspuitemsMapper rspuitemsMapper;
    RkitsMapper rkitsMapper;
    Ticket_rsMapper ticketrsMapper;
    KitfixchargeMapper kitfixchargeMapper;
    CaterrsMapper caterrsMapper;
    SpursMapper spursMapper;
    GiftrsMapper giftrsMapper;
    MeetingrsMapper meetingrsMapper;
    DiscountMapper discountMapper;
    ActrsMapper actrsMapper;
    RgiftitemsMapper rgiftitemsMapper;
    CoreAvl coreAvl;

    @Autowired
    public CoreOrderWriter(BookingrsMapper bookingrsMapper, RoomrsMapper roomrsMapper, Ticket_rsMapper ticketrsMapper, RticketsMapper rticketsMapper, RkitsMapper rkitsMapper, RspuitemsMapper rspuitemsMapper,
                           KitfixchargeMapper kitfixchargeMapper, CaterrsMapper caterrsMapper, SpursMapper spursMapper, GiftrsMapper giftrsMapper,
                           MeetingrsMapper meetingrsMapper, DiscountMapper discountMapper, ActrsMapper actrsMapper, RgiftitemsMapper rgiftitemsMapper, CoreAvl coreAvl) {
        this.bookingrsMapper = bookingrsMapper;
        this.roomrsMapper = roomrsMapper;
        this.ticketrsMapper = ticketrsMapper;
        this.rticketsMapper = rticketsMapper;
        this.rkitsMapper = rkitsMapper;
        this.rspuitemsMapper = rspuitemsMapper;
        this.kitfixchargeMapper = kitfixchargeMapper;
        this.caterrsMapper = caterrsMapper;
        this.giftrsMapper = giftrsMapper;
        this.spursMapper = spursMapper;
        this.meetingrsMapper = meetingrsMapper;
        this.discountMapper = discountMapper;
        this.actrsMapper = actrsMapper;
        this.rgiftitemsMapper = rgiftitemsMapper;
        this.coreAvl = coreAvl;
    }

    public void writeCreateOrder2DataDb(StdOrderData orderData) throws DefinedException {
        boolean lfree = CalculateNumber.isZero(orderData.getBookingRs().getAmount());
        orderData.getBookingRs().setMainstatus(lfree ? StatusUtil.BookingRsStatus.PAY : StatusUtil.BookingRsStatus.CONFIRM);

        bookingrsMapper.saveAndFlush(orderData.getBookingRs());
        for (Room_rs room : orderData.getRooms()) {
            TRsdata oldData = new TRsdata(room, true);
            //扣减资源
            roomrsMapper.saveAndFlush(room);
            coreAvl.updateAvailability(oldData, room);
        }
        if (orderData.getTickets().size() > 0) {
            LocalDateTime localnow = LocalDateTime.now();
            LocalDateTime nextDay = LocalDateTime.of(localnow.toLocalDate().plusDays(1), LocalTime.of(0, 1));
            long seconds = Duration.between(localnow, nextDay).getSeconds();
            for (Ticket_rs ticket : orderData.getTickets()) {
                ticketrsMapper.saveAndFlush(ticket);

                Ticket t = (Ticket) ProdFactory.getProd(ProdType.TICKET).getProdRecord(ticket.getTcode(), ticket.getProjectid(), null);
                if (t != null && t.getIddailylimit() != null && t.getIddailylimit() > 0 && t.getLreal()) {
                    if (!ticket.getIdinfo().isEmpty()) {
                        List<OrderGuestInfo> guestInfos = JSON.parseArray(ticket.getIdinfo(), OrderGuestInfo.class);
                        RMapCache<String, Integer> cache = RedissonTool.getInstance().getMapCache(RedisKey.TICKETIDLIMITCACHE);
                        for (OrderGuestInfo guestInfo : guestInfos) {
                            String key = StrUtil.join(":", ticket.getProjectid(), ticket.getTcode(), guestInfo.getIdtype(), guestInfo.getIdno());
                            cache.put(key, cache.getOrDefault(key, 0) + 1, seconds, TimeUnit.SECONDS);    //发码成功后.累计购买数量.
                            //log.info("门票key: {} 剩余过期时间:{}", key, DateUtil.formatBetween(TimeUnit.SECONDS.toMillis(seconds)));
                            //long lefttime = cache.remainTimeToLive(key);
                            //log.info("实际剩余时间:lefttime:{}-{}", lefttime, DateUtil.formatBetween(lefttime));

                        }
                    }
                }
            }
            List<TSkuUpd> updList = OpskuPickup.splitSkus(coreAvl.getOrderSkuUpd(null, orderData), ProdType.TICKET);
            coreAvl.updateTicketAvailability(updList, orderData.getBookingRs().getProjectid());

        }

        for (Cater_rs cater : orderData.getCaters()) {
            caterrsMapper.saveAndFlush(cater);
        }
        for (Spu_rs spus : orderData.getSpus()) {
            spursMapper.saveAndFlush(spus);
            if (SysFuncLibTool.isGridSpus(spus.getCode(), spus.getProjectid())) {
                List<TSkuUpd> updList = OpskuPickup.splitSkus(coreAvl.getOrderSkuUpd(null, orderData), ProdType.WARES);
                coreAvl.updateSpuitemAvailability(updList, orderData.getBookingRs().getProjectid());
            }
        }
        if (orderData.getGifts().size() > 0) {
            for (Gift_rs gift : orderData.getGifts()) {
                giftrsMapper.saveAndFlush(gift);
                //伴手礼库存
                List<TSkuUpd> updList = OpskuPickup.splitSkus(coreAvl.getOrderSkuUpd(null, orderData), ProdType.ITEMS);
                coreAvl.updateGiftAvailability(updList, orderData.getBookingRs().getProjectid());
            }
        }
        for (Meeting_rs meeting : orderData.getMeetings()) {
            meetingrsMapper.saveAndFlush(meeting);
        }
        for (Kitfixcharge kitfixcharge : orderData.getKitfixcharges()) {
            kitfixchargeMapper.saveAndFlush(kitfixcharge);
        }
        for (Discount discount : orderData.getDiscountList()) {
            discountMapper.saveAndFlush(discount);
        }
        if (orderData.getBookingRs().getPtype().equals(ProdType.TAOCAN.val())) {
            rkitsMapper.updateRkitsPickup(orderData.getBookingRs().getAnz(),
                    orderData.getBookingRs().getProduct(), orderData.getUseDate(), orderData.getBookingRs().getProjectid());
        }
    }

    public void updStatus(String bookingId, String status) throws DefinedException {
        Booking_rs bookingRs = bookingrsMapper.findBooking_rsByBookingid(bookingId);
        if (bookingRs != null) {
            bookingRs.setMainstatus(status);
            bookingrsMapper.saveAndFlush(bookingRs);
        }
    }

    public void updProdBookingRsStatus(String bookingId, String status, ProdType prodType) throws DefinedException {
        Booking_rs bookingRs = bookingrsMapper.findBooking_rsByBookingid(bookingId);
        if (bookingRs != null && bookingRs.getPtype().equals(prodType.val())) {
            bookingRs.setMainstatus(status);
            bookingrsMapper.saveAndFlush(bookingRs);
        }
    }

    public void updActRsStatus(String bookingId, String status) throws DefinedException {
        Act_rs actRs = actrsMapper.findAct_rsByBookingid(bookingId);
        if (actRs != null) {
            actRs.setStatus(status);
            actrsMapper.saveAndFlush(actRs);
        }
    }


    public void writeCancelOrder2DataDb(StdOrderData orderData) throws DefinedException {
        updCancelData(orderData, true);
//        for (Cater_rs cater : orderData.getCaters()) {
//            caterrsMapper.saveAndFlush(cater);
//        }
//        for (Spu_rs spus : orderData.getSpus()) {
//            spursMapper.saveAndFlush(spus);
//        }
//        for (Meeting_rs meeting : orderData.getMeetings()) {
//            meetingrsMapper.saveAndFlush(meeting);
//        }
//        for (Kitfixcharge kitfixcharge : orderData.getKitfixcharges()) {
//            kitfixchargeMapper.saveAndFlush(kitfixcharge);
//        }
//        for (Discount discount : orderData.getDiscountList()) {
//            discountMapper.saveAndFlush(discount);
//        }
        orderData.getBookingRs().setOstatus(StatusUtil.BookingRsStatus.CANCEL);
        orderData.getBookingRs().setMainstatus(StatusUtil.BookingRsStatus.CANCEL);
        orderData.getBookingRs().setCanceldate(LocalDateTime.now());
        bookingrsMapper.saveAndFlush(orderData.getBookingRs());
    }

    public void updCancelData(StdOrderData orderData, boolean freeResource) throws DefinedException {
        LocalDateTime canceldatetime = LocalDateTime.now();
        for (Room_rs room : orderData.getRooms()) {
            TRsdata oldData = new TRsdata(room);
            //扣减资源
            room.setCanceldate(canceldatetime);
            roomrsMapper.saveAndFlush(room);
            coreAvl.updateAvailability(oldData, room);
        }
        for (Ticket_rs ticket : orderData.getTickets()) {
            ticket.setCanceldate(canceldatetime);
            ticketrsMapper.saveAndFlush(ticket);
            rticketsMapper.updateRticketsPickup(-ticket.getAnz(), ticket.getTcode(), ticket.getUsedate(), ticket.getUsedate(), ticket.getProjectid());

            Ticket t = (Ticket) ProdFactory.getProd(ProdType.TICKET).getProdRecord(ticket.getTcode(), ticket.getProjectid(), null);
            if (t != null) {
                if (!ticket.getIdinfo().isEmpty() && t.getIddailylimit() > 0) {
                    List<OrderGuestInfo> guestInfos = JSON.parseArray(ticket.getIdinfo(), OrderGuestInfo.class);
                    RMapCache<String, Integer> cache = RedissonTool.getInstance().getMapCache(RedisKey.TICKETIDLIMITCACHE);
                    for (OrderGuestInfo guestInfo : guestInfos) {//清空当日购买次数
                        String key = StrUtil.join(":", t.getProjectid(), t.getCode(), guestInfo.getIdtype(), guestInfo.getIdno());
                        cache.remove(key);
                    }
                }
            }
        }
        for (Spu_rs rs : orderData.getSpus()) {//释放景区产品资源
            if (SysFuncLibTool.isGridSpus(rs.getCode(), rs.getProjectid())) {
                rspuitemsMapper.updateRspuitemPickup(-rs.getAnz(0), rs.getCode(), rs.getProjectid());
            }
        }
        if (ProdType.TAOCAN.val().equals(orderData.getBookingRs().getPtype())) {//释放套餐资源
            rkitsMapper.updateRkitsPickup(-orderData.getBookingRs().getAnz(), orderData.getBookingRs().getProduct(), orderData.getBookingRs().getArrdate(), orderData.getBookingRs().getProjectid());
        }
        for (Gift_rs giftRs : orderData.getGifts()) {

        }
    }

    public void updActCancel(Act_rs rs) {

    }


}
