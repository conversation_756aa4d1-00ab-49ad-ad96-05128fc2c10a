package com.cw.core.report.base;

import com.cw.core.report.base.output.RpoutputConfig;
import com.cw.utils.rp.RpType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 报表运行生成对象. 用于打印
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/9/21 17:31
 **/
@Setter
public abstract class BaseRp implements RpPrint {

    protected RpType type;  //报表类型
    protected String rpname; //报表名称
    protected RpoutputConfig rpoutputConfig;//输出配置
    protected List<RpPrintColumn> printColumns = null;  //打印列
    protected List<RpInputCompent> queryForm = null;//输入条件的表单
    /********默认内置的一些表单参数*********/
    protected final String Q_DATE = "qDate"; //查询日期
    protected final String Q_STARTDATE = "qStartDate"; //查询日期开始
    protected final String Q_ENDDATE = "qEndDate"; //查询日期结束
    protected final String Q_IN = "qIn"; //查询范围
    protected final String Q_PARAM1 = "qParam1"; //查询参数1  比如业态产品
    protected final String Q_PARAM2 = "qParam2"; //查询参数2 比如产品大组ID
    protected String showUrl;  //报表预览或者下载地址  可能是一个pdf 或者excel 文件
    protected Map<String, String> infomap = Maps.newHashMap(); //报表信息 比如把打印日期.景区名字这些内容先存进来.然后输出
    protected List<String> loopName = Lists.newArrayList();
    protected List<String[]> loopList = Lists.newArrayList();  //对应表格数据.每一行的内容.

    public BaseRp(RpType type, String rpname) {
        this.type = type;
        this.rpname = rpname;
        this.printColumns = getInitPrintColumn();
        this.queryForm = getinitQueryForm();
    }


    public abstract List<RpPrintColumn> getInitPrintColumn();

    public abstract List<RpInputCompent> getinitQueryForm();

    @Override
    public RpType getType() {
        return type;
    }

    @Override
    public String getRpname() {
        return rpname;
    }


    @Override
    public RpoutputConfig getRpoutputConfig() {
        return rpoutputConfig;
    }

/*    @Override
    public List<RpPrintColumn> getPrintColumns() {
        return printColumns;
    }

    @Override
    public List<RpInputCompent> getQueryForm() {
        return queryForm;
    }*/

    @Override
    public String getShowUrl() {
        return showUrl;
    }
}
