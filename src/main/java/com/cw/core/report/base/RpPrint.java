package com.cw.core.report.base;

import com.cw.core.report.base.output.RpoutputConfig;
import com.cw.utils.rp.RpType;

/**
 * 报表运行生成对象. 用于打印
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/11/16 10:39
 **/
public interface RpPrint {


    /**
     * @return 报表类型
     */
    RpType getType();

    /**
     * @return 报表名称
     */
    String getRpname();

    /**
     * @return 输出配置
     */
    RpoutputConfig getRpoutputConfig();

    /*    *//**
     * @return 打印列
     *//*
    List<RpPrintColumn> getPrintColumns();

    */

    /**
     * @return 输入条件的表单
     *//*
    List<RpInputCompent> getQueryForm();*/


    String getShowUrl();


}
