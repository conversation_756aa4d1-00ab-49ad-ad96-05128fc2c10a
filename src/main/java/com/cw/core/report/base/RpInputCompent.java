package com.cw.core.report.base;

import com.cw.utils.rp.RpInputCompType;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/9/21 17:44
 **/
@Data
public class RpInputCompent {

    RpInputCompType compType;
    private String inputName;
    private String param; //如果是时间范围组件.格式为:开始时间参数名|结束时间参数名 例如:startTime|endTime
    private String defaultValue;//如果是时间范围组件.如果不填写的话,默认为当天的时间范围
    private String userData;//自定义格式数据  比如下拉.或者多选的数据 .订单类型等等这些.


    public RpInputCompent(RpInputCompType compType, String inputName, String param, String defaultValue, String userData) {
        this.compType = compType;
        this.inputName = inputName;
        this.param = param;
        this.defaultValue = defaultValue;
        this.userData = userData;
    }


}
