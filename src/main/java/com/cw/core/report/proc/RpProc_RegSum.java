package com.cw.core.report.proc;

import com.cw.core.report.RpProcCode;
import com.cw.core.report.RpProcProp;
import com.cw.core.report.base.RpInputCompent;
import com.cw.core.report.base.RpPrint;
import com.cw.core.report.base.RpPrintColumn;
import com.cw.core.report.impl.ProduceRp;
import com.cw.pojo.dto.statistic.req.RpPostForm;
import com.cw.utils.rp.RpInputCompType;
import com.google.common.collect.Lists;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/8/28 11:48
 **/
@RpProcProp(PROC_CODE = RpProcCode.RP_REGSOURCE)
public class RpProc_RegSum extends ProduceRp {


    public RpProc_RegSum() {
        super(RpProcCode.RP_SAMPLE.getDesc());
    }

    @Override
    public List<RpPrintColumn> getInitPrintColumn() {

        printColumns = Lists.newArrayList();
        printColumns.add(new RpPrintColumn("test", "姓名"));
        printColumns.add(new RpPrintColumn("regdate", "注册日期"));

        return printColumns;
    }

    @Override
    public List<RpInputCompent> getinitQueryForm() {
        queryForm = Lists.newArrayList();
        queryForm.add(new RpInputCompent(RpInputCompType.DATE, "查询日期", Q_DATE, LocalDate.now().toString(), null));
        return queryForm;
    }

    @Override
    public void fillRpPrint(RpPrint rpPrint, RpPostForm rpPostForm) {


    }


}



