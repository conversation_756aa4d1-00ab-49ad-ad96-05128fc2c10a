package com.cw.core.report.proc;

import com.cw.core.report.RpProcCode;
import com.cw.core.report.RpProcProp;
import com.cw.core.report.base.RpInputCompent;
import com.cw.core.report.base.RpPrint;
import com.cw.core.report.base.RpPrintColumn;
import com.cw.core.report.impl.ProduceRp;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.dto.statistic.req.RpPostForm;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.rp.RpInputCompType;
import com.google.common.collect.Lists;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/8/28 11:48
 **/
@RpProcProp(PROC_CODE = RpProcCode.RP_SAMPLE)
public class RpProc_Sample extends ProduceRp {


    public RpProc_Sample() {
        super(RpProcCode.RP_SAMPLE.getDesc());
    }

    @Override
    public List<RpPrintColumn> getInitPrintColumn() {
        printColumns = Lists.newArrayList();
        printColumns.add(new RpPrintColumn("test", "姓名"));
        printColumns.add(new RpPrintColumn("regdate", "注册日期"));
        return printColumns;
    }

    @Override
    public List<RpInputCompent> getinitQueryForm() {
        queryForm = Lists.newArrayList();
        queryForm.add(new RpInputCompent(RpInputCompType.DATE, "查询日期", Q_DATE, LocalDate.now().toString(), null));
        queryForm.add(new RpInputCompent(RpInputCompType.TEXT, "查询姓名", Q_PARAM1, LocalDate.now().toString(), null));
        queryForm.add(new RpInputCompent(RpInputCompType.DATERANGE, "查询范围", Q_STARTDATE + SystemUtil.PARAMSIGNAL + Q_ENDDATE,
                LocalDate.now() + SystemUtil.PARAMSIGNAL + LocalDate.now(), null));
        queryForm.add(new RpInputCompent(RpInputCompType.SINGLESELECT, "单选下拉", Q_PARAM2, LocalDate.now().toString(), null));
        queryForm.add(new RpInputCompent(RpInputCompType.MULTISELECT, "多选下拉", Q_IN, "", null));


        return queryForm;
    }

    @Override
    public void fillRpPrint(RpPrint rpPrint, RpPostForm rpPostForm) {
        System.out.println(this.getClass().getName() + " :  正在填充报表数据");
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Object[]> list = daoLocal.getNativeObjectList("select username,regdate from app_user where  order by regdate desc limit 10");

        for (Object[] objects : list) {
            String[] strRow = new String[printColumns.size()];
            for (int i = 0; i < printColumns.size(); i++) {
                Object obj = objects[i]; // 这里的obj就是数据库查询出来的每一行数据
                if (obj instanceof Date || obj instanceof java.util.Date) {
                    strRow[i] = CalculateDate.dateToString((Date) obj);
                } else {
                    strRow[i] = obj.toString();
                }
            }
            loopList.add(strRow);
        }
        System.out.println(this.getClass().getName() + " :  填充报表数据完成");
    }



}



