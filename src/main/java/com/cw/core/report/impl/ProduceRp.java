package com.cw.core.report.impl;

import com.cw.core.report.base.BaseRp;
import com.cw.core.report.base.RpPrint;
import com.cw.pojo.dto.statistic.req.RpPostForm;
import com.cw.utils.rp.RpType;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/9/21 17:53
 **/
public abstract class ProduceRp extends BaseRp {

    public ProduceRp(String rpname) {
        super(RpType.PRODUCE, rpname);
    }


    /**
     * 根据客户端请求.把数据结果填充到rpPrint 内.进行输出
     *
     * @param rpPrint
     * @param rpPostForm
     */
    abstract public void fillRpPrint(RpPrint rpPrint, RpPostForm rpPostForm);
}
