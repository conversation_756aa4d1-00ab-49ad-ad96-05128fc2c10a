package com.cw.core.report.manual;

import com.cw.core.report.base.RpInputCompent;
import com.cw.core.report.base.RpPrintColumn;
import com.cw.core.report.impl.SqlRp;
import com.cw.utils.rp.RpType;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/9/25 16:27
 **/
public class RpSql_Standard extends SqlRp {

    public RpSql_Standard(String rpname) {
        super(rpname);
        setType(RpType.SQL);
    }

    @Override
    public List<RpPrintColumn> getInitPrintColumn() {
        return printColumns;
    }

    @Override
    public List<RpInputCompent> getinitQueryForm() {
        return queryForm;
    }


}
