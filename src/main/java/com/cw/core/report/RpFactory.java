package com.cw.core.report;

import cn.hutool.core.lang.ClassScanner;
import com.cw.core.report.base.BaseRp;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 报表输出工厂
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/11/16 15:18
 **/
public class RpFactory {
    public static HashMap<String, String> produceRpInfo = new HashMap();

    static {
        ClassScanner.scanAllPackageByAnnotation("com.cw.core.report.proc", RpProcProp.class).forEach(clazz -> {
            RpProcCode procCode = clazz.getAnnotation(RpProcProp.class).PROC_CODE();
            try {
                produceRpInfo.put(procCode.name(), clazz.getName());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public static BaseRp getProduceRp(String procCode) {
        try {
            return (BaseRp) Class.forName(produceRpInfo.get(procCode)).newInstance();
        } catch (InstantiationException e) {
        } catch (IllegalAccessException e) {
        } catch (ClassNotFoundException e) {
        }
        return null;
    }


    public static List<String> getColumns(String sql) {
        String selectPart = "";
        Pattern pattern = Pattern.compile("select\\s+(.+?)\\s+from");
        Matcher matcher = pattern.matcher(sql);
        if (matcher.find()) {
            selectPart = matcher.group(1);
        }

        List<String> fields = Lists.newArrayList();
        String[] tokens = selectPart.split(",");
        for (String token : tokens) {
            String[] parts = token.split(" as ");
            if (parts.length == 2) {
                fields.add(parts[1].replaceAll("'", ""));
            } else {
                fields.add(parts[0].replaceAll("'", ""));
            }
        }

        return fields;
    }


}
