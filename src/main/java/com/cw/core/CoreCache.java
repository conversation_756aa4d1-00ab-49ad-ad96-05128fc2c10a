package com.cw.core;

import cn.hutool.core.date.DateUtil;
import com.cw.arithmetic.others.CodeDetail;
import com.cw.arithmetic.others.SkuDetail;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.config.exception.CustomException;
import com.cw.entity.Giftitem;
import com.cw.entity.Rcoupon;
import com.cw.entity.Spusitem;
import com.cw.exception.DefinedException;
import com.cw.mapper.RcouponMapper;
import com.cw.mapper.UserCouponMapping;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.common.core.CacheGrid;
import com.cw.pojo.common.core.CachePrice;
import com.cw.pojo.sqlresult.Produce_calcpricePo;
import com.cw.utils.CalculateDate;
import com.cw.utils.RedisKey;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.LoggerType;
import com.cw.utils.enums.ProdType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class CoreCache {

    private Logger logger = LoggerFactory.getLogger(LoggerType.sys.name());

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    CoreAvl coreAvl;

    @Autowired
    CorePrice corePrice;


    /**
     * 获取价格缓存key
     *
     * @param projectId
     * @param ratecode
     * @param producttype
     * @param productCode
     * @return
     */
    public static String getRateCacheKey(String projectId, String ratecode, String producttype, String productCode) {
        String key = RedisKey.MallRateCacheKey + projectId + ":" + ratecode + ":" + producttype + ":" + productCode;
        return key;
    }

    /**
     * 获取缓存Map 关键字key
     *
     * @param projectId
     * @param channelid
     * @param productype
     * @param productCode
     * @return
     */
    public static String getGridCacheKey(String projectId, String channelid, String productype, String productCode) {
        if (ProdType.ITEMS.val().equals(productype)) {
            productCode = productCode.split(":")[0];
        }
        if (ProdType.ACTGROUP.val().equals(productype)) {
            //活动产品返回的是产品的大组代码,做拼接
        }
        String key = RedisKey.MallGridCacheKey + projectId + ":" + channelid + ":" + productype + ":" + productCode;
        return key;
    }

    /**
     * 获取库存缓存key
     *
     * @param projectId
     * @param ratecode
     * @param productype
     * @param productCode
     * @return
     */
    public static String getResourceCacheKey(String projectId, String ratecode, String productype, String productCode) {
        String key = RedisKey.MallGridCacheKey + projectId + ":" + ratecode + ":" + productype + ":" + productCode;
        return key;
    }

    public static boolean isSpecProdType(ProdType prodType) {
        return ProdType.WARES.equals(prodType) || ProdType.ITEMS.equals(prodType) || ProdType.ACTGROUP.equals(prodType);
    }

    /**
     * 计算占用资源的开始,结束日期
     * 例如 1号入住,3号离店. start传1号,end 传2号
     *
     * @param channelid
     * @param productype
     * @param productCode
     * @param start       占用资源的开始日期
     * @param end         占用资源的结束日期
     * @return
     */
    public static List<String> getGridSkuWriteKeys(String projectId, String channelid, String productype, String productCode, Date start, Date end) {
        List<String> keys = getDateKeys(start, end);
        String signal = getGridCacheKey(projectId, channelid, productype, productCode);
        for (int i = 0; i < keys.size(); i++) {
            keys.set(i, signal + ":" + keys.get(i));
        }
        return keys;
    }

    public static List<String> getProdFetchMapKeys(String productCode, ProdType prodType, Date start, Date end) {
        if (isSpecProdType(prodType)) {
            //根据产品代码获取产品的sku
            if (ProdType.ITEMS.equals(prodType)) {
                return Arrays.asList(productCode);
            }
            return Arrays.asList(productCode);
        } else {
            return getDateKeys(start, end);
        }

    }

    public static List<String> getDateKeys(Date start, Date end) {
        List<String> keys = new ArrayList<>();
        for (int i = 0; i < CalculateDate.compareDates(end, start) + 1; i++) {
            keys.add(CalculateDate.dateToString(CalculateDate.reckonDay(start, 5, i)));
        }
        return keys;
    }

    public static boolean isCacheProductType(ProdType prodType) {
        return prodType.equals(ProdType.ROOM) || prodType.equals(ProdType.TICKET)
                || prodType.equals(ProdType.WARES) || prodType.equals(ProdType.TAOCAN)
                || prodType.equals(ProdType.ACTGROUP) || prodType.equals(ProdType.ITEMS);
    }

    public BigDecimal getCacheShowPrice(String projectId, String ratecode,
                                        ProdType prodType, String productcode, Date calcDate) {
        if (prodType.equals(ProdType.WARES)) {
            Spusitem spusitem = (Spusitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM).getRecord(projectId, productcode);
            if (spusitem != null && !spusitem.getLavl()) {
                return spusitem.getShowprice();  //价格简单模式
            }
        } else if (prodType.equals(ProdType.ITEMS)) {
            Giftitem giftitem = (Giftitem) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM).getRecord(projectId, productcode);
            if (giftitem != null) {
                return giftitem.getShowprice();  //价格简单模式
            }
        }

        String cachekey = getRateCacheKey(projectId, ratecode, prodType.val(), productcode);
        Object cachePrice = redisTemplate.opsForHash().get(cachekey, CalculateDate.dateToString(calcDate));

        boolean luseDbQuery = false;
        if (cachePrice != null) {//缓存不为空.直接在 redis 中获取
            return new BigDecimal(cachePrice + "");
        } else {
            return corePrice.getShowPrice(prodType, calcDate, productcode, projectId);
        }

//        return prodType.equals(ProdType.ROOM.name()) || prodType.equals(ProdType.TICKET.name());
    }


    /**
     * 优先从缓存中获取价格,取不到的话,从数据库中获取
     *
     * @param projectId
     * @param ratecode
     * @param type
     * @param productcode
     * @param startdate
     * @param enddate
     * @return
     */
    public List<CachePrice> calcPrice_cache(String projectId, String ratecode,
                                            String type, String productcode, Date startdate, Date enddate, String specode) {
        List<CachePrice> result = new ArrayList<>();
        boolean luseDbQuery = false;

        if (type.equals(ProdType.ROOM.val()) || type.equals(ProdType.TICKET.val())) {//现在除了房都不查缓存了
            try {
                int len = CalculateDate.compareDates(enddate, startdate).intValue();
                List<String> keys = new ArrayList<>();
                for (int i = 0; i < len + 1; i++) {//+1是为了保证查询的每一天都能查询到
                    keys.add(CalculateDate.dateToString(CalculateDate.reckonDay(startdate, 5, i)));
                }
                String cachekey = getRateCacheKey(projectId, ratecode, type, productcode);
                List<String> os = redisTemplate.opsForHash().multiGet(cachekey, keys);

                if (os.size() == keys.size()) {
                    for (int i = 0; i < keys.size(); i++) {
                        CachePrice cachePrice = new CachePrice();
                        cachePrice.setDate(keys.get(i));
                        Date date = CalculateDate.stringToDate(keys.get(i));
                        if (os.get(i) == null) {
                            if (CalculateDate.isBefore(date, CalculateDate.getSystemDate())) {  //查询历史的.值就是0
                                cachePrice.setPrice(BigDecimal.ZERO);
                            } else {
                                throw new DefinedException("缓存价格缺失:" + keys.get(i) + " 价格代码:" + ratecode + " 产品代码:" + productcode);
                            }
                        } else {
                            cachePrice.setPrice(new BigDecimal(os.get(i)));

                        }
                        result.add(cachePrice);
                    }
                }
            } catch (Exception e) {
                luseDbQuery = true;
                logger.error("查询缓存价格异常: {}", e.getMessage());
//            e.printStackTrace();
            }
        } else {
            luseDbQuery = true;
        }

        if (luseDbQuery) {
            List<Produce_calcpricePo> dbresult = null; //查询数据库做填充 //enddate需要+1
            try {
                dbresult = corePrice.calcPrice_DB(ratecode, ProdType.valueOf(type), productcode, startdate,
                        CalculateDate.reckonDay(enddate, 5, 1), 1, 0, 0, "", "", "", specode, false);
            } catch (DefinedException e) {
                e.printStackTrace();
                dbresult = new ArrayList<>();
            }
            result = new ArrayList<>();
            for (Produce_calcpricePo po : dbresult) {
//                Date d = CalculateDate.stringToDate(row[0].toString());
//                BigDecimal calcprice = new BigDecimal(row[1].toString());
                CachePrice cachePrice = new CachePrice();
                cachePrice.setDate(CalculateDate.date2Str(po.getDate()));
                cachePrice.setPrice(po.getPrice());
                result.add(cachePrice);
            }
        }
        return result;
    }

    public List<CacheGrid> calcRoom_cache(String projectId, String channelid, String rmtype, Date startdate, Date enddate, String gridCacheKey) throws DefinedException {
        boolean luseDbQuery = false; //是否有缓存异常.
        List<CacheGrid> result = new ArrayList<>();
        return result;
    }

    public List<CacheGrid> calcTicket_cache(String projectId, String channelid, String tickettype, Date startdate, Date enddate) throws DefinedException {
        List<CacheGrid> result = new ArrayList<CacheGrid>();
        return result;
    }

    public CodeDetail ratesQueryCache(String projectId,
                                      ProdType type, List<String> prods, Date showDate) {

        CodeDetail codeDetail = new CodeDetail();
        for (String prod : prods) {
            String ratecode = CorePrice.getRateCode(projectId, prod);
            String cachekey = getRateCacheKey(projectId, ratecode, type.val(), prod);
            Object cachePrice = redisTemplate.opsForHash().get(cachekey, CalculateDate.dateToString(showDate));
            if (cachePrice != null) {
                codeDetail.putProductPrice(prod, new BigDecimal(cachePrice.toString()));
            }
        }
        return codeDetail;
    }

    /**
     * 价格批量缓存查询
     *
     * @param projectId
     * @param type
     * @param groupcode
     * @param skuids
     * @return
     */
    public CodeDetail rateQuerySpecCache(String projectId,
                                         ProdType type, String groupcode, List<String> skuids) {
        CodeDetail codeDetail = new CodeDetail();
        String ratecode = CorePrice.getRateCode(projectId, groupcode);
        String cachekey = getRateCacheKey(projectId, ratecode, type.val(), groupcode);
        List<Object> redisresult = redisTemplate.opsForHash().multiGet(cachekey, skuids);
        //logger.info("redis 批量查询规格价格{}", redisresult);

        for (int i = 0; i < skuids.size(); i++) {
            String skuid = skuids.get(i);
            Object cachePrice = redisresult.get(i);
            codeDetail.putProductPrice(skuid, cachePrice == null ? BigDecimal.ZERO : new BigDecimal(cachePrice.toString()));
        }
        return codeDetail;

    }

    public SkuDetail queryMinAvailNum(Date startdate, Date enddate, List<String> prods, String projectId,
                                      Integer requireNum, ProdType prodType, String groupItemcode) {
//        List<Produce_minAvlPo> pos = prods.size() > 0 ? rroomsMapper.queryMinAvlNum(startdate, enddate, prods, projectId, requireNum) : Lists.newArrayList();

        SkuDetail skuDetail = new SkuDetail();
        if (prodType.equals(ProdType.ROOM) || prodType.equals(ProdType.TICKET) || prodType.equals(ProdType.TAOCAN) || prodType.equals(ProdType.WARES)) {
            List<String> dateKeys = CoreCache.getDateKeys(startdate, ProdType.ROOM.equals(prodType) ? CalculateDate.reckonDay(enddate, 5, 0) : enddate);
            for (String prod : prods) {
                String skuMapkey = CoreCache.getGridCacheKey(projectId, SystemUtil.DEFAULT_CHANNELID, prodType.val(), prod);

                List redisResult = RedisTool.getInstance().opsForHash().multiGet(skuMapkey, dateKeys);
                List<Integer> minAvlNum = new ArrayList<>();
                for (int i = 0; i < redisResult.size(); i++) {
                    Integer cachenum = redisResult.get(i) == null ? 0 : Integer.parseInt(redisResult.get(i).toString());
                    minAvlNum.add(cachenum);
                }
                Optional<Integer> minop = minAvlNum.stream().min(Integer::compareTo);
                minop.ifPresent(integer -> skuDetail.putProductAvl(prod, integer));
            }
        }
        if (prodType.equals(ProdType.ACTGROUP) || prodType.equals(ProdType.ITEMS)) {//活动预约或者是伴手礼缓存型产品
            String skuMapkey = CoreCache.getGridCacheKey(projectId, SystemUtil.DEFAULT_CHANNELID, prodType.val(), groupItemcode);
            List redisResult = RedisTool.getInstance().opsForHash().multiGet(skuMapkey, prods);
            for (int i = 0; i < redisResult.size(); i++) {
                Integer cachenum = redisResult.get(i) == null ? 0 : Integer.parseInt(redisResult.get(i).toString());
                skuDetail.putProductAvl(prods.get(i), cachenum);//将查询到的redis 产品缓存数量更新到查询结果
            }
        }
        return skuDetail;
    }

//    public List<CacheCateringGrid> calcCatering_cache(String section, String channelid, String cateringType, Date startdate, Date enddate) throws DefinedException {
//        List<CacheCateringGrid> result = new ArrayList<CacheCateringGrid>();
//        LinkedHashMap<String, Integer> grids = coreAvl.queryProductAvailGrid(section,
//                channelid, cateringType, startdate,enddate, SystemUtil.ProductType.CANYIN);
//        List<String> periodList = new ArrayList<String>();
//        Map<String,Restaurant_time> timeHM = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RESTAURANT_TIME).getDataMap();
//        Iterator<String> iterator = timeHM.keySet().iterator();
//        Restaurant_time restaurant_time;
//        while(iterator.hasNext()){
//            restaurant_time = timeHM.get(iterator.next());
//            if(restaurant_time.getRestaurant().isEmpty()){//取公共餐段
//                periodList.add(restaurant_time.getCode());
//            }
//        }
//        int len = CalculateDate.compareDates(enddate, startdate).intValue()+1;
//        String tempDate = "";
//        for (int i = 0; i < len; i++) {
//            tempDate = CalculateDate.dateToString(CalculateDate.reckonDay(startdate, 5, i));
//            for(String period: periodList){
//                int num = grids.getOrDefault(tempDate+"_"+period, -1);
//                CacheCateringGrid cacheGrid = new CacheCateringGrid();
//                cacheGrid.setDate(tempDate);
//                cacheGrid.setPeriod(period);
//                cacheGrid.setNum(num);
//                result.add(cacheGrid);
//            }
//        }
//        return result;
//    }

    public SkuDetail cleanCache(Date startdate, Date enddate, List<String> prods, String projectId, ProdType prodType, String ratecode) {

        SkuDetail skuDetail = new SkuDetail();
        List<String> dateKeys = CoreCache.getDateKeys(startdate, ProdType.ROOM.equals(prodType) ? CalculateDate.reckonDay(enddate, 5, 0) : enddate);
        for (String prod : prods) {
            String skuMapkey = CoreCache.getGridCacheKey(projectId, SystemUtil.DEFAULT_CHANNELID, prodType.val(), prod);
            String priceMapkey = CoreCache.getRateCacheKey(projectId, ratecode, prodType.val(), prod);

            Long skudelnum = RedisTool.getInstance().opsForHash().delete(skuMapkey, dateKeys.toArray());
            Long pricedelnum = RedisTool.getInstance().opsForHash().delete(priceMapkey, dateKeys.toArray());

            if (skudelnum > 0 || pricedelnum > 0) {
                logger.info("skumap{}移除数量:{}", skuMapkey, skudelnum, pricedelnum);
            }
        }

//        for (Produce_minAvlPo po : pos) {
//            skuDetail.putProductAvl(po.getSku(), po.getAvl());
//        }
        return skuDetail;
    }

    /**
     * @param couponCode 优惠券代码
     * @param projectId
     * @return 查询优惠券缓存
     */
    public Integer getCouponAvlCache(String couponCode, String projectId) {
        Integer avl = null;
        String num = (String) redisTemplate.opsForValue().get(RedisKey.COUPONAVL + couponCode + projectId);
        if (num == null) { //没有缓存查找数据库本地缓存
            RcouponMapper rcouponMapper = SpringUtil.getBean(RcouponMapper.class);
            Rcoupon rcoupon = rcouponMapper.findByProjectidAndCouponcode(projectId, couponCode);
            if (rcoupon != null) {
                avl = rcoupon.getAvl();
                redisTemplate.opsForValue().set(RedisKey.COUPONAVL + couponCode + projectId, avl + "");
            }
        } else {
            avl = Integer.parseInt(num);
        }
        return avl != null ? avl : 0;
    }

    public void updCouponAvlCache(String couponCode, String projectId, Integer avl) {
        redisTemplate.opsForValue().set(RedisKey.COUPONAVL + couponCode + projectId, avl + "");
    }

    /**
     * @param groupId   优惠券大类代码，优惠券的groupid
     * @param projectId
     * @return 获取当天 优惠券大组领取总金额
     */
    public BigDecimal getCouponGroupTodayTotal(String groupId, String projectId) {
        BigDecimal totalPrice = BigDecimal.ZERO;
        String todayStr = DateUtil.today();
        String todayRedis = (String) redisTemplate.opsForValue().get(RedisKey.COUPONGROUPLIMIT + ":" + todayStr + ":" + groupId + projectId);
        if (todayRedis == null) { //没有缓存查找数据库本地缓存
            UserCouponMapping userCouponMapping = SpringUtil.getBean(UserCouponMapping.class);
            BigDecimal todayTotal = userCouponMapping.countTodayTotalPriceByGroupid(projectId, DateUtil.beginOfDay(new Date()),
                    DateUtil.endOfDay(new Date()), groupId);
            if (todayTotal == null) {
                todayTotal = new BigDecimal(BigInteger.ZERO);
            }
            totalPrice = todayTotal;
            //设置当天大组总领取金额缓存
            redisTemplate.opsForValue().set(RedisKey.COUPONGROUPLIMIT + ":" + todayStr + ":" + groupId + projectId, totalPrice + "", 18, TimeUnit.HOURS);
        } else {
            totalPrice = new BigDecimal(todayRedis);
        }
        return totalPrice;
    }

    /**
     * 更新当天优惠券大组 总领取金额
     *
     * @param groupId
     * @param projectId
     * @param price
     */
    public void updCouponGroupTodayTotal(String groupId, String projectId, BigDecimal price) {
        String todayStr = DateUtil.today();
        BigDecimal total = getCouponGroupTodayTotal(groupId, projectId);
        total = total.add(price);
        redisTemplate.opsForValue().set(RedisKey.COUPONGROUPLIMIT + ":" + todayStr + ":" + groupId + projectId, total + "", 18, TimeUnit.HOURS);
    }

    /**
     * 检查优惠券库存，预扣库存
     *
     * @param couponCode
     * @param projectId
     */
    public void checkCouponAvlCache(String couponCode, String projectId) {
        Integer redisAvl = getCouponAvlCache(couponCode, projectId);
        if (redisAvl - 1 < 0) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("已领完"));
        } else {
            //预扣库存
            redisAvl = redisAvl - 1;
            redisTemplate.opsForValue().set(RedisKey.COUPONAVL + couponCode + projectId, redisAvl + "");
        }

    }

    /**
     * 释放预扣库存
     *
     * @param couponCode
     * @param projectId
     */
    public void releaseCouponAvlCache(String couponCode, String projectId) {
        Integer redisAvl = getCouponAvlCache(couponCode, projectId);
        redisAvl = redisAvl + 1;
        redisTemplate.opsForValue().set(RedisKey.COUPONAVL + couponCode + projectId, redisAvl + "");


    }

    /**
     * 查询用户重复领取产品点击缓存
     *
     * @param productCode 产品代码
     * @param userid      用户id
     * @param projectId   项目ID
     * @return
     */
    public boolean IsDoubleClickProduct(String productCode, String userid, String projectId) {
        String key = RedisKey.COUPONCLICK + productCode + ":" + userid + projectId;
        Object userClick = redisTemplate.opsForValue().get(key);
        //如果存在redis记录，则标识前3秒内点击
        if (userClick != null) {
            return true;
        }
        //不存在则缓存3秒钟用户点击操作
        Long value = redisTemplate.opsForValue().increment(key, 1);
        redisTemplate.expire(key, 3, TimeUnit.SECONDS);
        return false;

    }
}
