package com.cw.core;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.func.prodfactory.ProdFactory;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.OrderHandler;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.pojo.common.rule.CreateOrderRuleValidData;
import com.cw.pojo.dto.app.res.AppCancelOrderResult;
import com.cw.pojo.dto.app.res.AppSingleOrderResult;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.service.mq.ExpireMessagePostProcessor;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.bussiness.Bussness_CancelNoPayMsg;
import com.cw.utils.*;
import com.cw.utils.enums.ProdType;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单逻辑处理
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/20 10:10
 **/
@Slf4j
@Service
public class CoreSync3 {

    @Autowired
    OrderHandler orderHandler;

    @Autowired
    CorePrice corePrice;


    @Autowired
    CoreAvl coreAvl;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Lazy
    @Autowired
    CoreOrderWriter coreOrderWriter;

    @Autowired
    RedissonClient redissonClient;

    public AppSingleOrderResult saveOrder_Now(StdOrderData orderData) throws DefinedException {
        AppSingleOrderResult orderResult = diapatchCreateOrderAction(orderData);
        return orderResult;
    }

    private AppSingleOrderResult diapatchCreateOrderAction(StdOrderData orderData) throws DefinedException {
        Booking_rs bookingRs = orderData.getBookingRs();

        checkOrderRequest(orderData);//检查所有表单的日期.不能小于当前日期

        checkAvl(orderData); //检查数据库库存

        //checkProductRule(orderData);//检查产品规则是否可卖

        updPriceAndFill(orderData);//计算价格.并填充

        orderHandler.createInitialOrder(orderData);//提交订单到 接口

        coreOrderWriter.writeCreateOrder2DataDb(orderData);//更新写入数据库

        generateQr(orderData.getBookingRs());//发送二维码

        AppSingleOrderResult orderResult = new AppSingleOrderResult(); // getCreateNewOrderResult(col_rs, orderData.getPricetype());

        orderResult.setOrderid(orderData.getBookingRs().getBookingid());
        orderResult.setTotalAmount(orderData.getBookingRs().getAmount());

        //添加userlog
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        String content = "系统订单创建成功，订单号：" + bookingRs.getBookingid();
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
                bookingRs.getBookingid(), content, bookingRs.getProjectid());

        if (CalculateNumber.isGreaterThanZero(orderData.getBookingRs().getAmount())) { //如果订单不需要产生支付.就不需要放到自动取消队列里
            int exprieMin = ContentCacheTool.getSysOrderExpireMinutue(orderData.getBookingRs().getProjectid());
            long countDown = DateUtil.offset(CalculateDate.asUtilDate(orderData.getBookingRs().getCreatedate()),
                    DateField.MINUTE, exprieMin).getTime();
            orderResult.setExpireTime(countDown + "");
            log.info("订单创建成功 将在{} 取消订单", DateUtil.format(DateUtil.date(countDown), "yyyy-MM-dd HH:mm:ss"));

            Bussness_CancelNoPayMsg msg = new Bussness_CancelNoPayMsg();
            msg.setBookingId(orderData.getBookingRs().getBookingid());
            msg.setProjectId(orderData.getBookingRs().getProjectid());
            msg.setAddTime(DateUtil.format(DateUtil.date(countDown), "yyyy-MM-dd HH:mm:ss"));

            int extraTime = 20;//实际额外的自动取消时间.暂时先加个20秒 .微信支付用户输密码是不算超时的

            String delaySignal = MqNameUtils.getDelaySignal(MqNameUtils.DirectExchange.MALL, MqNameUtils.BussinessTask.CANCELNOPAY.name());
            ExpireMessagePostProcessor expire = new ExpireMessagePostProcessor(TimeUnit.MINUTES.toSeconds(exprieMin) + extraTime, TimeUnit.SECONDS);//延迟个5秒.防止跟关闭交易时间有冲突
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.DELAY.name(), delaySignal,
                    JSON.toJSONString(msg), expire);
        }


        return orderResult;
    }

    private void generateQr(Booking_rs rs) throws DefinedException {
        if (CalculateNumber.isZero(rs.getAmount())) {//暂时先用订单是否免费来确认是否跳过支付前发码
            if (rs.getPtype().equals(ProdType.TICKET.val())) {
                boolean sendResult = ProdFactory.getProd(ProdType.TICKET).sendOrderQr(rs.getBookingid(), rs.getProjectid());
                if (!sendResult) {
                    throw new DefinedException("二维码订单生成失败,请检查订单信息");
                }
            }
        }
    }


    /**
     * 检查数据库中的可卖表记录情况
     *
     * @param orderData
     */
    private void checkAvl(StdOrderData orderData) throws DefinedException {

        if (!orderData.getRooms().isEmpty()) {//现在没有 block .但是是给后面的促销活动做准备
            Map<String, List<Room_rs>> blockRoomsMap = orderData.getRooms().stream().collect(Collectors.groupingBy(Room_rs::getBlock));
            for (Map.Entry<String, List<Room_rs>> blockEntry : blockRoomsMap.entrySet()) {
                HashMap<String, Object> checkResult = coreAvl.checkBatchCreateAvailability(blockEntry.getValue(), blockEntry.getKey(), orderData.getBookingRs().getProjectid());
                if (checkResult.size() > 0) {  //如果这些客房预订都来自不同的 BLOCK. 就批量循环检查
                    String errmsg = "";
                    for (Object o : checkResult.values()) {
                        errmsg += errmsg.isEmpty() ? o.toString() : "," + o.toString();
                    }
                    throw new DefinedException(errmsg, SystemUtil.SystemerrorCode.ERR009_CANCELFAIL);
                }
            }
        }

    }

    private void checkOrderRequest(StdOrderData orderData) throws DefinedException {
        Date systemDate = CalculateDate.getSystemDate();
        if (!orderData.getRooms().isEmpty()) {
            for (Room_rs room : orderData.getRooms()) {
                if (CalculateDate.isBefore(room.getArrdate(), systemDate)) {
                    throw new DefinedException("客房预订日期不能早于当前日期", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }
            }
        }
        if (!orderData.getTickets().isEmpty()) {
            for (Ticket_rs ticket : orderData.getTickets()) {
                if (CalculateDate.isBefore(ticket.getUsedate(), systemDate)) {
                    throw new DefinedException("门票预订日期不能早于当前日期", SystemUtil.SystemerrorCode.ERR015_FORMERR);
                }
            }
        }

    }

    /**
     * 判断订单是否符合售卖规则
     *
     * @param orderData
     * @throws DefinedException
     */
    public void checkProductRule(StdOrderData orderData) throws DefinedException {


        CreateOrderRuleValidData validData = SysFuncLibTool.judgeRuleCanBook(orderData.getBookingRs().getArrdate(), orderData.getBookingRs().getAnz(),
                orderData.getBookingRs().getPtype(), orderData.getBookingRs().getProduct(), orderData.getBookingRs().getProjectid(), orderData.getUid());
        if (!validData.getLallow()) {
            throw new DefinedException(validData.getReason(), SystemUtil.SystemerrorCode.ERR015_FORMERR);
        }

    }

    /**
     * 写入最新价格
     *
     * @param orderData
     */
    public void updPriceAndFill(StdOrderData orderData) throws DefinedException {
        BigDecimal totalAmount = BigDecimal.ZERO;
        String actcode = orderData.getActCode();
        String kitcode = orderData.getKitCode();
        if (kitcode.isEmpty()) {
            for (Room_rs room : orderData.getRooms()) {
                corePrice.writeRoomPrice(orderData, room, orderData.getKitCode(), orderData.getActCode(), true);
                totalAmount = room.getAmount().add(totalAmount);
            }
            for (Ticket_rs ticket : orderData.getTickets()) {
                corePrice.writeTicketPrice(ticket, orderData.getKitCode(), orderData.getActCode(), true);
                totalAmount = ticket.getAmount().add(totalAmount);

            }
            for (Cater_rs cater : orderData.getCaters()) {
                corePrice.writeCaterPrice(cater, orderData.getKitCode(), orderData.getActCode(), true);
                totalAmount = cater.getAmount().add(totalAmount);
            }
            for (Spu_rs spus : orderData.getSpus()) {
                corePrice.writeSpuPrice(spus, orderData.getKitCode(), orderData.getActCode(), true);
                totalAmount = spus.getAmount().add(totalAmount);
            }
            if (CollectionUtil.isNotEmpty(orderData.getGifts())) {
                BigDecimal giftPrice = corePrice.writeGiftPrice(orderData, orderData.getGifts(),
                        orderData.getKitCode(), orderData.getActCode(), orderData.getBookingRs().getProjectid());
                totalAmount = giftPrice.add(totalAmount);
                //BigDecimal extrafee=BigDecimal.ZERO;//TODO 计算运费
                // totalAmount=totalAmount.add(extrafee);
            }
        } else {
            totalAmount = corePrice.overrideKitsPrice(orderData.getBookingRs().getArrdate(), orderData);
        }
        orderData.getBookingRs().setAmount(totalAmount); //生成总金额
        orderData.setTotalAmount(totalAmount);
        if (orderData.getCombineInfo() != null) {//combine 合单信息 需要保存创建订单时的价格.
            orderData.getBookingRs().setCombineinfo(JSON.toJSONString(orderData.getCombineInfo()));
        }
        //后续这里还会有一个优惠券的相关计算.还有折扣的计算
    }

    public AppCancelOrderResult diapatchCancelOrderAction(StdOrderData orderData) throws DefinedException {
        orderHandler.cancelOrder(orderData);

        coreOrderWriter.writeCancelOrder2DataDb(orderData);

        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        String content = "系统订单取消成功，订单号：" + orderData.getBookingRs().getBookingid();
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                orderData.getBookingRs().getBookingid(), content, orderData.getBookingRs().getProjectid());

        AppCancelOrderResult orderResult = new AppCancelOrderResult(); // getCreateNewOrderResult(col_rs, orderData.getPricetype());
        orderResult.setDebit(orderData.getBookingRs().getAmount());//暂时全退
        return orderResult;
    }


    public void cancelLocalOrder(StdOrderData orderData) throws DefinedException {
        coreOrderWriter.writeCancelOrder2DataDb(orderData);  //一般是跟第三方系统交互失败.无法交付给用户完整状态的物品.本地直接取消并退款
    }


//    public AppCancelOrderResult diapatchCancelPCOrderAction(StdOrderData orderData) throws DefinedException {
//        orderHandler.cancelPCOrder(orderData);
//
//        coreOrderWriter.writeCancelOrder2DataDb(orderData);
//
//        AppCancelOrderResult orderResult = new AppCancelOrderResult(); // getCreateNewOrderResult(col_rs, orderData.getPricetype());
//        orderResult.setDebit(orderData.getBookingRs().getAmount());//暂时全退
//        return orderResult;
//    }

    /**
     * @param projectId
     * @param bookingId
     * @param status
     */
    public void updateBookingRsStatus(String projectId, String bookingId, String status) {
        RLock orderLock = redissonClient.getLock(RedisKey.getOrderLockKey(projectId, bookingId));
        boolean lok = false;//等待5秒.
        try {
            lok = orderLock.tryLock(3, 5, TimeUnit.SECONDS);
            if (lok) {
                coreOrderWriter.updStatus(bookingId, status);
                orderLock.unlock();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updProductMainRsStatus(String projectId, String bookingId, String status, ProdType prodType) {
        RLock orderLock = redissonClient.getLock(RedisKey.getOrderLockKey(projectId, bookingId));
        boolean lok = false;//等待5秒.
        try {
            lok = orderLock.tryLock(3, 5, TimeUnit.SECONDS);
            if (lok) {
                coreOrderWriter.updProdBookingRsStatus(bookingId, status, prodType);
                orderLock.unlock();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
