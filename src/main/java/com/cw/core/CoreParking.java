package com.cw.core;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.arithmetic.sku.YamlTool;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.config.confyaml.VendorYaml;
import com.cw.config.confyaml.node.Conf_DsParking;
import com.cw.core.orderhandler.ParkingVendorSwitcher;
import com.cw.core.vendor.parking.ParkingVendorHandler;
import com.cw.entity.Vendorconfig;
import com.cw.outsys.stdop.common.StdParkingQueryNode;
import com.cw.outsys.stdop.request.StdQueryParkingStatusRequest;
import com.cw.outsys.stdop.response.StdParkingStatusResponse;
import com.cw.pojo.dto.app.res.node.ParkSiteExtraData;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.VendorType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.redisson.api.RMapCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/12/2 16:56
 **/
@Service
public class CoreParking {
    @Autowired
    ParkingVendorSwitcher switcher;


    public Map<String, List<ParkSiteExtraData>> fillParkSiteData(String parkingType, String projectId) {
        //Vendorconfig vendorconfig= ParkingVendorHandler
        VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);

        Vendorconfig vendorconfig = configCache.getVendorConfig(projectId, EnumUtil.fromString(VendorType.class, parkingType, VendorType.LOCAL));
        if (vendorconfig == null || StrUtil.isBlank(vendorconfig.getYaml())) {
            return null;
        }
        VendorYaml yaml = YamlTool.readValue(vendorconfig.getYaml(), VendorYaml.class);
        Conf_DsParking dsParking = yaml.getParking();
        if (dsParking == null) {
            return null;
        }

        Map<String, StdParkingQueryNode> parkingInfos = getQueryNodes(vendorconfig, parkingType, projectId);
        Map<String, List<ParkSiteExtraData>> data = Maps.newHashMap();
        for (Conf_DsParking.Mapping mapping : dsParking.getMapping()) {
            List<ParkSiteExtraData> siteExtraDat = Lists.newArrayList();
            for (Conf_DsParking.Site site : mapping.getSites()) {
                if (parkingInfos != null && parkingInfos.containsKey(site.getOutcode())) {
                    StdParkingQueryNode queryNode = parkingInfos.get(site.getOutcode());
                    ParkSiteExtraData extraData = new ParkSiteExtraData();
                    extraData.setName(site.getDescription());
                    extraData.setNotice(StrUtil.format("剩余车位{}个", queryNode.getAvl()));
                    extraData.setStatus(SysFuncLibTool.getSkuStatus(queryNode.getAvl(), queryNode.getTotal()));
                    siteExtraDat.add(extraData);
                }
            }
            data.put(mapping.getCode(), siteExtraDat);
        }
        return data;
    }

    /**
     * 接口+缓存查询停车场信息数据
     *
     * @param vendorconfig
     * @param parkingType
     * @param projectId
     * @return
     */
    private Map<String, StdParkingQueryNode> getQueryNodes(Vendorconfig vendorconfig, String parkingType, String projectId) {
        Map<String, StdParkingQueryNode> result = null;
        RMapCache<String, Map<String, StdParkingQueryNode>> mapCache = RedisTool.getRedissonClient().getMapCache(RedisKey.PARKING_IFCINFO);
        Map<String, StdParkingQueryNode> queryNodes = mapCache.get(parkingType + projectId);
        if (queryNodes == null) {  //缓存中没有数据，则从vendorconfig 接口中获取数据并缓存
            ParkingVendorHandler handler = switcher.getVendorHandler(VendorType.valueOf(parkingType));
            StdQueryParkingStatusRequest request = new StdQueryParkingStatusRequest();
            request.setProjectId(projectId);
            StdParkingStatusResponse response = handler.queryParkingStatus(request);
            queryNodes = response.getSiteSummaryData();
            //System.out.println("queryNodes=" + JSON.toJSONString(queryNodes));
            mapCache.put(parkingType + projectId, queryNodes, 2, TimeUnit.MINUTES);
        }
        result = queryNodes;
        return result;
    }


}
