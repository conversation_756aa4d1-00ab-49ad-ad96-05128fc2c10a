package com.cw.core.sender.robot;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.entity.Notifyrobot;
import com.cw.exception.DefinedException;
import com.cw.mapper.NotifyrobotMapper;
import com.cw.pojo.dto.conf.req.notifyrobot.NotifyRobot_Send_Req;
import com.cw.pojo.dto.conf.req.notifyrobot.TextMessageRequest;
import com.cw.pojo.dto.conf.res.notifyrobot.RobotMessageResponse;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Component
public class RobotSender {

    @Autowired
    RestTemplate restTemplate;

    public RobotMessageResponse sendTextMessage(TextMessageRequest request) throws DefinedException {
        RobotMessageResponse response = null;
        NotifyRobot_Send_Req req = new NotifyRobot_Send_Req();
        BeanUtils.copyProperties(request, req);
        String code = req.getCode();//如果有code 那就是点了报警列表来测试用的
        String group = req.getGroup();
        if (StringUtils.isNotBlank(code)) { //测试用
            Notifyrobot notifyrobot = SpringUtil.getBean(NotifyrobotMapper.class).getRobotByCode(code);
            if (notifyrobot != null) {
                RobotUtils robotUtils = new RobotUtils();
                String url = "";
                if ("2".equals(notifyrobot.getType())) {
                    url = robotUtils.getDingRobotUrl(notifyrobot.getSecrect(), notifyrobot.getUrl());
                } else {
                    url = notifyrobot.getUrl();
                }
                if (!url.isEmpty()) {
                    req.setContent("发送" + ("2".equals(notifyrobot.getType()) ? "钉钉" : "微信") + "测试信息");
                    response = robotUtils.robotHttp(url, req.toJsonString());
                }
            }
        } else if (StringUtils.isNotBlank(group)) {//自动触发
            //String projectId = GlobalContext.getCurrentProjectId();
            //todo 获取项目projectid通过缓存获取
            //List<Notifyrobot> notifyrobots = GlobalCache.getDataStructure()
            //        .getCache(SystemUtil.GlobalDataType.NOTIFYROBOT).getGroupList(projectId, group);
            List<Notifyrobot> notifyrobots = SpringUtil.getBean(NotifyrobotMapper.class).findAll();
            if (CollectionUtil.isNotEmpty(notifyrobots)) {
                RobotUtils robotUtils = new RobotUtils();
                for (Notifyrobot notifyrobot : notifyrobots) {
                    if (!notifyrobot.getRobotstatus() || !notifyrobot.getGroup().equals(group)) {
                        continue;
                    }
                    String url = "";
                    if ("2".equals(notifyrobot.getType())) {
                        url = new RobotUtils().getDingRobotUrl(notifyrobot.getSecrect(), notifyrobot.getUrl());
                    } else {
                        url = notifyrobot.getUrl();
                    }
                    if (!url.isEmpty()) {
                        response = robotUtils.robotHttp(url, req.toJsonString());
                    }
                }
            }
        }
        return response;
    }


}
