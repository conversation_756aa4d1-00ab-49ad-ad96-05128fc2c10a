package com.cw.core;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.config.exception.CustomException;
import com.cw.entity.*;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_UpdSeq_Req;
import com.cw.service.context.GlobalContext;
import com.cw.service.log.UserLogService;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 公共的表记录排序方法
 *
 * <AUTHOR>
 */
@Service
public class SequenceService {


    private static Map<String, SystemUtil.GlobalDataType> seqSuportMap = Maps.newHashMap();

    static {
        seqSuportMap.put(Roomtype.class.getSimpleName(), SystemUtil.GlobalDataType.ROOMTYPE);
        seqSuportMap.put(Hotel.class.getSimpleName(), SystemUtil.GlobalDataType.HOTEL);
        seqSuportMap.put(Ticket.class.getSimpleName(), SystemUtil.GlobalDataType.TICKET);
        seqSuportMap.put(Ticketgroup.class.getSimpleName(), SystemUtil.GlobalDataType.TICKETGROUP);
        seqSuportMap.put(Meeting.class.getSimpleName(), SystemUtil.GlobalDataType.MEETING);
        seqSuportMap.put(Meetinggroup.class.getSimpleName(), SystemUtil.GlobalDataType.MEETINGGROUP);
        seqSuportMap.put(Menu_content.class.getSimpleName(), SystemUtil.GlobalDataType.MENUCONTENT);
        seqSuportMap.put(Kitgroup.class.getSimpleName(), SystemUtil.GlobalDataType.KITGROUP);
        seqSuportMap.put(Productkit.class.getSimpleName(), SystemUtil.GlobalDataType.PRODUCTKIT);
        seqSuportMap.put(Factor.class.getSimpleName(), SystemUtil.GlobalDataType.FACTOR);
        seqSuportMap.put(Menus.class.getSimpleName(), SystemUtil.GlobalDataType.MENUS);
        seqSuportMap.put(Gift.class.getSimpleName(), SystemUtil.GlobalDataType.GIFT);
        seqSuportMap.put(Giftitem.class.getSimpleName(), SystemUtil.GlobalDataType.GIFTITEM);
        seqSuportMap.put(Actgroup.class.getSimpleName(), SystemUtil.GlobalDataType.ACTGROUP);
        seqSuportMap.put(Actsite.class.getSimpleName(), SystemUtil.GlobalDataType.ACTSITE);
        seqSuportMap.put(Spugroup.class.getSimpleName(), SystemUtil.GlobalDataType.SPUGROUP);
        seqSuportMap.put(Spusitem.class.getSimpleName(), SystemUtil.GlobalDataType.SPUSITEM);
        seqSuportMap.put(Park.class.getSimpleName(), SystemUtil.GlobalDataType.PARK);
        seqSuportMap.put(Parksite.class.getSimpleName(), SystemUtil.GlobalDataType.PARKSITE);
        seqSuportMap.put(Parkbusiness.class.getSimpleName(), SystemUtil.GlobalDataType.PARKBUSS);
        seqSuportMap.put(Traveltip.class.getSimpleName(), SystemUtil.GlobalDataType.TRAVELTIP);
        seqSuportMap.put(Coupon.class.getSimpleName(), SystemUtil.GlobalDataType.COUPON);
        seqSuportMap.put(Coupongroup.class.getSimpleName(), SystemUtil.GlobalDataType.COUPON);
        seqSuportMap.put(Perform.class.getSimpleName(), SystemUtil.GlobalDataType.PERFORM);
        seqSuportMap.put(Contestentry.class.getSimpleName(), SystemUtil.GlobalDataType.CONTESTENTRY);

    }

    private DaoLocal<?> daoLocal;
    private GlobalCache globalCache;
    @Autowired
    UserLogService userLogService;

    public SequenceService(DaoLocal<?> daoLocal, GlobalCache globalCache) {
        this.daoLocal = daoLocal;
        this.globalCache = globalCache;
    }

    private SystemUtil.GlobalDataType getSupportType(Common_UpdSeq_Req req) {
        return seqSuportMap.getOrDefault(req.getTableName(), null);
    }

    /**
     * 获取新的排序字段值
     *
     * @param tableClass
     * @param projectId
     * @param columnMap
     * @return
     */
    public int getNewSeq(Class<?> tableClass, String projectId, Map<String, Object> columnMap) {
        //todo 并发下不一定是最新数据
        synchronized (this) {
            int count = 0;
            if (seqSuportMap.containsKey(tableClass.getSimpleName())) {
                //max(seq)为null返回0
                String jpql = StrUtil.format("select coalesce(max(seq), 0) from {} where projectid='{}' ", tableClass.getSimpleName(), projectId);
                if (CollectionUtil.isNotEmpty(columnMap)) {
                    for (String columnKey : columnMap.keySet()) {
                        if (columnMap.get(columnKey) instanceof String) {
                            jpql = jpql + StrUtil.format(" and {}='{}' ", columnKey, columnMap.get(columnKey));
                        } else {
                            jpql = jpql + StrUtil.format(" and {}={} ", columnKey, columnMap.get(columnKey));
                        }
                    }

                }
                count = daoLocal.getCountOption(jpql);
            }
            return count + 1;
        }
    }

    /**
     * @param tableClass 需要置顶类的表
     * @param projectId  项目Id
     * @param sqlid      需要置顶的slqid
     * @param columnMap  条件约束的集合
     * @return
     */
    public void updateTopSeq(Class<?> tableClass, String projectId, Long sqlid, Map<String, Object> columnMap) {

        if (seqSuportMap.containsKey(tableClass.getSimpleName())) {
            //min(seq)为null返回1
            String jpql = StrUtil.format("select coalesce(min(seq), 1) from {} where projectid='{}' ", tableClass.getSimpleName(), projectId);
            //条件约束
            if (CollectionUtil.isNotEmpty(columnMap)) {
                for (String columnKey : columnMap.keySet()) {
                    if (columnMap.get(columnKey) instanceof String) {
                        jpql = jpql + StrUtil.format(" and {}='{}' ", columnKey, columnMap.get(columnKey));
                    } else {
                        jpql = jpql + StrUtil.format(" and {}={} ", columnKey, columnMap.get(columnKey));
                    }
                }

            }
            //获取搜索条件内最小排序  更新最小值数据seq+1，设置当前数据seq最小值
            Integer minSeq = daoLocal.getCountOption(jpql);
            Integer nextSeq = minSeq + 1;
            String updSql1 = "update {} set seq=?1 where seq=?2  and projectid=?3 ";
            if (CollectionUtil.isNotEmpty(columnMap)) {
                updSql1 = updSql1 + getMapSql(columnMap);

            }
            String updSql2 = "update {} set seq=?1 where sqlid=?2";
            updSql1 = StrUtil.format(updSql1, tableClass.getSimpleName());
            updSql2 = StrUtil.format(updSql2, tableClass.getSimpleName());
            daoLocal.batchOption(updSql1, nextSeq, minSeq, projectId);
            daoLocal.batchOption(updSql2, minSeq, sqlid);
        }

    }


    public void updTableSeq(Common_UpdSeq_Req seqReq) {
        SystemUtil.GlobalDataType dataType = getSupportType(seqReq);
        String projectId = GlobalContext.getCurrentProjectId();
        if (dataType == null) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg(seqReq.getTableName() + "不支持排序"));
        }

        String seekJpql = StrUtil.format("select  seq from {} where sqlid={} ", seqReq.getTableName(), seqReq.getSqlId());
        Integer orgSeq = daoLocal.getObject(seekJpql);
        if (orgSeq == null) {
            return;
        }
        //有可能有相同序号 如
        String seekTargetJpql = StrUtil.format("select seq from {} where sqlid<>{} and projectid='{}' and seq {} {}",
                seqReq.getTableName(),
                seqReq.getSqlId(),
                projectId,
                seqReq.getLup() ? "<=" : ">=",
                orgSeq);

        //添加额外条件
        if (CollectionUtil.isNotEmpty(seqReq.getColumnMap())) {
            seekTargetJpql = seekTargetJpql + getMapSql(seqReq.getColumnMap());

        }

        //判断请求升降排序 选相临近的排序
        String sort = seqReq.getLup() ? "desc" : "asc";
        seekTargetJpql = seekTargetJpql + StrUtil.format("order by seq {}", sort);

        Integer targetSeq = daoLocal.getObject(seekTargetJpql);
        //判断初始排序和模板排序是否一致，一致则不需要更新
        if (orgSeq != null && targetSeq != null) {
            if (!orgSeq.equals(targetSeq)) {
                //如果没有相同排序 互换排序
                String updSql1 = "update {} set seq=?1 where seq=?2  and projectid=?3 ";
                //添加额外条件
                if (CollectionUtil.isNotEmpty(seqReq.getColumnMap())) {
                    updSql1 = updSql1 + getMapSql(seqReq.getColumnMap());
                    updSql1 = StrUtil.format(updSql1, seqReq.getTableName());
                    //互换排序
                    daoLocal.batchOption(updSql1, orgSeq, targetSeq, projectId);
                }
            } else {
                //如果有相同排序 判断排序加减
                if (seqReq.getLup()) {
                    targetSeq = targetSeq - 1 <= 0 ? 1 : targetSeq - 1; //上升排序判断边界
                } else {
                    targetSeq = targetSeq + 1;//下降直接+1
                }
            }
            //更新目标排序
            String updSql2 = "update {} set seq=?1 where sqlid=?2";
            updSql2 = StrUtil.format(updSql2, seqReq.getTableName());
            daoLocal.batchOption(updSql2, targetSeq, seqReq.getSqlId());
            ////写入日志
            writeUserLog(seqReq, orgSeq, targetSeq);

        }
    }

    /**
     * 判断更新排序的类型写入操作日志
     *
     * @param seqReq    变更排序信息
     * @param orgSeq    初始排序号
     * @param targetSeq 更新排序号
     */
    private void writeUserLog(Common_UpdSeq_Req seqReq, Integer orgSeq, Integer targetSeq) {
        SystemUtil.UserLogType type = null;
        if (seqReq.getTableName().equalsIgnoreCase(SystemUtil.UserLogType.MENUS.name())) {
            type = SystemUtil.UserLogType.MENUS;
        } else if (seqReq.getTableName().equalsIgnoreCase(SystemUtil.UserLogType.MENU_CONTENT.name())) {
            type = SystemUtil.UserLogType.MENU_CONTENT;
        } else if (seqReq.getTableName().equalsIgnoreCase(SystemUtil.UserLogType.HOTEL.name())) {
            type = SystemUtil.UserLogType.HOTEL;
        } else if (seqReq.getTableName().equalsIgnoreCase(SystemUtil.UserLogType.ROOMTYPE.name())) {
            type = SystemUtil.UserLogType.ROOMTYPE;
        } else if (seqReq.getTableName().equalsIgnoreCase(SystemUtil.UserLogType.TICKET.name())) {
            type = SystemUtil.UserLogType.TICKET;
        } else if (seqReq.getTableName().equalsIgnoreCase(SystemUtil.UserLogType.PRODUCTKIT.name())) {
            type = SystemUtil.UserLogType.PRODUCTKIT;
        } else if (seqReq.getTableName().equalsIgnoreCase(SystemUtil.UserLogType.FACTOR.name())) {
            type = SystemUtil.UserLogType.FACTOR;
        } else if (seqReq.getTableName().equalsIgnoreCase(SystemUtil.UserLogType.PERFORM.name())) {
            type = SystemUtil.UserLogType.PERFORM;
        }
        if (type != null) {
            //todo 根据类型sql 去查找对应实体描述
            String content = "变更" + type.getDesc() + "id" + seqReq.getSqlId() + "排序：" + orgSeq + "=>" + targetSeq;
            userLogService.writeLog(type, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                    seqReq.getSqlId() + "", content, GlobalContext.getCurrentProjectId());
        }
    }

    /**
     * map参数拼接，默认拼接字符型参数
     *
     * @param map
     * @return
     */
    private String getMapSql(Map<String, Object> map) {
        String sqlStr = "";
        for (String columnKey : map.keySet()) {
            if (map.get(columnKey) instanceof String) {
                sqlStr = sqlStr + StrUtil.format(" and {}='{}' ", columnKey, map.get(columnKey));
            } else {
                sqlStr = sqlStr + StrUtil.format(" and {}={} ", columnKey, map.get(columnKey));
            }
        }
        return sqlStr;
    }


}
