package com.cw.core.vendor.crm;

import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.stdop.request.crm.*;
import com.cw.outsys.stdop.response.crm.*;

/**
 * @Describe
 * <AUTHOR>
 * @Create on 2022-03-28
 */
public interface CrmVendorHandler {

    void initClient();

    void refreshClientConfig(Vendorconfig vendorconfig);

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdAddCustomerResponse addCrmMember(StdAddCustomerRequest request) throws DefinedException {
        return null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdCancelCouponResponse cancelCrmCoupon(StdCancelCouponRequest request) throws DefinedException {
        return null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdCheckCouponValidResponse checkCrmCouponValid(StdCheckCouponVaildRequest request) throws DefinedException {
        return null;
    }


    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdConsumeCouponResponse cancelCrmCoupon(StdConsumeCouponRequest request) throws DefinedException {
        return null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdConsumeCrmPointResponse consumeCrmPoint(StdConsumeCrmPointRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdCrmCouponActDetailResponse getCrmActDetail(StdCrmCouponActDetailRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdCrmCouponInfoResponse getCrmCouponInfo(StdCrmCouponInfoRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdGetProdCouponResponse getCrmProdCoupon(StdGetProdCouponRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdOpCrmPointResponse opCrmPoint(StdOpCrmPointRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdQueryCrmAccountResponse queryCrmAccount(StdQueryCrmAccountRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdQueryCustomerResponse queryCrmCustomer(StdQueryCustomerRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdQueryLabelResponse queryCrmLabel(StdQueryLabelRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdQueryMyCouponResponse queryCrmMyCoupon(StdQueryMyCouponRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdSendCrmCouponResponse sendCrmCoupon(StdSendCrmCouponRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdUpdCustomerLabelResponse updCrmLabel(StdUpdCustomerLabelRequest request) throws DefinedException {
        throw null;
    }

    /**
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdUpdCustomerResponse updCrmMember(StdUpdCustomerRequest request) throws DefinedException {
        throw null;
    }


}
