package com.cw.core.vendor.crm;

import com.cw.core.orderhandler.VendorAdapter;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.crm.ZjCrmClient;
import com.cw.outsys.pojo.crm.zhijian.request.*;
import com.cw.outsys.pojo.crm.zhijian.response.*;
import com.cw.outsys.stdop.request.crm.*;
import com.cw.outsys.stdop.response.crm.*;
import com.cw.utils.enums.VendorType;

/**
 * 中景CRM 适配器
 * <p>
 * 智简CRM的套壳.所以直接用智简的实现类
 */
@VendorAdapter(vendorType = VendorType.ZJ_CRM)
public class ZjCrmVendor extends BaseOutCrmVendor<ZjCrmClient> {


    @Override
    public StdAddCustomerResponse addCrmMember(StdAddCustomerRequest request) throws DefinedException {
        ZhijianAddCustomerRequest outRequest = new ZhijianAddCustomerRequest().transfer(request);
        ZhijianAddCustomerResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdAddCustomerResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdUpdCustomerResponse updCrmMember(StdUpdCustomerRequest request) throws DefinedException {
//        if (true) {
//            throw new DefinedException("OHHHHH!");
//        }
        ZhijianUpdCustomerRequest outRequest = new ZhijianUpdCustomerRequest();
        ZhijianUpdCustomerResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdUpdCustomerResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdUpdCustomerLabelResponse updCrmLabel(StdUpdCustomerLabelRequest request) throws DefinedException {
        ZhijianUpdCustomerLabelRequest outRequest = new ZhijianUpdCustomerLabelRequest();
        ZhijianUpdCustomerLabelResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdUpdCustomerLabelResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdCancelCouponResponse cancelCrmCoupon(StdCancelCouponRequest request) throws DefinedException {
        ZhijianCancelCouponRequest outRequest = new ZhijianCancelCouponRequest();
        ZhijianCancelCouponResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdCancelCouponResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdCheckCouponValidResponse checkCrmCouponValid(StdCheckCouponVaildRequest request) throws DefinedException {
        ZhijianCheckCouponVaildRequest outRequest = new ZhijianCheckCouponVaildRequest();
        ZhijianCheckCouponValidResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdCheckCouponValidResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdConsumeCouponResponse cancelCrmCoupon(StdConsumeCouponRequest request) throws DefinedException {
        ZhijianConsumeCouponRequest outRequest = new ZhijianConsumeCouponRequest();
        ZhijianConsumeCouponResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdConsumeCouponResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdConsumeCrmPointResponse consumeCrmPoint(StdConsumeCrmPointRequest request) throws DefinedException {
        ZhijianConsumeCrmPointRequest outRequest = new ZhijianConsumeCrmPointRequest();
        ZhijianConsumeCrmPointResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdConsumeCrmPointResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdCrmCouponActDetailResponse getCrmActDetail(StdCrmCouponActDetailRequest request) throws DefinedException {
        ZhijianCrmCouponActDetailRequest outRequest = new ZhijianCrmCouponActDetailRequest();
        ZhijianCrmCouponActDetailResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdCrmCouponActDetailResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdCrmCouponInfoResponse getCrmCouponInfo(StdCrmCouponInfoRequest request) throws DefinedException {
        ZhijianCrmCouponInfoRequest outRequest = new ZhijianCrmCouponInfoRequest();
        ZhijianCrmCouponInfoResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdCrmCouponInfoResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdGetProdCouponResponse getCrmProdCoupon(StdGetProdCouponRequest request) throws DefinedException {
        ZhijianGetProdCouponRequest outRequest = new ZhijianGetProdCouponRequest();
        ZhijianGetProdCouponResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdGetProdCouponResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdOpCrmPointResponse opCrmPoint(StdOpCrmPointRequest request) throws DefinedException {
        ZhijianOpCrmPointRequest outRequest = new ZhijianOpCrmPointRequest();
        ZhijianOpCrmPointResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdOpCrmPointResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdQueryCrmAccountResponse queryCrmAccount(StdQueryCrmAccountRequest request) throws DefinedException {
        ZhijianQueryCrmAccountRequest outRequest = new ZhijianQueryCrmAccountRequest();
        ZhijianQueryCrmAccountResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdQueryCrmAccountResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdQueryCustomerResponse queryCrmCustomer(StdQueryCustomerRequest request) throws DefinedException {
        ZhijianQueryCustomerRequest outRequest = new ZhijianQueryCustomerRequest();
        ZhijianQueryCustomerResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdQueryCustomerResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdQueryLabelResponse queryCrmLabel(StdQueryLabelRequest request) throws DefinedException {
        ZhijianQueryLabelRequest outRequest = new ZhijianQueryLabelRequest();
        ZhijianQueryLabelResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdQueryLabelResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdQueryMyCouponResponse queryCrmMyCoupon(StdQueryMyCouponRequest request) throws DefinedException {
        ZhijianQueryMyCouponRequest outRequest = new ZhijianQueryMyCouponRequest();
        ZhijianQueryMyCouponResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdQueryMyCouponResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }

    @Override
    public StdSendCrmCouponResponse sendCrmCoupon(StdSendCrmCouponRequest request) throws DefinedException {
        ZhijianSendCrmCouponRequest outRequest = new ZhijianSendCrmCouponRequest();
        ZhijianSendCrmCouponResponse response = getClient(request.getProjectId()).execute(outRequest);
        StdSendCrmCouponResponse stdResponse = response.getSysStdResponse();
        return stdResponse;
    }
}
