package com.cw.core.vendor.sms;

import com.cw.core.orderhandler.VendorAdapter;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.sms.SmsClient;
import com.cw.outsys.pojo.sms.request.SmsQueryStatusRequest;
import com.cw.outsys.pojo.sms.request.SmsQueryTemplateRequest;
import com.cw.outsys.pojo.sms.request.SmsSendMsgRequest;
import com.cw.outsys.pojo.sms.resonse.SmsQueryStatusResponse;
import com.cw.outsys.pojo.sms.resonse.SmsQueryTemplateResponse;
import com.cw.outsys.pojo.sms.resonse.SmsSendMsgResponse;
import com.cw.outsys.stdop.request.StdSmsQueryStatusRequest;
import com.cw.outsys.stdop.request.StdSmsQueryTemplateRequest;
import com.cw.outsys.stdop.request.StdSmsSendMsgRequest;
import com.cw.outsys.stdop.response.StdSmsQueryStatusResponse;
import com.cw.outsys.stdop.response.StdSmsQueryTemplateResponse;
import com.cw.outsys.stdop.response.StdSmsSendMsgResponse;
import com.cw.utils.enums.VendorType;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2022-03-28
 */
@VendorAdapter(vendorType = VendorType.ZJ_SMS)
public class SmsVendor extends BaseOutSmsVendor<SmsClient> {

    /**
     * @param stdSmsSendMsgRequest
     * @return 发送短信
     * @throws DefinedException
     */
    public StdSmsSendMsgResponse sendSmsMsg(StdSmsSendMsgRequest stdSmsSendMsgRequest) throws DefinedException {
        SmsClient client = getClient(stdSmsSendMsgRequest.getProjectId());
        SmsSendMsgRequest request = new SmsSendMsgRequest().transfer(stdSmsSendMsgRequest);
        SmsSendMsgResponse response = client.execute(request);
        StdSmsSendMsgResponse stdSmsSendMsgResponse = response.getSysStdResponse();
        return stdSmsSendMsgResponse;
    }

    /**
     * @param stdSmsQueryStatusRequest
     * @return 查询短信回执状态
     * @throws DefinedException
     */
    public StdSmsQueryStatusResponse querySmsStatus(StdSmsQueryStatusRequest stdSmsQueryStatusRequest) throws DefinedException {
        SmsClient client = getClient(stdSmsQueryStatusRequest.getProjectId());
        SmsQueryStatusRequest request = new SmsQueryStatusRequest().transfer(stdSmsQueryStatusRequest);
        SmsQueryStatusResponse response = client.execute(request);
        StdSmsQueryStatusResponse stdSmsQueryStatusResponse = response.getSysStdResponse();
        return stdSmsQueryStatusResponse;
    }

    /**
     * @param stdSmsSendMsgRequest
     * @return 查询模板
     * @throws DefinedException
     */
    public StdSmsQueryTemplateResponse querySmsTemplate(StdSmsQueryTemplateRequest stdSmsSendMsgRequest) throws DefinedException {
        SmsClient client = getClient(stdSmsSendMsgRequest.getProjectId());
        SmsQueryTemplateRequest request = new SmsQueryTemplateRequest().transfer(stdSmsSendMsgRequest);
        SmsQueryTemplateResponse response = client.execute(request);
        StdSmsQueryTemplateResponse stdSmsQueryTemplateResponse = response.getSysStdResponse();
        return stdSmsQueryTemplateResponse;
    }




}
