package com.cw.core.vendor.sms;

import com.cw.core.orderhandler.VendorAdapter;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.sms.GsmsClient;
import com.cw.outsys.pojo.sms.request.gsms.GsmsSendMsgRequest;
import com.cw.outsys.pojo.sms.resonse.gsms.GsmsSendMsgResponse;
import com.cw.outsys.stdop.request.StdSmsSendMsgRequest;
import com.cw.outsys.stdop.response.StdSmsSendMsgResponse;
import com.cw.utils.enums.VendorType;

/**
 * @Describe 高斯通短信
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2024-11-15
 */
@VendorAdapter(vendorType = VendorType.GST_SMS)
public class GsmsVendor extends BaseOutSmsVendor<GsmsClient> {

    /**
     * @param stdSmsSendMsgRequest
     * @return 发送短信
     * @throws DefinedException
     */
    public StdSmsSendMsgResponse sendSmsMsg(StdSmsSendMsgRequest stdSmsSendMsgRequest) throws DefinedException {
        GsmsClient client = getClient(stdSmsSendMsgRequest.getProjectId());
        GsmsSendMsgRequest request = new GsmsSendMsgRequest().transfer(stdSmsSendMsgRequest);
        GsmsSendMsgResponse response = client.execute(request);
        StdSmsSendMsgResponse stdSmsSendMsgResponse = response.getSysStdResponse();
        return stdSmsSendMsgResponse;
    }

}
