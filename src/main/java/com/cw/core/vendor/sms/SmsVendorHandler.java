package com.cw.core.vendor.sms;

import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.stdop.request.StdSmsQueryStatusRequest;
import com.cw.outsys.stdop.request.StdSmsQueryTemplateRequest;
import com.cw.outsys.stdop.request.StdSmsSendMsgRequest;
import com.cw.outsys.stdop.response.StdSmsQueryStatusResponse;
import com.cw.outsys.stdop.response.StdSmsQueryTemplateResponse;
import com.cw.outsys.stdop.response.StdSmsSendMsgResponse;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2022-03-28
 */
public interface SmsVendorHandler {

    void initClient();

    void refreshClientConfig(Vendorconfig vendorconfig);

    default StdSmsSendMsgResponse sendSmsMsg(StdSmsSendMsgRequest stdSmsSendMsgRequest) throws DefinedException {
        return new StdSmsSendMsgResponse();
    }

    default StdSmsQueryStatusResponse querySmsStatus(StdSmsQueryStatusRequest stdSmsQueryStatusRequest) throws DefinedException {
        return new StdSmsQueryStatusResponse();
    }

    default StdSmsQueryTemplateResponse querySmsTemplate(StdSmsQueryTemplateRequest stdSmsSendMsgRequest) throws DefinedException {
        return new StdSmsQueryTemplateResponse();
    }
}
