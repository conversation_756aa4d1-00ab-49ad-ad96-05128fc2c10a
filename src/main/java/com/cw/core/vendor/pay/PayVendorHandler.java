package com.cw.core.vendor.pay;

import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.pay.PassOrderPayParams;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdPayQueryParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.pojo.dto.app.res.AppCommonPayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 声明厂商处理能力的接口
 * 实现类根据实例处理能力去调用相应的方法做实现
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:32
 **/
public interface PayVendorHandler {

    /**
     * 启动时初始化所有处理器
     */
    void initClient();

    /**
     * 刷新vendor 内的处理器
     *
     * @param vendorconfig
     */
    void refreshClientConfig(Vendorconfig vendorconfig);


    /**
     * 微信小程序支付
     *
     * @param stdPayParams
     * @param <T>
     * @return
     */
    default <T> T wxJsapiPay(StdPayParams stdPayParams) throws DefinedException {
        return null;
    }

    /**
     * 微信扫码支付
     *
     * @param stdPayParams
     * @param <T>
     * @return
     */
    default <T> T wxNativeQrcodePay(StdPayParams stdPayParams) throws DefinedException {
        return null;
    }

    /**
     * 微信扫码支付
     *
     * @param stdPayParams
     * @param <T>
     * @return
     */
    default <T> T wxH5Pay(StdPayParams stdPayParams) throws DefinedException {
        return null;
    }

    /**
     * 支付宝当面二维码支付
     *
     * @param stdPayParams
     * @param <T>
     * @return
     */
    default <T> T aliQrcodePay(StdPayParams stdPayParams) throws DefinedException {
        return null;
    }


    /**
     * 支付宝移动端WAP 支付
     *
     * @param stdPayParams
     * @param <T>
     * @return
     * @throws DefinedException
     */
    default <T> T aliWapPay(StdPayParams stdPayParams) throws DefinedException {
        return null;
    }


    /**
     * 支付宝跳转支付
     *
     * @param stdPayParams
     * @param <T>
     * @return
     */
    default void aliPcClientPay(StdPayParams stdPayParams, HttpServletResponse response) throws DefinedException {
        return;
    }


    /**
     * 支付宝当面二维码支付
     *
     * @param stdPayParams
     * @param <T>
     * @return
     */
    default <T> T unionQrcodePay(StdPayParams stdPayParams) throws DefinedException {
        return null;
    }


    /**
     * 退款
     *
     * @param stdRefundParams
     */
    default void refundPay(StdRefundParams stdRefundParams) throws DefinedException {
        return;
    }


    /**
     * 付款查询  返回1为支付成功
     *
     * @param stdPayQueryParams
     */
    default AppQueryPayRes queryPay(StdPayQueryParams stdPayQueryParams) throws DefinedException {
        return new AppQueryPayRes();
    }

    /**
     * 支付结果通知
     *
     * @param appid
     * @param body
     * @param request
     * @param <T>
     * @return
     */
    <T> T payCallBack(String appid, String body, HttpServletRequest request);

    /**
     * 退款结果通知
     *
     * @param appid
     * @param body
     * @param request
     * @param <T>
     * @return
     */
    <T> T refundCallBack(String appid, String body, HttpServletRequest request);

    default AppCommonPayRes anonymousPay(PassOrderPayParams payParams) throws DefinedException {
        return null;
    }

    /**
     * 线下订单退款
     *
     * @param stdRefundParams
     */
    default void refundAnonymousPay(StdRefundParams stdRefundParams) throws DefinedException {
        return;
    }


    <T> T payAnonymousCallBack(String appid, String body, HttpServletRequest request);

    <T> T refundAnonymousCallBack(String appid, String body, HttpServletRequest request);


}
