package com.cw.core.vendor.pay.wx;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.pay.PassByPayAttachInfo;
import com.cw.arithmetic.pay.PassByPayRefundAttachInfo;
import com.cw.arithmetic.pay.PayAttachInfo;
import com.cw.arithmetic.pay.RefundAttachInfo;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.config.exception.CustomException;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.platform.wechat.wxpay.WxNotifyResult;
import com.cw.core.vendor.pay.BaseOutPayVendor;
import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.pay.PassOrderPayParams;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdPayQueryParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.res.*;
import com.cw.utils.CalculateDate;
import com.cw.utils.CwUtils;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.datetime.DateStyle;
import com.cw.utils.enums.OnlinePayType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.pay.OnlinePayMethod;
import com.cw.utils.pay.PayUtil;
import com.cw.utils.pay.wx.WechatPayHttpHeaders;
import com.cw.utils.pay.wx.WxPayUtil;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayOrderQueryV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@Slf4j
@VendorAdapter(vendorType = VendorType.WX_PAY)
public class WxPayVendor extends BaseOutPayVendor {

    private static Map<String, WxPayService> payServices = Maps.newConcurrentMap(); //存放每个 projectid 的微信支付处理实例对象


    @Override
    public AppWxJsApiPayRes wxJsapiPay(StdPayParams stdPayParams) throws DefinedException {
        WxPayService wxPayService = payServices.get(stdPayParams.getProjectId());
        VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig appVendorConfig = null;
        if (stdPayParams.getPaymode() == OnlinePayMethod.WX_JSAPI) {
            appVendorConfig = configCache.getRecord(stdPayParams.getProjectId(), VendorType.WX_APP.name());
        } else {
            appVendorConfig = configCache.getRecord(stdPayParams.getProjectId(), VendorType.WX_MP.name());
        }
        if (appVendorConfig == null) {//获取配置
            throw new DefinedException("支付配置异常", SystemUtil.SystemerrorCode.ERR013_PAYFAIL);
        }

        WxPayUnifiedOrderV3Request v3PayRequest = new WxPayUnifiedOrderV3Request();
        v3PayRequest.setAppid(appVendorConfig.getAppid());//APPid
        v3PayRequest.setMchid(wxPayService.getConfig().getMchId());//直连商户号
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(PayUtil.Yuan2Fen(stdPayParams.getTotalPay().doubleValue()));//支付金额.对应分
        v3PayRequest.setAmount(amount);
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(stdPayParams.getPayerId());  //支付用户
        v3PayRequest.setPayer(payer);

        PayAttachInfo payAttachInfo = generatePayAttach(stdPayParams);
        RMapCache<String, PayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = payAttachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);
        String time = DateUtil.format(DateTime.of(stdPayParams.getExpireTime()), WxPayUtil.WXDATETIMEZONEFORMAT);  // getPayExpireTime(booking_rsList, projectid);
        v3PayRequest.setTimeExpire(time);
        v3PayRequest.setDescription(StrUtil.sub(stdPayParams.getOrderDesc(), 0, 36));//购买支付的商品描述 微信最多128位
        v3PayRequest.setOutTradeNo(stdPayParams.getOutTradeNo());
        v3PayRequest.setNotifyUrl(stdPayParams.getNotifyDomain() + "/pay/notify/jsapi/order/" + wxPayService.getConfig().getAppId()); //支付成功回调地址
        log.info("{}发起支付请求:{}", stdPayParams.getPaymode() == OnlinePayMethod.WX_JSAPI ? "小程序" : "公众号网页", JSON.toJSONString(v3PayRequest));
        WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = null;
        AppWxJsApiPayRes res = new AppWxJsApiPayRes();
        try {
            jsapiResult = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, v3PayRequest);
        } catch (WxPayException e) {
            e.printStackTrace();
            log.error("{}  请求支付错误  流水号:{}", stdPayParams.getOrderids().size() == 1 ? "单订单" : "购物车提交", stdPayParams.getOutTradeNo());
            throw new DefinedException(e.getMessage(), SystemUtil.SystemerrorCode.ERR013_PAYFAIL);
        }
        if (jsapiResult != null) {
            log.info("支付获取到微信返回:{}", jsapiResult);
            BeanUtil.copyProperties(jsapiResult, res);
        }
        return res;
    }

    @Override
    public AppQrcodePayRes wxNativeQrcodePay(StdPayParams stdPayParams) {
        WxPayService wxPayService = payServices.get(stdPayParams.getProjectId());

        WxPayUnifiedOrderV3Request v3PayRequest = new WxPayUnifiedOrderV3Request();
        v3PayRequest.setAppid(wxPayService.getConfig().getAppId());//APPid
        v3PayRequest.setMchid(wxPayService.getConfig().getMchId());//直连商户号
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(PayUtil.Yuan2Fen(stdPayParams.getTotalPay().doubleValue()));//支付金额.对应分
        v3PayRequest.setAmount(amount);

        PayAttachInfo payAttachInfo = generatePayAttach(stdPayParams);
        payAttachInfo.setLnotify(true);
        RMapCache<String, PayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = payAttachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);

//        v3PayRequest.setAttach(JSON.toJSONString(payAttachInfo));//自定义附加数据.会在查询以及通知回调中返回 可以返回要关联支付的订单号
        v3PayRequest.setDescription(StrUtil.sub(stdPayParams.getOrderDesc(), 0, 36));//购买支付的商品描述
        v3PayRequest.setOutTradeNo(stdPayParams.getOutTradeNo());//TODO 改成一个随机的流水号.同一个订单.重复调用会失败
        v3PayRequest.setNotifyUrl(stdPayParams.getNotifyDomain() + "/pay/notify/jsapi/order/" + wxPayService.getConfig().getAppId()); //支付成功回调地址

        AppQrcodePayRes res = new AppQrcodePayRes();
        try {
            String s = wxPayService.createOrderV3(TradeTypeEnum.NATIVE, v3PayRequest);

            QrConfig config = new QrConfig(280, 280);
            config.setRatio(5);
            String base64qr = QrCodeUtil.generateAsBase64(s, config,
                    ImgUtil.IMAGE_TYPE_PNG, PayUtil.getPayQrLogo(OnlinePayType.WX));
            res.setQrcode(base64qr);
//            String base64qr = QrCodeUtil.generateAsBase64(s, config, ImgUtil.IMAGE_TYPE_PNG);
//            res.setQrcode(base64qr);
        } catch (WxPayException e) {
            e.printStackTrace();
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("微信PC端扫码支付发起异常"));
        }
        return res;
    }


    @Override
    public AppH5payRes wxH5Pay(StdPayParams stdPayParams) throws DefinedException {
        WxPayService wxPayService = payServices.get(stdPayParams.getProjectId());

        WxPayUnifiedOrderV3Request v3PayRequest = new WxPayUnifiedOrderV3Request();
        v3PayRequest.setAppid(wxPayService.getConfig().getAppId());//APPid
        v3PayRequest.setMchid(wxPayService.getConfig().getMchId());//直连商户号
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(PayUtil.Yuan2Fen(stdPayParams.getTotalPay().doubleValue()));//支付金额.对应分
        v3PayRequest.setAmount(amount);

        PayAttachInfo payAttachInfo = generatePayAttach(stdPayParams);
        payAttachInfo.setLnotify(true);
        RMapCache<String, PayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = payAttachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);

//        v3PayRequest.setAttach(JSON.toJSONString(payAttachInfo));//自定义附加数据.会在查询以及通知回调中返回 可以返回要关联支付的订单号
        v3PayRequest.setDescription(StrUtil.sub(stdPayParams.getOrderDesc(), 0, 36));//购买支付的商品描述
        v3PayRequest.setOutTradeNo(stdPayParams.getOutTradeNo());//TODO 改成一个随机的流水号.同一个订单.重复调用会失败
        v3PayRequest.setNotifyUrl(stdPayParams.getNotifyDomain() + "/pay/notify/jsapi/order/" + wxPayService.getConfig().getAppId()); //支付成功回调地址

        WxPayUnifiedOrderV3Request.SceneInfo sceneInfo = new WxPayUnifiedOrderV3Request.SceneInfo();//H5支付信息
        sceneInfo.setPayerClientIp("*******");
        WxPayUnifiedOrderV3Request.H5Info h5Info = new WxPayUnifiedOrderV3Request.H5Info();
        h5Info.setType("Wap");
        sceneInfo.setH5Info(h5Info);
        v3PayRequest.setSceneInfo(sceneInfo);

        AppH5payRes res = new AppH5payRes();
        try {
            String s = wxPayService.createOrderV3(TradeTypeEnum.H5, v3PayRequest);

            if (StrUtil.isNotBlank(stdPayParams.getReturn_url())) {
                String signal = stdPayParams.getReturn_url().contains("?") ? "&" : "?";
                String params = signal + "payMode=" + stdPayParams.getPaymode() + "&outTradeNo=" + stdPayParams.getOutTradeNo();
                s = s + "&redirect_url=" + URLUtil.encodeAll(stdPayParams.getReturn_url() + params);
            }


            res.setPayform(s);
        } catch (WxPayException e) {
            e.printStackTrace();
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("微信H5支付发起异常"));
        }
        return res;
    }

    @Override
    public WxNotifyResult payCallBack(String appid, String body, HttpServletRequest request) {
        WxPayService wxPayService = payServices.get(getProjectIdByAppid(appid));
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {//解密 确认是否为支付成功
            WxPayOrderNotifyV3Result v3Result = wxPayService.parseOrderNotifyV3Result(body, signatureHeader);
            getCorePay().createPrepayWithNotify(OnlinePayType.WX, v3Result.getResult().getOutTradeNo(), v3Result.getResult().getTransactionId());
        } catch (WxPayException e) {
            log.error(appid + "支付验签失败" + e.getMessage());
//            e.printStackTrace();
            return WxNotifyResult.fail("支付验签失败");
        }
        return WxNotifyResult.ok();
    }

    @Override
    public void refundPay(StdRefundParams stdRefundParams) throws DefinedException {
        WxPayService wxPayService = payServices.get(stdRefundParams.getAttachProjectId());

        WxPayRefundV3Request refundV3Request = new WxPayRefundV3Request();
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        BigDecimal total = stdRefundParams.getOrgAmount(); //prepay.getOrgamount();  发起支付/购物车合并支付时的总金额
        BigDecimal refund = stdRefundParams.getRefund();// cancelData.getRefund();

        amount.setTotal(PayUtil.Yuan2Fen(total.doubleValue()));//原始支付总金额
        amount.setRefund(PayUtil.Yuan2Fen(refund.doubleValue()));//退款金额
        amount.setCurrency(PayUtil.DEFAULT_CURRENCY);
        refundV3Request.setAmount(amount);

        log.info("即将对订单做退款 原支付金额 {} 现在退款 {}", amount.getTotal(), amount.getRefund());


        refundV3Request.setTransactionId(stdRefundParams.getTransactionId());//原支付单号
        refundV3Request.setOutTradeNo(stdRefundParams.getOutTradeNo());//原商户订单号
        refundV3Request.setOutRefundNo(stdRefundParams.getOutRefundNo());//生成一个本地退款流水号
        refundV3Request.setNotifyUrl(stdRefundParams.getNotifyDomain() + "/pay/notify/jsapi/refund/" + wxPayService.getConfig().getAppId());//TODO  通知地址改下前缀

        RefundAttachInfo refundAttachInfo = generateRefundAttachInfo(stdRefundParams);
        RMapCache<String, RefundAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.REFUND_ATTACHINFO);
        String rskey = stdRefundParams.getOutRefundNo();//退款信息 用单个订单号作为 KEY
        attachInfoRMapCache.put(rskey, refundAttachInfo, 1, TimeUnit.DAYS);

        WxPayRefundV3Result v3Result;
        try {
            v3Result = wxPayService.refundV3(refundV3Request);
        } catch (WxPayException e) {
            e.printStackTrace();
            log.error("发起微信退款失败");
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("发起微信退款失败:" + e.getErrCodeDes()));
        }

    }


    @Override
    public AppCommonPayRes anonymousPay(PassOrderPayParams stdPayParams) throws DefinedException {
        AppCommonPayRes commonPayRes = new AppCommonPayRes();
        WxPayService wxPayService = payServices.get(stdPayParams.getProjectId());
        PassByPayAttachInfo payAttachInfo = generatePassPayAttach(stdPayParams);
        payAttachInfo.setPayment(OnlinePayType.WX.name());
        if (stdPayParams.getPaymode() == OnlinePayMethod.WX_JSAPI || stdPayParams.getPaymode() == OnlinePayMethod.WX_H5) {//公众号内支付线下订单

            WxPayUnifiedOrderV3Request v3PayRequest = new WxPayUnifiedOrderV3Request();
            WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
            amount.setTotal(PayUtil.Yuan2Fen(stdPayParams.getAmount().doubleValue()));//支付金额.对应分
            v3PayRequest.setAmount(amount);


            RMapCache<String, PassByPayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PASSBY_PAY_ATTACHINFO);
            String rskey = payAttachInfo.getOutTradeNo();
            attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);
            String time = DateUtil.format(DateTime.of(stdPayParams.getExpireTime()), WxPayUtil.WXDATETIMEZONEFORMAT);  // getPayExpireTime(booking_rsList, projectid);
            v3PayRequest.setTimeExpire(time);
            v3PayRequest.setDescription(StrUtil.sub(stdPayParams.getOrderDesc(), 0, 36));//购买支付的商品描述 微信最多128位
            v3PayRequest.setOutTradeNo(stdPayParams.getOutTradeNo());
            v3PayRequest.setNotifyUrl(stdPayParams.getNotifyDomain() + "/pay/notify/jsapi/passbyorder/" + wxPayService.getConfig().getAppId()); //支付成功回调地址

            if (stdPayParams.getPaymode() == OnlinePayMethod.WX_JSAPI) {
                VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
                Vendorconfig appVendorConfig = configCache.getRecord(stdPayParams.getProjectId(), VendorType.WX_MP.name());
                if (appVendorConfig == null) {//获取公众号配置
                    throw new DefinedException("支付配置异常", SystemUtil.SystemerrorCode.ERR013_PAYFAIL);
                }
                v3PayRequest.setAppid(appVendorConfig.getAppid());//APPid
                v3PayRequest.setMchid(wxPayService.getConfig().getMchId());//直连商户号
                WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
                payer.setOpenid(stdPayParams.getPayerId());  //支付用户
                v3PayRequest.setPayer(payer);


                WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = null;
                try {
                    jsapiResult = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, v3PayRequest);
                } catch (WxPayException e) {
                    e.printStackTrace();
                    throw new DefinedException(e.getMessage(), SystemUtil.SystemerrorCode.ERR013_PAYFAIL);
                }
                AppWxJsApiPayRes jsApiPayRes = new AppWxJsApiPayRes();
                if (jsapiResult != null) {
                    BeanUtil.copyProperties(jsapiResult, jsApiPayRes);
                    commonPayRes.setWxJsApiForm(jsApiPayRes);
                }
            }
            if (stdPayParams.getPaymode() == OnlinePayMethod.WX_H5) {
                WxPayUnifiedOrderV3Request.SceneInfo sceneInfo = new WxPayUnifiedOrderV3Request.SceneInfo();//H5支付信息
                sceneInfo.setPayerClientIp("*******");
                WxPayUnifiedOrderV3Request.H5Info h5Info = new WxPayUnifiedOrderV3Request.H5Info();
                h5Info.setType("Wap");
                sceneInfo.setH5Info(h5Info);
                v3PayRequest.setSceneInfo(sceneInfo);

                try {
                    String s = wxPayService.createOrderV3(TradeTypeEnum.H5, v3PayRequest);

                    if (StrUtil.isNotBlank(stdPayParams.getReturn_url())) {
                        String signal = stdPayParams.getReturn_url().contains("?") ? "&" : "?";
                        String params = signal + "payMode=" + stdPayParams.getPaymode() + "&outTradeNo=" + stdPayParams.getOutTradeNo();
//                        String params = "?payMode=1&outTradeNo=" + stdPayParams.getOutTradeNo();
                        s = s + "&redirect_url=" + URLUtil.encodeAll(stdPayParams.getReturn_url() + params);
                    }
                    commonPayRes.setWxwappayurl(s);
                } catch (WxPayException e) {
                    e.printStackTrace();
                    throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg("微信H5支付发起异常"));
                }
            }
        }


        return commonPayRes;
    }

    @Override
    public WxNotifyResult payAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        WxPayService wxPayService = payServices.get(getProjectIdByAppid(appid));
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {//解密 确认是否为支付成功
            WxPayOrderNotifyV3Result v3Result = wxPayService.parseOrderNotifyV3Result(body, signatureHeader);
            getCorePay().createPassByOrderWithNotify(OnlinePayType.WX, v3Result.getResult().getOutTradeNo(), v3Result.getResult().getTransactionId());
        } catch (WxPayException e) {
            log.error(appid + "支付验签失败" + e.getMessage());
//            e.printStackTrace();
            return WxNotifyResult.fail("支付验签失败");
        }
        return WxNotifyResult.ok();
    }

    @Override
    public void refundAnonymousPay(StdRefundParams stdRefundParams) throws DefinedException {
        WxPayService wxPayService = payServices.get(stdRefundParams.getAttachProjectId());

        WxPayRefundV3Request refundV3Request = new WxPayRefundV3Request();
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        BigDecimal total = stdRefundParams.getOrgAmount(); //prepay.getOrgamount();  发起支付/购物车合并支付时的总金额
        BigDecimal refund = stdRefundParams.getRefund();// cancelData.getRefund();

        amount.setTotal(PayUtil.Yuan2Fen(total.doubleValue()));//原始支付总金额
        amount.setRefund(PayUtil.Yuan2Fen(refund.doubleValue()));//退款金额
        amount.setCurrency(PayUtil.DEFAULT_CURRENCY);
        refundV3Request.setAmount(amount);

        log.info("即将对订单做退款 原支付金额 {} 现在退款 {}", amount.getTotal(), amount.getRefund());


        refundV3Request.setTransactionId(stdRefundParams.getTransactionId());//原支付单号
        refundV3Request.setOutTradeNo(stdRefundParams.getOutTradeNo());//原商户订单号
        refundV3Request.setOutRefundNo(stdRefundParams.getOutRefundNo());//生成一个本地退款流水号
        refundV3Request.setNotifyUrl(stdRefundParams.getNotifyDomain() + "/pay/notify/jsapi/passbyrefund/" + wxPayService.getConfig().getAppId());


        PassByPayRefundAttachInfo refundAttachInfo = generatePassByRefundAttachInfo(stdRefundParams);
        RMapCache<String, PassByPayRefundAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PASSBY_REFUND_ATTACHINFO);
        String rskey = stdRefundParams.getOutRefundNo();//退款信息 用单个订单号作为 KEY
        attachInfoRMapCache.put(rskey, refundAttachInfo, 1, TimeUnit.DAYS);

        WxPayRefundV3Result v3Result;
        try {
            v3Result = wxPayService.refundV3(refundV3Request);
        } catch (WxPayException e) {
            log.error("发起微信退款失败");
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("发起微信退款失败"));
        }
    }

    @Override
    public AppQueryPayRes queryPay(StdPayQueryParams stdPayQueryParams) throws DefinedException {
        WxPayService wxPayService = payServices.get(stdPayQueryParams.getProjectId());
        AppQueryPayRes res = new AppQueryPayRes();
        if (wxPayService == null) {
            return res;
        }
        WxPayOrderQueryV3Request queryV3Request = new WxPayOrderQueryV3Request();
        queryV3Request.setOutTradeNo(stdPayQueryParams.getOutTradeNo());
        queryV3Request.setMchid(wxPayService.getConfig().getMchId());//直连商户号

        try {
            WxPayOrderQueryV3Result v3Result = wxPayService.queryOrderV3(queryV3Request);
            String state = v3Result.getTradeState();
            res.setPayStatus(state.equals(CwUtils.SUCCESSSTR) ? 1 : 0);
            res.setTransId(v3Result.getTransactionId());
            res.setOutTradeNo(stdPayQueryParams.getOutTradeNo());
            res.setPaytime(CalculateDate.dateFormat(CalculateDate.stringToDate(v3Result.getSuccessTime(), DateStyle.YYYY_MM_DD_T_HH_MM_SS),
                    DateStyle.YYYY_MM_DD_HH_MM_SS.getValue()));
            return res;
        } catch (WxPayException e) {
            return res;
        }
    }

    @Override
    public WxNotifyResult refundAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        WxPayService wxPayService = payServices.get(getProjectIdByAppid(appid));
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {
            WxPayRefundNotifyV3Result v3Result = wxPayService.parseRefundNotifyV3Result(body, signatureHeader);
            getCorePay().writePassByRefundInfoWithNotify(OnlinePayType.WX, v3Result.getResult().getOutRefundNo());
        } catch (WxPayException e) {
            log.error(e.getMessage());
            return WxNotifyResult.fail("验签失败");
        }
        return WxNotifyResult.ok();
    }

    @Override
    public WxNotifyResult refundCallBack(String appid, String body, HttpServletRequest request) {
        WxPayService wxPayService = payServices.get(getProjectIdByAppid(appid));
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE));
        signatureHeader.setNonce(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE));
        signatureHeader.setTimeStamp(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP));
        signatureHeader.setSerial(request.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SERIAL));
        try {
            WxPayRefundNotifyV3Result v3Result = wxPayService.parseRefundNotifyV3Result(body, signatureHeader);
            getCorePay().writeRefundInfoWithNotify(OnlinePayType.WX, v3Result.getResult().getOutRefundNo());
        } catch (WxPayException e) {
            log.error(e.getMessage());
            return WxNotifyResult.fail("验签失败");
        }
        return WxNotifyResult.ok();
    }


    @Override
    protected void initConfigEvent(Vendorconfig vendorconfig) {
        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(generateNewPayConfig(vendorconfig));
        payServices.put(vendorconfig.getProjectid(), wxPayService);
    }

    @Override
    protected void updateConfigEvent(Vendorconfig vendorconfig) {
        if (!payServices.containsKey(vendorconfig.getProjectid())) {
            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(generateNewPayConfig(vendorconfig));
            payServices.put(vendorconfig.getProjectid(), wxPayService);
        } else {
            WxPayService wxPayService = payServices.get(vendorconfig.getAppid());
            wxPayService.setConfig(generateNewPayConfig(vendorconfig));
        }
    }

    @Override
    protected void removeConfigEvent(String projectId) {
        if (payServices.containsKey(projectId)) {
            payServices.remove(projectId);
        }
    }

    private WxPayConfig generateNewPayConfig(Vendorconfig payVendorConfig) {
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setAppId(payVendorConfig.getAppid());
        payConfig.setMchId(payVendorConfig.getUserid());
        payConfig.setMchKey(payVendorConfig.getUserpwd());//商户key 目前统一用v3 密钥就可以了 这个用处不大
        payConfig.setPrivateKeyPath(getPrivateKeyPath(payConfig.getAppId(), payVendorConfig.getPrivatekey()));//放回一个临时文件地址
        payConfig.setPrivateCertPath(getPrivateCertPath(payConfig.getAppId(), payVendorConfig.getCertkey()));//返回一个临时文件地址
        payConfig.setApiV3Key(payVendorConfig.getWxpayv3key()); //目前统一用v3 密钥就可以了
        return payConfig;
    }

    private String getPrivateKeyPath(String appid, String content) {
        String tmpDirsLocation = System.getProperty("java.io.tmpdir");
        File file = new File(StrUtil.format(tmpDirsLocation + "/{}key.pem", appid));
        file.deleteOnExit();
        FileUtil.touch(file);
        FileUtil.writeBytes(content.getBytes(), file);
        return file.getPath();
    }

    private String getPrivateCertPath(String appid, String content) {
        String tmpDirsLocation = System.getProperty("java.io.tmpdir");
        File file = new File(StrUtil.format(tmpDirsLocation + "/{}cert.pem", appid));
        file.deleteOnExit();
        FileUtil.touch(file);
        FileUtil.writeBytes(content.getBytes(), file);
        return file.getPath();
    }

}
