package com.cw.core.vendor.pay;

import cn.hutool.core.annotation.AnnotationUtil;
import com.cw.arithmetic.pay.PassByPayAttachInfo;
import com.cw.arithmetic.pay.PassByPayRefundAttachInfo;
import com.cw.arithmetic.pay.PayAttachInfo;
import com.cw.arithmetic.pay.RefundAttachInfo;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.core.CorePay;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.entity.Vendorconfig;
import com.cw.outsys.pay.PassOrderPayParams;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.datetime.DateStyle;
import com.google.common.collect.Maps;
import org.redisson.api.RedissonClient;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 所有 vendor 的景区 client 工具访问实例
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/25 16:58
 **/
public abstract class BaseOutPayVendor implements PayVendorHandler {

    protected Map<String, Vendorconfig> configMap = Maps.newConcurrentMap();
    protected Map<String, String> appid2ProjectIdMap = Maps.newConcurrentMap();

    @Override
    public void initClient() {
        VendorAdapter adapter = AnnotationUtil.getAnnotation(this.getClass(), VendorAdapter.class);
        if (adapter != null) {
            VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
            List<Vendorconfig> configs = configCache.getVendorTypeConfig(adapter.vendorType());
            if (configs.size() > 0) {
                for (Vendorconfig vendorconfig : configCache.getVendorTypeConfig(adapter.vendorType())) {
                    initConfig(vendorconfig);
                    LoggerFactory.getLogger(this.getClass()).info("{} {}初始化支付客户端完成", vendorconfig.getProjectid(), vendorconfig.getVtype());
                }
            }
        }
    }

    protected Vendorconfig getClient(String projectid) {
        if (!configMap.containsKey(projectid)) {
            throw new RuntimeException("没有找到" + projectid + "对应的 client");
        } else {
            return configMap.get(projectid);
        }
    }

    private void initConfig(Vendorconfig vendorconfig) {
        configMap.put(vendorconfig.getProjectid(), vendorconfig);
        appid2ProjectIdMap.put(vendorconfig.getAppid(), vendorconfig.getProjectid());
        initConfigEvent(vendorconfig);
    }

    @Override
    public void refreshClientConfig(Vendorconfig vendorconfig) {
        configMap.put(vendorconfig.getProjectid(), vendorconfig);
        appid2ProjectIdMap.put(vendorconfig.getAppid(), vendorconfig.getProjectid());
        updateConfigEvent(vendorconfig);
        LoggerFactory.getLogger(this.getClass()).info("{} 刷新{}支付客户端完成", vendorconfig.getProjectid(), vendorconfig.getVtype());
    }

    public void removeClient(String projectid) {
        removeConfigEvent(projectid);
        configMap.remove(projectid);

    }

    /**
     * 配置初始化事件,给子类根据需要重载
     */
    protected void initConfigEvent(Vendorconfig vendorconfig) {
    }


    /**
     * 配置更新事件,给子类根据需要重载
     */
    protected void updateConfigEvent(Vendorconfig vendorconfig) {

    }

    /**
     * 配置删除事件,给子类根据需要重载
     */
    protected void removeConfigEvent(String projectId) {

    }

    protected RedissonClient getRedisssonClient() {
        return SpringUtil.getBean(RedissonClient.class);
    }

    protected CorePay getCorePay() {
        return SpringUtil.getBean(CorePay.class);
    }

    protected PassByPayAttachInfo generatePassPayAttach(PassOrderPayParams passOrderPayParams) {
        PassByPayAttachInfo attachInfo = new PassByPayAttachInfo();
        attachInfo.setPaymode(passOrderPayParams.getPaymode());
        attachInfo.setProjectId(passOrderPayParams.getProjectId());
        attachInfo.setOutTradeNo(passOrderPayParams.getOutTradeNo());
        attachInfo.setOrderDesc(passOrderPayParams.getOrderDesc());

        attachInfo.setPayerId(passOrderPayParams.getPayerId());
        attachInfo.setBookingid(passOrderPayParams.getBookingid());
        attachInfo.setOutid(passOrderPayParams.getOutid());
        attachInfo.setMemo(passOrderPayParams.getMemo());
        attachInfo.setPtype(passOrderPayParams.getPtype());
        attachInfo.setTel(passOrderPayParams.getTel());
        attachInfo.setGuestname(passOrderPayParams.getGuestname());
//        attachInfo.setCreatedate(passOrderPayParams.getCreatedate());
        attachInfo.setAmount(passOrderPayParams.getAmount());
        attachInfo.setCreatedate(CalculateDate.dateFormat(new Date(), DateStyle.YYYY_MM_DD_HH_MM_SS.getValue()));

        attachInfo.setOnlinePayMethod(passOrderPayParams.getOnlinePayMethod());
        return attachInfo;
    }

    protected PassByPayRefundAttachInfo generatePassByRefundAttachInfo(StdRefundParams stdRefundParams) {
        PassByPayRefundAttachInfo refundAttachInfo = new PassByPayRefundAttachInfo();
        refundAttachInfo.setBookingid(stdRefundParams.getAttachBookingId());
        refundAttachInfo.setUid(stdRefundParams.getAttachUid());
        refundAttachInfo.setProejectid(stdRefundParams.getAttachProjectId());
        refundAttachInfo.setReqTime(LocalDateTime.now().toString());
        refundAttachInfo.setRefundAmount(stdRefundParams.getAttachRefundAmount());
        return refundAttachInfo;
    }

    protected PayAttachInfo generatePayAttach(StdPayParams stdPayParams) {
        PayAttachInfo payAttachInfo = new PayAttachInfo();
        payAttachInfo.setBookingids(stdPayParams.getOrderids()); //要批量创建预付款的订单号
        payAttachInfo.setProejectid(stdPayParams.getProjectId());
        payAttachInfo.setTotalPay(stdPayParams.getTotalPay());
        payAttachInfo.setOutTradeNo(stdPayParams.getOutTradeNo());
        payAttachInfo.setScene(stdPayParams.getPayscene());
        return payAttachInfo;
    }


    protected RefundAttachInfo generateRefundAttachInfo(StdRefundParams stdRefundParams) {
        RefundAttachInfo refundAttachInfo = new RefundAttachInfo();
        refundAttachInfo.setBookingid(stdRefundParams.getAttachBookingId());
        refundAttachInfo.setUid(stdRefundParams.getAttachUid());
        refundAttachInfo.setProejectid(stdRefundParams.getAttachProjectId());
        refundAttachInfo.setReqTime(LocalDateTime.now().toString());
        refundAttachInfo.setRefundAmount(stdRefundParams.getAttachRefundAmount());
        refundAttachInfo.setPayscene(stdRefundParams.getPayscene());
        return refundAttachInfo;
    }

    protected String getProjectIdByAppid(String appid) {
        return appid2ProjectIdMap.getOrDefault(appid, "");
    }

}
