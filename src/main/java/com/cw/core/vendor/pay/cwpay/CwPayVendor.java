package com.cw.core.vendor.pay.cwpay;

import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.vendor.pay.BaseOutPayVendor;
import com.cw.exception.DefinedException;
import com.cw.outsys.pay.PassOrderPayParams;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdPayQueryParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.pojo.dto.app.res.AppCommonPayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;
import com.cw.utils.enums.VendorType;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 城湾SDK 支付接口
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@VendorAdapter(vendorType = VendorType.CW_PAY)
public class CwPayVendor extends BaseOutPayVendor {

    @Override
    public <T> T wxJsapiPay(StdPayParams stdPayParams) throws DefinedException {
        return super.wxJsapiPay(stdPayParams);
    }

    @Override
    public <T> T wxNativeQrcodePay(StdPayParams stdPayParams) throws DefinedException {
        return super.wxNativeQrcodePay(stdPayParams);
    }

    @Override
    public <T> T wxH5Pay(StdPayParams stdPayParams) throws DefinedException {
        return super.wxH5Pay(stdPayParams);
    }

    @Override
    public <T> T aliQrcodePay(StdPayParams stdPayParams) throws DefinedException {
        return super.aliQrcodePay(stdPayParams);
    }

    @Override
    public <T> T aliWapPay(StdPayParams stdPayParams) throws DefinedException {
        return super.aliWapPay(stdPayParams);
    }

    @Override
    public void aliPcClientPay(StdPayParams stdPayParams, HttpServletResponse response) throws DefinedException {
        super.aliPcClientPay(stdPayParams, response);
    }


    @Override
    public void refundPay(StdRefundParams stdRefundParams) throws DefinedException {
        super.refundPay(stdRefundParams);
    }

    @Override
    public AppQueryPayRes queryPay(StdPayQueryParams stdPayQueryParams) throws DefinedException {
        return super.queryPay(stdPayQueryParams);
    }

    @Override
    public AppCommonPayRes anonymousPay(PassOrderPayParams payParams) throws DefinedException {
        return super.anonymousPay(payParams);
    }

    @Override
    public void refundAnonymousPay(StdRefundParams stdRefundParams) throws DefinedException {
        super.refundAnonymousPay(stdRefundParams);
    }

    @Override
    public <T> T refundCallBack(String appid, String body, HttpServletRequest request) {
        return null;
    }

    @Override
    public <T> T payAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        return null;
    }

    @Override
    public <T> T refundAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        return null;
    }

    @Override
    public <T> T payCallBack(String appid, String body, HttpServletRequest request) {
        return null;
    }
}
