package com.cw.core.vendor.pay.yl;

import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.vendor.pay.BaseOutPayVendor;
import com.cw.utils.enums.VendorType;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@VendorAdapter(vendorType = VendorType.YL_PAY)
public class YlPayVendor extends BaseOutPayVendor {


    @Override
    public <T> T refundCallBack(String appid, String body, HttpServletRequest request) {
        return null;
    }

    @Override
    public <T> T payAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        return null;
    }

    @Override
    public <T> T refundAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        return null;
    }

    @Override
    public <T> T payCallBack(String appid, String body, HttpServletRequest request) {
        return null;
    }
}
