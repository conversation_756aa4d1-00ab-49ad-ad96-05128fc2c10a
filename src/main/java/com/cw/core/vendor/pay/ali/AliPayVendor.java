package com.cw.core.vendor.pay.ali;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradePrecreateModel;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.cw.arithmetic.pay.PassByPayAttachInfo;
import com.cw.arithmetic.pay.PayAttachInfo;
import com.cw.arithmetic.pay.RefundAttachInfo;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.config.exception.CustomException;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.vendor.pay.BaseOutPayVendor;
import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.pay.PassOrderPayParams;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdPayQueryParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.outsys.pay.config.AliPayConfig;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.app.res.AppCommonPayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;
import com.cw.pojo.notify.ali.AliAntMsg;
import com.cw.pojo.notify.ali.AliAntRefundCompleteMsg;
import com.cw.utils.CalculateDate;
import com.cw.utils.CalculateNumber;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.cw.utils.datetime.DateStyle;
import com.cw.utils.enums.OnlinePayType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.pay.OnlinePayMethod;
import com.cw.utils.pay.PayUtil;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@Slf4j
@VendorAdapter(vendorType = VendorType.ALI_PAY)
public class AliPayVendor extends BaseOutPayVendor {


    private static Map<String, AlipayClient> clientMap = Maps.newConcurrentMap();//projectid 里对应的client

    public static AlipayClient getAlipayClient(String projectId) {
        return clientMap.get(projectId);
    }

    /**
     * 将request中的参数转换成Map
     *
     * @param request
     * @return
     */
    public static Map<String, String> convertRequestParamsToMap(HttpServletRequest request) {
        Map<String, String> retMap = new HashMap<String, String>();

        Set<Map.Entry<String, String[]>> entrySet = request.getParameterMap().entrySet();

        for (Map.Entry<String, String[]> entry : entrySet) {
            String name = entry.getKey();
            String[] values = entry.getValue();
            int valLen = values.length;

            if (valLen == 1) {
                retMap.put(name, values[0]);
            } else if (valLen > 1) {
                StringBuilder sb = new StringBuilder();
                for (String val : values) {
                    sb.append(",").append(val);
                }
                retMap.put(name, sb.toString().substring(1));
            } else {
                retMap.put(name, "");
            }
        }
        return retMap;
    }

    @Override
    public void aliPcClientPay(StdPayParams stdPayParams, HttpServletResponse response) throws DefinedException {

    }

    @Override
    public AppCommonPayRes anonymousPay(PassOrderPayParams payParams) throws DefinedException {
        AppCommonPayRes res = new AppCommonPayRes();

        PassByPayAttachInfo attachInfo = generatePassPayAttach(payParams);
        attachInfo.setPayment(OnlinePayType.TB.name());
        RMapCache<String, PassByPayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PASSBY_PAY_ATTACHINFO);
        String rskey = attachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, attachInfo, 1, TimeUnit.DAYS);

        AlipayClient alipayClient = clientMap.get(payParams.getProjectId());
        Vendorconfig vendorconfig = configMap.get(payParams.getProjectId());

        long expireMinute = Math.max(1, TimeUnit.MILLISECONDS.toMinutes(payParams.getExpireTime() - new Date().getTime()));//支付宝要求最小单位是分钟

        String subject = payParams.getOrderDesc();
        String totalAmount = payParams.getAmount().toString();
        String notifyUrl = payParams.getNotifyDomain() + "/pay/notify/alipay/passby/" + vendorconfig.getAppid();

        if (payParams.getPaymode() == OnlinePayMethod.ALI_QRCODE) {
            AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();
            model.setSubject(StrUtil.sub(subject, 0, 36));
            model.setTotalAmount(totalAmount);
            model.setTimeoutExpress(expireMinute + "m");  //支付宝的到期时间只能设置按请求发起时间
            model.setOutTradeNo(payParams.getOutTradeNo());
            AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
            request.setBizModel(model);
            request.setNotifyUrl(notifyUrl);
            try {
                AlipayTradePrecreateResponse response = alipayClient.execute(request);
                if (response.isSuccess()) {
                    QrConfig config = new QrConfig(280, 280);
                    config.setRatio(4);
                    String base64qr = QrCodeUtil.generateAsBase64(response.getQrCode(), config,
                            ImgUtil.IMAGE_TYPE_PNG, PayUtil.getPayQrLogo(OnlinePayType.TB));
                    res.setQrcode(base64qr);
                } else {
                    throw new DefinedException("获取二维码失败,请刷新后重新扫码", ResultCode.PAYFAIL.code());
                }
            } catch (AlipayApiException e) {
                throw new DefinedException(e);
            }
        }

        if (payParams.getPaymode() == OnlinePayMethod.ALI_WAPPAY) {
            VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
            Vendorconfig mh5config = configCache.getVendorConfig(payParams.getProjectId(), VendorType.M_H5);

            AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
            AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
            model.setSubject(StrUtil.sub(subject, 0, 36));
            model.setTotalAmount(totalAmount);
            model.setProductCode("QUICK_WAP_WAY");
            model.setTimeExpire(CalculateDate.dateFormat(new Date(payParams.getExpireTime()),
                    DateStyle.YYYY_MM_DD_HH_MM_SS.getValue()));
            model.setOutTradeNo(payParams.getOutTradeNo());

            if (mh5config != null) {
                String signal = payParams.getReturn_url().contains("?") ? "&" : "?";
                String params = signal + "payMode=" + payParams.getPaymode() + "&outTradeNo=" + payParams.getOutTradeNo();
                request.setReturnUrl(payParams.getReturn_url() + params);
                model.setQuitUrl(mh5config.getUrl());
            }
            request.setBizModel(model);
            request.setNotifyUrl(notifyUrl);

            try {
                String form = alipayClient.pageExecute(request).getBody();
                res.setAliwapform(form);

            } catch (AlipayApiException e) {
                e.printStackTrace();
                throw new DefinedException(e);
            }

        }
        return res;
    }

    @Override
    public String aliWapPay(StdPayParams stdPayParams) throws DefinedException {
        AlipayClient alipayClient = clientMap.get(stdPayParams.getProjectId());
        Vendorconfig vendorconfig = configMap.get(stdPayParams.getProjectId());
        VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        Vendorconfig mh5config = configCache.getVendorConfig(stdPayParams.getProjectId(), VendorType.M_H5);

        long expireMinute = Math.max(1, TimeUnit.MILLISECONDS.toMinutes(stdPayParams.getExpireTime() - new Date().getTime()));//支付宝要求最小单位是分钟

        String subject = stdPayParams.getOrderDesc();
        String totalAmount = stdPayParams.getTotalPay().setScale(2, RoundingMode.HALF_EVEN).toString();
        String notifyUrl = stdPayParams.getNotifyDomain() + "/pay/notify/alipay/order/" + vendorconfig.getAppid();

        AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();

        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
        model.setSubject(StrUtil.sub(subject, 0, 36));
        model.setTotalAmount(totalAmount);
        model.setProductCode("QUICK_WAP_WAY");
        model.setTimeExpire(CalculateDate.dateFormat(new Date(stdPayParams.getExpireTime()), DateStyle.YYYY_MM_DD_HH_MM_SS.getValue()));
        model.setOutTradeNo(stdPayParams.getOutTradeNo());

        if (mh5config != null && StrUtil.isNotBlank(mh5config.getNotifyurl())) {
            String signal = stdPayParams.getReturn_url().contains("?") ? "&" : "?";
            String params = signal + "payMode=" + stdPayParams.getPaymode() + "&outTradeNo=" + stdPayParams.getOutTradeNo();
            request.setReturnUrl(stdPayParams.getReturn_url() + params);
//            request.setReturnUrl(mh5config.getNotifyurl());
            model.setQuitUrl(mh5config.getNotifyurl());
        }
        request.setBizModel(model);
        request.setNotifyUrl(notifyUrl);


//        request.setBizContent("{" +
//                " \"out_trade_no\":\""+stdPayParams.getOutTradeNo()+"\"," +
//                " \"total_amount\":\"0.01\"," +
//                " \"subject\":\"Iphone6 16G\"," +
//                " \"product_code\":\"QUICK_WAP_WAY\"" +
//                " }");


        PayAttachInfo payAttachInfo = generatePayAttach(stdPayParams);
        RMapCache<String, PayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = payAttachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);

        try {
//            AlipayTradeWapPayResponse response = alipayClient.pageExecute(request,"get");
//            return response.getBody();
            String form = alipayClient.pageExecute(request).getBody();

            System.out.println("支付宝form\n" + form);
            return form;
        } catch (AlipayApiException e) {
            e.printStackTrace();
            throw new DefinedException(e);
        }
    }

    @Override
    public String aliQrcodePay(StdPayParams stdPayParams) throws DefinedException {

        AlipayClient alipayClient = clientMap.get(stdPayParams.getProjectId());
        Vendorconfig vendorconfig = configMap.get(stdPayParams.getProjectId());

        long expireMinute = Math.max(1, TimeUnit.MILLISECONDS.toMinutes(stdPayParams.getExpireTime() - new Date().getTime()));//支付宝要求最小单位是分钟

        String subject = stdPayParams.getOrderDesc();
        String totalAmount = stdPayParams.getTotalPay().toString();
        String notifyUrl = stdPayParams.getNotifyDomain() + "/pay/notify/alipay/order/" + vendorconfig.getAppid();

        log.info("当前剩余支付时间:" + expireMinute);

        AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();
        model.setSubject(StrUtil.sub(subject, 0, 36));
        model.setTotalAmount(totalAmount);
        model.setTimeoutExpress(expireMinute + "m");  //支付宝的到期时间只能设置按请求发起时间
        model.setOutTradeNo(stdPayParams.getOutTradeNo());

        AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
        request.setBizModel(model);
        request.setNotifyUrl(notifyUrl);

        PayAttachInfo payAttachInfo = generatePayAttach(stdPayParams);
        payAttachInfo.setLnotify(true);
        RMapCache<String, PayAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.PAY_ATTACHINFO);
        String rskey = payAttachInfo.getOutTradeNo();
        attachInfoRMapCache.put(rskey, payAttachInfo, 1, TimeUnit.DAYS);

        try {
            AlipayTradePrecreateResponse response = alipayClient.execute(request);

            if (response.isSuccess()) {
                QrConfig config = new QrConfig(280, 280);
                config.setRatio(4);

                String base64qr = QrCodeUtil.generateAsBase64(response.getQrCode(), config,
                        ImgUtil.IMAGE_TYPE_PNG, PayUtil.getPayQrLogo(OnlinePayType.TB));
                return base64qr;
            } else {
                if (response.getSubCode().equals("ACQ.APPLY_PC_MERCHANT_CODE_ERROR")) {//连续请求二维码次数过多.需要更换支付单号
                    if (stdPayParams.getOrderids().size() == 1) {
                        RMapCache<String, String> rMapCache = getRedisssonClient().getMapCache(RedisKey.SeqNo_Map);
                        String cacheKey = SystemUtil.SequenceKey.PREPAY + stdPayParams.getOrderids().get(0) + OnlinePayMethod.ALI_QRCODE;
                        String content = rMapCache.get(cacheKey);
                        if (content != null && stdPayParams.getOutTradeNo().equals(content)) {//将重复等幂的CACHEKEY删除.重新扫码生成
                            rMapCache.remove(cacheKey);
                        }
                    }
                }
                throw new DefinedException("获取二维码失败,请刷新后重新扫码", ResultCode.PAYFAIL.code());
            }

        } catch (AlipayApiException e) {
            throw new DefinedException(e);
        }

    }


    @Override
    public AppQueryPayRes queryPay(StdPayQueryParams stdPayQueryParams) throws DefinedException {
        AlipayClient alipayClient = clientMap.get(stdPayQueryParams.getProjectId());
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        AlipayTradeQueryModel model = new AlipayTradeQueryModel();
        model.setOutTradeNo(stdPayQueryParams.getOutTradeNo());
        request.setBizModel(model);
        AppQueryPayRes res = new AppQueryPayRes();
        try {
            AlipayTradeQueryResponse aliPayResponse = alipayClient.execute(request);
            if (aliPayResponse.isSuccess()) {
                if (aliPayResponse.getTradeStatus().equals(AliPayTradeStatus.TRADE_SUCCESS)) {
                    res.setPayStatus(1);
                    res.setOutTradeNo(stdPayQueryParams.getOutTradeNo());
                    res.setTransId(aliPayResponse.getTradeNo());
                    res.setPaytime(CalculateDate.dateFormat(aliPayResponse.getSendPayDate(), DateStyle.YYYY_MM_DD_HH_MM_SS.getValue()));
                    return res;
                }
            }
        } catch (AlipayApiException e) {
            return new AppQueryPayRes();
        }
        return new AppQueryPayRes();
    }

    @Override
    public String refundCallBack(String appid, String body, HttpServletRequest request) {
        // 将异步通知中收到的待验证所有参数都存放到map中
        Map<String, String> params = convertRequestParamsToMap(request);
        String paramsJson = JSON.toJSONString(params);
        log.info("支付宝退款回调，{}", paramsJson);

        try {
            // 调用SDK验证签名
            Vendorconfig vendorconfig = configMap.get(getProjectIdByAppid(appid));

            boolean signVerified = AlipaySignature.rsaCheckV1(params, vendorconfig.getCertkey(), AliPayConfig.charSet, AliPayConfig.signType);
            if (signVerified) {

                AliAntMsg aliNotifyParams = JSON.parseObject(paramsJson, AliAntMsg.class);

                log.info("支付宝银行卡退款回调成功");  //开始处理业务逻辑

                AliAntRefundCompleteMsg aliAntRefundCompleteMsg =
                        JSON.parseObject(aliNotifyParams.getBiz_content(), AliAntRefundCompleteMsg.class);

                getCorePay().writeRefundInfoWithNotify(OnlinePayType.TB, aliAntRefundCompleteMsg.getOut_request_no());
                return "success";
            } else {
                log.info("支付宝回调签名认证失败，signVerified=false, paramsJson:{}", paramsJson);
                return "failure";
            }
        } catch (Exception e) {
            log.error("支付宝回调签名认证失败,paramsJson:{},errorMsg:{}", paramsJson, e.getMessage());
            return "failure";
        }
    }

    @Override
    public void refundPay(StdRefundParams stdRefundParams) throws DefinedException {
        AlipayClient alipayClient = clientMap.get(stdRefundParams.getAttachProjectId());
        Vendorconfig vendorconfig = configMap.get(stdRefundParams.getAttachProjectId());

        try {
            AlipayTradeRefundModel model = new AlipayTradeRefundModel();
            model.setOutTradeNo(stdRefundParams.getOutTradeNo());
            model.setOutRequestNo(stdRefundParams.getOutRefundNo());//退款流水号
            model.setRefundAmount(stdRefundParams.getRefund().toString());
            model.setRefundReason("退款");

            RefundAttachInfo refundAttachInfo = generateRefundAttachInfo(stdRefundParams);
            RMapCache<String, RefundAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.REFUND_ATTACHINFO);
            String rskey = stdRefundParams.getOutRefundNo();//退款信息 用单个订单号作为 KEY
            attachInfoRMapCache.put(rskey, refundAttachInfo, 1, TimeUnit.DAYS);

            String notifyUrl = stdRefundParams.getNotifyDomain() + "/pay/notify/alipay/refund/" + vendorconfig.getAppid();

            AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
            request.setBizModel(model);
            request.setNotifyUrl(notifyUrl);

            AlipayTradeRefundResponse response = alipayClient.execute(request);
            if (response.isSuccess()) {
                if (response.getFundChange().equals("Y")) { //余额支付.即时到账
                    getCorePay().writeRefundInfoWithNotify(OnlinePayType.TB, model.getOutRequestNo());
                    log.info("支付宝退款到账成功:-->" + JSON.toJSONString(response));
                } else {  //等待银行卡退款通知
                    log.info("支付宝退款到银行卡:等待到账通知-->" + JSON.toJSONString(response));
                }
            } else {
                throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg(response.getSubMsg()));
            }
        } catch (AlipayApiException e) {
            e.printStackTrace();
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("发起支付宝退款失败"));
        }
    }

    @Override
    public void refundAnonymousPay(StdRefundParams stdRefundParams) throws DefinedException {
        AlipayClient alipayClient = clientMap.get(stdRefundParams.getAttachProjectId());
        Vendorconfig vendorconfig = configMap.get(stdRefundParams.getAttachProjectId());

        try {
            AlipayTradeRefundModel model = new AlipayTradeRefundModel();
            model.setOutTradeNo(stdRefundParams.getOutTradeNo());
            model.setOutRequestNo(stdRefundParams.getOutRefundNo());//退款流水号
            model.setRefundAmount(stdRefundParams.getRefund().toString());
            model.setRefundReason("退款");

            RefundAttachInfo refundAttachInfo = generateRefundAttachInfo(stdRefundParams);
            RMapCache<String, RefundAttachInfo> attachInfoRMapCache = getRedisssonClient().getMapCache(RedisKey.REFUND_ATTACHINFO);
            String rskey = stdRefundParams.getOutRefundNo();//退款信息 用单个订单号作为 KEY
            attachInfoRMapCache.put(rskey, refundAttachInfo, 1, TimeUnit.DAYS);

            String notifyUrl = stdRefundParams.getNotifyDomain() + "/pay/notify/alipay/passbyrefund/" + vendorconfig.getAppid();

            AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
            request.setBizModel(model);
            request.setNotifyUrl(notifyUrl);

            AlipayTradeRefundResponse response = alipayClient.execute(request);
            if (response.isSuccess()) {
                if (response.getFundChange().equals("Y")) { //余额支付.即时到账
                    getCorePay().writePassByRefundInfoWithNotify(OnlinePayType.TB, model.getOutRequestNo());
                    log.info("支付宝退款到账成功:-->" + JSON.toJSONString(response));
                } else {  //等待银行卡退款通知
                    log.info("支付宝退款到银行卡:等待到账通知-->" + JSON.toJSONString(response));
                }
            }
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
    }

    @Override
    public String payAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        // 将异步通知中收到的待验证所有参数都存放到map中
        Map<String, String> params = convertRequestParamsToMap(request);
        String paramsJson = JSON.toJSONString(params);
        log.info("支付宝线下订单退款成功回调，{}", paramsJson);

        try {
            // 调用SDK验证签名
            Vendorconfig vendorconfig = configMap.get(getProjectIdByAppid(appid));

            boolean signVerified = AlipaySignature.rsaCheckV1(params, vendorconfig.getCertkey(), AliPayConfig.charSet, AliPayConfig.signType);
//                    rsaCertCheckV1(params, aliPayConfig.getAlipay_cert_path(),
//                    aliPayConfig.getGetCharset(), aliPayConfig.getGetSignType());
            if (signVerified) {

                AliNotifyParams aliNotifyParams = JSON.parseObject(paramsJson, AliNotifyParams.class);
//                log.info("支付宝回调线下订单支付成功");  //开始处理业务逻辑
                String out_trade_no = aliNotifyParams.getOut_trade_no();// params.get("out_trade_no");
                String transaction_id = aliNotifyParams.getTrade_no();// params.get("trade_no");
                String trade_status = aliNotifyParams.getTrade_status();// params.get("trade_status");
                BigDecimal refundFee = aliNotifyParams.getRefund_fee();    // NumberUtil.parseDouble(params.getOrDefault("refund_fee","0.00")) ;

                if (trade_status.equals(AliPayTradeStatus.TRADE_SUCCESS) && CalculateNumber.isZero(refundFee)) {//
                    getCorePay().createPassByOrderWithNotify(OnlinePayType.TB, out_trade_no, transaction_id);
                }
                return "success";
            } else {
                log.info("支付宝回调签名认证失败，signVerified=false, paramsJson:{}", paramsJson);
                return "failure";
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("支付宝回调签名认证失败,paramsJson:{},errorMsg:{}", paramsJson, e.getMessage());
            return "failure";
        }
    }

    @Override
    public String refundAnonymousCallBack(String appid, String body, HttpServletRequest request) {
        // 将异步通知中收到的待验证所有参数都存放到map中
        Map<String, String> params = convertRequestParamsToMap(request);
        String paramsJson = JSON.toJSONString(params);
        log.info("支付宝退款回调，{}", paramsJson);

        try {
            // 调用SDK验证签名
            Vendorconfig vendorconfig = configMap.get(getProjectIdByAppid(appid));

            boolean signVerified = AlipaySignature.rsaCheckV1(params, vendorconfig.getCertkey(), AliPayConfig.charSet, AliPayConfig.signType);
            if (signVerified) {

                AliAntMsg aliNotifyParams = JSON.parseObject(paramsJson, AliAntMsg.class);

                log.info("支付宝银行卡退款回调成功");  //开始处理业务逻辑

                AliAntRefundCompleteMsg aliAntRefundCompleteMsg =
                        JSON.parseObject(aliNotifyParams.getBiz_content(), AliAntRefundCompleteMsg.class);

                //getCorePay().writeRefundInfoWithNotify(OnlinePayType.TB, aliAntRefundCompleteMsg.getOut_request_no());
                getCorePay().writePassByRefundInfoWithNotify(OnlinePayType.WX, aliAntRefundCompleteMsg.getOut_request_no());

                return "success";
            } else {
                log.info("支付宝回调签名认证失败，signVerified=false, paramsJson:{}", paramsJson);
                return "failure";
            }
        } catch (Exception e) {
            log.error("支付宝回调签名认证失败,paramsJson:{},errorMsg:{}", paramsJson, e.getMessage());
            return "failure";
        }
    }

    @Override
    public String payCallBack(String appid, String body, HttpServletRequest request) {
        // 将异步通知中收到的待验证所有参数都存放到map中
        Map<String, String> params = convertRequestParamsToMap(request);
        String paramsJson = JSON.toJSONString(params);
        log.info("支付宝支付成功回调，{}", paramsJson);

        try {
            // 调用SDK验证签名
            Vendorconfig vendorconfig = configMap.get(getProjectIdByAppid(appid));

            boolean signVerified = AlipaySignature.rsaCheckV1(params, vendorconfig.getCertkey(), AliPayConfig.charSet, AliPayConfig.signType);
//                    rsaCertCheckV1(params, aliPayConfig.getAlipay_cert_path(),
//                    aliPayConfig.getGetCharset(), aliPayConfig.getGetSignType());
            if (signVerified) {

                AliNotifyParams aliNotifyParams = JSON.parseObject(paramsJson, AliNotifyParams.class);

                log.info("支付宝回调成功");  //开始处理业务逻辑
                String out_trade_no = aliNotifyParams.getOut_trade_no();// params.get("out_trade_no");
                String transaction_id = aliNotifyParams.getTrade_no();// params.get("trade_no");
                String trade_status = aliNotifyParams.getTrade_status();// params.get("trade_status");
                BigDecimal refundFee = aliNotifyParams.getRefund_fee();    // NumberUtil.parseDouble(params.getOrDefault("refund_fee","0.00")) ;

                if (trade_status.equals(AliPayTradeStatus.TRADE_SUCCESS) && CalculateNumber.isZero(refundFee)) {//
                    getCorePay().createPrepayWithNotify(OnlinePayType.TB,
                            out_trade_no, transaction_id);
                }
                return "success";
            } else {
                log.info("支付宝回调签名认证失败，signVerified=false, paramsJson:{}", paramsJson);
                return "failure";
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("支付宝回调签名认证失败,paramsJson:{},errorMsg:{}", paramsJson, e.getMessage());
            return "failure";
        }

    }

    @Override
    protected void initConfigEvent(Vendorconfig vendorconfig) {
//        AliPayConfig apiPayConfig = generateNewPayConfig(vendorconfig);
//        clientMap.put(vendorconfig.getProjectid(), apiPayConfig);
        updateConfigEvent(vendorconfig);

//        LoggerFactory.getLogger(this.getClass()).info("支付宝支付证书地址: {} {} {}", apiPayConfig.getAppCertPath(), apiPayConfig.getAliPayPublicCertPath(), apiPayConfig.getAliPayRootCertPath());
    }

    @Override
    protected void updateConfigEvent(Vendorconfig vendorconfig) {
//        AliPayConfig apiPayConfig = generateNewPayConfig(vendorconfig);
//        clientMap.put(vendorconfig.getProjectid(), apiPayConfig);
//        AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do","app_id","your private_key","json","GBK","alipay_public_key","RSA2");
        AlipayClient alipayClient = new DefaultAlipayClient(AliPayConfig.serverUrl, vendorconfig.getAppid(),
                vendorconfig.getPrivatekey(), "json", "UTF-8", vendorconfig.getCertkey(), AliPayConfig.signType);
        clientMap.put(vendorconfig.getProjectid(), alipayClient);

//        log.info("支付宝私钥:" + vendorconfig.getPrivatekey());
//        log.info("支付宝证书:" + vendorconfig.getCertkey());

    }

    @Override
    protected void removeConfigEvent(String projectId) {
        clientMap.remove(projectId);
    }


    //支付宝根证书
    private String getAliRootCertPath(String appid, String content) {
        String tmpDirsLocation = System.getProperty("java.io.tmpdir");
        File file = new File(StrUtil.format(tmpDirsLocation + "/{}alirootcert.crt", appid));
        file.deleteOnExit();
        FileUtil.touch(file);
        FileUtil.writeBytes(content.getBytes(), file);
        return file.getPath();
    }

    //支付宝公钥
    private String getAlipayCertPubKeyPath(String appid, String content) {
        String tmpDirsLocation = System.getProperty("java.io.tmpdir");
        File file = new File(StrUtil.format(tmpDirsLocation + "/{}alipaypubcert.crt", appid));
        file.deleteOnExit();
        FileUtil.touch(file);
        FileUtil.writeBytes(content.getBytes(), file);
        return file.getPath();
    }


    //APP 私钥
    private String getAppCertPath(String appid, String content) {
        String tmpDirsLocation = System.getProperty("java.io.tmpdir");
        File file = new File(StrUtil.format(tmpDirsLocation + "/{}apppubcert.crt", appid));
        file.deleteOnExit();
        FileUtil.touch(file);
        FileUtil.writeBytes(content.getBytes(), file);
        return file.getPath();
    }

    private AliPayConfig generateNewPayConfig(Vendorconfig payVendorConfig) {
        String appid = payVendorConfig.getAppid();
        AliPayConfig payConfig = new AliPayConfig();
        payConfig.setAppId(payVendorConfig.getAppid());
        payConfig.setPrivateKey(payVendorConfig.getPrivatekey());
        payConfig.setAliPayPublicCertContent(payVendorConfig.getCertkey()); //公钥模式.对应支付宝公钥

        //目前不区分是否证书模式.
//        payConfig.setAppCertPath(getAppCertPath(appid, payVendorConfig.getApprivatekey()));
//        payConfig.setAliPayPublicCertPath(getAlipayCertPubKeyPath(appid, payVendorConfig.getCertkey()));
//        payConfig.setAliPayRootCertPath(getAliRootCertPath(appid, payVendorConfig.getAprootcertkey()));
        return payConfig;
    }


    @Data
    private static class AliNotifyParams {
        LocalDateTime notify_time;
        String notify_type;
        String notify_id;
        String sign_type;
        String sign;

        String trade_no;
        String app_id;
        String out_trade_no;
        String out_biz_no;
        String buyer_id;
        String buyer_logon_id;
        String seller_id;
        String seller_email;

        String trade_status;//交易状态

        BigDecimal total_amount;
        BigDecimal receipt_amount;
        BigDecimal invoice_amount;
        BigDecimal buyer_pay_amount;
        BigDecimal point_amount;
        BigDecimal refund_fee;
        BigDecimal send_back_fee;

        String subject;
        LocalDateTime gmt_create;
        LocalDateTime gmt_payment;
        LocalDateTime gmt_refund;
        LocalDateTime gmt_close;

        String fund_bill_list;
        String passback_params;
        String voucher_detail_list;

    }


    private static class AliPayTradeStatus {
        public static final String TRADE_SUCCESS = "TRADE_SUCCESS";
        public static final String TRADE_FINISHED = "TRADE_FINISHED";
        public static final String WAIT_BUYER_PAY = "WAIT_BUYER_PAY";
        public static final String TRADE_CLOSED = "TRADE_CLOSED";
        public static final String TRADE_PENDING = "TRADE_PENDING";
        public static final String TRADE_REFUNDING = "TRADE_REFUNDING";
        public static final String TRADE_REFUND_SUCCESS = "TRADE_REFUND_SUCCESS";
        public static final String TRADE_REFUND_CLOSED = "TRADE_REFUND_CLOSED";
        public static final String TRADE_REFUND_FINISHED = "TRADE_REFUND_FINISHED";
    }


}
