package com.cw.core.vendor.parking;

import com.cw.core.orderhandler.VendorAdapter;
import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.parking.DsParkingClient;
import com.cw.outsys.pojo.parking.request.DsParkingQueryRequest;
import com.cw.outsys.pojo.parking.response.DsParkingQueryResponse;
import com.cw.outsys.stdop.request.StdQueryParkingStatusRequest;
import com.cw.outsys.stdop.response.StdParkingStatusResponse;
import com.cw.utils.enums.VendorType;

/**
 * @Describe 数景停车场接口
 * <AUTHOR> Just
 * @Create on 2024-11-15
 */
@VendorAdapter(vendorType = VendorType.DSPARKING)
public class DsParkingVendor extends BaseOutParkingVendor<DsParkingClient> {


    @Override
    public StdParkingStatusResponse queryParkingStatus(StdQueryParkingStatusRequest request) {
        DsParkingClient client = getClient(request.getProjectId());
        Vendorconfig vendorconfig = client.getConfig();

        DsParkingQueryRequest queryRequest = new DsParkingQueryRequest().transfer(request);
        DsParkingQueryResponse response = null;
        try {
            response = client.execute(queryRequest);
        } catch (DefinedException e) {
            //throw new RuntimeException(e);
            throw new RuntimeException(e.getMessage());
        }
        StdParkingStatusResponse stdParkingStatusResponse = response.getSysStdResponse();

        return stdParkingStatusResponse;
    }


}
