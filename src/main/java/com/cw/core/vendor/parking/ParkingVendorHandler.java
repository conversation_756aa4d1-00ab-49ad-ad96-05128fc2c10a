package com.cw.core.vendor.parking;

import com.cw.entity.Vendorconfig;
import com.cw.outsys.stdop.request.StdQueryParkingStatusRequest;
import com.cw.outsys.stdop.response.StdParkingStatusResponse;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-28
 */
public interface ParkingVendorHandler {

    void initClient();

    void refreshClientConfig(Vendorconfig vendorconfig);

    default StdParkingStatusResponse queryParkingStatus(StdQueryParkingStatusRequest request) {
        return new StdParkingStatusResponse();
    }


}
