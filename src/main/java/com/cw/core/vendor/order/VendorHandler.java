package com.cw.core.vendor.order;

import com.cw.core.func.order.StdOrderData;
import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import org.slf4j.LoggerFactory;

/**
 * 声明厂商处理能力的接口
 * 实现类根据实例处理能力去调用相应的方法做实现
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/5/14 15:32
 **/
public interface VendorHandler {

    void initClient();

    void refreshClientConfig(Vendorconfig vendorconfig);


    /**
     * 创建订单
     *
     * @param structure
     * @return
     * @throws DefinedException
     */
    default StdOrderResponse createOrder(StdOrderData structure) throws DefinedException {
        LoggerFactory.getLogger(this.getClass()).info("适配器创建订单");
        return new StdOrderResponse();
    }

    /**
     * 取消订单
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    default Object cancelOrder(StdCancelOrderRequest request) throws DefinedException {
        LoggerFactory.getLogger(this.getClass()).info("适配器取消订单");
        StdCancelOrderResponse response = new StdCancelOrderResponse();
        return response;
    }

    /**
     * 对订单做付款
     *
     * @param
     * @return
     * @throws DefinedException
     */
    default StdPayResponse payOrder(StdPayRequest stdPayRequest) throws DefinedException {
        LoggerFactory.getLogger(this.getClass()).info("适配器订单支付");
        StdPayResponse response = new StdPayResponse();
        return response;
    }

    /**
     * 查询系统中套餐小类
     *
     * @param request
     * @return
     */
    default StdKitListQueryResponse queryKitList(StdKitListQueryRequest request) throws DefinedException {
        return new StdKitListQueryResponse();
    }

    /**
     * 同步系统中套餐价格
     *
     * @param request
     * @return
     */
    default StdKitPriceQueryResponse queryKitPriceList(StdKitPriceQueryRequest request) throws DefinedException {
        return new StdKitPriceQueryResponse();
    }

    /**
     * 同步系统中套餐价格
     *
     * @param request
     * @return
     */
    default StdKitAvailableQueryResponse queryKitAvailableList(StdKitAvailableQueryRequest request) throws DefinedException {
        return new StdKitAvailableQueryResponse();
    }

    /**
     * 同步所有酒店
     *
     * @param request
     * @return
     */
    default StdHotelListResponse queryHotelList(StdHotelListRequest request) throws DefinedException {
        return new StdHotelListResponse();
    }

    /**
     * 同步所有餐厅
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    default StdRestaurantQueryResponse queryRestaurantList(StdRestaurantQueryRequest request) throws DefinedException {
        return new StdRestaurantQueryResponse();
    }

    /**
     * 同步订单状态
     *
     * @param request
     * @return
     */
    default StdOrderStatusQueryResponse queryOrderStatus(StdOrderStatusQueryRequest request) throws DefinedException {
        return new StdOrderStatusQueryResponse();
    }

    /**
     * 查询价格码列表
     *
     * @param request
     * @return
     */
    default StdRateCodeListQueryResponse queryRateCodeList(StdRateCodeListQueryRequest request) throws DefinedException {
        return new StdRateCodeListQueryResponse();
    }

    /**
     * 查询客房可用资源数
     *
     * @param request 请求参数
     * @return
     */
    default StdRoomAvlResponse queryRoomAvl(StdRoomAvlRequest request) throws DefinedException {
        return new StdRoomAvlResponse();
    }

    default StdRoomDetailQueryResponse queryRoomDetail(StdRoomDetailQueryRequest request) throws DefinedException {
        return new StdRoomDetailQueryResponse();
    }

    default StdRoomPriceResponse queryRoomPrice(StdRoomPriceRequest request) throws DefinedException {
        return new StdRoomPriceResponse();
    }

    default StdRoomtypeListQueryResponse queryRoomtypeList(StdRoomtypeListQueryRequest request) throws DefinedException {
        return new StdRoomtypeListQueryResponse();
    }

    default StdTicketDetailQueryResponse queryTicketDetail(StdTicketDetailQueryRequest request) throws DefinedException {
        return new StdTicketDetailQueryResponse();
    }

    default StdTicketListQueryResponse queryTicketList(StdTicketListQueryRequest request) throws DefinedException {
        return new StdTicketListQueryResponse();
    }

    default StdTicketQrCodeUrlResponse queryTicketQrCodeUrl(StdTicketQrCodeUrlRequest request) throws DefinedException {
        return new StdTicketQrCodeUrlResponse();
    }

    default StdTicketQueryQrPicResponse queryTicketQrPic(StdTicketQueryQrPicRequest request) throws DefinedException {
        return new StdTicketQueryQrPicResponse();
    }

    default StdTicketQueryResponse queryTicket(StdTicketQueryRequest request) throws DefinedException {
        return new StdTicketQueryResponse();
    }

    default StdTicketQueryStatusResponse queryTicketStatus(StdTicketQueryStatusRequest request) throws DefinedException {
        return new StdTicketQueryStatusResponse();
    }

    default StdTicketSendMsgResponse sendTicketMsg(StdTicketSendMsgRequest request) throws DefinedException {
        return new StdTicketSendMsgResponse();
    }

    default StdTicketChangeDateResponse changeTicketDate(StdTicketChangeDateRequest request) throws DefinedException {
        return new StdTicketChangeDateResponse();
    }

    default StdTicketChangeNumResponse changeTicketRoom(StdTicketChangeNumRequest request) throws DefinedException {
        return new StdTicketChangeNumResponse();
    }

    default StdTicketQueryCancelStatusResponse queryTicketCacnelStatus(StdTicketQueryCanelStatusRequest request) throws DefinedException {
        return new StdTicketQueryCancelStatusResponse();
    }

    default StdTicketReturnNumResponse returnTicketNum(StdTicketReturnNumRequest request) throws DefinedException {
        return new StdTicketReturnNumResponse();
    }


}
