package com.cw.core.vendor.order.ticket;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.cw.config.exception.CustomException;
import com.cw.core.LocalTicketService;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.orderhandler.pojo.StdIdResult;
import com.cw.core.vendor.order.BaseOutSysVendor;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.ticket.LocalTicketClient;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@VendorAdapter(vendorType = VendorType.LOCAL_TICKET)
public class LocalTicketVendor extends BaseOutSysVendor<LocalTicketClient> {

    private LocalTicketService getLocalTicketService() {
        return SpringUtil.getBean(LocalTicketService.class);
    }

    //企业码-->appid
    //用户名--->userid
    //用户密码 -->userpwd

    @Override
    public StdOrderResponse createOrder(StdOrderData structure) throws DefinedException {
        String bookingId = structure.getBookingRs().getBookingid();

        StdOrderResponse stdOrderResponse = getLocalTicketService().createOrder(structure.toStdOrderReq());

        LoggerFactory.getLogger(this.getClass()).info("本地发码处理器执行请求" + (stdOrderResponse.getStd_flag() ? "成功" : "失败"));
        StdIdResult result = new StdIdResult();
        if (stdOrderResponse.getStd_flag()) {
            result.fillid(ProdType.MAIN.val(), bookingId, stdOrderResponse.getOutid());
            structure.getBookingRs().setOutid(stdOrderResponse.getOutid());
        } else {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR));
        }
        return stdOrderResponse;
    }


    @Override
    public StdCancelOrderResponse cancelOrder(StdCancelOrderRequest request) throws DefinedException {


        StdCancelOrderResponse stdRes = getLocalTicketService().cancelOrder(request);

        return stdRes;
    }

    @Override
    public StdTicketQrCodeUrlResponse queryTicketQrCodeUrl(StdTicketQrCodeUrlRequest request) throws DefinedException {
        return new StdTicketQrCodeUrlResponse();
    }

    @Override
    public StdTicketQueryResponse queryTicket(StdTicketQueryRequest request) throws DefinedException {
        StdTicketQueryResponse response = getLocalTicketService().queryTicket(request);
        return response;
    }

    @Override
    public StdTicketQueryQrPicResponse queryTicketQrPic(StdTicketQueryQrPicRequest request) throws DefinedException {
        StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setColno(request.getColno());
        StdTicketQueryResponse queryRes = getLocalTicketService().queryTicket(queryRequest);


        StdTicketQueryQrPicResponse stdTicketQueryQrPicResponse = new StdTicketQueryQrPicResponse();
        QrConfig config = new QrConfig(280, 280);
        config.setRatio(4);
        String base64qr = QrCodeUtil.generateAsBase64(queryRes.getAssistCheckNo(), config, ImgUtil.IMAGE_TYPE_PNG);
        base64qr = base64qr.replaceAll("data:image/png;base64,", "");
        stdTicketQueryQrPicResponse.setImg(base64qr);
        if (!stdTicketQueryQrPicResponse.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdTicketQueryQrPicResponse;
    }

    @Override
    public StdTicketQueryStatusResponse queryTicketStatus(StdTicketQueryStatusRequest request) throws DefinedException {
        StdTicketQueryStatusResponse response = getLocalTicketService().queryTicketStatus(request);
        return response;
    }



    @Override
    public StdTicketSendMsgResponse sendTicketMsg(StdTicketSendMsgRequest request) throws DefinedException {
        return new StdTicketSendMsgResponse();
    }

    @Override
    public StdTicketChangeDateResponse changeTicketDate(StdTicketChangeDateRequest request) throws DefinedException {
        return new StdTicketChangeDateResponse();
    }

    @Override
    public StdTicketChangeNumResponse changeTicketRoom(StdTicketChangeNumRequest request) throws DefinedException {
        return new StdTicketChangeNumResponse();
    }

    @Override
    public StdTicketQueryCancelStatusResponse queryTicketCacnelStatus(StdTicketQueryCanelStatusRequest request) throws DefinedException {

        StdTicketQueryCancelStatusResponse stdRes = new StdTicketQueryCancelStatusResponse();

        return stdRes;
    }

    @Override
    public StdTicketReturnNumResponse returnTicketNum(StdTicketReturnNumRequest request) throws DefinedException {
        StdCancelOrderRequest cancelOrderRequest = new StdCancelOrderRequest();
        cancelOrderRequest.setOtaorderid(request.getBookingId());
        cancelOrderRequest.setProjectId(request.getProjectId());

        StdCancelOrderResponse stdCancelOrderResponse = getLocalTicketService().cancelOrder(cancelOrderRequest);
        if (stdCancelOrderResponse.getStd_flag()) {
            StdTicketReturnNumResponse stdTicketReturnNumResponse = new StdTicketReturnNumResponse();
            stdTicketReturnNumResponse.setStd_flag(true);
            stdTicketReturnNumResponse.setOutCancelNo(RandomUtil.randomString(6));
            return stdTicketReturnNumResponse;
        } else {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("门票退票失败"));
        }
    }

}
