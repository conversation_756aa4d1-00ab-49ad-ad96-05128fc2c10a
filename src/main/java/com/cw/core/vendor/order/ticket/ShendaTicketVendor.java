package com.cw.core.vendor.order.ticket;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.cw.config.exception.CustomException;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.orderhandler.pojo.StdIdResult;
import com.cw.core.vendor.order.BaseOutSysVendor;
import com.cw.entity.Ticket_rs;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.ticket.ShendaTicketClient;
import com.cw.outsys.pojo.sdticket.request.*;
import com.cw.outsys.pojo.sdticket.response.*;
import com.cw.outsys.pojo.sdticket.xmlpojo.common.XmlCredential;
import com.cw.outsys.pojo.sdticket.xmlpojo.common.XmlTicketOrder;
import com.cw.outsys.pojo.sdticket.xmlpojo.request.XmlOrderRq;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.XmlUtils;
import com.cw.utils.enums.IdCardType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@VendorAdapter(vendorType = VendorType.SD_TICKET)
public class ShendaTicketVendor extends BaseOutSysVendor<ShendaTicketClient> {

    //企业码-->appid
    //用户名--->userid
    //用户密码 -->userpwd

    @Override
    public StdOrderResponse createOrder(StdOrderData structure) throws DefinedException {
        String projectId = structure.getBookingRs().getProjectid();
        String bookingId = structure.getBookingRs().getBookingid();
        ShendaTicketClient client = getClient(projectId);
        SDOrderReq request = new SDOrderReq().setVendor(client.getConfig()).transfer(structure.toStdOrderReq());
        SDOrderRes response = client.execute(request);

        StdOrderResponse stdOrderResponse = response.getSysStdResponse();
        LoggerFactory.getLogger(this.getClass()).info("深大门票系统处理器执行请求" + (stdOrderResponse.getStd_flag() ? "成功" : "失败"));
        StdIdResult result = new StdIdResult();
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (stdOrderResponse.getStd_flag()) {
            //TDOO 写LOG
            result.fillid(ProdType.MAIN.val(), bookingId, stdOrderResponse.getOutid());
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
                    bookingId, "票务接口发码成功，订单号：" + bookingId, projectId);
        } else {
            if (stdOrderResponse.getStd_message().contains("证件类型")) {//证件类型错误的时候.尝试换个万能证件类型跟号码.避免发码失败 JUST 22.07.24
                request = getRetrySdorderReq(request, stdOrderResponse);
                response = client.execute(request);
                stdOrderResponse = response.getSysStdResponse();
                if (stdOrderResponse.getStd_flag()) {
                    return stdOrderResponse;
                } else {
                    throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("深大门票订单创建失败"));
                }
            }

//            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
//                    bookingId, "创建订单失败，订单号：" + bookingId, projectId);
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("深大门票订单创建失败"));
        }
        return stdOrderResponse;
    }

    /**
     * 证件类型错误的时候.尝试换个万能证件类型跟号码.避免发码失败
     *
     * @param sdOrderReq
     * @return
     */
    private SDOrderReq getRetrySdorderReq(SDOrderReq sdOrderReq, StdOrderResponse stdOrderResponse) {
        XmlOrderRq xmlOrderRq = XmlUtils.fromXML(sdOrderReq.getData(), XmlOrderRq.class);
        List<XmlTicketOrder> ticketOrderList = xmlOrderRq.getOrderRequest().getOrder().getTicketOrders();
        for (XmlTicketOrder xmlTicketOrder : ticketOrderList) {
            List<XmlCredential> xmlCredentials = xmlTicketOrder.getCredentials();
            for (XmlCredential xmlCredential : xmlCredentials) {
                if (stdOrderResponse.getStd_message().contains(xmlCredential.getId())) {//把出问题的证件号替换掉
                    xmlCredential.setIdType(IdCardType.getFormval(IdCardType.PASS.val()));
                    xmlCredential.setId(RandomUtil.randomNumbers(10));
                }
            }
        }
        String xmlRequest = XmlUtils.toXML(xmlOrderRq);
        sdOrderReq.setData(xmlRequest);
        return sdOrderReq;
    }


    @Override
    public StdCancelOrderResponse cancelOrder(StdCancelOrderRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        //TODO 先查询订单 .没有创建成功的话.就直接返回成功
        StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setColno(request.getOtaorderid());
        SDQueryReq sdQueryReq = new SDQueryReq().setVendor(client.getConfig()).transfer(queryRequest);
        SDQueryRes queryRes = client.execute(sdQueryReq);
        if (!queryRes.getSysStdResponse().getStd_flag()) {   //查询出错.说明订单不存在.直接取消订单
            StdCancelOrderResponse cancelOrderResponse = new StdCancelOrderResponse();
            cancelOrderResponse.setStd_flag(true);
            return cancelOrderResponse;
        }

        if (request.getOrderDataContext() != null && CollUtil.isNotEmpty(request.getOrderDataContext().getTickets())) {
            List<Ticket_rs> ticketRsList = request.getOrderDataContext().getTickets();
            HashSet<Boolean> auditResult = new HashSet<>();
            boolean lneedQuery = false;
            for (Ticket_rs ticketRs : ticketRsList) {
                if (!ticketRs.getCancelno().isEmpty()) {//如果带有退票批次号
                    lneedQuery = true;
                    StdTicketQueryCanelStatusRequest queryCanelStatusRequest = new StdTicketQueryCanelStatusRequest();
                    queryCanelStatusRequest.setProjectId(request.getProjectId());
                    queryCanelStatusRequest.setCancelno(ticketRs.getCancelno());
                    SDQueryCancelStatusReq cancelStatusReq = new SDQueryCancelStatusReq().setVendor(client.getConfig()).transfer(queryCanelStatusRequest);
                    SDQueryCancelStatusRes cancelStatusRes = client.execute(cancelStatusReq);
                    auditResult.add(cancelStatusRes.getSysStdResponse().getStd_flag());
                }
            }
            if (lneedQuery) {
                if (auditResult.contains(Boolean.TRUE) && !auditResult.contains(Boolean.FALSE)) {
//                    System.out.println("深大门票审核全部已通过");
                    StdCancelOrderResponse stdRes = new StdCancelOrderResponse();  //深大门票已经全部取消
                    stdRes.setStd_flag(true);
                    return stdRes;
                } else {
                    throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("深大门票审核未通过"));
                }
            }
        }


        SDCancelReq cancelReq = new SDCancelReq().setVendor(client.getConfig()).transfer(request);
        SDCancelRes res = client.execute(cancelReq);
        StdCancelOrderResponse stdRes = res.getSysStdResponse();
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (!stdRes.getStd_flag()) {
            //保存日志
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                    request.getOtaorderid(), "取消订单失败，订单号：" + request.getOtaorderid(), request.getProjectId());
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单取消失败"));
        }
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                request.getOtaorderid(), "取消订单成功，订单号：" + request.getOtaorderid(), request.getProjectId());
        return stdRes;
    }

    @Override
    public StdTicketQrCodeUrlResponse queryTicketQrCodeUrl(StdTicketQrCodeUrlRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        SDQRCodeUrlReq qrCodeUrlReq = new SDQRCodeUrlReq().setVendor(client.getConfig()).transfer(request);
        SDQRCodeUrlRes res = client.execute(qrCodeUrlReq);
        StdTicketQrCodeUrlResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketQueryResponse queryTicket(StdTicketQueryRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        SDQueryReq queryReq = new SDQueryReq().setVendor(client.getConfig()).transfer(request);
        SDQueryRes res = client.execute(queryReq);
        StdTicketQueryResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketQueryQrPicResponse queryTicketQrPic(StdTicketQueryQrPicRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        SDQueryQrPicReq queryQrPicReq = new SDQueryQrPicReq().setVendor(client.getConfig()).transfer(request);
        SDQueryQrPicRes res = client.execute(queryQrPicReq);
        StdTicketQueryQrPicResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketQueryStatusResponse queryTicketStatus(StdTicketQueryStatusRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        SDQueryStatusReq queryStatusReq = new SDQueryStatusReq().setVendor(client.getConfig()).transfer(request);
        SDQueryStatusRes res = client.execute(queryStatusReq);
        StdTicketQueryStatusResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketSendMsgResponse sendTicketMsg(StdTicketSendMsgRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        SDSendMsgReq sendMsgReq = new SDSendMsgReq().setVendor(client.getConfig()).transfer(request);
        SDSendMsgRes res = client.execute(sendMsgReq);
        StdTicketSendMsgResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketChangeDateResponse changeTicketDate(StdTicketChangeDateRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        SDChangeDateReq changeDateReq = new SDChangeDateReq().setVendor(client.getConfig()).transfer(request);
        SDChangeDateRes res = client.execute(changeDateReq);
        StdTicketChangeDateResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketChangeNumResponse changeTicketRoom(StdTicketChangeNumRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        SDChangeNumReq changeNumReq = new SDChangeNumReq().setVendor(client.getConfig()).transfer(request);
        SDChangeNumRes res = client.execute(changeNumReq);
        StdTicketChangeNumResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketQueryCancelStatusResponse queryTicketCacnelStatus(StdTicketQueryCanelStatusRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        SDQueryCancelStatusReq cancelStatusReq = new SDQueryCancelStatusReq().setVendor(client.getConfig()).transfer(request);
        SDQueryCancelStatusRes cancelStatusRes = client.execute(cancelStatusReq);
        StdTicketQueryCancelStatusResponse stdRes = cancelStatusRes.getSysStdResponse();
//        if(!stdRes.getStd_flag()){
//            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
//        }
        return stdRes;
    }

    @Override
    public StdTicketReturnNumResponse returnTicketNum(StdTicketReturnNumRequest request) throws DefinedException {
        ShendaTicketClient client = getClient(request.getProjectId());
        SDReturnNumReq returnNumReq = new SDReturnNumReq().setVendor(client.getConfig()).transfer(request);
        SDReturnNumRes returnNumRes = client.execute(returnNumReq);

        StdTicketReturnNumResponse stdRes = returnNumRes.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }
}
