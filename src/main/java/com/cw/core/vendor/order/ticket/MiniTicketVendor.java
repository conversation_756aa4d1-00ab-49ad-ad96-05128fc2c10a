package com.cw.core.vendor.order.ticket;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.config.exception.CustomException;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.orderhandler.pojo.StdIdResult;
import com.cw.core.vendor.order.BaseOutSysVendor;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.ticket.MiniTicketClient;
import com.cw.outsys.pojo.mtticket.pojo.request.MTOrderCancelReqData;
import com.cw.outsys.pojo.mtticket.pojo.request.MTQueryRequestData;
import com.cw.outsys.pojo.mtticket.request.MTOrderCancelRequest;
import com.cw.outsys.pojo.mtticket.request.MTOrderRequest;
import com.cw.outsys.pojo.mtticket.request.MTQueryRequest;
import com.cw.outsys.pojo.mtticket.request.MTSendMsgRequest;
import com.cw.outsys.pojo.mtticket.response.MTOrderCancelResponse;
import com.cw.outsys.pojo.mtticket.response.MTOrderResponse;
import com.cw.outsys.pojo.mtticket.response.MTQueryResponse;
import com.cw.outsys.pojo.mtticket.response.MTSendMsgResponse;
import com.cw.outsys.stdop.common.array.StdTicketSubStatusNode;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.LoggerType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.TicketUtil;
import com.cw.utils.enums.VendorType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 微票
 * <AUTHOR> Tony Leung
 * @Create on 2024-09-13
 */
@VendorAdapter(vendorType = VendorType.MINI_TICKET)
public class MiniTicketVendor extends BaseOutSysVendor<MiniTicketClient> {

    private Logger logger = LoggerFactory.getLogger(LoggerType.sys.name());

    /**
     * 创建微票订单
     *
     * @param structure
     * @return
     * @throws DefinedException
     */
    @Override
    public StdOrderResponse createOrder(StdOrderData structure) throws DefinedException {
        String projectId = structure.getBookingRs().getProjectid();
        String bookingId = structure.getBookingRs().getBookingid();
        MiniTicketClient client = getClient(projectId);

        MTOrderRequest request = new MTOrderRequest().setVendor(client.getConfig()).transfer(structure.toStdOrderReq());

        MTOrderResponse response = client.execute(request);

        StdOrderResponse stdOrderResponse = response.getSysStdResponse();
        LoggerFactory.getLogger(this.getClass()).info("微票门票系统处理器执行请求" + (stdOrderResponse.getStd_flag() ? "成功" : "失败"));
        StdIdResult result = new StdIdResult();
        if (stdOrderResponse.getStd_flag()) {
            //TDOO 写LOG
            result.fillid(ProdType.MAIN.val(), bookingId, stdOrderResponse.getOutid());
            structure.getBookingRs().setOutid(stdOrderResponse.getOutid());
        } else {
            String msg = stdOrderResponse.getStd_sub_msg().contains("身份证") ? stdOrderResponse.getStd_sub_msg() : "门票订单创建失败";
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg(msg));
        }
        stdOrderResponse.setStdIdResult(result);
        return stdOrderResponse;
    }

    @Override
    public StdCancelOrderResponse cancelOrder(StdCancelOrderRequest request) throws DefinedException {
        MiniTicketClient client = getClient(request.getProjectId());
        //先查询订单 .没有创建成功的话.就直接返回成功
        if (StringUtils.isBlank(request.getOutid())) {
            //没有outid 说明订单未创建订单没有支付 定时取消的
            StdCancelOrderResponse cancelOrderResponse = new StdCancelOrderResponse();
            cancelOrderResponse.setStd_flag(true);
            return cancelOrderResponse;
        }
        StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setColno(request.getOtaorderid());
        queryRequest.setOutId(request.getOutid());
        MTQueryRequest sdQueryReq = new MTQueryRequest().setVendor(client.getConfig()).transfer(queryRequest);
        MTQueryResponse queryRes = client.execute(sdQueryReq);
        StdTicketQueryResponse response = queryRes.getSysStdResponse();
        //判断状态  0.订单未找到 1.订单创建成功 2.订单退款审核中 3.订单退款审核成功 4.订单退款审核失败 5.游客已入园
        if (!response.getStd_flag() || "0".equals(response.getStatus())) {   //查询出错.说明订单不存在.直接取消订单
            StdCancelOrderResponse cancelOrderResponse = new StdCancelOrderResponse();
            cancelOrderResponse.setStd_flag(true);
            return cancelOrderResponse;
        } else if (response.getStd_flag() && "5".equals(response.getStatus())) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("门票系统已检票.票务订单取消退款审核失败."));
        }
        //else if (response.getStd_flag() && !"1".equals(response.getStatus())) {//2-4
        //    throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("票务退款审核中，不可取消"));
        //}
        StdCancelOrderResponse stdRes = new StdCancelOrderResponse();
        //刚创建订单
        //1代表的是成功.2.审核中.一般是微票需要跟闸机通信等待异步通知.
        if ("1".equals(response.getStatus()) || "2".equals(response.getStatus())) {
            MTOrderCancelRequest cancelReq = new MTOrderCancelRequest().setVendor(client.getConfig()).transfer(request);
            cancelReq.setVendor(client.getConfig()).transfer(request);
            MTOrderCancelResponse res = client.execute(cancelReq);
            stdRes = res.getSysStdResponse();

        } else {
            if ("3".equals(response.getStatus())) {//退款审核成功
                stdRes.setStd_flag(true);
            } else {
                //4 退款失败
                String msg = "订单退款审核失败";
                stdRes.setStd_flag(false);//退款审核中 或者退款审核失败
                stdRes.setStd_message(msg);
            }
        }
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (!stdRes.getStd_flag()) {
            //保存日志
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                    request.getOtaorderid(), "取消订单失败，订单号：" + request.getOtaorderid(), request.getProjectId());
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单取消失败 " + stdRes.getStd_message()));
        }
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                request.getOtaorderid(), "取消订单成功，订单号：" + request.getOtaorderid(), request.getProjectId());
        return stdRes;

    }

    @Override
    public StdTicketQrCodeUrlResponse queryTicketQrCodeUrl(StdTicketQrCodeUrlRequest request) throws DefinedException {
        return super.queryTicketQrCodeUrl(request);
    }

    /**
     * 查询票务状态
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    @Override
    public StdTicketQueryResponse queryTicket(StdTicketQueryRequest request) throws DefinedException {
        MiniTicketClient client = getClient(request.getProjectId());
        MTQueryRequest queryReq = new MTQueryRequest().setVendor(client.getConfig()).transfer(request);
        MTQueryResponse res = client.execute(queryReq);
        StdTicketQueryResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketQueryQrPicResponse queryTicketQrPic(StdTicketQueryQrPicRequest request) throws DefinedException {
        StdTicketQueryQrPicResponse response = new StdTicketQueryQrPicResponse();
        //辅助码 生成图片二维码base64编码
        if (StringUtils.isBlank(request.getAssistCode())) {
            //创建订单没返回门票辅助码
            logger.error("微票门票辅助码为空，订单号：{}", request.getColno());
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        String img = SysFuncLibTool.generateEncodeQr("", request.getAssistCode(), request.getProjectId());
        response.setImg(img);
        response.setStd_flag(true);
        return response;
    }

    /**
     * 查询检票状态
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    @Override
    public StdTicketQueryStatusResponse queryTicketStatus(StdTicketQueryStatusRequest request) throws DefinedException {
        MiniTicketClient client = getClient(request.getProjectId());
        MTQueryRequest queryReq = new MTQueryRequest();
        MTQueryRequestData requestData = new MTQueryRequestData();
        requestData.setOrderSerialId(request.getColno());
        requestData.setPartnerOrderId(request.getOutId());
        queryReq.setData(JSON.toJSONString(requestData));
        MTQueryResponse res = client.execute(queryReq);
        StdTicketQueryResponse stdTicketQueryResponse = res.getSysStdResponse();
        if (!stdTicketQueryResponse.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }


        StdTicketQueryStatusResponse stdTicketQueryStatusResponse = new StdTicketQueryStatusResponse();
        stdTicketQueryStatusResponse.setStd_flag(stdTicketQueryResponse.getStd_flag());
        List<StdTicketSubStatusNode> subStatus = new ArrayList<StdTicketSubStatusNode>();
        StdTicketSubStatusNode statusNode = new StdTicketSubStatusNode();
        Integer returnnum = stdTicketQueryResponse.getOrders().get(0).getReturnnum();
        Integer checknum = stdTicketQueryResponse.getOrders().get(0).getChecknum();
        Integer totalNum = stdTicketQueryResponse.getOrders().get(0).getNum();
        statusNode.setChecknum(checknum);//检票数量
        statusNode.setReturnnum(returnnum);//退款数量
        statusNode.setNeedchecknum(totalNum);//总数量
        //判断状态  0.订单未找到 1.订单创建成功 2.订单退款审核中 3.订单退款审核成功 4.订单退款审核失败 5.游客已入园
        //statusNode.setStatus(changeMiniTicketStatus(stdTicketQueryResponse.getStatus())); //2024-12-08更新 只需要判断是否检票
        //已经检票
        if (checknum > 0) {
            if (checknum < returnnum) {
                //未检票完
                statusNode.setStatus(TicketUtil.TicketCheckStatus.CHECKING.name());
            } else {
                //已经检票完毕
                statusNode.setStatus(TicketUtil.TicketCheckStatus.CHECKED.name());
            }
        } else {
            statusNode.setStatus(TicketUtil.TicketCheckStatus.UNCHECK.name());
        }
        subStatus.add(statusNode);

        stdTicketQueryStatusResponse.setSubOrders(subStatus);

        return stdTicketQueryStatusResponse;
    }

    private String changeMiniTicketStatus(String mtStatus) {
        if ("01".contains(mtStatus)) {
            return TicketUtil.TicketCheckStatus.UNCHECK.name();
            //} else if ("2345".contains(mtStatus)) {
        } else if ("5".contains(mtStatus)) {
            return TicketUtil.TicketCheckStatus.CHECKED.name();
        } else {
            return TicketUtil.TicketCheckStatus.UNCHECK.name();
        }
    }

    @Override
    public StdTicketSendMsgResponse sendTicketMsg(StdTicketSendMsgRequest request) throws DefinedException {
        MiniTicketClient client = getClient(request.getProjectId());
        MTSendMsgRequest sendMsgReq = new MTSendMsgRequest().setVendor(client.getConfig()).transfer(request);
        MTSendMsgResponse res = client.execute(sendMsgReq);
        StdTicketSendMsgResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketChangeDateResponse changeTicketDate(StdTicketChangeDateRequest request) throws DefinedException {
        return new StdTicketChangeDateResponse();
    }

    @Override
    public StdTicketChangeNumResponse changeTicketRoom(StdTicketChangeNumRequest request) throws DefinedException {
        return new StdTicketChangeNumResponse();
    }

    /**
     * 查询订单取消状态
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    @Override
    public StdTicketQueryCancelStatusResponse queryTicketCacnelStatus(StdTicketQueryCanelStatusRequest request) throws DefinedException {
        MiniTicketClient client = getClient(request.getProjectId());
        //MTQueryCancelStatusRequest cancelStatusReq = new MTQueryCancelStatusRequest().setVendor(client.getConfig()).transfer(request);
        //MTQueryCancelStatusResponse cancelStatusRes = client.execute(cancelStatusReq);
        //StdTicketQueryCancelStatusResponse stdRes = cancelStatusRes.getSysStdResponse();
        //return stdRes;
        StdTicketQueryCancelStatusResponse stdRes = new StdTicketQueryCancelStatusResponse();
        boolean lsuccess = false;
        MTQueryRequest queryReq = new MTQueryRequest();
        MTQueryRequestData requestData = new MTQueryRequestData();
        requestData.setOrderSerialId(request.getColno());
        requestData.setPartnerOrderId(request.getOutId());
        queryReq.setData(JSON.toJSONString(requestData));
        MTQueryResponse res = client.execute(queryReq);
        StdTicketQueryResponse stdTicketQueryResponse = res.getSysStdResponse();
        if (!stdTicketQueryResponse.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }

        //取消审核成功
        if ("3".equals(stdTicketQueryResponse.getStatus() + "")) {
            lsuccess = true;
        }


        stdRes.setStd_flag(lsuccess);//这里不处理异常

        return stdRes;
    }

    /**
     * 直接退款
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    @Override
    public StdTicketReturnNumResponse returnTicketNum(StdTicketReturnNumRequest request) throws DefinedException {

        MiniTicketClient client = getClient(request.getProjectId());
        MTOrderCancelRequest cancelReq = new MTOrderCancelRequest();
        MTOrderCancelReqData data = new MTOrderCancelReqData();
        data.setOrderSerialId(request.getBookingId());
        data.setPartnerOrderId(request.getOutId());//微票订单号
        data.setTickets(Integer.parseInt(request.getReturnNum()));
        cancelReq.setData(JSON.toJSONString(data));
        MTOrderCancelResponse res = client.execute(cancelReq);
        StdCancelOrderResponse stdCancelOrderResponse = res.getSysStdResponse();

        //3：取消失败，直接返回报错
        if (!stdCancelOrderResponse.getStd_flag() && "3".equals(stdCancelOrderResponse.getStd_sub_code())) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg(stdCancelOrderResponse.getStd_message()));

        }
        StdTicketReturnNumResponse stdTicketReturnNumResponse = new StdTicketReturnNumResponse();
        //有延迟退款 基本都是审核中 直接返回成功
        stdTicketReturnNumResponse.setStd_flag(true);
        stdTicketReturnNumResponse.setOutCancelNo(RandomUtil.randomString(6));//中景没有这个.只是为了后面审核的时候可以发起查询
        return stdTicketReturnNumResponse;

    }
}
