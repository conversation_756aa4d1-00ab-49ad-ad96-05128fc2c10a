package com.cw.core.vendor.order.colorder;

import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.ContentCacheTool;
import com.cw.common.client.CwApiClient;
import com.cw.config.exception.CustomException;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.orderhandler.pojo.StdIdResult;
import com.cw.core.vendor.order.BaseOutSysVendor;
import com.cw.entity.Booking_rs;
import com.cw.entity.Room_rs;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.platform.ZjPlatformClient;
import com.cw.outsys.pojo.zjplatform.common.*;
import com.cw.outsys.pojo.zjplatform.request.*;
import com.cw.outsys.pojo.zjplatform.response.*;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import com.cw.pms.request.CwColRsReq;
import com.cw.pms.response.CwColRsRes;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.sync.SyncOutGridMqMsg;
import com.cw.service.mq.msgmodel.sync.SyncOutRateMqMsg;
import com.cw.utils.CalculateDate;
import com.cw.utils.RobotUtils;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@VendorAdapter(vendorType = VendorType.ZJPLPATFORM)
public class ZjPlatformVendor extends BaseOutSysVendor<ZjPlatformClient> {
    public static final String OKRESULT = "success";
    public static final String FAILRESULT = "fail";


    @Override
    public StdOrderResponse createOrder(StdOrderData structure) throws DefinedException {
//        return super.createOrder(structure);
        //组装厂商接口参数..交给client 发送
        String projectId = structure.getBookingRs().getProjectid();
        String bookingId = structure.getBookingRs().getBookingid();
        ZjPlatformClient client = getClient(projectId);
        OtaOrderRequest request = packOrderRequest(structure, client);
        OtaOrderResponse response = client.execute(request);
        StdOrderResponse stdOrderResponse = response.getSysStdResponse();
        StdIdResult result = new StdIdResult();
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (stdOrderResponse.getStd_flag()) {
            result.fillid(ProdType.MAIN.val(), bookingId, stdOrderResponse.getOutid());

            structure.getBookingRs().setOutid(response.getData().getOrderid());  //关联中景订单号保存
            structure.getBookingRs().setAmount(response.getData().getTotalamount());  //保存中景的订单总金额


            //sendPms(structure);//演示用.直接发到PMS

            //订单新建记录
//            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
//                    bookingId, "创建订单成功，订单号：" + bookingId, projectId);
        } else {
//            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
//                    bookingId, "创建订单失败，订单号：" + bookingId, projectId);
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLINFO,
                    RobotUtils.transRobotTicketMsg("中景创建订单失败", structure.getBookingRs().getBookingid(), RobotUtils.RobotGroup.DSMALLINFO,
                            JSON.toJSONString(request) + "返回响应: " + JSON.toJSONString(response)));

            if ("101".equals(stdOrderResponse.getStd_sub_code())) {//库存对不上
                tryFixAvl(structure);
            }
//            if("106".equals(stdOrderResponse.getStd_sub_code())){  //价格对不上的修复处理.暂时只处理库存对不上的问题
//                tryFixRate(structure);
//            }

            throw new DefinedException("订单创建失败", SystemUtil.SystemerrorCode.ERR001_OVERBOOKROOM);
//            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单创建失败"));
        }
        stdOrderResponse.setStdIdResult(result);
        return stdOrderResponse;
    }


    private void tryFixAvl(StdOrderData structure) {
        RabbitTemplate rabbitTemplate = SpringUtil.getBean("rabbitTemplate", RabbitTemplate.class);
        String routingKey = MqNameUtils.getMallOpSignal(MqNameUtils.SyncOp_Task.OUTGRID);
        for (Room_rs room : structure.getRooms()) {
            SyncOutGridMqMsg mqMsg = new SyncOutGridMqMsg();
            mqMsg.setStartdate(CalculateDate.dateToString(structure.getBookingRs().getArrdate()));
            mqMsg.setEnddate(CalculateDate.dateToString(structure.getBookingRs().getDeptdate()));
            mqMsg.setProjectId(structure.getBookingRs().getProjectid());
            mqMsg.setProductType(ProdType.ROOM.name());
            mqMsg.setProductCode(room.getRmtype());
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), routingKey, JSON.toJSONString(mqMsg));
        }
        if (!structure.getKitCode().isEmpty()) {
            SyncOutGridMqMsg mqMsg = new SyncOutGridMqMsg();
            mqMsg.setStartdate(CalculateDate.dateToString(structure.getBookingRs().getArrdate()));
            mqMsg.setEnddate(CalculateDate.dateToString(structure.getBookingRs().getDeptdate()));
            mqMsg.setProjectId(structure.getBookingRs().getProjectid());
            mqMsg.setProductType(ProdType.TAOCAN.name());
            mqMsg.setProductCode(structure.getKitCode());
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), routingKey, JSON.toJSONString(mqMsg));
        }
    }

    private void sendPms(StdOrderData structure) {
        if (!structure.getBookingRs().getProjectid().equals("007") && !structure.getBookingRs().getPtype().equals(ProdType.ROOM.val())) {
            return;
        }
        CwApiClient client = new CwApiClient(
                "http://121.43.50.247:9530", // 测试服务器地址
                "002",                 // 测试应用ID
                "5bcaqh19ks128q5auw1lo23v6fi7cjht",            // 测试私钥
                "RSA2"                         // 签名方式
        );


        CwColRsReq req = new CwColRsReq();
        req.setBooker(structure.getBookingRs().getGuestname());
        req.setPhone(structure.getBookingRs().getTel());
        req.setNetworkId(structure.getBookingRs().getBookingid());
        req.setOtaOrderId(structure.getBookingRs().getBookingid());
        req.setArrDate(structure.getBookingRs().getArrdate());
        req.setDeptDate(structure.getBookingRs().getDeptdate());

        List<CwColRsReq.Room> rooms = new ArrayList<>();
        for (Room_rs roomrs : structure.getRooms()) {
            CwColRsReq.Room room = new CwColRsReq.Room();
            room.setRoomType(roomrs.getRmtype());
            room.setNumberOfRooms(roomrs.getAnz());
            room.setRateCode("SKJ");
            room.setCommon(structure.getBookingRs().getMemo());
            room.getStayDateRange().setStartDate(roomrs.getArrdate());
            room.getStayDateRange().setEndDate(roomrs.getDeptdate());
            room.setChannel("XCX");

            rooms.add(room);
            req.setRooms(rooms);
        }


        CwColRsRes response = client.execute(req);
        System.out.println("Group reservation response: " + JSON.toJSONString(response));
    }

    private void tryFixRate(StdOrderData structure) {
        RabbitTemplate rabbitTemplate = SpringUtil.getBean("rabbitTemplate", RabbitTemplate.class);
        String routingKey = MqNameUtils.getMallOpSignal(MqNameUtils.SyncOp_Task.OUTRATE);
        for (Room_rs room : structure.getRooms()) {
            SyncOutRateMqMsg mqMsg = new SyncOutRateMqMsg();
            mqMsg.setStartdate(CalculateDate.dateToString(structure.getBookingRs().getArrdate()));
            mqMsg.setEnddate(CalculateDate.dateToString(structure.getBookingRs().getDeptdate()));
            mqMsg.setProjectId(structure.getBookingRs().getProjectid());
            mqMsg.setProductType(ProdType.ROOM.name());
            mqMsg.setProductCode(room.getRmtype());
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), routingKey, JSON.toJSONString(mqMsg));
        }
    }

    private OtaOrderRequest packOrderRequest(StdOrderData structure, ZjPlatformClient client) {
        OtaOrderRequest request = new OtaOrderRequest();
        Booking_rs bookingRs = structure.getBookingRs();
        request.setOtaorderid(structure.getBookingRs().getBookingid());
        request.setSceniccode(client.getConfig().getOutid());
        //套餐信息
        if (bookingRs.getPtype().equals(ProdType.TAOCAN.val())) {
            request.setPackagecode(structure.getKitCode());
            request.setPackagenum(structure.getBookingRs().getAnz());
        }
        //客人信息
        OtaOrder_guestNode guestInfo = new OtaOrder_guestNode();
        guestInfo.setGuestname(bookingRs.getGuestname());
        guestInfo.setMobile(bookingRs.getTel());
        request.setGuestinfo(guestInfo);

        OtaOrder_ordersNode ordersNode = new OtaOrder_ordersNode();
        if (structure.getRooms().size() > 0) {
            ordersNode.setRoom(structure.getRooms().stream().map(r -> {
                OtaOrder_roomNode roomNode = new OtaOrder_roomNode();
                roomNode.setHotelid(ContentCacheTool.getProductGroupInfo(ProdType.ROOM.val(), r.getRmtype(), bookingRs.getProjectid(), true));
                roomNode.setStartdate(r.getArrdate());
                roomNode.setEnddate(r.getDeptdate());
                roomNode.setRoomtype(r.getRmtype());
                roomNode.setNum(r.getAnz());
                return roomNode;
            }).collect(Collectors.toList()));
        }
        if (!structure.getKitCode().isEmpty() && structure.getTickets().size() > 0) {//纯票时是否发送?
            ordersNode.setTicket(structure.getTickets().stream().map(t -> {
                OtaOrder_ticketNode ticketNode = new OtaOrder_ticketNode();
                ticketNode.setNum(t.getAnz());
                ticketNode.setTicketcode(t.getTcode());
                ticketNode.setUsedate(t.getUsedate());
                return ticketNode;
            }).collect(Collectors.toList()));
        }
        if (!structure.getKitCode().isEmpty() && structure.getCaters().size() > 0) {
            ordersNode.setPos(structure.getCaters().stream().map(c -> {
                OtaOrder_posNode posNode = new OtaOrder_posNode();
                posNode.setNum(c.getAnz(0));
                posNode.setPoscode(c.getCcode().isEmpty() ? c.getRestaurant() : c.getCcode());
                posNode.setUsedate(c.getUsedate());
                //posNode.setPostype(c.getCcode().isEmpty() ? "R" : "T"); 2023.11.20 废弃
                posNode.setPeriod(c.getTime());//2024.3.22  更新为按kititem 里配置的餐段来写入
                return posNode;
            }).collect(Collectors.toList()));

        }
        if (!structure.getKitCode().isEmpty() && structure.getKitfixcharges().size() > 0) {
            ordersNode.setCustomitem(structure.getKitfixcharges().stream().map(kitfixcharge -> {
                OtaOrder_itemNode customitemNode = new OtaOrder_itemNode();
                customitemNode.setCode(kitfixcharge.getCode());
                customitemNode.setNum(kitfixcharge.getAnz(0));
                return customitemNode;
            }).collect(Collectors.toList()));
        }
        request.setOrderlist(ordersNode);

        request.setTotalamount(bookingRs.getAmount());
        request.setRemark(bookingRs.getMemo());

        return request;
    }

    @Override
    public StdCancelOrderResponse cancelOrder(StdCancelOrderRequest request) throws DefinedException {
        if (request.getOrderDataContext().getBookingRs().getOutid().isEmpty()) {//原来的非直连订单.直接返回成功
            StdCancelOrderResponse stdCancelOrderResponse = new StdCancelOrderResponse();
            stdCancelOrderResponse.setStd_flag(true);
            return stdCancelOrderResponse;
        }
        ZjPlatformClient client = getClient(request.getProjectId());
        OtaCancelOrderRequest cancelRequest = (OtaCancelOrderRequest) new OtaCancelOrderRequest().transfer(request);
        cancelRequest.setSceniccode(client.getConfig().getOutid());
        OtaCancelOrderResponse cancelResponse = client.execute(cancelRequest);
        StdCancelOrderResponse response = cancelResponse.getSysStdResponse();
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (!response.getStd_flag()) {
            //订单取消
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                    request.getOtaorderid(), "取消订单失败，订单号：" + request.getOtaorderid(), request.getProjectId());
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单取消失败"));
        }
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                request.getOtaorderid(), "取消订单成功，订单号：" + request.getOtaorderid(), request.getProjectId());
        return response;
    }

    @Override
    public StdPayResponse payOrder(StdPayRequest request) throws DefinedException {
        ZjPlatformClient client = getClient(request.getProjectId());
        OtaPayRequest payRequest = (OtaPayRequest) new OtaPayRequest().transfer(request);
        payRequest.setSceniccode(client.getConfig().getOutid());
        OtaPayResponse payResponse = client.execute(payRequest);
        StdPayResponse response = payResponse.getSysStdResponse();
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (!response.getStd_flag()) {
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.PAY, SystemUtil.DEFAULTUSERID,
                    request.getBookingId(), "支付推送：fail", request.getProjectId());
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单支付失败"));
        }
        //userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.PAY, SystemUtil.DEFAULTUSERID,
        //        request.getBookingId(), "支付推送：ok" + request.getBookingId(), request.getProjectId());
        return response;
    }

    @Override
    public StdHotelListResponse queryHotelList(StdHotelListRequest request) throws DefinedException {
        OtaHotelListRequest outRequest = new OtaHotelListRequest().transfer(request);
        ZjPlatformClient client = getClient(request.getProjectId());
        outRequest.setSceniccode(client.getConfig().getOutid());
        OtaHotelListResponse outResponse = client.execute(outRequest);
        StdHotelListResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdRestaurantQueryResponse queryRestaurantList(StdRestaurantQueryRequest request) throws DefinedException {
        OtaRestaurantQueryRequest outRequest = new OtaRestaurantQueryRequest().transfer(request);
        ZjPlatformClient client = getClient(request.getProjectId());
        outRequest.setSceniccode(client.getConfig().getOutid());
        OtaRestaurantQueryResponse outResponse = client.execute(outRequest);
        StdRestaurantQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdKitListQueryResponse queryKitList(StdKitListQueryRequest request) throws DefinedException {
        OtaKitListQueryRequest outRequest = new OtaKitListQueryRequest().transfer(request);
        ZjPlatformClient client = getClient(request.getProjectId());
        outRequest.setSceniccode(client.getConfig().getOutid());
        OtaKitListQueryResponse outResponse = client.execute(outRequest);
        StdKitListQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdKitPriceQueryResponse queryKitPriceList(StdKitPriceQueryRequest request) throws DefinedException {
        OtaKitPriceQueryRequest outRequest = new OtaKitPriceQueryRequest().transfer(request);
        outRequest.setSceniccode(getClient(request.getProjectId()).getConfig().getOutid());
        OtaKitPriceQueryResponse outResponse = getClient(request.getProjectId()).execute(outRequest);
        StdKitPriceQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdKitAvailableQueryResponse queryKitAvailableList(StdKitAvailableQueryRequest request) throws DefinedException {
        OtaKitAvailableQueryRequest outRequest = new OtaKitAvailableQueryRequest().transfer(request);
        outRequest.setSceniccode(getClient(request.getProjectId()).getConfig().getOutid());
        OtaKitAvailableQueryResponse outResponse = getClient(request.getProjectId()).execute(outRequest);
        StdKitAvailableQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdOrderStatusQueryResponse queryOrderStatus(StdOrderStatusQueryRequest request) throws DefinedException {
        OtaOrderStatusQueryRequest outRequest = new OtaOrderStatusQueryRequest().transfer(request);
        ZjPlatformClient client = getClient(request.getProjectId());
        outRequest.setSceniccode(client.getConfig().getOutid());
        OtaOrderStatusQueryResponse outResponse = client.execute(outRequest);
        StdOrderStatusQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdRateCodeListQueryResponse queryRateCodeList(StdRateCodeListQueryRequest request) throws DefinedException {
        OtaRateCodeListQueryRequest outRequest = new OtaRateCodeListQueryRequest().transfer(request);
        ZjPlatformClient client = getClient(request.getProjectId());
        outRequest.setSceniccode(client.getConfig().getOutid());
        OtaRateCodeListQueryResponse outResponse = client.execute(outRequest);
        StdRateCodeListQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdRoomAvlResponse queryRoomAvl(StdRoomAvlRequest request) throws DefinedException {
        OtaRoomAvlRequest outRequest = new OtaRoomAvlRequest().transfer(request);
        ZjPlatformClient client = getClient(request.getProjectId());
        outRequest.setSceniccode(client.getConfig().getOutid());
        OtaRoomAvlResponse outResponse = client.execute(outRequest);
        StdRoomAvlResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdRoomDetailQueryResponse queryRoomDetail(StdRoomDetailQueryRequest request) throws DefinedException {
        OtaRoomDetailQueryRequest outRequest = new OtaRoomDetailQueryRequest().transfer(request);
        ZjPlatformClient client = getClient(request.getProjectId());
        outRequest.setSceniccode(client.getConfig().getOutid());
        OtaRoomDetailQueryResponse outResponse = client.execute(outRequest);
        StdRoomDetailQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdRoomPriceResponse queryRoomPrice(StdRoomPriceRequest request) throws DefinedException {
        OtaRoomPriceRequest outRequest = new OtaRoomPriceRequest().transfer(request);
        outRequest.setSceniccode(getClient(request.getProjectId()).getConfig().getOutid());
        OtaRoomPriceResponse outResponse = getClient(request.getProjectId()).execute(outRequest);
        StdRoomPriceResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdRoomtypeListQueryResponse queryRoomtypeList(StdRoomtypeListQueryRequest request) throws DefinedException {
        OtaRoomtypeListQueryRequest outRequest = new OtaRoomtypeListQueryRequest().transfer(request);
        outRequest.setSceniccode(getClient(request.getProjectId()).getConfig().getOutid());
        OtaRoomtypeListQueryResponse outResponse = getClient(request.getProjectId()).execute(outRequest);
        StdRoomtypeListQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdTicketDetailQueryResponse queryTicketDetail(StdTicketDetailQueryRequest request) throws DefinedException {
        OtaTicketDetailQueryRequest outRequest = new OtaTicketDetailQueryRequest().transfer(request);
        outRequest.setSceniccode(getClient(request.getProjectId()).getConfig().getOutid());
        OtaTicketDetailQueryResponse outResponse = getClient(request.getProjectId()).execute(outRequest);
        StdTicketDetailQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdTicketListQueryResponse queryTicketList(StdTicketListQueryRequest request) throws DefinedException {
        OtaTicketListQueryRequest outRequest = new OtaTicketListQueryRequest().transfer(request);
        outRequest.setSceniccode(getClient(request.getProjectId()).getConfig().getOutid());
        OtaTicketListQueryResponse outResponse = getClient(request.getProjectId()).execute(outRequest);
        StdTicketListQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }
}
