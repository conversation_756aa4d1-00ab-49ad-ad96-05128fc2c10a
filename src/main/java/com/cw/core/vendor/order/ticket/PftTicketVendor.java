package com.cw.core.vendor.order.ticket;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.cw.config.exception.CustomException;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.orderhandler.pojo.StdIdResult;
import com.cw.core.vendor.order.BaseOutSysVendor;
import com.cw.entity.Ticket_rs;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.ticket.PftTicketClient;
import com.cw.outsys.pojo.pft.pojo.PftOrderChangeProBody;
import com.cw.outsys.pojo.pft.pojo.PftOrderChangeProEntity;
import com.cw.outsys.pojo.pft.pojo.PftOrderQueryBody;
import com.cw.outsys.pojo.pft.pojo.PftOrderQueryEntity;
import com.cw.outsys.pojo.pft.pojo.req.PftOrderChangePro;
import com.cw.outsys.pojo.pft.pojo.req.PftOrderQuery;
import com.cw.outsys.pojo.pft.request.PFTCancelReq;
import com.cw.outsys.pojo.pft.request.PFTOrderReq;
import com.cw.outsys.pojo.pft.request.PFTQueryReq;
import com.cw.outsys.pojo.pft.request.PFTSendReq;
import com.cw.outsys.pojo.pft.response.PFTCancelRes;
import com.cw.outsys.pojo.pft.response.PFTOrderRes;
import com.cw.outsys.pojo.pft.response.PFTQueryRes;
import com.cw.outsys.pojo.pft.response.PFTSendRes;
import com.cw.outsys.pojo.sdticket.request.SDQueryCancelStatusReq;
import com.cw.outsys.pojo.sdticket.response.SDQueryCancelStatusRes;
import com.cw.outsys.stdop.common.array.StdTicketSubStatusNode;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.utils.JAXBXmlUtil;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.TicketUtil;
import com.cw.utils.enums.VendorType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 票付通门票系统处理器
 *
 * <AUTHOR>
 * @Create 2021/10/21 15:29
 */
@VendorAdapter(vendorType = VendorType.PFT_TICKET)
public class PftTicketVendor extends BaseOutSysVendor<PftTicketClient> {

    private static final Logger log = LoggerFactory.getLogger(PftTicketVendor.class);

    @Override
    public StdOrderResponse createOrder(StdOrderData structure) throws DefinedException {
        String projectId = structure.getBookingRs().getProjectid();
        String bookingId = structure.getBookingRs().getBookingid();
        PftTicketClient client = getClient(projectId);
        // 构建订单请求
        PFTOrderReq request = new PFTOrderReq()
                .setVendor(client.getConfig())
                .transfer(structure.toStdOrderReq());

        // 执行订单创建
        PFTOrderRes response = client.execute(request);
//     log.info("PFTOrderRes response {} ", response);
       StdOrderResponse stdResponse = response.getSysStdResponse();

        // 日志记录
        LoggerFactory.getLogger(this.getClass())
                .info("票付通订单创建{} | bookingId:{}",
                        stdResponse.getStd_flag() ? "成功" : "失败" + stdResponse.getStd_message(), bookingId);
        StdIdResult result = new StdIdResult();
        if (stdResponse.getStd_flag()) {
            // 成功处理

            result.fillid(ProdType.MAIN.val(), bookingId, stdResponse.getOutid());
            structure.getBookingRs().setOutid(stdResponse.getOutid());

        } else {
            // 失败处理
            String errorMsg = stdResponse.getStd_message();
            String finalMsg = errorMsg.contains("身份证") ? errorMsg : "票付通订单创建失败" + stdResponse.getStd_message();
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg(finalMsg));
        }
        stdResponse.setStdIdResult(result);
        return stdResponse;
    }

    @Override
    public StdCancelOrderResponse cancelOrder(StdCancelOrderRequest request) throws DefinedException {
        PftTicketClient client = getClient(request.getProjectId());

        // 空订单直接返回成功
        if (StringUtils.isBlank(request.getOutid())) {
            StdCancelOrderResponse cancelOrderResponse = new StdCancelOrderResponse();
            cancelOrderResponse.setStd_flag(true);
            return cancelOrderResponse;
        }

        // 前置状态查询
        StdTicketQueryRequest queryReq = new StdTicketQueryRequest();
        queryReq.setColno(request.getOtaorderid());
        queryReq.setOutId(request.getOutid());
        queryReq.setProjectId(request.getProjectId());


        PFTQueryReq sdQueryReq = new PFTQueryReq().setVendor(client.getConfig()).transfer(queryReq);
        PFTQueryRes queryRes = client.execute(sdQueryReq);
        StdTicketQueryResponse response = queryRes.getSysStdResponse();
        // 状态判断逻辑
        if (!response.getStd_flag()) {
            StdCancelOrderResponse cancelOrderResponse = new StdCancelOrderResponse();
            cancelOrderResponse.setStd_flag(true);
            return cancelOrderResponse;
        }

        if (request.getOrderDataContext() != null && CollUtil.isNotEmpty(request.getOrderDataContext().getTickets())) {
//            PFTCancelReq cancelReq = new PFTCancelReq().setVendor(client.getConfig()).transfer(request);
            List<Ticket_rs> ticketRsList = request.getOrderDataContext().getTickets();
            HashSet<Boolean> auditResult = new HashSet<>();
            boolean lneedQuery = false;
            for (Ticket_rs ticketRs : ticketRsList) {
                if (!ticketRs.getCancelno().isEmpty()) {//如果带有退票批次号
                    lneedQuery = true;
                    StdTicketQueryCanelStatusRequest queryCanelStatusRequest = new StdTicketQueryCanelStatusRequest();
                    queryCanelStatusRequest.setProjectId(request.getProjectId());
                    queryCanelStatusRequest.setCancelno(ticketRs.getCancelno());
                    SDQueryCancelStatusReq cancelStatusReq = new SDQueryCancelStatusReq().setVendor(client.getConfig()).transfer(queryCanelStatusRequest);
                    SDQueryCancelStatusRes cancelStatusRes = client.execute(cancelStatusReq);
                    auditResult.add(cancelStatusRes.getSysStdResponse().getStd_flag());
                }
            }
            if (lneedQuery) {
                if (auditResult.contains(Boolean.TRUE) && !auditResult.contains(Boolean.FALSE)) {
                 System.out.println("票付通门票审核全部已通过");
                    StdCancelOrderResponse stdRes = new StdCancelOrderResponse();
                    stdRes.setStd_flag(true);
                    return stdRes;
                } else {
                    throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("票付通门票审核全部已通过"));
                }
            }
        }
        PFTCancelReq cancelReq = new PFTCancelReq().setVendor(client.getConfig()).transfer(request);
        PFTCancelRes res = client.execute(cancelReq);
        StdCancelOrderResponse stdRes = res.getSysStdResponse();
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (!stdRes.getStd_flag()) {
            //保存日志
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                    request.getOtaorderid(), "取消订单失败，订单号：" + request.getOtaorderid(), request.getProjectId());
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单取消失败 " + stdRes.getStd_message()));
        }
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                request.getOtaorderid(), "取消订单成功，订单号：" + request.getOtaorderid(), request.getProjectId());
        return stdRes;
    }

    @Override
    public StdTicketQueryResponse queryTicket(StdTicketQueryRequest request) throws DefinedException {
        PftTicketClient client = getClient(request.getProjectId());
        PFTQueryReq queryReq = new PFTQueryReq().setVendor(client.getConfig()).transfer(request);
        PFTQueryRes res = client.execute(queryReq);
        StdTicketQueryResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketQueryQrPicResponse queryTicketQrPic(StdTicketQueryQrPicRequest request) throws DefinedException {

        StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setColno(request.getColno());
        queryRequest.setOutId(request.getOutId());

        PftTicketClient client = getClient(request.getProjectId());
        PFTQueryReq queryReq = new PFTQueryReq().setVendor(client.getConfig()).transfer(queryRequest);
        PFTQueryRes res = client.execute(queryReq);

        StdTicketQueryResponse stdTicketQueryResponse = res.getSysStdResponse();
        StdTicketQueryQrPicResponse stdTicketQueryQrPicResponse = new StdTicketQueryQrPicResponse();
        stdTicketQueryQrPicResponse.setStd_flag(stdTicketQueryResponse.getStd_flag());

        QrConfig config = new QrConfig(280, 280);
        config.setRatio(4);
        String base64qr = QrCodeUtil.generateAsBase64(stdTicketQueryResponse.getAssistCheckNo(),
                config, ImgUtil.IMAGE_TYPE_PNG);
        base64qr = base64qr.replaceAll("data:image/png;base64,", "");

        stdTicketQueryQrPicResponse.setImg(base64qr);

        return stdTicketQueryQrPicResponse;
    }

    @Override
    public StdTicketQueryStatusResponse queryTicketStatus(StdTicketQueryStatusRequest request) throws DefinedException {

        PftTicketClient client = getClient(request.getProjectId());
        PftOrderQueryEntity entity = new PftOrderQueryEntity();
        PftOrderQueryBody body = new PftOrderQueryBody();
        PFTQueryReq queryReq = new PFTQueryReq();
        PftOrderQuery data = new PftOrderQuery();
        data.setAc(client.getConfig().getAppid());
        data.setPw(client.getConfig().getAppsecrect());
        data.setPftOrdernum(request.getOutId());
        data.setRemotenum(request.getColno());
        body.setOrderQuery(data);
        entity.setBody(body);
        queryReq.setData(JAXBXmlUtil.convertToXml(entity));
        PFTQueryRes res = client.execute(queryReq);
        StdTicketQueryResponse stdTicketQueryResponse = res.getSysStdResponse();
        if (!stdTicketQueryResponse.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }


        StdTicketQueryStatusResponse stdTicketQueryStatusResponse = new StdTicketQueryStatusResponse();
        stdTicketQueryStatusResponse.setStd_flag(stdTicketQueryResponse.getStd_flag());
        List<StdTicketSubStatusNode> subStatus = new ArrayList<>();
        StdTicketSubStatusNode statusNode = new StdTicketSubStatusNode();
        Integer returnnum = stdTicketQueryResponse.getOrders().get(0).getReturnnum();
        Integer checknum = stdTicketQueryResponse.getOrders().get(0).getChecknum();
        Integer totalNum = stdTicketQueryResponse.getOrders().get(0).getNum();
        statusNode.setChecknum(checknum);//检票数量
        statusNode.setReturnnum(returnnum);//退款数量
        statusNode.setNeedchecknum(totalNum);//总数量
        //已经检票
        if (checknum > 0) {
            if (checknum < returnnum) {
                //未检票完
                statusNode.setStatus(TicketUtil.TicketCheckStatus.CHECKING.name());
            } else {
                //已经检票完毕
                statusNode.setStatus(TicketUtil.TicketCheckStatus.CHECKED.name());
            }
        } else {
            statusNode.setStatus(TicketUtil.TicketCheckStatus.UNCHECK.name());
        }
        subStatus.add(statusNode);

        stdTicketQueryStatusResponse.setSubOrders(subStatus);

        return stdTicketQueryStatusResponse;
    }

    @Override
    public StdTicketSendMsgResponse sendTicketMsg(StdTicketSendMsgRequest request) throws DefinedException {
        PftTicketClient client = getClient(request.getProjectId());
        PFTSendReq sendMsgReq = new PFTSendReq().setVendor(client.getConfig()).transfer(request);
        PFTSendRes res = client.execute(sendMsgReq);
        StdTicketSendMsgResponse stdRes = res.getSysStdResponse();
        if (!stdRes.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdRes;
    }

    @Override
    public StdTicketChangeDateResponse changeTicketDate(StdTicketChangeDateRequest request) throws DefinedException {

        return new StdTicketChangeDateResponse();
    }

    @Override
    public StdTicketChangeNumResponse changeTicketRoom(StdTicketChangeNumRequest request) throws DefinedException {
        return new StdTicketChangeNumResponse();
    }
    /**
     * 查询订单取消状态
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    @Override
    public StdTicketQueryCancelStatusResponse queryTicketCacnelStatus(StdTicketQueryCanelStatusRequest request) throws DefinedException {
        PftTicketClient client = getClient(request.getProjectId());

        StdTicketQueryCancelStatusResponse stdRes = new StdTicketQueryCancelStatusResponse();
        boolean lsuccess = false;
        PftOrderQueryEntity entity = new PftOrderQueryEntity();
        PftOrderQueryBody body = new PftOrderQueryBody();
        PFTQueryReq queryReq = new PFTQueryReq();
        PftOrderQuery data = new PftOrderQuery();
        data.setAc(client.getConfig().getAppid());
        data.setPw(client.getConfig().getAppsecrect());
        data.setPftOrdernum(request.getOutId());
        data.setRemotenum(request.getColno());
        body.setOrderQuery(data);
        entity.setBody(body);
        queryReq.setData(JAXBXmlUtil.convertToXml(entity));
        PFTQueryRes res = client.execute(queryReq);
        StdTicketQueryResponse stdTicketQueryResponse = res.getSysStdResponse();
        if (!stdTicketQueryResponse.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }

        //取消审核成功
        if ("3".equals(stdTicketQueryResponse.getStatus())) {
            lsuccess = true;
        }else {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        stdRes.setStd_flag(lsuccess);//这里不处理异常

        return stdRes;
    }
    /**
     * 审核中直接退款
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    @Override
    public StdTicketReturnNumResponse returnTicketNum(StdTicketReturnNumRequest request) throws DefinedException {
        PftTicketClient client = getClient(request.getProjectId());
        // 前置状态查询
        StdTicketQueryRequest queryReq = new StdTicketQueryRequest();
        queryReq.setColno(request.getBookingId());
        queryReq.setOutId(request.getOutId());
        queryReq.setProjectId(request.getProjectId());
        PFTQueryReq sdQueryReq = new PFTQueryReq().setVendor(client.getConfig()).transfer(queryReq);
        PFTQueryRes queryRes = client.execute(sdQueryReq);
        StdTicketQueryResponse response = queryRes.getSysStdResponse();

        if (!response.getStd_flag()) {
            return null;
        }
            PftOrderChangeProEntity entity = new PftOrderChangeProEntity();
            PftOrderChangeProBody body = new PftOrderChangeProBody();
            PFTCancelReq queryReqs = new PFTCancelReq();
            PftOrderChangePro data = new PftOrderChangePro();
            data.setAc(client.getConfig().getAppid());
            data.setPw(client.getConfig().getAppsecrect());
            data.setOrdern(request.getOutId());
            Integer returnNum = Integer.valueOf(request.getReturnNum());
            Integer num = response.getOrders().get(0).getNum();
            int i = num - returnNum;
            data.setNum(String.valueOf(i));
            body.setOrder_Change_Pro(data);
            entity.setBody(body);
            queryReqs.setData(JAXBXmlUtil.convertToXml(entity));
            PFTCancelRes res = client.execute(queryReqs);
            StdCancelOrderResponse stdCancelOrderResponse = res.getSysStdResponse();
            //3：取消失败，直接返回报错
            if (!stdCancelOrderResponse.getStd_flag() && "3".equals(stdCancelOrderResponse.getStd_sub_code())) {
                throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg(stdCancelOrderResponse.getStd_message()));

            }
            StdTicketReturnNumResponse stdTicketReturnNumResponse = new StdTicketReturnNumResponse();
            //有延迟退款 基本都是审核中 直接返回成功
            stdTicketReturnNumResponse.setStd_flag(true);
            stdTicketReturnNumResponse.setOutCancelNo(stdCancelOrderResponse.getTraceno());//中景没有这个.只是为了后面审核的时候可以发起查询
            return stdTicketReturnNumResponse;

    }
    }
