package com.cw.core.vendor.order.room;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.emoji.EmojiUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.KititemCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.config.confyaml.VendorYaml;
import com.cw.config.confyaml.node.Conf_Xms;
import com.cw.config.exception.CustomException;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.orderhandler.pojo.StdIdResult;
import com.cw.core.vendor.order.BaseOutSysVendor;
import com.cw.entity.Booking_rs;
import com.cw.entity.Kititem;
import com.cw.entity.Prepay;
import com.cw.entity.Room_rs;
import com.cw.exception.DefinedException;
import com.cw.mapper.common.DaoLocal;
import com.cw.outsys.client.impl.room.XmsRoomClient;
import com.cw.outsys.pojo.xms.PayMapping;
import com.cw.outsys.pojo.xms.ProductCodeResponse;
import com.cw.outsys.pojo.xms.pojo.DateRange;
import com.cw.outsys.pojo.xms.pojo.KitCodeResult;
import com.cw.outsys.pojo.xms.pojo.KitDetailResult;
import com.cw.outsys.pojo.xms.pojo.ProductCodeResult;
import com.cw.outsys.pojo.xms.request.*;
import com.cw.outsys.pojo.xms.response.*;
import com.cw.outsys.stdop.common.*;
import com.cw.outsys.stdop.common.array.StdKitItemDailyPriceNode;
import com.cw.outsys.stdop.common.array.StdKitlListNode;
import com.cw.outsys.stdop.common.array.StdSkuPriceNode;
import com.cw.outsys.stdop.common.array.StdTicketListNode;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.service.mq.MqNameUtils;
import com.cw.service.mq.msgmodel.sync.SyncOutGridMqMsg;
import com.cw.utils.*;
import com.cw.utils.enums.LoggerType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@VendorAdapter(vendorType = VendorType.XMSPMS)
public class XmsPmsVendor extends BaseOutSysVendor<XmsRoomClient> {
    private Logger logger = LoggerFactory.getLogger(LoggerType.sys.name());

    @Override
    public StdOrderResponse createOrder(StdOrderData structure) throws DefinedException {
        //组装厂商接口参数..交给client 发送
        String projectId = structure.getBookingRs().getProjectid();
        String bookingId = structure.getBookingRs().getBookingid();
        XmsRoomClient client = getClient(projectId);
        //转换标准请求参数
        XmsColligateReservationReq request = new XmsColligateReservationReq().transfer(structure.toStdOrderReq());
        XmsColligateReservationCommonRes response = client.execute(request);
        StdOrderResponse stdOrderResponse = response.getSysStdResponse();
        StdIdResult result = new StdIdResult();
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (stdOrderResponse.getStd_flag()) {
            result.fillid(ProdType.MAIN.val(), bookingId, stdOrderResponse.getOutid());
            structure.getBookingRs().setOutid(stdOrderResponse.getOutid());  //关联中景订单号保存
        } else {
//            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
//                    bookingId, "创建订单失败，订单号：" + bookingId, projectId);
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLINFO,
                    RobotUtils.transRobotTicketMsg("XMS创建订单失败", structure.getBookingRs().getBookingid(), RobotUtils.RobotGroup.DSMALLINFO,
                            JSON.toJSONString(request) + "返回响应: " + JSON.toJSONString(response)));

            //if ("101".equals(stdOrderResponse.getStd_sub_code())) {//库存对不上
            //    tryFixAvl(structure);
            //}
//            if("106".equals(stdOrderResponse.getStd_sub_code())){  //价格对不上的修复处理.暂时只处理库存对不上的问题
//                tryFixRate(structure);
//            }

            throw new DefinedException("订单创建失败", SystemUtil.SystemerrorCode.ERR001_OVERBOOKROOM);
//            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单创建失败"));
        }
        stdOrderResponse.setStdIdResult(result);
        return stdOrderResponse;
    }


    private void tryFixAvl(StdOrderData structure) {
        RabbitTemplate rabbitTemplate = SpringUtil.getBean("rabbitTemplate", RabbitTemplate.class);
        String routingKey = MqNameUtils.getMallOpSignal(MqNameUtils.SyncOp_Task.OUTGRID);
        for (Room_rs room : structure.getRooms()) {
            SyncOutGridMqMsg mqMsg = new SyncOutGridMqMsg();
            mqMsg.setStartdate(CalculateDate.dateToString(structure.getBookingRs().getArrdate()));
            mqMsg.setEnddate(CalculateDate.dateToString(structure.getBookingRs().getDeptdate()));
            mqMsg.setProjectId(structure.getBookingRs().getProjectid());
            mqMsg.setProductType(ProdType.ROOM.name());
            mqMsg.setProductCode(room.getRmtype());
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), routingKey, JSON.toJSONString(mqMsg));
        }
        if (!structure.getKitCode().isEmpty()) {
            SyncOutGridMqMsg mqMsg = new SyncOutGridMqMsg();
            mqMsg.setStartdate(CalculateDate.dateToString(structure.getBookingRs().getArrdate()));
            mqMsg.setEnddate(CalculateDate.dateToString(structure.getBookingRs().getDeptdate()));
            mqMsg.setProjectId(structure.getBookingRs().getProjectid());
            mqMsg.setProductType(ProdType.TAOCAN.name());
            mqMsg.setProductCode(structure.getKitCode());
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.MALL.name(), routingKey, JSON.toJSONString(mqMsg));
        }
    }

    @Override
    public StdCancelOrderResponse cancelOrder(StdCancelOrderRequest request) throws DefinedException {
        StdCancelOrderResponse cancelOrderResponse = new StdCancelOrderResponse();
        if (request.getOrderDataContext().getBookingRs().getOutid().isEmpty()) {//原来的非直连订单.直接返回成功
            StdCancelOrderResponse stdCancelOrderResponse = new StdCancelOrderResponse();
            stdCancelOrderResponse.setStd_flag(true);
            return stdCancelOrderResponse;
        }
        //查询账项是否需要平账
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        StdPayRequest stdPayRequest = packStdPayRequest(request.getProjectId(), request.getOrderDataContext().getBookingRs().getBookingid());
        if (stdPayRequest != null) {
            StdPayResponse stdPayResponse = payOrder(stdPayRequest);
            if (!stdPayResponse.getStd_flag()) {
                //订单取消
                userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                        request.getOtaorderid(), "取消订单预付款失败，订单号：" + request.getOtaorderid(), request.getProjectId());
                throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单取消失败"));
            }

        }
        //取消接口请求
        XmsRoomClient client = getClient(request.getProjectId());
        //转换取消请求
        XmsColligateReservationReq req = packCancelOrderRequest(request);
        XmsColligateReservationCommonRes cancelResponse = client.execute(req);
        StdOrderResponse response = cancelResponse.getSysStdResponse();

        if (!response.getStd_flag()) {
            //订单取消
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                    request.getOtaorderid(), "取消订单失败，订单号：" + request.getOtaorderid(), request.getProjectId());
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单取消失败"));
        }
        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
                request.getOtaorderid(), "取消订单成功，订单号：" + request.getOtaorderid(), request.getProjectId());

        cancelOrderResponse.setStd_flag(response.getStd_flag());
        return cancelOrderResponse;
    }


    /**
     * @param projectId
     * @param bookingId
     * @return 返回取消预付款的标准请求
     */
    private StdPayRequest packStdPayRequest(String projectId, String bookingId) {
        StdPayRequest request = new StdPayRequest();
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        Prepay prepay = daoLocal.getObject("from Prepay where bookingid=?1 and projectid=?2 ", bookingId, projectId);
        if (prepay == null) {
            return null;
        }
        request.setBookingId(bookingId);
        request.setProjectId(projectId);
        StdPay_payNode payNode = new StdPay_payNode();
        payNode.setPayid(prepay.getSerialno());
        payNode.setPaymenttype(prepay.getPaymethod());
        payNode.setAmount(prepay.getAmount().negate());//取金额负数
        request.getPayinfo().add(payNode);

        return request;
    }

    private String transEmojiAndAsciiRemark(String remark) {
        String removeEmoji = EmojiUtil.removeAllEmojis(remark);
        String removeAscii = removeEmoji.replaceAll("[^\\u0000-\\uFFFF]", "");
//        if(!remark.equals(removeAscii)){
//            SystemLogTool.getInstance().sendOfficeMsg(RobotUtil.RobotGroup.CRSINFO,
//                    RobotUtil.transRobotPmsExceptionMsg("转换备注",
//                            RobotUtil.RobotGroup.CRSINFO,"原备注:"+ remark + "\n新备注:"+removeAscii));
//        }
        return removeAscii;
    }

    /**
     * 转换取消实体接口，
     *
     * @param request
     * @return
     */
    private XmsColligateReservationReq packCancelOrderRequest(StdCancelOrderRequest request) {
        XmsColligateReservationReq req = new XmsColligateReservationReq();

        VendorConfigCache vendorConfigCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
        VendorYaml vendorYaml = vendorConfigCache.getVendorYaml(VendorType.XMSPMS);
        if (vendorYaml == null) {
            try {
                throw new DefinedException("XMS未配置yaml");
            } catch (DefinedException e) {
                e.printStackTrace();
            }
        }
        Conf_Xms xmsConf = vendorYaml.getXms();
        //获取xms 配置
        XmsColligateReservationReq.ColligateBookingRequest bookingRequest = new XmsColligateReservationReq.ColligateBookingRequest();
        StdOrderData stdOrderData = request.getOrderDataContext();

        Booking_rs bookingRs = stdOrderData.getBookingRs();
        bookingRequest.setBooker(bookingRs.getBookername());//预订人
        bookingRequest.setPhone(bookingRs.getTel());//预订电话
        bookingRequest.setSource(xmsConf.getSource());//客源代码
        bookingRequest.setMarket(xmsConf.getMarket());//市场代码
        bookingRequest.setChannelcode(xmsConf.getChannel());//渠道代码
        bookingRequest.setBookingType(xmsConf.getRstype() + ""); //预订类型 一般预定  必填
        bookingRequest.setNetworkid(bookingRs.getBookingid());//第三方与pms交互的id，由第三方接口提供
        //bookingRequest.setOtaorderid(bookingRs.getOtaorderid());//新增网络订单号
        bookingRequest.setPrice(bookingRs.getAmount().doubleValue());//新增价格
        //取消 需要传递状态和XMS预定号
        bookingRequest.setStatus(XmsUtil.XmsColrsStatus.XmsStatus.CANCEL.getXmstype());
        bookingRequest.setConfirmid(bookingRs.getOutid());//新建成功返回的colligateid 综合预定号，

        //获取主单支付方式 对应xms配置的账项代码 WX TB对应的账项代码
        if (StringUtils.isNotBlank(bookingRs.getPayment())) {
            List<PayMapping> payMappings = xmsConf.getPaymapping();
            List<String> deptCode = payMappings.stream().filter(p -> p.getPay().equals(bookingRs.getPayment())).map(PayMapping::getDepcode).collect(Collectors.toList());
            bookingRequest.setPayment(deptCode.get(0));//付款方式 支付代码需要转换账项代码code
        }
        Date startDate = bookingRs.getArrdate();
        Date endDate = bookingRs.getDeptdate();
        bookingRequest.setArrdate(startDate);//到店时间
        //如果到到店日期和离店日期是同一天，离店日期则+1天
        if (CalculateDate.isEqual(startDate, endDate)) {
            bookingRequest.setDeptdate(CalculateDate.reckonDay(startDate, 5, 1));
        } else {
            bookingRequest.setDeptdate(endDate);//离店时间
        }
        if (StringUtils.isNotBlank(bookingRs.getMemo())) {
            bookingRequest.setRemark(transEmojiAndAsciiRemark(bookingRs.getMemo()));//备注
        } else {
            bookingRequest.setRemark("");//备注
        }
        req.setColligateBooking(bookingRequest);

        return req;
    }

    @Override
    public StdPayResponse payOrder(StdPayRequest stdPayRequest) throws DefinedException {
        //综合预定入预付款
        XmsRoomClient client = getClient(stdPayRequest.getProjectId());
        XmsPaymentAccountReq payRequest = (XmsPaymentAccountReq) new XmsPaymentAccountReq().transfer(stdPayRequest);
        XmsPaymentAccountRes payResponse = client.execute(payRequest);
        StdPayResponse response = payResponse.getSysStdResponse();
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (!response.getStd_flag()) {
            //判断金额支付还是取消
            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.PAY, SystemUtil.DEFAULTUSERID,
                    stdPayRequest.getBookingId(), "支付推送：fail", stdPayRequest.getProjectId());
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单支付失败"));
        }
        return response;
    }


    @Override
    public StdHotelListResponse queryHotelList(StdHotelListRequest request) throws DefinedException {
        XmsGetBuildingReq outRequest = new XmsGetBuildingReq().transfer(request);
        XmsRoomClient client = getClient(request.getProjectId());
        XmsGetBuildingRes outResponse = client.execute(outRequest);
        StdHotelListResponse response = outResponse.getSysStdResponse();
        return response;
    }


    /**
     * 查询库存
     *
     * @param request 请求参数
     * @return
     * @throws DefinedException
     */
    @Override
    public StdRoomAvlResponse queryRoomAvl(StdRoomAvlRequest request) throws DefinedException {
        XmsRoomInventoryReq outRequest = new XmsRoomInventoryReq().transfer(request);
        XmsRoomClient client = getClient(request.getProjectId());
        XmsRoomInventoryRes outResponse = client.execute(outRequest);
        StdRoomAvlResponse response = outResponse.getSysStdResponse();
        return response;
    }

    /**
     * 缺少XMS
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    @Override
    public StdRoomDetailQueryResponse queryRoomDetail(StdRoomDetailQueryRequest request) throws DefinedException {
        StdRoomDetailQueryResponse response = new StdRoomDetailQueryResponse();
        XmsRoomClient client = getClient(request.getProjectId());

        List<StdSkuPriceNode> avlData = new ArrayList<>();
        List<StdSkuPriceNode> priceData = new ArrayList<>();
        //分别查询房量
        XmsRoomInventoryReq avlRequest = new XmsRoomInventoryReq();
        XmsRoomInventoryReq.RoomInventoryRequest roomInventoryRequest = new XmsRoomInventoryReq.RoomInventoryRequest();
        roomInventoryRequest.setRoomType(request.getRoomtype());
        DateRange dateRange = new DateRange();
        dateRange.setStartDate(request.getStartdate());
        dateRange.setEndDate(request.getEnddate());
        roomInventoryRequest.setDateRange(dateRange);
        avlRequest.setRoomInventoryReuqest(roomInventoryRequest);


        XmsRoomInventoryRes avlResponse = client.execute(avlRequest);
        StdRoomAvlResponse stdRoomAvlResponse = avlResponse.getSysStdResponse();
        if (stdRoomAvlResponse.getStd_flag()) {
            avlData = stdRoomAvlResponse.getData();
        } else {
            response.setStd_flag(false);
            return response;
        }

        //查询房价
        XmsRoomPriceQueryReq priceQueryReq = new XmsRoomPriceQueryReq();
        XmsRoomPriceQueryReq.RateSourceRequest rateSourceRequest = new XmsRoomPriceQueryReq.RateSourceRequest();
        rateSourceRequest.setRoomType(request.getRoomtype());
        rateSourceRequest.setDateRange(dateRange);
        rateSourceRequest.setRateCode(request.getRatecode());
        priceQueryReq.setRateSouceRequest(rateSourceRequest);

        XmsRoomPriceQueryRes outResponse = getClient(request.getProjectId()).execute(priceQueryReq);
        StdRoomPriceResponse stdRoomPriceResponse = outResponse.getSysStdResponse();

        if (stdRoomPriceResponse.getStd_flag()) {
            priceData = stdRoomPriceResponse.getData();
        } else {
            response.setStd_flag(false);
            return response;
        }
        //合并 库存价格
        List<StdSkuPriceNode> responseData = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(avlData)) {
            Map<Date, StdSkuPriceNode> map = new HashMap<>();
            for (StdSkuPriceNode node : avlData) {
                map.put(node.getDate(), node);
            }

            for (StdSkuPriceNode node : priceData) {
                if (map.containsKey(node.getDate())) {
                    StdSkuPriceNode nodeData = map.get(node.getDate());
                    nodeData.setPrice(node.getPrice());
                    map.put(node.getDate(), nodeData);
                }
            }
            responseData = new ArrayList<>(map.values());
        }


        response.setData(responseData);
        return response;
    }

    /**
     * 查询价格
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    @Override
    public StdRoomPriceResponse queryRoomPrice(StdRoomPriceRequest request) throws DefinedException {
        XmsRoomPriceQueryReq outRequest = new XmsRoomPriceQueryReq().transfer(request);
        XmsRoomPriceQueryRes outResponse = getClient(request.getProjectId()).execute(outRequest);
        StdRoomPriceResponse response = outResponse.getSysStdResponse();
        return response;
    }

    /**
     * 同步所有房型
     *
     * @param request
     * @return
     * @throws DefinedException
     */

    @Override
    public StdRoomtypeListQueryResponse queryRoomtypeList(StdRoomtypeListQueryRequest request) throws DefinedException {
        XmsGetRoomTypeReq outRequest = new XmsGetRoomTypeReq().transfer(request);
        //outRequest.setSceniccode(getClient(request.getProjectId()).getConfig().getOutid());
        XmsGetRoomTypeRes outResponse = getClient(request.getProjectId()).execute(outRequest);
        StdRoomtypeListQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    /**
     * 查询单个套餐代码
     *
     * @param request
     * @return
     * @throws DefinedException
     */

    @Override
    public StdKitListQueryResponse queryKitList(StdKitListQueryRequest request) throws DefinedException {
        StdKitListQueryResponse response = new StdKitListQueryResponse();
        //xms 返回所有数据 判断
        XmsGetKitCodeReq outRequest = new XmsGetKitCodeReq().transfer(request);
        XmsRoomClient client = getClient(request.getProjectId());
        XmsGetKitCodeRes outResponse = client.execute(outRequest);
        if (!outResponse.getSuccess()) {
            response.setStd_flag(false);
            return response;
        }
        List<KitCodeResult> outResults = outResponse.getResults();
        if (CollectionUtil.isNotEmpty(outResults)) {
            //查询单个套餐
            if (StringUtils.isNotBlank(request.getPackagecode()) && !request.getPackagecode().equalsIgnoreCase("all")) {
                //只能查询单个套餐明细
                XmsGetKitDetailReq outDetailReq = new XmsGetKitDetailReq();
                outDetailReq.setKitcode(request.getPackagecode());
                XmsGetKitDetailRes res = client.execute(outDetailReq);
                if (!res.getSuccess()) {
                    response.setStd_flag(false);
                    return response;
                }
                if (CollectionUtil.isEmpty(res.getResults())) {
                    return response;
                }
                //返回单个套餐代码数据
                response = outResponse.getSysStdResponse();
                List<StdKitlListNode> data = response.getData();
                //从所有套餐返回信息 返回请求套餐代码
                data = data.stream().filter(k -> k.getPackagecode().equals(request.getPackagecode())).collect(Collectors.toList());
                for (StdKitlListNode node : data) {
                    //当前节点添加套餐详情信息
                    StdKit_detailNode detailNode = fillPackDetailData(res.getResults());
                    node.setPackagedetail(detailNode);
                }
                response.setData(data);

            } else {
                //不传默认同步所有
                response = outResponse.getSysStdResponse();
                List<StdKitlListNode> data = response.getData();
                List<StdKitlListNode> resultKitList = new ArrayList<>();
                for (KitCodeResult result : outResults) {
                    //套餐停用 不同步
                    if ("T".equals(result.getHalt())) {
                        continue;
                    }
                    XmsGetKitDetailReq outDetailReq = new XmsGetKitDetailReq();
                    outDetailReq.setKitcode(result.getCode());//查询返回的套餐
                    XmsGetKitDetailRes res = client.execute(outDetailReq);
                    if (!res.getSuccess()) {
                        logger.info("查询所有套餐，获取指定套餐明细出错，套餐代码:{},报错信息:{}", result.getCode(), res.getMsg());
                        continue;
                        //response.setStd_flag(false);
                        ////查询报错
                        //return response;
                    }
                    if (CollectionUtil.isEmpty(res.getResults())) {
                        logger.info("查询所有套餐，获取指定套餐明细为空，套餐代码:{}", result.getCode());
                        continue;
                        //返回内容为空
                        //return response;
                    }
                    //返回单个套餐代码数据
                    //从所有套餐返回信息 没有子节点的套餐信息
                    List<StdKitlListNode> currentKitList = data.stream().filter(k -> k.getPackagecode().equals(result.getCode())).collect(Collectors.toList());
                    for (StdKitlListNode node : currentKitList) {
                        StdKit_detailNode detailNode = fillPackDetailData(res.getResults());
                        //当前节点添加套餐详情信息
                        node.setPackagedetail(detailNode);
                    }
                    //保存已经
                    resultKitList.addAll(currentKitList);

                }
                response.setData(resultKitList);

            }


        }

        return response;
    }

    /**
     * 填充XMS套餐明细到标准节点
     *
     * @param results
     * @return
     */
    private StdKit_detailNode fillPackDetailData(List<KitDetailResult> results) {
        StdKit_detailNode detailNode = new StdKit_detailNode();
        for (KitDetailResult nodeDetail : results) {
            if (nodeDetail.getType().equals(1)) {
                //套餐客房类型
                StdKit_detail_roomNode roomNode = new StdKit_detail_roomNode();
                roomNode.setRoomtype(nodeDetail.getCode());
                roomNode.setRtname(nodeDetail.getDesc());
                roomNode.setNum(nodeDetail.getNum());
                roomNode.setNights(nodeDetail.getDays());
                //roomNode.setHotelid();//归属酒店 缺少这个字段
                if (nodeDetail.getMode() == 2) {
                    roomNode.setGroupid("1");//多选一
                } else {
                    roomNode.setGroupid("-1");//固定
                }
                roomNode.setRate1(nodeDetail.getMon());
                roomNode.setRate2(nodeDetail.getTue());
                roomNode.setRate3(nodeDetail.getWed());
                roomNode.setRate4(nodeDetail.getThus());
                roomNode.setRate5(nodeDetail.getFri());
                roomNode.setRate6(nodeDetail.getSat());
                roomNode.setRate7(nodeDetail.getSun());
                detailNode.getRoom().add(roomNode);
            } else if (nodeDetail.getType().equals(2)) {
                //餐饮明细
                StdKit_detail_posNode posNode = new StdKit_detail_posNode();
                posNode.setRestaurantcode(nodeDetail.getCode());
                posNode.setRestaurantname(nodeDetail.getDesc());
                posNode.setNum(nodeDetail.getNum());
                if (nodeDetail.getMode() == 2) {
                    posNode.setGroupid("1");//多选一
                } else {
                    posNode.setGroupid("-1");//固定
                }
                //添加餐段
                posNode.setPeriod(Arrays.asList(nodeDetail.getInfocode().split(",")));
                posNode.setRate1(nodeDetail.getMon());
                posNode.setRate2(nodeDetail.getTue());
                posNode.setRate3(nodeDetail.getWed());
                posNode.setRate4(nodeDetail.getThus());
                posNode.setRate5(nodeDetail.getFri());
                posNode.setRate6(nodeDetail.getSat());
                posNode.setRate7(nodeDetail.getSun());
                detailNode.getPos().add(posNode);
            } else if (nodeDetail.getType().equals(3)) {
                //娱乐
                StdKit_detail_spaNode spaNode = new StdKit_detail_spaNode();
                spaNode.setCode(nodeDetail.getCode());
                spaNode.setName(nodeDetail.getDesc());
                spaNode.setNum(nodeDetail.getNum());
                if (nodeDetail.getMode() == 2) {
                    spaNode.setGroupid("1");//多选一
                } else {
                    spaNode.setGroupid("-1");//固定
                }
                spaNode.setRate1(nodeDetail.getMon());
                spaNode.setRate2(nodeDetail.getTue());
                spaNode.setRate3(nodeDetail.getWed());
                spaNode.setRate4(nodeDetail.getThus());
                spaNode.setRate5(nodeDetail.getFri());
                spaNode.setRate6(nodeDetail.getSat());
                spaNode.setRate7(nodeDetail.getSun());
                detailNode.getSpa().add(spaNode);
            } else if (nodeDetail.getType().equals(4)) {
                //门票明细
                StdKit_detail_ticketNode ticketNode = new StdKit_detail_ticketNode();
                ticketNode.setTicketcode(nodeDetail.getCode());
                ticketNode.setTicketname(nodeDetail.getDesc());
                ticketNode.setNum(nodeDetail.getNum());
                if (nodeDetail.getMode() == 2) {
                    ticketNode.setGroupid("1");//多选一
                } else {
                    ticketNode.setGroupid("-1");//固定
                }
                ticketNode.setRate1(nodeDetail.getMon());
                ticketNode.setRate2(nodeDetail.getTue());
                ticketNode.setRate3(nodeDetail.getWed());
                ticketNode.setRate4(nodeDetail.getThus());
                ticketNode.setRate5(nodeDetail.getFri());
                ticketNode.setRate6(nodeDetail.getSat());
                ticketNode.setRate7(nodeDetail.getSun());
                detailNode.getTicket().add(ticketNode);
            } else if (nodeDetail.getType().equals(5)) {
                //车船
                StdKit_detail_boatNode boatNode = new StdKit_detail_boatNode();
                boatNode.setCode(nodeDetail.getCode());
                boatNode.setName(nodeDetail.getDesc());
                boatNode.setNum(nodeDetail.getNum());
                if (nodeDetail.getMode() == 2) {
                    boatNode.setGroupid("1");//多选一
                } else {
                    boatNode.setGroupid("-1");//固定
                }
                boatNode.setRate1(nodeDetail.getMon());
                boatNode.setRate2(nodeDetail.getTue());
                boatNode.setRate3(nodeDetail.getWed());
                boatNode.setRate4(nodeDetail.getThus());
                boatNode.setRate5(nodeDetail.getFri());
                boatNode.setRate6(nodeDetail.getSat());
                boatNode.setRate7(nodeDetail.getSun());
                detailNode.getBoat().add(boatNode);
            } else if (nodeDetail.getType().equals(6)) {
                //导游
                StdKit_detail_guideNode guideNode = new StdKit_detail_guideNode();
                guideNode.setCode(nodeDetail.getCode());
                guideNode.setName(nodeDetail.getDesc());
                guideNode.setNum(nodeDetail.getNum());
                if (nodeDetail.getMode() == 2) {
                    guideNode.setGroupid("1");//多选一
                } else {
                    guideNode.setGroupid("-1");//固定
                }
                guideNode.setRate1(nodeDetail.getMon());
                guideNode.setRate2(nodeDetail.getTue());
                guideNode.setRate3(nodeDetail.getWed());
                guideNode.setRate4(nodeDetail.getThus());
                guideNode.setRate5(nodeDetail.getFri());
                guideNode.setRate6(nodeDetail.getSat());
                guideNode.setRate7(nodeDetail.getSun());
                detailNode.getGuide().add(guideNode);
            } else if (nodeDetail.getType().equals(7)) {
                //其他
                StdKit_detail_itemNode itemNode = new StdKit_detail_itemNode();
                itemNode.setCode(nodeDetail.getCode());
                itemNode.setName(nodeDetail.getDesc());
                itemNode.setNum(nodeDetail.getNum());
                if (nodeDetail.getMode() == 2) {
                    itemNode.setGroupid("1");//多选一
                } else {
                    itemNode.setGroupid("-1");//固定
                }
                itemNode.setRate1(nodeDetail.getMon());
                itemNode.setRate2(nodeDetail.getTue());
                itemNode.setRate3(nodeDetail.getWed());
                itemNode.setRate4(nodeDetail.getThus());
                itemNode.setRate5(nodeDetail.getFri());
                itemNode.setRate6(nodeDetail.getSat());
                itemNode.setRate7(nodeDetail.getSun());
                detailNode.getCustomitem().add(itemNode);
            }

        }
        return detailNode;
    }

    @Override
    public StdKitPriceQueryResponse queryKitPriceList(StdKitPriceQueryRequest request) throws DefinedException {
        StdKitPriceQueryResponse response = new StdKitPriceQueryResponse();
        response = xmsSyncLocalKititemPrice(request.getPackagecode(), request.getStartdate(), request.getEnddate(), request.getProjectId());
        //是否根据明细日期直接更新价格
        //XmsGetKitDetailReq outRequest = new XmsGetKitDetailReq();
        //XmsRoomClient client = getClient(request.getProjectId());
        //outRequest.setKitcode(request.getPackagecode());
        //XmsGetKitDetailRes res = client.execute(outRequest);
        ////获取套餐明细失败
        //if (!res.getSuccess()) {
        //    response.setStd_flag(false);
        //    return response;
        //}
        ////获取套餐明细后 按照日期填充价格信息
        //if (CollectionUtil.isNotEmpty(res.getResults())) {
        //    Date startDate = request.getStartdate();
        //    Date endDate = request.getEnddate();
        //    int days = (int) DateUtil.between(startDate, endDate, DateUnit.DAY);
        //    List<StdKitItemDailyPriceNode> priceNodeList = new ArrayList<>();
        //    if (days > 0) {
        //        for (int i = 0; i < days; i++) {
        //            StdKitItemDailyPriceNode priceNode = new StdKitItemDailyPriceNode();
        //            priceNode.setDate(DateUtil.offsetDay(startDate, i));
        //            for (KitDetailResult nodeDetail : res.getResults()) {
        //                if (nodeDetail.getType().equals(1)) {
        //                    //套餐客房类型
        //                    StdKit_price_roomNode roomNode = new StdKit_price_roomNode();
        //                    roomNode.setRoomtype(nodeDetail.getCode());
        //                    roomNode.setPrice(nodeDetail.getFri());
        //                    priceNode.getRoom().add(roomNode);
        //                } else if (nodeDetail.getType().equals(2)) {
        //                    //餐饮明细
        //                    StdKit_price_posNode posNode = new StdKit_price_posNode();
        //                    posNode.setPoscode(nodeDetail.getCode());
        //                    posNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()));
        //                    priceNode.getPos().add(posNode);
        //                } else if (nodeDetail.getType().equals(3)) {
        //                    //娱乐
        //                    StdKit_price_spaNode spaNode = new StdKit_price_spaNode();
        //                    spaNode.setCode(nodeDetail.getCode());
        //                    spaNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()));
        //                    priceNode.getSpa().add(spaNode);
        //                } else if (nodeDetail.getType().equals(4)) {
        //                    //门票明细
        //                    StdKit_price_ticketNode ticketNode = new StdKit_price_ticketNode();
        //                    ticketNode.setTicketcode(nodeDetail.getCode());
        //                    ticketNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()));
        //
        //                    priceNode.getTicket().add(ticketNode);
        //                } else if (nodeDetail.getType().equals(5)) {
        //                    //车船
        //                    StdKit_price_boatNode boatNode = new StdKit_price_boatNode();
        //                    boatNode.setCode(nodeDetail.getCode());
        //                    boatNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()).toString());
        //                    priceNode.getBoat().add(boatNode);
        //                } else if (nodeDetail.getType().equals(6)) {
        //                    //导游
        //                    StdKit_price_guideNode guideNode = new StdKit_price_guideNode();
        //                    guideNode.setCode(nodeDetail.getCode());
        //                    guideNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()).toString());
        //                    priceNode.getGuide().add(guideNode);
        //                } else if (nodeDetail.getType().equals(7)) {
        //                    //其他
        //                    StdKit_price_itemNode itemNode = new StdKit_price_itemNode();
        //                    itemNode.setCode(nodeDetail.getCode());
        //                    itemNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()));
        //                    priceNode.getCustomitem().add(itemNode);
        //                }
        //                //添加日期节点
        //                priceNodeList.add(priceNode);
        //            }
        //
        //        }
        //
        //    }else {
        //        //当天信息
        //        StdKitItemDailyPriceNode priceNode = new StdKitItemDailyPriceNode();
        //        priceNode.setDate(DateUtil.offsetDay(startDate, 0));
        //        for (KitDetailResult nodeDetail : res.getResults()) {
        //            if (nodeDetail.getType().equals(1)) {
        //                //套餐客房类型
        //                StdKit_price_roomNode roomNode = new StdKit_price_roomNode();
        //                roomNode.setRoomtype(nodeDetail.getCode());
        //                roomNode.setPrice(nodeDetail.getFri());
        //                priceNode.getRoom().add(roomNode);
        //            } else if (nodeDetail.getType().equals(2)) {
        //                //餐饮明细
        //                StdKit_price_posNode posNode = new StdKit_price_posNode();
        //                posNode.setPoscode(nodeDetail.getCode());
        //                posNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()));
        //                priceNode.getPos().add(posNode);
        //            } else if (nodeDetail.getType().equals(3)) {
        //                //娱乐
        //                StdKit_price_spaNode spaNode = new StdKit_price_spaNode();
        //                spaNode.setCode(nodeDetail.getCode());
        //                spaNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()));
        //                priceNode.getSpa().add(spaNode);
        //            } else if (nodeDetail.getType().equals(4)) {
        //                //门票明细
        //                StdKit_price_ticketNode ticketNode = new StdKit_price_ticketNode();
        //                ticketNode.setTicketcode(nodeDetail.getCode());
        //                ticketNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()));
        //
        //                priceNode.getTicket().add(ticketNode);
        //            } else if (nodeDetail.getType().equals(5)) {
        //                //车船
        //                StdKit_price_boatNode boatNode = new StdKit_price_boatNode();
        //                boatNode.setCode(nodeDetail.getCode());
        //                boatNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()).toString());
        //                priceNode.getBoat().add(boatNode);
        //            } else if (nodeDetail.getType().equals(6)) {
        //                //导游
        //                StdKit_price_guideNode guideNode = new StdKit_price_guideNode();
        //                guideNode.setCode(nodeDetail.getCode());
        //                guideNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()).toString());
        //                priceNode.getGuide().add(guideNode);
        //            } else if (nodeDetail.getType().equals(7)) {
        //                //其他
        //                StdKit_price_itemNode itemNode = new StdKit_price_itemNode();
        //                itemNode.setCode(nodeDetail.getCode());
        //                itemNode.setPrice(KitDetailResult.getPrice(nodeDetail, priceNode.getDate()));
        //                priceNode.getCustomitem().add(itemNode);
        //            }
        //            //添加日期节点
        //            priceNodeList.add(priceNode);
        //        }
        //    }
        //
        //    response.setData(priceNodeList);
        //}

        return response;
    }

    private StdKitPriceQueryResponse xmsSyncLocalKititemPrice(String kitCode, Date startDate, Date endDate, String projectId) {
        StdKitPriceQueryResponse response = new StdKitPriceQueryResponse();
        long days = CalculateDate.compareDates(endDate, startDate);
        int totalDays = (int) days;
        List<StdKitItemDailyPriceNode> stdKitItemDailyPriceNodeList = new ArrayList<>();
        KititemCache kititemCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITITEM);
        List<Kititem> kititemList = kititemCache.getDataList(projectId);
        List<Kititem> sysKititemList = kititemList.stream().filter(k -> k.getKitcode().equals(kitCode)).collect(Collectors.toList());
        //查数据库
        for (int i = 0; i <= totalDays; i++) {
            //套餐库存
            StdKitItemDailyPriceNode priceNode = new StdKitItemDailyPriceNode();
            Date datum = CalculateDate.reckonDay(startDate, 5, i);
            priceNode.setDate(datum);
            for (Kititem sysNode : sysKititemList) {
                if (sysNode.getProducttype().equals(ProdType.ROOM.val())) {
                    StdKit_price_roomNode roomNode = new StdKit_price_roomNode();
                    roomNode.setPrice(getLocalKititemWeekPrice(sysNode, datum));
                    roomNode.setRoomtype(sysNode.getProductcode());
                    priceNode.getRoom().add(roomNode);
                } else if (sysNode.getProducttype().equals(ProdType.TICKET.val())) {
                    StdKit_price_ticketNode ticketNode = new StdKit_price_ticketNode();
                    ticketNode.setPrice(getLocalKititemWeekPrice(sysNode, datum));
                    ticketNode.setTicketcode(sysNode.getProductcode());
                    priceNode.getTicket().add(ticketNode);
                } else if (sysNode.getProducttype().equals(ProdType.CANYIN.val())) {
                    StdKit_price_posNode posNode = new StdKit_price_posNode();
                    posNode.setPrice(getLocalKititemWeekPrice(sysNode, datum));
                    posNode.setPoscode(sysNode.getProductcode());
                    priceNode.getPos().add(posNode);
                } else if (sysNode.getProducttype().equals(ProdType.BOAT.val())) {
                    StdKit_price_boatNode boatNode = new StdKit_price_boatNode();
                    boatNode.setPrice(getLocalKititemWeekPrice(sysNode, datum) + "");
                    boatNode.setCode(sysNode.getProductcode());
                    priceNode.getBoat().add(boatNode);
                } else if (sysNode.getProducttype().equals(ProdType.KITITEM.val())) {
                    StdKit_price_itemNode itemNode = new StdKit_price_itemNode();
                    itemNode.setPrice(getLocalKititemWeekPrice(sysNode, datum));
                    itemNode.setCode(sysNode.getProductcode());
                    priceNode.getCustomitem().add(itemNode);
                } else if (sysNode.getProducttype().equals(ProdType.SPA.val())) {

                    StdKit_price_spaNode spaNode = new StdKit_price_spaNode();
                    spaNode.setPrice(getLocalKititemWeekPrice(sysNode, datum));
                    spaNode.setCode(sysNode.getProductcode());
                    priceNode.getSpa().add(spaNode);
                } else if (sysNode.getProducttype().equals(ProdType.GUIDE.val())) {

                    StdKit_price_guideNode guideNode = new StdKit_price_guideNode();
                    guideNode.setPrice(getLocalKititemWeekPrice(sysNode, datum) + "");
                    guideNode.setCode(sysNode.getProductcode());
                    priceNode.getGuide().add(guideNode);
                }

            }
            stdKitItemDailyPriceNodeList.add(priceNode);
        }

        response.setData(stdKitItemDailyPriceNodeList);
        return response;


    }


    /**
     * 返回kititem 对应的周一-周日价格
     *
     * @param sysNode
     * @param datum
     * @return
     */
    private BigDecimal getLocalKititemWeekPrice(Kititem sysNode, Date datum) {
        int week = CalculateDate.getDayinWeek(datum);
        switch (week) {
            case 1:
                return sysNode.getRate1();
            case 2:
                return sysNode.getRate2();
            case 3:
                return sysNode.getRate3();
            case 4:
                return sysNode.getRate4();
            case 5:
                return sysNode.getRate5();
            case 6:
                return sysNode.getRate6();
            case 0:
                return sysNode.getRate7();
        }
        return sysNode.getRate1();
    }

    /**
     * 查询
     *
     * @param request
     * @return
     * @throws DefinedException
     */
    @Override
    public StdKitAvailableQueryResponse queryKitAvailableList(StdKitAvailableQueryRequest request) throws DefinedException {
        StdKitAvailableQueryResponse response = new StdKitAvailableQueryResponse();
        //XmsGetKitDetailReq outRequest = new XmsGetKitDetailReq();
        //outRequest.setKitcode(request.getPackagecode());
        //XmsRoomClient client = getClient(request.getProjectId());
        //outRequest.setKitcode(request.getPackagecode());
        //XmsGetKitDetailRes res = client.execute(outRequest);
        ////获取套餐明细失败
        //if (!res.getSuccess()) {
        //    response.setStd_flag(false);
        //    return response;
        //}
        response = xmsSyncLocalKititemAvl(request.getStartdate(), request.getEnddate());


        return response;
    }

    /**
     * xms同步本地套餐库存
     *
     * @param startDate
     * @param endDate
     */
    private StdKitAvailableQueryResponse xmsSyncLocalKititemAvl(Date startDate, Date endDate) {
        StdKitAvailableQueryResponse response = new StdKitAvailableQueryResponse();
        long days = CalculateDate.compareDates(endDate, startDate);
        int totalDays = (int) days;
        List<StdSkuPriceNode> priceNodeList = new ArrayList<>();
        for (int i = 0; i <= totalDays; i++) {
            //套餐库存
            StdSkuPriceNode avlNode = new StdSkuPriceNode();
            avlNode.setDate(CalculateDate.reckonDay(startDate, 5, i));
            avlNode.setAvailnum(999);//默认同步先999
            priceNodeList.add(avlNode);
        }
        response.setData(priceNodeList);
        return response;

    }


    @Override
    public StdRestaurantQueryResponse queryRestaurantList(StdRestaurantQueryRequest request) throws DefinedException {
        XmsGetRestaurantReq outRequest = new XmsGetRestaurantReq().transfer(request);
        XmsRoomClient client = getClient(request.getProjectId());
        XmsGetRestaurantRes outResponse = client.execute(outRequest);
        StdRestaurantQueryResponse response = outResponse.getSysStdResponse();
        return response;
    }

    @Override
    public StdTicketListQueryResponse queryTicketList(StdTicketListQueryRequest request) throws DefinedException {
        XmsGetProductCodeReq productCodeReq = new XmsGetProductCodeReq();
        productCodeReq.setCat("ticket");

        XmsRoomClient client = getClient(request.getProjectId());
        XmsGetProductCodeRes outResponse = client.execute(productCodeReq);

        ProductCodeResponse ticketRes = outResponse.getSysStdResponse();
        StdTicketListQueryResponse response = new StdTicketListQueryResponse();
        if (!ticketRes.getStd_flag()) {
            response.setStd_flag(false);
        } else {
            if (CollectionUtil.isNotEmpty(outResponse.getResults())) {
                List<StdTicketListNode> nodeList = new ArrayList<>();
                for (ProductCodeResult ticket : outResponse.getResults()) {
                    StdTicketListNode node = new StdTicketListNode();
                    node.setTicketcode(ticket.getCode());
                    node.setTicketname(ticket.getDescript());
                    //xms 没有票型说法
                    nodeList.add(node);
                }
                response.setData(nodeList);
            }
        }
        return response;
    }
}
