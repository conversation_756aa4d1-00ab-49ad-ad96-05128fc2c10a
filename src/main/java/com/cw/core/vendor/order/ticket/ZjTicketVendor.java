package com.cw.core.vendor.order.ticket;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.cw.config.exception.CustomException;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.core.orderhandler.pojo.StdIdResult;
import com.cw.core.vendor.order.BaseOutSysVendor;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.platform.ZjPlatformClient;
import com.cw.outsys.pojo.zjplatform.request.OtaCancelTicketOrderRequest;
import com.cw.outsys.pojo.zjplatform.request.OtaTicketOrderQueryRequest;
import com.cw.outsys.pojo.zjplatform.request.OtaTicketOrderRequest;
import com.cw.outsys.pojo.zjplatform.response.OtaCancelTicketOrderResponse;
import com.cw.outsys.pojo.zjplatform.response.OtaOrderResponse;
import com.cw.outsys.pojo.zjplatform.response.OtaTicketOrderQueryResponse;
import com.cw.outsys.stdop.common.array.StdTicketSubStatusNode;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.log.impl.UserLogServiceImpl;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.TicketUtil;
import com.cw.utils.enums.VendorType;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/21 15:29
 **/
@VendorAdapter(vendorType = VendorType.ZJ_TICKET)
public class ZjTicketVendor extends BaseOutSysVendor<ZjPlatformClient> {

    //企业码-->appid
    //用户名--->userid
    //用户密码 -->userpwd

    @Override
    public StdOrderResponse createOrder(StdOrderData structure) throws DefinedException {
        String projectId = structure.getBookingRs().getProjectid();
        String bookingId = structure.getBookingRs().getBookingid();
        ZjPlatformClient client = getClient(projectId);

        OtaTicketOrderRequest request = new OtaTicketOrderRequest().setVendor(client.getConfig()).transfer(structure.toStdOrderReq());
        request.setSceniccode(client.getConfig().getOutid());

        OtaOrderResponse response = client.execute(request);

        StdOrderResponse stdOrderResponse = response.getSysStdResponse();
        LoggerFactory.getLogger(this.getClass()).info("中景门票系统处理器执行请求" + (stdOrderResponse.getStd_flag() ? "成功" : "失败"));
        StdIdResult result = new StdIdResult();
        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
        if (stdOrderResponse.getStd_flag()) {
            //TDOO 写LOG
            result.fillid(ProdType.MAIN.val(), bookingId, stdOrderResponse.getOutid());
            structure.getBookingRs().setOutid(stdOrderResponse.getOutid());
//            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
//                    bookingId, "票务接口发码成功，订单号：" + bookingId, projectId);
        } else {

//            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.NEW, SystemUtil.DEFAULTUSERID,
//                    bookingId, "创建订单失败，订单号：" + bookingId, projectId);
            String msg = stdOrderResponse.getStd_sub_msg().contains("身份证") ? stdOrderResponse.getStd_sub_msg() : "门票订单创建失败";
            msg = formatErrorMessage(msg);
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg(msg));
        }
        return stdOrderResponse;
    }

    public String formatErrorMessage(String errorMsg) {
        if (errorMsg == null || errorMsg.isEmpty()) {
            return errorMsg;
        }

        // 查找 "request id:" 的位置
        int requestIdIndex = errorMsg.indexOf(",request_id:");

        // 如果找到了 "request id:"，则只返回之前的部分
        if (requestIdIndex != -1) {
            return errorMsg.substring(0, requestIdIndex).trim();
        }

        // 如果没有找到 "request id:"，则返回原始消息
        return errorMsg;
    }


    @Override
    public StdCancelOrderResponse cancelOrder(StdCancelOrderRequest request) throws DefinedException {
        ZjPlatformClient client = getClient(request.getProjectId());
        //TODO 先查询订单是否已经检票

        StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setColno(request.getOtaorderid());

        OtaTicketOrderQueryRequest sdQueryReq = new OtaTicketOrderQueryRequest().transfer(queryRequest);
        sdQueryReq.setSceniccode(client.getConfig().getOutid());

        OtaTicketOrderQueryResponse queryRes = client.execute(sdQueryReq);
        if (!queryRes.getSysStdResponse().getStd_flag() || (queryRes.getData().getChecknum() != null && queryRes.getData().getChecknum() > 0)) {   //查询出错.说明订单不存在.直接取消订单
            StdCancelOrderResponse cancelOrderResponse = new StdCancelOrderResponse();
            cancelOrderResponse.setStd_flag(false);
            cancelOrderResponse.setStd_message("门票系统已检票.票务订单取消退款审核失败.");
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("门票系统已检票.票务订单取消退款审核失败."));
        }

        Integer ticketnum = request.getOrderDataContext().getBookingRs().getAnz();
        OtaCancelTicketOrderRequest cancelReq = new OtaCancelTicketOrderRequest().transfer(request);
        cancelReq.setSceniccode(client.getConfig().getOutid());
        cancelReq.setReturnnum(ticketnum);//取消要填退票数量

        OtaCancelTicketOrderResponse res = client.execute(cancelReq);
        StdCancelOrderResponse stdRes = res.getSysStdResponse();


//        UserLogServiceImpl userLogService = SpringUtil.getBean(UserLogServiceImpl.class);
//        if (!stdRes.getStd_flag()) {
//            //保存日志
//            userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
//                    request.getOtaorderid(), "取消中景门票订单失败，订单号：" + request.getOtaorderid(), request.getProjectId());
//            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("订单取消失败"));
//        }
//        userLogService.writeLog(SystemUtil.UserLogType.COL, SystemUtil.UserLogOpType.CANCEL, SystemUtil.DEFAULTUSERID,
//                request.getOtaorderid(), "取消订单成功，订单号：" + request.getOtaorderid(), request.getProjectId());
        return stdRes;
    }

    @Override
    public StdTicketQrCodeUrlResponse queryTicketQrCodeUrl(StdTicketQrCodeUrlRequest request) throws DefinedException {
        ZjPlatformClient client = getClient(request.getProjectId());

        StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setColno(request.getColno());

        OtaTicketOrderQueryRequest sdQueryReq = new OtaTicketOrderQueryRequest().transfer(queryRequest);
        sdQueryReq.setSceniccode(client.getConfig().getOutid());

        OtaTicketOrderQueryResponse queryRes = client.execute(sdQueryReq);

        StdTicketQueryResponse stdTicketQueryResponse = queryRes.getSysStdResponse();

        StdTicketQrCodeUrlResponse stdTicketQrCodeUrlResponse = new StdTicketQrCodeUrlResponse();
        stdTicketQrCodeUrlResponse.setStd_flag(stdTicketQueryResponse.getStd_flag());
        stdTicketQrCodeUrlResponse.setUrl(stdTicketQueryResponse.getQrcodeurl());

        if (!stdTicketQueryResponse.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("票务查询请求失败"));
        }
        return stdTicketQrCodeUrlResponse;
    }

    @Override
    public StdTicketQueryResponse queryTicket(StdTicketQueryRequest request) throws DefinedException {
        ZjPlatformClient client = getClient(request.getProjectId());

        OtaTicketOrderQueryRequest queryRequest = new OtaTicketOrderQueryRequest().transfer(request);
        queryRequest.setSceniccode(client.getConfig().getOutid());


        OtaTicketOrderQueryResponse queryRes = client.execute(queryRequest);

        StdTicketQueryResponse stdTicketQueryResponse = queryRes.getSysStdResponse();
        if (!stdTicketQueryResponse.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdTicketQueryResponse;
    }

    @Override
    public StdTicketQueryQrPicResponse queryTicketQrPic(StdTicketQueryQrPicRequest request) throws DefinedException {
        ZjPlatformClient client = getClient(request.getProjectId());

        StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setColno(request.getColno());

        OtaTicketOrderQueryRequest sdQueryReq = new OtaTicketOrderQueryRequest().transfer(queryRequest);
        sdQueryReq.setSceniccode(client.getConfig().getOutid());

        OtaTicketOrderQueryResponse queryRes = client.execute(sdQueryReq);
        String writeData = queryRes.getData().getWriteoffcode();

        if (StrUtil.isBlank(writeData)) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("门票接口查询失败"));
        }

        StdTicketQueryResponse stdTicketQueryResponse = queryRes.getSysStdResponse();

        StdTicketQueryQrPicResponse stdTicketQueryQrPicResponse = new StdTicketQueryQrPicResponse();
        stdTicketQueryQrPicResponse.setStd_flag(stdTicketQueryResponse.getStd_flag());

        QrConfig config = new QrConfig(280, 280);
        config.setRatio(4);


        //LoggerFactory.getLogger(this.getClass()).info(request.getColno() + "门票订单生成二维码数据：asscode:{},writeoffcode:{},查询响应:{}",
        //        request.getAssistCode(), queryRes.getData().getWriteoffcode(), JSON.toJSONString(queryRes));
        String base64qr = QrCodeUtil.generateAsBase64(writeData, config, ImgUtil.IMAGE_TYPE_PNG);
        base64qr = base64qr.replaceAll("data:image/png;base64,", "");

        stdTicketQueryQrPicResponse.setImg(base64qr);


        if (!stdTicketQueryQrPicResponse.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdTicketQueryQrPicResponse;
    }

    @Override
    public StdTicketQueryStatusResponse queryTicketStatus(StdTicketQueryStatusRequest request) throws DefinedException {
        ZjPlatformClient client = getClient(request.getProjectId());

        StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setColno(request.getColno());

        OtaTicketOrderQueryRequest sdQueryReq = new OtaTicketOrderQueryRequest().transfer(queryRequest);
        sdQueryReq.setSceniccode(client.getConfig().getOutid());

        OtaTicketOrderQueryResponse queryRes = client.execute(sdQueryReq);
        StdTicketQueryResponse stdTicketQueryResponse = queryRes.getSysStdResponse();

        StdTicketQueryStatusResponse stdTicketQueryStatusResponse = new StdTicketQueryStatusResponse();
        stdTicketQueryStatusResponse.setStd_flag(stdTicketQueryResponse.getStd_flag());
        List<StdTicketSubStatusNode> subStatus = new ArrayList<StdTicketSubStatusNode>();
        StdTicketSubStatusNode statusNode = new StdTicketSubStatusNode();
        statusNode.setChecknum(queryRes.getData().getChecknum());
        statusNode.setReturnnum(queryRes.getData().getReturnnum());
        //0-成功 1-订单不存在 2-线下接口关 闭 3-查询失败 4-取消审核中 5-取消审核成功 6-取 消审核失败 7-已使用 8-已完成
        statusNode.setStatus(changeZJTicketStatus(queryRes.getData().getQuerycode() + ""));
        subStatus.add(statusNode);

        stdTicketQueryStatusResponse.setSubOrders(subStatus);

        if (!stdTicketQueryStatusResponse.getStd_flag()) {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
        }
        return stdTicketQueryStatusResponse;
    }

    private String changeZJTicketStatus(String zjStatus) {
        if ("0123456".contains(zjStatus)) {
            return TicketUtil.TicketCheckStatus.UNCHECK.name();
        } else if ("78".contains(zjStatus)) {
            return TicketUtil.TicketCheckStatus.CHECKED.name();
        } else {
            return TicketUtil.TicketCheckStatus.UNCHECK.name();
        }
    }


    @Override
    public StdTicketSendMsgResponse sendTicketMsg(StdTicketSendMsgRequest request) throws DefinedException {
        //ZjPlatformClient client = getClient(request.getProjectId());
//        SDSendMsgReq sendMsgReq = new SDSendMsgReq().setVendor(client.getConfig()).transfer(request);
//        SDSendMsgRes res = client.execute(sendMsgReq);
//        StdTicketSendMsgResponse stdRes = res.getSysStdResponse();
//        if (!stdRes.getStd_flag()) {
//            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("请求失败"));
//        }
        return new StdTicketSendMsgResponse();
    }

    @Override
    public StdTicketChangeDateResponse changeTicketDate(StdTicketChangeDateRequest request) throws DefinedException {
        return new StdTicketChangeDateResponse();
    }

    @Override
    public StdTicketChangeNumResponse changeTicketRoom(StdTicketChangeNumRequest request) throws DefinedException {
        return new StdTicketChangeNumResponse();
    }

    @Override
    public StdTicketQueryCancelStatusResponse queryTicketCacnelStatus(StdTicketQueryCanelStatusRequest request) throws DefinedException {
        ZjPlatformClient client = getClient(request.getProjectId());

        StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();
        queryRequest.setProjectId(request.getProjectId());
        queryRequest.setColno(request.getColno());

        OtaTicketOrderQueryRequest sdQueryReq = new OtaTicketOrderQueryRequest().transfer(queryRequest);
        sdQueryReq.setSceniccode(client.getConfig().getOutid());

        boolean lsuccess = false;

        OtaTicketOrderQueryResponse queryRes = client.execute(sdQueryReq);
        if (queryRes.getData() != null) {
            if ("5".equals(queryRes.getData().getQuerycode() + "")) {
                lsuccess = true;
            }
        }


        StdTicketQueryCancelStatusResponse stdRes = new StdTicketQueryCancelStatusResponse();
        stdRes.setStd_flag(lsuccess);//这里不处理异常

        return stdRes;
    }

    @Override
    public StdTicketReturnNumResponse returnTicketNum(StdTicketReturnNumRequest request) throws DefinedException {
        ZjPlatformClient client = getClient(request.getProjectId());

        OtaCancelTicketOrderRequest cancelReq = new OtaCancelTicketOrderRequest();
        cancelReq.setSceniccode(client.getConfig().getOutid());
        cancelReq.setReturnnum(Integer.parseInt(request.getReturnNum()));//取消要填退票数量
        cancelReq.setOtaorderid(request.getBookingId());

        OtaCancelTicketOrderResponse res = client.execute(cancelReq);  //中景接口退票 就是取消
        StdCancelOrderResponse stdCancelOrderResponse = res.getSysStdResponse();

        if (stdCancelOrderResponse.getStd_flag()) {
           /* StdTicketQueryRequest queryRequest = new StdTicketQueryRequest();  取消前的预检查已经做了.这里不再查询.票务人工审核还会再做一次
            queryRequest.setProjectId(request.getProjectId());
            queryRequest.setColno(request.getBookingId());

            OtaTicketOrderQueryRequest sdQueryReq = new OtaTicketOrderQueryRequest().transfer(queryRequest);
            sdQueryReq.setSceniccode(client.getConfig().getOutid());


            OtaTicketOrderQueryResponse queryRes = client.execute(sdQueryReq);
            if (!queryRes.getSysStdResponse().getStd_flag() || (queryRes.getData().getChecknum() > 0)) {   //查询出错.说明订单不存在.直接取消订单
                StdTicketReturnNumResponse cancelOrderResponse = new StdTicketReturnNumResponse();
                cancelOrderResponse.setStd_flag(false);
                cancelOrderResponse.setStd_message("已检票.不可线上取消");
                return cancelOrderResponse;
            }else{
                System.out.println("退票结果确认查询成功");
            }*/

            StdTicketReturnNumResponse stdTicketReturnNumResponse = new StdTicketReturnNumResponse();
            stdTicketReturnNumResponse.setStd_flag(true);
            stdTicketReturnNumResponse.setOutCancelNo(RandomUtil.randomString(6));//中景没有这个.只是为了后面审核的时候可以发起查询
            return stdTicketReturnNumResponse;
        } else {
            throw new CustomException(ResultJson.failure(ResultCode.PMSERR).msg("门票退票失败"));
        }
    }
}
