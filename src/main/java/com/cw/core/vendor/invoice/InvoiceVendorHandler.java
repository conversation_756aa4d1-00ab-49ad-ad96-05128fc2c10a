package com.cw.core.vendor.invoice;

import com.cw.entity.Vendorconfig;
import com.cw.exception.DefinedException;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-02-28
 */
public interface InvoiceVendorHandler {
    void initClient();

    void refreshClientConfig(Vendorconfig vendorconfig);

    default StdInvoiceCreateResponse InvoiceCreate(StdInvoiceCreateRequest stdInvoiceCreateRequest) throws DefinedException {
        return new StdInvoiceCreateResponse();
    }

    default StdInvoiceQueryResponse InvoiceQuery(StdInvoiceQueryRequest stdInvoiceQueryRequest) throws DefinedException {
        return new StdInvoiceQueryResponse();
    }

    default StdInvoiceCancelResponse InvoiceCancel(StdInvoiceCancelRequest stdInvoiceCancelRequest) throws DefinedException {
        return new StdInvoiceCancelResponse();
    }

    default StdInvoicePushResponse InvoicePush(StdInvoicePushRequest stdInvoicePushRequest) throws DefinedException {
        return new StdInvoicePushResponse();
    }

    default StdInvoiceReCreateResponse InvoiceReCreate(StdInvoiceReCreateRequest stdInvoiceReCreateRequest) throws DefinedException {
        return new StdInvoiceReCreateResponse();
    }

    /**
     * 发票红冲 诺诺发票对票种数电发票 pc的特殊红冲开票
     *
     * @param stdInvoiceRedRequest
     * @return
     * @throws DefinedException
     */
    default StdInvoiceRedResponse NNInvoiceRedCreate(StdInvoiceRedRequest stdInvoiceRedRequest) throws DefinedException {
        return new StdInvoiceRedResponse();
    }

    /**
     * 发票红冲 红字确认单申请
     *
     * @param stdInvoiceRedConfirmRequest
     * @return
     * @throws DefinedException
     */
    default StdInvoiceRedConfirmResponse NNInvoiceRedCreateConfirm(StdInvoiceRedConfirmRequest stdInvoiceRedConfirmRequest) throws DefinedException {
        return new StdInvoiceRedConfirmResponse();
    }

    /**
     * 查询红字确认单申请
     *
     * @param stdInvoiceQueryRedConfirmRequest
     * @return
     * @throws DefinedException
     */
    default StdInvoiceQueryRedConfirmResponse NNInvoiceQueryRedCreateConfirm(StdInvoiceQueryRedConfirmRequest stdInvoiceQueryRedConfirmRequest) throws DefinedException {
        return new StdInvoiceQueryRedConfirmResponse();
    }
}
