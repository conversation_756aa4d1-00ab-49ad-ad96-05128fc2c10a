package com.cw.core.vendor.invoice;

import com.cw.core.orderhandler.VendorAdapter;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.invoice.NNInvoiceClient;
import com.cw.outsys.pojo.invoice.request.*;
import com.cw.outsys.pojo.invoice.response.*;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import com.cw.utils.enums.VendorType;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-11
 */
@VendorAdapter(vendorType = VendorType.NN_INVOICE)
public class NNInvoiceVendor extends BaseOutInvoiceVendor<NNInvoiceClient> {

    /**
     * 开局发票
     *
     * @param stdInvoiceCreateRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoiceCreateResponse InvoiceCreate(StdInvoiceCreateRequest stdInvoiceCreateRequest) throws DefinedException {
        NNInvoiceClient client = getClient(stdInvoiceCreateRequest.getProjectId());
        NNInvoiceCreateRequest request = new NNInvoiceCreateRequest().transfer(stdInvoiceCreateRequest);
        NNInvoiceCreateResponse response = client.execute(request);
        StdInvoiceCreateResponse stdInvoiceCreateResponse = response.getSysStdResponse();
        return stdInvoiceCreateResponse;
    }

    /**
     * 查询发票
     *
     * @param stdInvoiceQueryRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoiceQueryResponse InvoiceQuery(StdInvoiceQueryRequest stdInvoiceQueryRequest) throws DefinedException {
        NNInvoiceClient client = getClient(stdInvoiceQueryRequest.getProjectId());
        NNInvoiceQueryRequest request = new NNInvoiceQueryRequest().transfer(stdInvoiceQueryRequest);
        NNInvoiceQueryResponse response = client.execute(request);
        StdInvoiceQueryResponse stdInvoiceQueryResponse = response.getSysStdResponse();
        return stdInvoiceQueryResponse;
    }

    ///**
    // * 作废发票
    // *
    // * @param stdInvoiceCancelRequest
    // * @return
    // * @throws DefinedException
    // */
    //@Override
    //public StdInvoiceCancelResponse InvoiceCancel(StdInvoiceCancelRequest stdInvoiceCancelRequest) throws DefinedException {
    //    NNInvoiceClient client = getClient(stdInvoiceCancelRequest.getProjectId());
    //    NNInvoiceCancelRequest request = new NNInvoiceCancelRequest().transfer(stdInvoiceCancelRequest);
    //    NNInvoiceCancelResponse response = client.execute(request);
    //    StdInvoiceCancelResponse stdInvoiceCancelResponse = response.getSysStdResponse();
    //    return stdInvoiceCancelResponse;
    //}

    /**
     * 发票推送 修改接收方式重发
     *
     * @param stdInvoicePushRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoicePushResponse InvoicePush(StdInvoicePushRequest stdInvoicePushRequest) throws DefinedException {
        NNInvoiceClient client = getClient(stdInvoicePushRequest.getProjectId());
        NNInvoicePushRequest request = new NNInvoicePushRequest().transfer(stdInvoicePushRequest);
        NNInvoicePushResponse response = client.execute(request);
        StdInvoicePushResponse stdInvoicePushResponse = response.getSysStdResponse();
        return stdInvoicePushResponse;
    }


    /**
     * 发票开具失败后重试
     *
     * @param stdInvoiceReCreateRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoiceReCreateResponse InvoiceReCreate(StdInvoiceReCreateRequest stdInvoiceReCreateRequest) throws DefinedException {
        NNInvoiceClient client = getClient(stdInvoiceReCreateRequest.getProjectId());
        NNInvoiceReCreateRequest request = new NNInvoiceReCreateRequest().transfer(stdInvoiceReCreateRequest);
        NNInvoiceReCreateResponse response = client.execute(request);
        StdInvoiceReCreateResponse stdInvoiceReCreateResponse = response.getSysStdResponse();
        return stdInvoiceReCreateResponse;
    }

    /**
     * 诺诺发票数电发票类型 极速红冲
     *
     * @param stdInvoiceRedRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoiceRedResponse NNInvoiceRedCreate(StdInvoiceRedRequest stdInvoiceRedRequest) throws DefinedException {
        NNInvoiceClient client = getClient(stdInvoiceRedRequest.getProjectId());
        NNInvoiceRedRequest request = new NNInvoiceRedRequest().transfer(stdInvoiceRedRequest);
        NNInvoiceRedResponse response = client.execute(request);
        StdInvoiceRedResponse stdInvoiceRedResponse = response.getSysStdResponse();
        return stdInvoiceRedResponse;
    }

    @Override
    public StdInvoiceRedConfirmResponse NNInvoiceRedCreateConfirm(StdInvoiceRedConfirmRequest stdInvoiceRedConfirmRequest) throws DefinedException {
        NNInvoiceClient client = getClient(stdInvoiceRedConfirmRequest.getProjectId());
        NNInvoiceRedConfirmRequest request = new NNInvoiceRedConfirmRequest().transfer(stdInvoiceRedConfirmRequest);
        NNInvoiceRedConfirmResponse response = client.execute(request);
        StdInvoiceRedConfirmResponse stdInvoiceRedResponse = response.getSysStdResponse();
        return stdInvoiceRedResponse;
    }

    @Override
    public StdInvoiceQueryRedConfirmResponse NNInvoiceQueryRedCreateConfirm(StdInvoiceQueryRedConfirmRequest stdInvoiceQueryRedConfirmRequest) throws DefinedException {
        NNInvoiceClient client = getClient(stdInvoiceQueryRedConfirmRequest.getProjectId());
        NNInvoiceQueryRedConfirmRequest request = new NNInvoiceQueryRedConfirmRequest().transfer(stdInvoiceQueryRedConfirmRequest);
        NNInvoiceQueryRedConfirmResponse response = client.execute(request);
        StdInvoiceQueryRedConfirmResponse stdInvoiceRedResponse = response.getSysStdResponse();
        return stdInvoiceRedResponse;
    }
}
