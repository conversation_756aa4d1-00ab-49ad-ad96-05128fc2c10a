package com.cw.core.vendor.invoice;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.util.ReflectUtil;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.VendorConfigCache;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.entity.Vendorconfig;
import com.cw.outsys.client.BaseSysClient;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;
import org.slf4j.LoggerFactory;

import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Map;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-02-28
 */
public abstract class BaseOutInvoiceVendor<T extends BaseSysClient> implements InvoiceVendorHandler {

    protected Map<String, T> clientMap = Maps.newConcurrentMap();

    @Override
    public void initClient() {
        VendorAdapter adapter = AnnotationUtil.getAnnotation(this.getClass(), VendorAdapter.class);
        if (adapter != null) {
            VendorConfigCache configCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.VENDORCONFIG);
            List<Vendorconfig> configs = configCache.getVendorTypeConfig(adapter.vendorType());
            if (configs.size() > 0) {
                for (Vendorconfig vendorconfig : configCache.getVendorTypeConfig(adapter.vendorType())) {
                    updateClient(vendorconfig);
                    LoggerFactory.getLogger(this.getClass()).info("{} {}初始化客户端完成", vendorconfig.getProjectid(), vendorconfig.getVtype());
                }
            }
        }
    }

    @Override
    public void refreshClientConfig(Vendorconfig vendorconfig) {
        updateClient(vendorconfig);
    }

    protected T getClient(String projectid) {
        if (!clientMap.containsKey(projectid)) {
            throw new RuntimeException("没有找到" + projectid + "对应的 client");
        } else {
            return clientMap.get(projectid);
        }
    }


    public void updateClient(Vendorconfig vendorconfig) {
        BaseSysClient client = clientMap.get(vendorconfig.getProjectid());
        Class<T> transclass = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
        if (client == null) {
            client = ReflectUtil.newInstance(transclass);
        }
        client.setConfig(vendorconfig);
        client.init();
        clientMap.put(vendorconfig.getProjectid(), (T) client);
    }

    public void removeClient(String projectid) {
        clientMap.remove(projectid);
    }
}
