package com.cw.core.vendor.invoice;

import com.cw.core.orderhandler.VendorAdapter;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.invoice.GZInvoiceClient;
import com.cw.outsys.pojo.invoice.request.GZInvoiceCancelRequest;
import com.cw.outsys.pojo.invoice.request.GZInvoiceCreateRequest;
import com.cw.outsys.pojo.invoice.request.GZInvoicePushRequest;
import com.cw.outsys.pojo.invoice.request.GZInvoiceQueryRequest;
import com.cw.outsys.pojo.invoice.response.GZInvoiceCancelResponse;
import com.cw.outsys.pojo.invoice.response.GZInvoiceCreateResponse;
import com.cw.outsys.pojo.invoice.response.GZInvoicePushResponse;
import com.cw.outsys.pojo.invoice.response.GZInvoiceQueryResponse;
import com.cw.outsys.stdop.request.StdInvoiceCancelRequest;
import com.cw.outsys.stdop.request.StdInvoiceCreateRequest;
import com.cw.outsys.stdop.request.StdInvoicePushRequest;
import com.cw.outsys.stdop.request.StdInvoiceQueryRequest;
import com.cw.outsys.stdop.response.StdInvoiceCancelResponse;
import com.cw.outsys.stdop.response.StdInvoiceCreateResponse;
import com.cw.outsys.stdop.response.StdInvoicePushResponse;
import com.cw.outsys.stdop.response.StdInvoiceQueryResponse;
import com.cw.utils.enums.VendorType;

/**
 * @Describe
 * <AUTHOR> Tony Leung
 * @Create on 2023-02-28
 */
@VendorAdapter(vendorType = VendorType.GZ_INVOICE)
public class GZInvoiceVendor extends BaseOutInvoiceVendor<GZInvoiceClient> {

    /**
     * 开局发票
     *
     * @param stdInvoiceCreateRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoiceCreateResponse InvoiceCreate(StdInvoiceCreateRequest stdInvoiceCreateRequest) throws DefinedException {
        GZInvoiceClient client = getClient(stdInvoiceCreateRequest.getProjectId());
        GZInvoiceCreateRequest request = new GZInvoiceCreateRequest().transfer(stdInvoiceCreateRequest);
        GZInvoiceCreateResponse response = client.execute(request);
        StdInvoiceCreateResponse stdInvoiceCreateResponse = response.getSysStdResponse();
        return stdInvoiceCreateResponse;
    }

    /**
     * 查询发票
     *
     * @param stdInvoiceQueryRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoiceQueryResponse InvoiceQuery(StdInvoiceQueryRequest stdInvoiceQueryRequest) throws DefinedException {
        GZInvoiceClient client = getClient(stdInvoiceQueryRequest.getProjectId());
        GZInvoiceQueryRequest request = new GZInvoiceQueryRequest().transfer(stdInvoiceQueryRequest);
        GZInvoiceQueryResponse response = client.execute(request);
        StdInvoiceQueryResponse stdInvoiceQueryResponse = response.getSysStdResponse();
        return stdInvoiceQueryResponse;
    }

    /**
     * 作废发票
     *
     * @param stdInvoiceCancelRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoiceCancelResponse InvoiceCancel(StdInvoiceCancelRequest stdInvoiceCancelRequest) throws DefinedException {
        GZInvoiceClient client = getClient(stdInvoiceCancelRequest.getProjectId());
        GZInvoiceCancelRequest request = new GZInvoiceCancelRequest().transfer(stdInvoiceCancelRequest);
        GZInvoiceCancelResponse response = client.execute(request);
        StdInvoiceCancelResponse stdInvoiceCancelResponse = response.getSysStdResponse();
        return stdInvoiceCancelResponse;
    }

    /**
     * 发票推送
     *
     * @param stdInvoicePushRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoicePushResponse InvoicePush(StdInvoicePushRequest stdInvoicePushRequest) throws DefinedException {
        GZInvoiceClient client = getClient(stdInvoicePushRequest.getProjectId());
        GZInvoicePushRequest request = new GZInvoicePushRequest().transfer(stdInvoicePushRequest);
        GZInvoicePushResponse response = client.execute(request);
        StdInvoicePushResponse stdInvoicePushResponse = response.getSysStdResponse();
        return stdInvoicePushResponse;
    }

}
