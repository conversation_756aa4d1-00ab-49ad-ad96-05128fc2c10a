package com.cw.core.vendor.invoice;

import com.cw.core.orderhandler.VendorAdapter;
import com.cw.exception.DefinedException;
import com.cw.outsys.client.impl.invoice.JLInvoiceClient;
import com.cw.outsys.pojo.invoice.request.JLInvoiceCreateRequest;
import com.cw.outsys.pojo.invoice.request.JLInvoiceQueryRequest;
import com.cw.outsys.pojo.invoice.response.JLInvoiceCreateResponse;
import com.cw.outsys.pojo.invoice.response.JLInvoiceQueryResponse;
import com.cw.outsys.stdop.request.StdInvoiceCreateRequest;
import com.cw.outsys.stdop.request.StdInvoiceQueryRequest;
import com.cw.outsys.stdop.response.StdInvoiceCreateResponse;
import com.cw.outsys.stdop.response.StdInvoiceQueryResponse;
import com.cw.utils.en<PERSON>.VendorType;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-10-23
 */
@VendorAdapter(vendorType = VendorType.JL_INVOICE)
public class JLInvoiceVendor extends BaseOutInvoiceVendor<JLInvoiceClient> {

    /**
     * 开局发票
     *
     * @param stdInvoiceCreateRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoiceCreateResponse InvoiceCreate(StdInvoiceCreateRequest stdInvoiceCreateRequest) throws DefinedException {
        JLInvoiceClient client = getClient(stdInvoiceCreateRequest.getProjectId());
        JLInvoiceCreateRequest request = new JLInvoiceCreateRequest().transfer(stdInvoiceCreateRequest);
        JLInvoiceCreateResponse response = client.execute(request);
        StdInvoiceCreateResponse stdInvoiceCreateResponse = response.getSysStdResponse();
        return stdInvoiceCreateResponse;
    }

    /**
     * 查询发票
     *
     * @param stdInvoiceQueryRequest
     * @return
     * @throws DefinedException
     */
    @Override
    public StdInvoiceQueryResponse InvoiceQuery(StdInvoiceQueryRequest stdInvoiceQueryRequest) throws DefinedException {
        JLInvoiceClient client = getClient(stdInvoiceQueryRequest.getProjectId());
        JLInvoiceQueryRequest request = new JLInvoiceQueryRequest().transfer(stdInvoiceQueryRequest);
        JLInvoiceQueryResponse response = client.execute(request);
        StdInvoiceQueryResponse stdInvoiceQueryResponse = response.getSysStdResponse();
        return stdInvoiceQueryResponse;
    }
}
