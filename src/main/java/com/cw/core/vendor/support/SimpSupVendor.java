package com.cw.core.vendor.support;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.DES;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.core.orderhandler.VendorAdapter;
import com.cw.entity.Vendorconfig;
import com.cw.outsys.client.impl.sup.SimpSupClient;
import com.cw.outsys.pojo.simpsup.request.SimpCallInUrlRequest;
import com.cw.outsys.stdop.request.StdSupCallInRequest;
import com.cw.utils.enums.VendorType;

import java.nio.charset.StandardCharsets;


/**
 * 信普飞科 呼叫适配器
 */
@VendorAdapter(vendorType = VendorType.SIMP_SUP)
public class SimpSupVendor extends BaseOutSupVendor<SimpSupClient> {

    @Override
    public String getCallInUrl(StdSupCallInRequest stdRequest) {
        SimpSupClient client = getClient(stdRequest.getProjectId());
        Vendorconfig vendorconfig = client.getConfig();
        String url = vendorconfig.getUrl() + "?param=";

        SimpCallInUrlRequest simpRequest = new SimpCallInUrlRequest();//声明信普飞科请求参数
        simpRequest.setName(stdRequest.getName());//姓名
        simpRequest.setPhoneNum(stdRequest.getPhoneNum());//手机号
        if (!stdRequest.isLmobile()) {
            simpRequest.setSourceTag("orig");//来源.PC还是移动端
        } else {
            simpRequest.setSourceTag("mobile");
            simpRequest.setParam1("mobile");
        }
        simpRequest.setDestID("9001");//技能码.不知道是什么东西

        String param = getParamVal(vendorconfig.getAppsecrect(), simpRequest);//加密一堆参数.拼接头像
        String result = url + param;
        if (StrUtil.isNotBlank(stdRequest.getAvatarUrl())) {
            result += "&avatar=" + stdRequest.getAvatarUrl();
        }
        return result;
    }

    private String getParamVal(String key, SimpCallInUrlRequest request) {
        String needEncryStr = SysFuncLibTool.getPostUrlParamStr(request);
        return secParam(key, needEncryStr);
    }

    public String secParam(String secKey, String postStr) {
        byte[] key = secKey.getBytes(StandardCharsets.UTF_8);
        DES des = SecureUtil.des(key);
        String enStr = des.encryptHex(postStr);
        return enStr;
    }
}
