package com.cw.core.vendor.support;

import com.cw.entity.Vendorconfig;
import com.cw.outsys.stdop.request.StdSupCallInRequest;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-28
 */
public interface SupVendorHandler {

    void initClient();

    void refreshClientConfig(Vendorconfig vendorconfig);

    default String getCallInUrl(StdSupCallInRequest request) {
        return "";
    }


}
