package com.cw.core;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.config.exception.CustomException;
import com.cw.core.report.RpFactory;
import com.cw.core.report.base.BaseRp;
import com.cw.core.report.base.RpInputCompent;
import com.cw.core.report.base.RpPrint;
import com.cw.core.report.base.RpPrintColumn;
import com.cw.core.report.base.output.RpoutputConfig;
import com.cw.core.report.impl.ProduceRp;
import com.cw.core.report.manual.RpSql_Standard;
import com.cw.entity.Rptemplate;
import com.cw.mapper.RptemplateMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.statistic.req.RpPostForm;
import com.cw.service.oss.OSSService;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.rp.RpInputCompType;
import com.cw.utils.rp.RpType;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.renderer.TableRenderer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/8/28 12:01
 **/
@Slf4j
@Service
public class CoreRp {

    @Autowired
    DaoLocal<?> daoLocal;

    @Resource(name = "${oss.type}")
    OSSService ossService;


    public void outputSqlReport(String rpid) {


    }

    /**
     * @param rpid 报表模板id
     * @return
     */
    public Rptemplate loadTemplate(String rpid) {
        RptemplateMapper rptemplateMapper = SpringUtil.getBean(RptemplateMapper.class);
        Rptemplate rptemplate = rptemplateMapper.findRptemplateByRpid(rpid);
        return rptemplate;
    }


    /**
     * @param rpPostForm 报表模板
     *
     * @return
     */
    public BaseRp getRpPrint(RpPostForm rpPostForm) {
        Rptemplate rptemplate = loadTemplate(rpPostForm.getRpId());
        BaseRp rp = null;
        if (rptemplate.getType().equals(RpType.SQL.name())) {//标准SQL报表
            rp = new RpSql_Standard(rptemplate.getDescription());//将用户配置的报表名称加载进来
            rp.setPrintColumns(JSON.parseArray(rptemplate.getColumn(), RpPrintColumn.class));
            rp.setRpoutputConfig(JSON.parseObject(rptemplate.getOutputconfig(), RpoutputConfig.class));

            fillSqlReport(rp, rptemplate, rpPostForm);

        } else {//程序化报表
            rp = RpFactory.getProduceRp(rptemplate.getClassname());
            rp.setPrintColumns(rp.getInitPrintColumn());  //字段打印列
            fillProcReport(rp, rptemplate, rpPostForm);
        }
        return rp;
    }


    public RpPrint fillSqlReport(BaseRp rpPrint, Rptemplate rptemplate, RpPostForm rpPostForm) {
        String templateSql = rptemplate.getSqlstr();//读取配置中书写的SQL
        List<String> columns = RpFactory.getColumns(templateSql);  //展开SQL中的字段

        List<RpInputCompent> compents = rpPrint.getinitQueryForm();
        if (compents != null) {
            for (RpInputCompent compent : compents) {//修正处理参数类型
                if (rpPostForm.getData().containsKey(compent.getParam())) {
                    if (compent.getCompType().equals(RpInputCompType.DATERANGE)) {//针对日期类型的表单做下处理
                        String[] params = compent.getParam().split("|");
                        String[] values = (rpPostForm.getData().get(compent.getParam()) + "").split("|");
                        if (params.length == 2 && values.length == 2) {
                            rpPostForm.getData().put(params[0], CalculateDate.stringToDate(values[0]));
                            rpPostForm.getData().put(params[1], CalculateDate.stringToDate(values[1]));
                        }
                    }
                    if (compent.getCompType().equals(RpInputCompType.DATE)) {//针对日期类型的表单做下处理
                        (rpPostForm.getData()).put(compent.getParam(),
                                CalculateDate.stringToDate(rpPostForm.getData().get(compent.getParam()) + ""));
                    }

                }
            }
        }
        List<Object[]> queryData = daoLocal.queryObjectListWithLimit(templateSql, 10000, rpPostForm.getData());

        try {
            String url = generatePdfFile2(rpPrint, queryData);
            rpPrint.setShowUrl(url);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("生成报表文件失败"));
        }


        return rpPrint;
    }


    public RpPrint fillProcReport(BaseRp rpPrint, Rptemplate rptemplate, RpPostForm rpPostForm) {
        ProduceRp produceRp = (ProduceRp) rpPrint;

        produceRp.fillRpPrint(rpPrint, rpPostForm);

        return rpPrint;
    }

    public String generatePdfFile(BaseRp rpPrint, List<Object[]> tableData) throws Exception {
        String fileName = LocalDateTime.now() + RandomUtil.randomString(5) + "report.pdf";
        PdfDocument pdfDocument = new PdfDocument(new PdfWriter(fileName));

        //根据配置设置横向或者是竖向打印.暂时默认为A4纸张大小
        PageSize pageSize = new PageSize(PageSize.A4);
        boolean lhorizon = rpPrint.getRpoutputConfig().isLHorizontalOutput();
        int pageMaxRow = lhorizon ? 20 : 50;  //横打的话.每页20行. 竖打每页50行
        if (lhorizon) {
            pageSize.rotate();
        }

        Document document = new Document(pdfDocument, pageSize);



        // 添加报表头
        Paragraph header = new Paragraph(rpPrint.getRpname() + "测试中文报表名");
        header.setTextAlignment(com.itextpdf.layout.properties.TextAlignment.LEFT).setBold().setFontSize(24);
        document.add(header);


        // 添加副标题
        Paragraph subtitle = new Paragraph(StrUtil.format("打印时间：{}", LocalDateTime.now()));
        subtitle.setTextAlignment(com.itextpdf.layout.properties.TextAlignment.LEFT).setBold().setFontSize(18);
        document.add(subtitle);


        // 添加表头
        Table table = new Table(rpPrint.getInitPrintColumn().size());
        table.setBorder(Border.NO_BORDER);
        for (RpPrintColumn printColumn : rpPrint.getInitPrintColumn()) {//按照表格表头顺序输出表格数据
            Cell cell = new Cell();
            cell.setFontSize(rpPrint.getRpoutputConfig().getTbaleFontSize());
            cell.add(new Paragraph(printColumn.getColDispVal()));
            table.addHeaderCell(cell);
            table.addCell(printColumn.getColDispVal());   //表头
        }

        // 添加表格数据
        for (int i = 0; i < tableData.size(); i++) {
            Object[] rows = tableData.get(0);//每行数据
            if (i > 0 && i % pageMaxRow == 0) {
                int pageNumber = i / pageMaxRow + 1;
                Paragraph pageNumberParagraph = new Paragraph(StrUtil.format("第{}页", pageNumber));
                pageNumberParagraph.setTextAlignment(TextAlignment.CENTER).setBold().setFontSize(10);
                document.add(pageNumberParagraph);

                document.add(new AreaBreak());
                document.add(header);
                document.add(subtitle);
                //document.add(table.getHeader());
                // 添加表格列头
                TableRenderer tableRenderer = (TableRenderer) table.createRendererSubTree();
                tableRenderer.setParent(document.getRenderer());
                document.getRenderer().addChild(tableRenderer);
            }

            int lastPageNumber = (tableData.size() - 1) / pageMaxRow + 1;
            Paragraph lastPageNumberParagraph = new Paragraph(StrUtil.format("第{}页", lastPageNumber));
            lastPageNumberParagraph.setTextAlignment(TextAlignment.CENTER).setBold().setFontSize(10);
            document.add(lastPageNumberParagraph);

        }


        //document.add(table);

        // 添加表格列头
        TableRenderer tableRenderer = (TableRenderer) table.createRendererSubTree();
        tableRenderer.setParent(document.getRenderer());
        document.getRenderer().addChild(tableRenderer);

        // 关闭文档
        document.close();
        //
        //// 创建 OSS 客户端
        //OSSClient ossClient = new OSSClient(ACCESS_KEY_ID, ACCESS_KEY_SECRET);
        //
        //// 上传文件
        //PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET_NAME, OBJECT_KEY, new File("report.pdf"));
        //ossClient.putObject(putObjectRequest);
        //
        //// 关闭 OSS 客户端
        //ossClient.shutdown();

        // 删除临时文件
        File file = new File(fileName);

        return "http://127.0.0.1:8080/report/";
        //OSSUploadFileRes ossUploadFileRes = ossService.uploadLocalFile(file, "report", "0", "001");//TODO  projectId  改成从全局获取
        //log.info("生成报表文件:{}", ossUploadFileRes.getOssFileUrl());
        //
        //boolean ldel = file.delete();

        //log.info("删除文件{}", ldel ? "成功" : "失败");

        //rpPrint.setShowUrl(ossUploadFileRes.getOssFileUrl());

        //return ossUploadFileRes.getOssFileUrl();
    }


    public String generatePdfFile2(BaseRp rpPrint, List<Object[]> tableData) {
        String pdfPath = LocalDateTime.now() + "report.pdf";

        try {
            PdfWriter writer = new PdfWriter(new FileOutputStream(pdfPath));
            PdfDocument pdf = new PdfDocument(writer);
            Document document = new Document(pdf);

            // 添加标题
            Paragraph title = new Paragraph("报表标题")
                    .setTextAlignment(TextAlignment.CENTER)
                    .setFontSize(20);
            document.add(title);

            // 添加日期
            LocalDate currentDate = LocalDate.now();
            String formattedDate = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            Paragraph date = new Paragraph("打印日期: " + formattedDate)
                    .setTextAlignment(TextAlignment.RIGHT)
                    .setFontSize(12);
            document.add(date);

            // 创建表格


            float[] columnWidths = {80, 80, 80, 80}; // 调整列宽
            Table table = new Table(UnitValue.createPercentArray(columnWidths));


            // 添加表头
            table.addCell(new Cell().add(new Paragraph("列1").setFontSize(14)));
            table.addCell(new Cell().add(new Paragraph("列2").setFontSize(14)));
            table.addCell(new Cell().add(new Paragraph("列3").setFontSize(14)));
            table.addCell(new Cell().add(new Paragraph("列4").setFontSize(14)));

            for (RpPrintColumn rpPrintColumn : rpPrint.getInitPrintColumn()) {
                table.addCell(new Cell().add(new Paragraph(rpPrintColumn.getColDispVal()).setFontSize(rpPrint.getRpoutputConfig().getTbaleFontSize())));

            }


            for (RpPrintColumn printColumn : rpPrint.getInitPrintColumn()) {//按照表格表头顺序输出表格数据
                Cell cell = new Cell();
                cell.setFontSize(rpPrint.getRpoutputConfig().getTbaleFontSize());
                cell.add(new Paragraph(printColumn.getColDispVal()));
                table.addHeaderCell(cell);
                table.addCell(printColumn.getColDispVal());   //表头
            }

            // 添加数据行
            for (int i = 0; i < 5; i++) {
                table.addCell(new Cell().add(new Paragraph("数据" + (i + 1) + "-1").setFontSize(12)));
                table.addCell(new Cell().add(new Paragraph("数据" + (i + 1) + "-2").setFontSize(12)));
                table.addCell(new Cell().add(new Paragraph("数据" + (i + 1) + "-3").setFontSize(12)));
                table.addCell(new Cell().add(new Paragraph("数据" + (i + 1) + "-4").setFontSize(12)));
            }

            document.add(table);

            document.close();
            return "PDF生成成功: " + pdfPath;
        } catch (Exception e) {
            e.printStackTrace();
            return "PDF生成失败: " + e.getMessage();
        }
    }








}
