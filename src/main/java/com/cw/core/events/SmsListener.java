package com.cw.core.events;

import com.cw.arithmetic.events.PushData;
import com.cw.arithmetic.events.SysPushEvent;
import com.cw.exception.DefinedException;
import com.cw.service.log.MsgService;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/8/1 16:15
 **/
@Slf4j
@Component
public class SmsListener extends AbstractSysEventLister {
    MsgService msgService;

    @Autowired
    public SmsListener(MsgService msgService) {
        this.msgService = msgService;
    }


    @Async("commonPool")
    @EventListener(SysPushEvent.class)
    public void sendSms(SysPushEvent sysPushEvent) {
        PushData pushData = sysPushEvent.getData();
        try {
            if (lsupportTrigger(pushData.getEvent())) {
                msgService.sendMessage(pushData.getData(), pushData.getEvent(), pushData.getMobile(), pushData.getContent(), pushData.getRegno(), pushData.getProjectid());
            }
        } catch (DefinedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean lsupportTrigger(MsgTriggerEnum trigger) {
        //return !trigger.equals(MsgTriggerEnum.FEEDBACKAUDITING) ;
        return !trigger.equals(MsgTriggerEnum.FEEDBACKAUDITING) && !trigger.equals(MsgTriggerEnum.REFUNDNOTIFY) && !trigger.equals(MsgTriggerEnum.TICKETQRNOTIFY) && !trigger.equals(MsgTriggerEnum.ACTCONFIRM);
    }
}
