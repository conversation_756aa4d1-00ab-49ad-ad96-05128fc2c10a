package com.cw.core.events;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.events.PushData;
import com.cw.arithmetic.events.SysPushEvent;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoleCache;
import com.cw.entity.Op_role;
import com.cw.entity.Op_user;
import com.cw.exception.DefinedException;
import com.cw.mapper.Op_userMapper;
import com.cw.service.log.MsgService;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/10/7 17:11
 **/
@Slf4j
@Component
public class ConsonleListener extends AbstractSysEventLister {
    MsgService msgService;


    @Autowired
    public ConsonleListener(MsgService msgService) {
        this.msgService = msgService;
    }

    @Override
    public boolean lsupportTrigger(MsgTriggerEnum trigger) {  //后台用户关注的系统消息
        //return trigger.equals(MsgTriggerEnum.FEEDBACKAUDITING);  //
        return trigger.equals(MsgTriggerEnum.FEEDBACKAUDITING) || trigger.equals(MsgTriggerEnum.REFUNDNOTIFY) || trigger.equals(MsgTriggerEnum.TICKETQRNOTIFY)
                || trigger.equals(MsgTriggerEnum.ACTCONFIRM);  //
    }

    @Async("commonPool")
    @EventListener(SysPushEvent.class)
    public void sendSms(SysPushEvent sysPushEvent) {
        PushData pushData = sysPushEvent.getData();

        if (lsupportTrigger(pushData.getEvent())) {
            log.info("收到后台用户关注的系统消息");
            RoleCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.USER_ROLE);
            List<Op_role> list = cache.getDataList(pushData.getProjectid());
            for (Op_role opRole : list) {
                if (!opRole.getRoleid().equals((SystemUtil.RESERVE_ROLE)) && !opRole.getSubevent().isEmpty()) {
                    Set<Integer> events = Arrays.stream(opRole.getSubevent().split(",")).map(Integer::parseInt).collect(java.util.stream.Collectors.toSet());
                    if (events.contains(pushData.getEvent().getEventType())) {
                        log.info("用户群组{}关注了事件{}", opRole.getRoleid(), pushData.getEvent().getDesc());

                        //查找该用户组下的用户
                        Op_userMapper opUserMapper = SpringUtil.getBean(Op_userMapper.class);
                        List<Op_user> users = opUserMapper.getOp_usersByProjectidAndRoleid(pushData.getProjectid(), opRole.getRoleid());//TODO  用户角色可以关联多个角色
                        for (Op_user user : users) {
                            if (StrUtil.isNotBlank(user.getTel()) && user.getOstatus()) {
                                try {
                                    msgService.sendMessage(pushData.getData(), pushData.getEvent(), user.getTel(), StrUtil.EMPTY, pushData.getRegno(), pushData.getProjectid());
                                } catch (DefinedException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                }
            }
        }
    }


}
