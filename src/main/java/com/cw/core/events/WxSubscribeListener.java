package com.cw.core.events;

import com.cw.arithmetic.events.PushData;
import com.cw.arithmetic.events.SysPushEvent;
import com.cw.exception.DefinedException;
import com.cw.service.log.MsgService;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/8/1 16:15
 **/
@Slf4j
@Component
public class WxSubscribeListener extends AbstractSysEventLister {
    MsgService msgService;

    @Autowired
    public WxSubscribeListener(MsgService msgService) {
        this.msgService = msgService;
    }


    @Async("commonPool")
    @EventListener(SysPushEvent.class)
    public void wxsubsribe(SysPushEvent sysPushEvent) {
        PushData pushData = sysPushEvent.getData();
        try {
            msgService.sendWxAppSubscrbeMessage(pushData.getData(), pushData.getEvent(), pushData.getMobile(), pushData.getContent(), pushData.getRegno(), pushData.getProjectid());
        } catch (DefinedException e) {
            e.printStackTrace();
            ;
            log.error("微信订阅失败");
        }


    }

    @Override
    public boolean lsupportTrigger(MsgTriggerEnum trigger) {
        return MsgTriggerEnum.FEEDBACKOK.equals(trigger);  //TODO  微信订阅消息以后有可能会按照场景继续丰富.
    }
}
