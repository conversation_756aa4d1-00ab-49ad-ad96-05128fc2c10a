package com.cw.cache;


import com.cw.cache.impl.SysConfCache;
import com.cw.entity.Sysconf;
import com.cw.utils.SystemUtil;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by flyhigh on 2016/7/10.
 */
@Data
@Component
public class DataStructure extends BaseDataStructure {
    private ConcurrentHashMap<SystemUtil.GlobalDataType, BaseCacheData> cacheMap = new ConcurrentHashMap<>();

    /**
     * 初始化扫描..实例化.添加注解缓存类
     */
    @PostConstruct
    public void initAdpter() {
        scanPackage("com.cw.cache.impl");
//        String cacheUnitPath = "com.zj.cache.impl";
//        //根据 包的路径.将请求类加载到map 缓存
//        ClassScanner.scanPackageByAnnotation(cacheUnitPath, CacheProp.class).forEach(clazz -> {
//            SystemUtil.GlobalDataType globalDataType = clazz.getAnnotation(CacheProp.class).dataType();
//            try {
//                cacheMap.put(globalDataType, (BaseCacheData) clazz.newInstance());
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        });
//        log.info("{} 缓存大小size cacheed!!!", cacheMap.values().size());
    }

//    public Sysconf getConf(String projectId) {
//        return (Sysconf) getCache(SystemUtil.GlobalDataType.SYSCONF).getOne(projectId);
//    }

    public SysConfCache getConfCache(){
        return getCache(SystemUtil.GlobalDataType.SYSCONF);
    }



}
