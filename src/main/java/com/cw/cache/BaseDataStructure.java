package com.cw.cache;

import cn.hutool.core.lang.ClassScanner;
import com.cw.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/10/26 17:00
 **/
@Slf4j
public class BaseDataStructure {
    protected ConcurrentHashMap<SystemUtil.GlobalDataType, BaseCacheData> cacheMap = new ConcurrentHashMap<>();

    protected void scanPackage(String cacheUnitPath) {
        //根据 包的路径.将请求类加载到map 缓存
        ClassScanner.scanPackageByAnnotation(cacheUnitPath, CacheProp.class).forEach(clazz -> {
            SystemUtil.GlobalDataType globalDataType = clazz.getAnnotation(CacheProp.class).dataType();
            try {
                cacheMap.put(globalDataType, (BaseCacheData) clazz.getDeclaredConstructor().newInstance());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        log.info("{} 缓存大小size cacheed!!!", cacheMap.values().size());
    }

    /**
     * 注意:这个只会刷新本地缓存
     *
     * @param type
     */
    public void refreshDataStructure(SystemUtil.GlobalDataType type, String projectId, String refreshKey) {
        if (type.equals(SystemUtil.GlobalDataType.ALL)) {  //如果刷新全部数据.遍厉刷新
            long s1 = System.currentTimeMillis();
            cacheMap.values().stream().forEach(t -> t.refreshData(null));
            log.info("{} ms 刷新完成", (System.currentTimeMillis() - s1));
        } else {
            BaseCacheData cacheData = cacheMap.get(type);   //刷新单表数据
            if (cacheData != null) {
                cacheData.refreshData(projectId);
            }
        }
    }

    public <T extends BaseCacheData> T getCache(SystemUtil.GlobalDataType type) {
        return (T) cacheMap.get(type);
    }

}
