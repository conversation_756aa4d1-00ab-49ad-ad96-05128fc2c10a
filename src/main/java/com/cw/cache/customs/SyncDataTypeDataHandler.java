package com.cw.cache.customs;


import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.EnumUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion 同步数据下拉框
 * @Create 2019/12/4 13:53
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.syncdata)
public class SyncDataTypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        if (EnumUtils.isValidEnumIgnoreCase(SystemUtil.GlobalDataType.class, key)) {
            SystemUtil.GlobalDataType dataType = SystemUtil.GlobalDataType.valueOf(key);
            return dataType.getDesc();
        } else {
            return key;
        }

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        //同步数据下拉框
        SystemUtil.CustomDataKey[] syncs = new SystemUtil.CustomDataKey[]{
                SystemUtil.CustomDataKey.hotel, SystemUtil.CustomDataKey.roomtype, SystemUtil.CustomDataKey.productkit,
                SystemUtil.CustomDataKey.restaurant,
                SystemUtil.CustomDataKey.ticket
                //SystemUtil.CustomDataKey.ratecode,
                //SystemUtil.CustomDataKey.roomtypedetail, SystemUtil.CustomDataKey.ticketdetail, SystemUtil.CustomDataKey.productkitdetail,
        };
        for (SystemUtil.CustomDataKey dataKey : syncs) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(dataKey.name());
            node.setDesc(dataKey.getDesc() == null ? dataKey.name() : dataKey.getDesc());
            selectList.add(node);
        }
        return selectList;
    }


}
