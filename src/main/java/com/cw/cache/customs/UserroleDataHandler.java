package com.cw.cache.customs;


import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoleCache;
import com.cw.entity.Op_role;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/4 13:53
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.userrole)
public class UserroleDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        RoleCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.USER_ROLE);
        Op_role record = cache.getRecord(projectId, key);
        return record == null ? key : record.getName();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        RoleCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.USER_ROLE);
        List<Op_role> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = list.stream().map(
                row -> {
                    SelectDataNode node = new SelectDataNode();
                    node.setCode(row.getRoleid());
                    node.setDesc(row.getName());
                    return node;
                }
        ).collect(Collectors.toList());   //重新组装数据.
        return selectList;
    }


}
