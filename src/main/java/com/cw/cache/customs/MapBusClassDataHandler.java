package com.cw.cache.customs;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Describe 园区业态类型下的分类
 * <AUTHOR> <PERSON>
 * @Create on 2023-06-15
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.mapbusclass)
public class MapBusClassDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor factor = cache.getRecord(projectId, key);
        if (factor != null) {
            return factor.getDescription();
        } else {
            return key;
        }
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        String businesscode = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        //查看园区标注类型下的分类数据
        List<Factor> list = cache.getDataList(projectId);
        //if (StringUtils.isNotBlank(businesscode)) {
        //    list = list.stream().filter(f -> f.getHeader().equals(businesscode)).collect(Collectors.toList());
        //}else {
        list = list.stream().filter(f -> f.getHeader().equals(SystemUtil.SourceType.MAPBUSCLASS.name())).collect(Collectors.toList());
        //}
        if (CollectionUtil.isNotEmpty(list)) {
            list.sort(Comparator.comparing(Factor::getSeq));
            for (Factor item : list) {
                if (item.getStatus() == 0) { //关闭的分类
                    continue;
                }
                SelectDataNode node = new SelectDataNode();
                node.setCode(item.getCode());
                node.setDesc(item.getDescription());
                selectList.add(node);
            }
        }


        return selectList;
    }
}
