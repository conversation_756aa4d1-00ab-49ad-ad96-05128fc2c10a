package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.menus.ContentSeqEnums;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.EnumUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 栏目内容排序下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/29 0029
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.contentseq)
public class MenusContentSeqDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        if (EnumUtils.isValidEnumIgnoreCase(ContentSeqEnums.class, key)) {
            ContentSeqEnums contentSeqEnums = ContentSeqEnums.valueOf(key);
            return contentSeqEnums.getDesc();
        } else {
            return key;
        }

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        //固定栏目模板
        ContentSeqEnums[] values = ContentSeqEnums.values();
        for (ContentSeqEnums contentSeq : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(contentSeq.name());
            node.setDesc(contentSeq.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
