package com.cw.cache.customs;

import com.cw.arithmetic.lang.R;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;

/**
 * @Describe 关闭购买原因下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2022/1/25 0025
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.closereason)
public class CloseReasonDataHandler extends FactorDataHandler {
    public CloseReasonDataHandler() {
        super();
        factoryType = SystemUtil.FactoryType.CLOSEREASON;
    }

    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : R.lang(record.getDescription());
    }
}
