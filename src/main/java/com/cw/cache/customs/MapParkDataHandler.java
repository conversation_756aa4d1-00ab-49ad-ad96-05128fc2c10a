package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ParkCache;
import com.cw.entity.Park;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-06
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.park)
public class MapParkDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        ParkCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PARK);
        Park record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ParkCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PARK);
        List<Park> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Park park : list) {
            if (!park.getLshow()) {
                continue;
            }
            SelectDataNode node = new SelectDataNode();
            node.setCode(park.getCode());
            node.setDesc(park.getDescription());
            selectList.add(node);
        }
        return selectList;
    }
}
