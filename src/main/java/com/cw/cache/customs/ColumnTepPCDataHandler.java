package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.menus.PCMenuCardType;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.EnumUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022/1/29 0029
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.pccomptype)
public class ColumnTepPCDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        if (EnumUtils.isValidEnumIgnoreCase(PCMenuCardType.class, key)) {
            PCMenuCardType cardType = PCMenuCardType.valueOf(key);
            return cardType.getDesc();
        } else {
            return key;
        }

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        //固定栏目模板
        PCMenuCardType[] values = PCMenuCardType.values();
        for (PCMenuCardType cardType : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(cardType.name());
            node.setDesc(cardType.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
