package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ActsiteCache;
import com.cw.entity.Actsite;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

@CustomDataProp(dataType = SystemUtil.CustomDataKey.actsite)
public class ActsiteDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        ActsiteCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTSITE);
        Actsite record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ActsiteCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTSITE);
        List<Actsite> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Actsite actsite : list) {
            //todo  是否排除没开放场所
            SelectDataNode node = new SelectDataNode();
            node.setCode(actsite.getCode());
            node.setDesc(actsite.getDescription());
            node.setGroup(actsite.getGroupid());
            selectList.add(node);
        }
        return selectList;
    }
}
