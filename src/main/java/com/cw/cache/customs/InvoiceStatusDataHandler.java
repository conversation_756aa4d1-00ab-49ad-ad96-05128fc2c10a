package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 发票状态下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/13 0013
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.invoicestatus)
public class InvoiceStatusDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        StatusUtil.InvoiceStatusEnum[] values = StatusUtil.InvoiceStatusEnum.values();
        for (StatusUtil.InvoiceStatusEnum item : values) {
            if (item.getCode().equals(key)) {
                return item.getDesc();
            }
        }
        return key;


    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        StatusUtil.InvoiceStatusEnum[] values = StatusUtil.InvoiceStatusEnum.values();
        for (StatusUtil.InvoiceStatusEnum cardType : values) {
            if (StatusUtil.InvoiceStatusEnum.CANCEL.getCode().equals(cardType.getCode())) {
                continue;
            }
            SelectDataNode node = new SelectDataNode();
            node.setCode(cardType.getCode());
            node.setDesc(cardType.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
