package com.cw.cache.customs;

import cn.hutool.core.util.StrUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.VendorType;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 主订单状态下拉
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/9 0009
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.vendortype)
public class VendorTypeDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        StatusUtil.BookingRSStatusEnum[] values = StatusUtil.BookingRSStatusEnum.values();
        for (StatusUtil.BookingRSStatusEnum statusEnum : values) {
            if (statusEnum.getCode().equals(key)) {
                return statusEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        VendorType[] vendorTypes = VendorType.values();
        String group = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        for (VendorType vendorType : vendorTypes) {
            if (StrUtil.isNotBlank(group)) {
                if (!vendorType.getGroup().isEmpty() && !vendorType.getGroup().equals(group)) {
                    continue;
                }
            }
            SelectDataNode node = new SelectDataNode();
            node.setCode(vendorType.name());
            node.setDesc(vendorType.getDesc());
            selectList.add(node);
        }
        return selectList;
    }
}
