package com.cw.cache.customs;

import com.cw.arithmetic.lang.R;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ParkBussinessCache;
import com.cw.entity.Parkbusiness;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.map.MapBusinessType;
import com.cw.utils.pagedata.SelectDataNode;
import jodd.util.StringUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @ 园区业态类型
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-04
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.mapbusinesstype)
public class MapBusinessTypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        MapBusinessType[] values = MapBusinessType.values();
        for (MapBusinessType item : values) {
            if (item.getIconType().equals(key)) {
                return item.getDesc();
            }
        }
        return key;

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        MapBusinessType[] values = MapBusinessType.values();
        //固定园区业态枚举
        //todo 限定园区勾选的枚举
        String parkcode = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        if (StringUtil.isNotBlank(parkcode)) {
            ParkBussinessCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PARKBUSS);
            List<Parkbusiness> parkbusinessList = cache.getDataListWithCondition(projectId, b -> b.getGroupid().equals(parkcode));
            for (Parkbusiness item : parkbusinessList) {
                SelectDataNode node = new SelectDataNode();
                node.setCode(item.getBusinesstype());
                node.setDesc(R.lang(MapBusinessType.valueOf(item.getBusinesstype()).getDesc()));
                selectList.add(node);
            }
        } else {//所有
            for (MapBusinessType mapBusinessType : values) {
                SelectDataNode node = new SelectDataNode();
                node.setCode(mapBusinessType.name());
                node.setDesc(mapBusinessType.getDesc());
                selectList.add(node);
            }

        }

        return selectList;
    }
}
