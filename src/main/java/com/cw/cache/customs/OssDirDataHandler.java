package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.OSSDirCache;
import com.cw.entity.Ossdir;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-11-23
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.ossroot)
public class OssDirDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        OSSDirCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.OSSDIR);
        Ossdir record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        OSSDirCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.OSSDIR);
        List<Ossdir> list = cache.getDataList(projectId);
        String root = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        //获取一级分组，group为空的数据
        for (Ossdir ossdir : list) {
            SelectDataNode node = new SelectDataNode();
            //获取所有
            if (StringUtils.isNotBlank(ossdir.getGroup()) && checkGroup(ossdir.getGroup())) {
                continue;
            }
            if (StringUtils.isNotBlank(root) && !ossdir.getCode().contains(root.toLowerCase(Locale.ROOT))) {
                continue;
            }
            node.setCode(ossdir.getCode());
            node.setDesc(ossdir.getDescription());
            node.setGroup(ossdir.getGroup());
            selectList.add(node);
        }
        return selectList;
    }

    private boolean checkGroup(String group) {
        int count = group.split("/").length;
        return count > 2; //根目录为tab，超过三个判断为2级目录  {projectId}/{tab}/
    }
}
