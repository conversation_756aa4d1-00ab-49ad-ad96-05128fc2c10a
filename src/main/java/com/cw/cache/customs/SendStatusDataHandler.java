package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 发货状态管理
 * <AUTHOR> <PERSON>
 * @Create on 2022-10-28
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.sendstatus)
public class SendStatusDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        StatusUtil.SendStatusEnum[] values = StatusUtil.SendStatusEnum.values();
        for (StatusUtil.SendStatusEnum statusEnum : values) {
            if (("" + statusEnum.getCode()).equals(key)) {
                return statusEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        StatusUtil.SendStatusEnum[] values = StatusUtil.SendStatusEnum.values();
        for (StatusUtil.SendStatusEnum status : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(status.getCode() + "");
            node.setDesc(status.getDesc());
            selectList.add(node);
        }

        return selectList;

    }
}
