package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Describe 商品类型下拉框 区分酒店预订 门票预订
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/8 0008
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.ptype)
public class ProdTypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        ProdType[] prodTypes = ProdType.values();
        String record = null;
        for (ProdType prodType : prodTypes) {
            if (key.equalsIgnoreCase(prodType.val())) {
                record = prodType.getDesc();
                break;
            }
        }
        return record == null ? key : record;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ProdType[] prodTypes = ProdType.values();
        List<SelectDataNode> selectList = new ArrayList<>();
        String type = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        if (type.contains(",")) {
            String[] ptypeArray = type.split(",");
            type = ptypeArray[0];
        }
        //目前产品类型
        List<String> prodList = Arrays.asList("R", "T", "Z", "W", "Y", "I");
        for (ProdType prodType : prodTypes) {
            SelectDataNode node = new SelectDataNode();
            if (StringUtils.isNotBlank(type) && prodType.val().equals("Y")) {
                continue;
            }
            if (StringUtils.isNotBlank(prodType.getDesc()) && prodList.contains(prodType.val())) {
                node.setCode(prodType.val());
                node.setDesc(prodType.getDesc());
                selectList.add(node);
            }

        }
        return selectList;
    }
}
