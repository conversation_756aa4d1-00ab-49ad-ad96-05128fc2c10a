package com.cw.cache.customs;


import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.entity.Roomtype;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/4 13:53
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.roomtype)
public class RoomtypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        RoomTypeCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
        Roomtype record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        RoomTypeCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE);
        List<Roomtype> list = cache.getDataList(projectId);
        String hotel = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        if (hotel.contains(",")) {
            String[] hotelArray = hotel.split(",");
            hotel = hotelArray[0];
        }
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Roomtype roomtype : list) {
            if (hotel.isEmpty() || "all".equals(hotel) || roomtype.getHotelcode().equals(hotel)) {
                SelectDataNode node = new SelectDataNode();
                node.setCode(roomtype.getCode());
                node.setDesc(roomtype.getDescription());
                node.setGroup(roomtype.getHotelcode());
                selectList.add(node);
            }
        }
        return selectList;
    }


}
