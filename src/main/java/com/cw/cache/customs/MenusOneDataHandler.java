package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomData;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.MenusCache;
import com.cw.entity.Menus;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/8 0008
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.menusone)
public class MenusOneDataHandler extends BaseCustomHandler {

    /**
     * 转换栏目名称
     *
     * @param projectId
     * @param key
     * @return
     */
    @Override
    public String trans(String projectId, String key) {
        MenusCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUS);
        Menus record = cache.getRecord(projectId, key);
        return record == null ? key : record.getMname();
    }


    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        MenusCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUS);
        //取分组数据
        List<Menus> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        String mtype = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        //排序List 按照sqlid columntype orgmenuid seq升序
        list.sort(Comparator.comparing(Menus::getColumntype).
                thenComparing(Menus::getOrgmenuid).thenComparing(Menus::getSeq).thenComparing(Menus::getSqlid));
        for (Menus menu : list) {
            //启动状态下拉框 orgmenuid字段为空为一级栏目
            if (StringUtils.isBlank(menu.getOrgmenuid())) {
                //如果过滤字段不为空，过滤字段和平台类型不等 则跳过
                if ((StringUtils.isNotBlank(mtype) && !mtype.equals(menu.getMtype()))) {
                    continue;
                }
                SelectDataNode node = new SelectDataNode();
                node.setCode(menu.getMenuid());
                node.setDesc(menu.getMname());
                //上级分类转换文字
                node.setGroup(CustomData.getDesc(projectId, menu.getColumntype(), SystemUtil.CustomDataKey.column));

                selectList.add(node);
            }
        }
        return selectList;
    }
}
