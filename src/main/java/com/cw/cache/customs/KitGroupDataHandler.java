package com.cw.cache.customs;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.KitGroupCache;
import com.cw.entity.Kitgroup;
import com.cw.entity.Productkit;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 套餐大组下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/1 0001
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.kitgroup)
public class KitGroupDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        KitGroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP);
        Kitgroup record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        KitGroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITGROUP);
        List<Kitgroup> list = cache.getDataList(projectId);
        String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Kitgroup kitgroup : list) {
            SelectDataNode node = new SelectDataNode();
            //如果需要查询分组数据
            if (StringUtils.isNotBlank(header) && header.equals(kitgroup.getCode())) {
                List<SelectDataNode> childList = new ArrayList<>();
                //获取缓存票型groupid为header参数产品子项数据列
                List<Productkit> productkitList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT).getGroupList(projectId, header);
                if (CollectionUtil.isNotEmpty(productkitList)) {
                    for (Productkit productkit : productkitList) {
                        SelectDataNode childNode = new SelectDataNode();
                        childNode.setCode(productkit.getCode());
                        childNode.setDesc(productkit.getDescription());
                        childNode.setGroup(productkit.getGroupid());
                        childList.add(childNode);
                    }
                }
                node.setChildren(childList);

            } else if (StringUtils.isNotBlank(header) && !header.equals(kitgroup.getCode())) {
                //如果输入指定特定参数 则跳过不符合条件的分组数据
                continue;
            }
            node.setCode(kitgroup.getCode());
            node.setDesc(kitgroup.getDescription());
            selectList.add(node);
        }
        return selectList;
    }
}
