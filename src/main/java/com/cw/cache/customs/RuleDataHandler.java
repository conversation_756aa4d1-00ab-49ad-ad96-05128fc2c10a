package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RulesCache;
import com.cw.entity.Rules;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/11 0011
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.rules)
public class RuleDataHandler extends BaseCustomHandler {


    @Override
    public String trans(String projectId, String key) {
        RulesCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RULES);
        Rules record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        RulesCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RULES);
        //取分组数据
        List<Rules> list = cache.getDataList(projectId);
        String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        list.sort(Comparator.comparing(Rules::getType).thenComparing(Rules::getSeq).thenComparing(Rules::getSqlid));
        for (Rules rule : list) {
            //按类型过滤
            if (StringUtils.isNotBlank(header) && !header.equals(rule.getType())) {
                continue;
            }
            SelectDataNode node = new SelectDataNode();
            node.setCode(rule.getCode());
            node.setDesc(rule.getDescription());
            node.setGroup(rule.getType());
            selectList.add(node);

        }
        return selectList;
    }


}
