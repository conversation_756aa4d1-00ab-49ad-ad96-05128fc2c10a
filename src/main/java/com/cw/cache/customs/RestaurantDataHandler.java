package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RestaurantCache;
import com.cw.entity.Restaurant;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-05-05
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.restaurant)
public class RestaurantDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        RestaurantCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RESTAURANT);
        Restaurant record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        RestaurantCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RESTAURANT);
        List<Restaurant> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Restaurant restaurant : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(restaurant.getCode());
            node.setDesc(restaurant.getDescription());
            selectList.add(node);
        }
        return selectList;
    }
}
