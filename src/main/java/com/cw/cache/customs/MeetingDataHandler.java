package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.MeetingCache;
import com.cw.entity.Meeting;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/25 0025
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.meeting)
public class MeetingDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        MeetingCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETING);
        Meeting record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        MeetingCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETING);
        List<Meeting> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Meeting meeting : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(meeting.getCode());
            node.setDesc(meeting.getDescription());
            selectList.add(node);
        }
        return selectList;
    }
}
