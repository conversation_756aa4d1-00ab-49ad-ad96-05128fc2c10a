package com.cw.cache.customs;


import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.cache.impl.TraveltipCache;
import com.cw.entity.Roomtype;
import com.cw.entity.Traveltip;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/4 13:53
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.traveltip)
public class TravelTipDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        RoomTypeCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TRAVELTIP);
        Roomtype record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        TraveltipCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TRAVELTIP);
        List<Traveltip> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Traveltip traveltip : list) {
            if (traveltip.getLshow()) {
                SelectDataNode node = new SelectDataNode();
                node.setCode(traveltip.getCode());
                node.setDesc(traveltip.getDescription());
                selectList.add(node);
            }
        }
        return selectList;
    }


}
