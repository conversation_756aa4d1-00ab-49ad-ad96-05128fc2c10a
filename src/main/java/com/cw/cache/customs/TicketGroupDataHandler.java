package com.cw.cache.customs;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.TicketgroupCache;
import com.cw.entity.Ticket;
import com.cw.entity.Ticketgroup;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 票务大组
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/18 0018
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.ticketgroup)
public class TicketGroupDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        TicketgroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP);
        Ticketgroup record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        TicketgroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKETGROUP);
        List<Ticketgroup> list = cache.getDataList(projectId);
        String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Ticketgroup ticketgroup : list) {
            SelectDataNode node = new SelectDataNode();
            //如果需要查询分组数据
            if (StringUtils.isNotBlank(header) && header.equals(ticketgroup.getCode())) {
                List<SelectDataNode> childList = new ArrayList<>();
                //获取缓存票型groupid为header参数产品子项数据列
                List<Ticket> ticketList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET).getGroupList(projectId, header);
                if (CollectionUtil.isNotEmpty(ticketList)) {
                    for (Ticket ticket : ticketList) {
                        SelectDataNode childNode = new SelectDataNode();
                        childNode.setCode(ticket.getCode());
                        childNode.setDesc(ticket.getDescription());
                        childNode.setGroup(ticket.getGroupid());
                        childList.add(childNode);
                    }

                }
                node.setChildren(childList);

            } else if (StringUtils.isNotBlank(header) && !header.equals(ticketgroup.getCode())) {
                //如果输入指定特定参数 则跳过不符合条件的分组数据
                continue;
            }
            node.setCode(ticketgroup.getCode());
            node.setDesc(ticketgroup.getDescription());
            selectList.add(node);
        }
        return selectList;
    }
}
