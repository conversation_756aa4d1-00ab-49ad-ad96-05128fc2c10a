package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ParksiteCache;
import com.cw.entity.Parksite;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import jodd.util.StringUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-07-13
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.parksite)
public class MapParksiteDateHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        ParksiteCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PARKSITE);
        Parksite record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        ParksiteCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PARKSITE);
        String parkId = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        if (StringUtil.isNotBlank(parkId)) {
            List<Parksite> list = cache.getDataListWithCondition(projectId, b -> b.getParkid().equals(parkId));
            for (Parksite parksite : list) {
                /*if (!parksite.getLshow()) {
                    continue;
                }*/
                SelectDataNode node = new SelectDataNode();
                node.setCode(parksite.getCode());
                node.setDesc(parksite.getDescription());
                node.setGroup(parksite.getCoordinate());//标注坐标
                selectList.add(node);
            }
        } else {//所有
            List<Parksite> list = cache.getDataList(projectId);
            for (Parksite parksite : list) {
               /* if (!parksite.getLshow()) {
                    continue;
                }*/
                SelectDataNode node = new SelectDataNode();
                node.setCode(parksite.getCode());
                node.setDesc(parksite.getDescription());
                node.setGroup(parksite.getCoordinate());//标注坐标
                selectList.add(node);
            }

        }


        return selectList;
    }
}
