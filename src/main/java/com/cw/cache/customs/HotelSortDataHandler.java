package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.HotelSortType;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-05-13
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.hotelsort)
public class HotelSortDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        HotelSortType[] values = HotelSortType.values();
        for (HotelSortType hotelSortType : values) {
            if (hotelSortType.getVal().equals(key)) {
                return hotelSortType.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        HotelSortType[] values = HotelSortType.values();
        for (HotelSortType hotelSortType : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(hotelSortType.getVal());
            node.setDesc(hotelSortType.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
