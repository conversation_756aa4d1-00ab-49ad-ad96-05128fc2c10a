package com.cw.cache.customs;

import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;

/**
 * @Describe 退款原因 和取消订单原因一致
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/25 0025
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.refundreason)
public class RefundReasonDataHandler extends FactorDataHandler {
    public RefundReasonDataHandler() {
        super();
        factoryType = SystemUtil.FactoryType.REFUNDREASON;
    }

    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }
}
