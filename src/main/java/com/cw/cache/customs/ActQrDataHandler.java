package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ActqrCache;
import com.cw.entity.Actqr;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-10-21
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.actqr)
public class ActQrDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        ActqrCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTQR);
        Actqr record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ActqrCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTQR);
        List<Actqr> list = cache.getDataList(projectId);

        List<SelectDataNode> selectList = new ArrayList<>();
        for (Actqr actqr : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(actqr.getCode());
            node.setDesc(actqr.getDescription());
            node.setGroup(actqr.getGroups());
            selectList.add(node);
        }
        return selectList;
    }
}
