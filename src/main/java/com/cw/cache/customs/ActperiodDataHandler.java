package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ActperiodCache;
import com.cw.entity.Actperiod;
import com.cw.entity.Actsite;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@CustomDataProp(dataType = SystemUtil.CustomDataKey.actperiod)
public class ActperiodDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        ActperiodCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTPERIOD);
        Actperiod record = cache.getRecord(projectId, key);
        return record == null ? key : record.getTime();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ActperiodCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTPERIOD);
        List<Actperiod> list = cache.getDataList(projectId);
        String sitecode = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        Actsite dbActsite = null;
        if (sitecode.contains(",")) {
            String[] ptypeArray = sitecode.split(",");
            sitecode = ptypeArray[0];
        }
        if (StringUtils.isNotBlank(sitecode)) {
            dbActsite = (Actsite) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTSITE).getRecord(projectId, sitecode);
        }
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Actperiod actperiod : list) {
            if (dbActsite != null) {
                if (!dbActsite.getPeriods().contains(actperiod.getCode())) {
                    continue;
                }
                SelectDataNode node = new SelectDataNode();
                node.setCode(actperiod.getCode());
                node.setDesc(actperiod.getDescription() + " " + actperiod.getTime());
                node.setGroup(actperiod.getTime());
                selectList.add(node);

            } else {
                SelectDataNode node = new SelectDataNode();
                node.setCode(actperiod.getCode());
                node.setDesc(actperiod.getDescription() + " " + actperiod.getTime());
                node.setGroup(actperiod.getTime());
                selectList.add(node);
            }
        }
        return selectList;
    }
}
