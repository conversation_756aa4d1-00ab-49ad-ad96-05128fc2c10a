package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Describe 业务规则类型数据缓存
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/11 0011
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.ruletype)
public class RulesTypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        ProdType[] prodTypes = ProdType.values();
        String record = null;
        for (ProdType prodType : prodTypes) {
            if (key.equalsIgnoreCase(prodType.val())) {
                record = prodType.getDesc();
                break;
            }
        }
        return record == null ? key : record;

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ProdType[] prodTypes = ProdType.values();
        List<SelectDataNode> selectList = new ArrayList<>();
        //目前产品类型
        List<String> prodList = Arrays.asList("R", "T", "Z", "W");
        for (ProdType prodType : prodTypes) {
            if (StringUtils.isNotBlank(prodType.getDesc()) && prodList.contains(prodType.val())) {
                SelectDataNode node = new SelectDataNode();
                node.setCode(prodType.val());
                node.setDesc(prodType.getDesc());
                selectList.add(node);
            }

        }
        return selectList;
    }
}
