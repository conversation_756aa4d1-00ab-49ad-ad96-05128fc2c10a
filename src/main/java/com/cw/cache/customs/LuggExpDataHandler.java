package com.cw.cache.customs;

import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/12/10 10:08
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.luggageexp)
public class LuggExpDataHandler extends FactorDataHandler {

    public LuggExpDataHandler() {
        super();
        factoryType = SystemUtil.FactoryType.LUGGEXPTAG;
    }

    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }


}
