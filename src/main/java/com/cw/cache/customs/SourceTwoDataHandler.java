package com.cw.cache.customs;

import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;

/**
 * @Describe 二级资源下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/25 0025
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.sourcetwo)
public class SourceTwoDataHandler extends FactorDataHandler {
    public SourceTwoDataHandler() {
        super();
        factoryType = SystemUtil.FactoryType.SOURCETWO;
    }

    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }
}
