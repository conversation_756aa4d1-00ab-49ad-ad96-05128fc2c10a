package com.cw.cache.customs;

import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/10 0010
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.hotelkit)
public class HotelKitDataHandler extends FactorDataHandler {

    public HotelKitDataHandler() {
        super();
        factoryType = SystemUtil.FactoryType.HOTELKIT;
    }

    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }
}
