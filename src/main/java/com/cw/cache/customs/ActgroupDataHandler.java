package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ActgroupCache;
import com.cw.entity.Actgroup;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

@CustomDataProp(dataType = SystemUtil.CustomDataKey.actgroup)
public class ActgroupDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        ActgroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTGROUP);
        Actgroup record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ActgroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ACTGROUP);
        List<Actgroup> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Actgroup actgroup : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(actgroup.getCode());
            node.setDesc(actgroup.getDescription());
            node.setGroup(actgroup.getNdallowtime());
            selectList.add(node);
        }
        return selectList;
    }

}
