package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.menus.MobileMenuCardType;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.EnumUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 默认栏目模板下拉框数据
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/29 0029
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.comptype)
public class ColumnTepDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        if (EnumUtils.isValidEnumIgnoreCase(MobileMenuCardType.class, key)) {
            MobileMenuCardType cardType = MobileMenuCardType.valueOf(key);
            return cardType.getDesc();
        } else {
            return key;
        }

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        //固定栏目模板
        MobileMenuCardType[] values = MobileMenuCardType.values();
        for (MobileMenuCardType cardType : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(cardType.name());
            node.setDesc(cardType.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
