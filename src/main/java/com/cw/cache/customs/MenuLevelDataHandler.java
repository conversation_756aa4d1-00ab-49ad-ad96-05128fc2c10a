package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.menus.MenuLevelType;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.EnumUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 栏目等级下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/1 0001
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.menulevel)
public class MenuLevelDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        if (EnumUtils.isValidEnumIgnoreCase(MenuLevelType.class, key)) {
            MenuLevelType menuLevelType = MenuLevelType.valueOf(key);
            return menuLevelType.name();
        } else {
            return key;
        }

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        //固定栏目模板
        MenuLevelType[] types = MenuLevelType.values();
        for (MenuLevelType levelType : types) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(levelType.name());
            node.setDesc(levelType.name());
            selectList.add(node);
        }

        return selectList;
    }
}
