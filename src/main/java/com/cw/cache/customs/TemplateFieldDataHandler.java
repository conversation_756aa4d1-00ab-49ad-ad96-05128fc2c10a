package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.sms.MsgTemplateFiledEnum;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-04-01
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.template_field)
public class TemplateFieldDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        MsgTemplateFiledEnum[] values = MsgTemplateFiledEnum.values();
        for (MsgTemplateFiledEnum item : values) {
            if (item.getGroup().equals(key)) {
                return item.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> list = new ArrayList<>();
        SelectDataNode selectDataNode = null;
        MsgTemplateFiledEnum[] template_fields = MsgTemplateFiledEnum.values();
        String group = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        if (!group.isEmpty()) { //必须要传入组才能搜索
            for (MsgTemplateFiledEnum template_field : template_fields) {
                if (template_field.getGroup().equals(group)) {
                    selectDataNode = new SelectDataNode();
                    selectDataNode.setCode(template_field.getCode());
                    selectDataNode.setDesc(template_field.getDesc());
                    selectDataNode.setGroup(template_field.getGroup());
                    list.add(selectDataNode);
                }
            }
        }
        return list;
    }
}
