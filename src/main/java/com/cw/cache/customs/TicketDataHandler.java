package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.TicketCache;
import com.cw.entity.Ticket;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 票型数据
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/18 0018
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.ticket)
public class TicketDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        TicketCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
        Ticket record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        TicketCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TICKET);
        List<Ticket> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        //是否按查询分组
        String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        for (Ticket ticket : list) {
            SelectDataNode node = new SelectDataNode();
            //获取所有
            if (StringUtils.isBlank(header) && StringUtils.isNotBlank(ticket.getGroupid())) {
                continue;
            }
            node.setCode(ticket.getCode());
            node.setDesc(ticket.getDescription());
            node.setGroup(ticket.getGroupid());
            selectList.add(node);
        }
        return selectList;
    }
}
