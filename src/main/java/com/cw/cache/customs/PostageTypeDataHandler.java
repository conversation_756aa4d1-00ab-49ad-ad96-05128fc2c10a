package com.cw.cache.customs;

import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;

/**
 * @Describe 邮费类型下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2022-12-01
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.postagetype)
public class PostageTypeDataHandler extends FactorDataHandler {

    public PostageTypeDataHandler() {
        super();
        this.factoryType = SystemUtil.FactoryType.POSTAGETYPE;
    }

    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }
}
