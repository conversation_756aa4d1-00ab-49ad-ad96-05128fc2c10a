package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.BedType;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-05-13
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.bedtype)
public class BedTypeDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        BedType[] values = BedType.values();
        for (BedType bedType : values) {
            if (bedType.getVal().equals(key)) {
                return bedType.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        BedType[] values = BedType.values();
        for (BedType bedType : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(bedType.getVal());
            node.setDesc(bedType.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
