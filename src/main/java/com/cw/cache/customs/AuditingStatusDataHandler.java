package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022/1/5 0005
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.auditingstatus)
public class AuditingStatusDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        StatusUtil.AuditingStatusEnum[] values = StatusUtil.AuditingStatusEnum.values();
        for (StatusUtil.AuditingStatusEnum statusEnum : values) {
            if (statusEnum.getCode().equals(key)) {
                return statusEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        StatusUtil.AuditingStatusEnum[] values = StatusUtil.AuditingStatusEnum.values();
        String str = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        for (StatusUtil.AuditingStatusEnum status : values) {
            if ("his".equals(str) && status.equals(StatusUtil.AuditingStatusEnum.EXPECTED)) {
                continue;
            }
            SelectDataNode node = new SelectDataNode();
            node.setCode(status.getCode());
            node.setDesc(status.getDesc());
            selectList.add(node);
        }

        return selectList;

    }
}
