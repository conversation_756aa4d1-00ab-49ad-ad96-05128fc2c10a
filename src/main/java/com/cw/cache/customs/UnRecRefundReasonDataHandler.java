package com.cw.cache.customs;

import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;

/**
 * @Describe 未收货退款原因
 * <AUTHOR> <PERSON>
 * @Create on 2023-02-15
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.unrecreason)
public class UnRecRefundReasonDataHandler extends FactorDataHandler {
    public UnRecRefundReasonDataHandler() {
        super();
        factoryType = SystemUtil.FactoryType.UNRECREASON;
    }

    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }
}
