package com.cw.cache.customs;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.MeetingGroupCache;
import com.cw.entity.Meeting;
import com.cw.entity.Meetinggroup;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/25 0025
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.meetinggroup)
public class MeetingGroupDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        MeetingGroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETINGGROUP);
        Meetinggroup record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        MeetingGroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETINGGROUP);
        List<Meetinggroup> list = cache.getDataList(projectId);
        String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Meetinggroup meetinggroup : list) {
            SelectDataNode node = new SelectDataNode();
            //如果需要查询分组数据
            if (StringUtils.isNotBlank(header) && header.equals(meetinggroup.getCode())) {
                List<SelectDataNode> childList = new ArrayList<>();
                //获取缓存票型groupid为header参数产品子项数据列
                List<Meeting> meetingList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MEETING).getGroupList(projectId, header);
                if (CollectionUtil.isNotEmpty(meetingList)) {
                    for (Meeting meeting : meetingList) {
                        SelectDataNode childNode = new SelectDataNode();
                        childNode.setCode(meeting.getCode());
                        childNode.setDesc(meeting.getDescription());
                        childNode.setGroup(meeting.getGroupid());
                        childList.add(childNode);
                    }

                }
                node.setChildren(childList);

            } else if (StringUtils.isNotBlank(header) && !header.equals(meetinggroup.getCode())) {
                //如果输入指定特定参数 则跳过不符合条件的分组数据
                continue;
            }
            node.setCode(meetinggroup.getCode());
            node.setDesc(meetinggroup.getDescription());

            selectList.add(node);
        }
        return selectList;
    }
}
