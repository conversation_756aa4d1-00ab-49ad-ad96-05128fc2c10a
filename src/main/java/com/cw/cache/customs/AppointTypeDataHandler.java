package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/26 0026
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.appointtype)
public class AppointTypeDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        ProdType[] prodTypes = ProdType.values();
        String record = null;
        for (ProdType prodType : prodTypes) {
            if (key.equalsIgnoreCase(prodType.val())) {
                record = prodType.getDesc();
                break;
            }
        }
        return record == null ? key : record;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ProdType[] prodTypes = ProdType.values();
        List<SelectDataNode> selectList = new ArrayList<>();
        //目前产品类型
        List<String> prodList = Arrays.asList("M");
        for (ProdType prodType : prodTypes) {
            SelectDataNode node = new SelectDataNode();
            if (StringUtils.isNotBlank(prodType.getDesc()) && prodList.contains(prodType.val())) {
                node.setCode(prodType.val());
                node.setDesc(prodType.getDesc());
                selectList.add(node);
            }

        }
        return selectList;
    }
}
