package com.cw.cache.customs;


import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.WxTemplateCache;
import com.cw.entity.Wxtemplate;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/4 13:53
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.wxtemplate)
public class WxTemplateDataHandler extends BaseCustomHandler {


    public Wxtemplate getTemplate(String code, MsgTriggerEnum trigger, String projectid) {
        WxTemplateCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.WXTEMPLATE);
        if (!code.isEmpty()) {
            return (Wxtemplate) cache.getRecord(projectid, code);
        }
        if (trigger == null) {
            return null;
        }
        List<Wxtemplate> list = cache.getDataList(projectid);
        if (list != null && !list.isEmpty()) {
            for (Wxtemplate template : list) {
                if (template.getTrigger().equals(trigger.name()) && template.getStatus()) {
                    return template;
                }
            }
        }
        return null;
    }

    /**
     * @param trigger   触发方式枚举
     * @param projectid 项目ID
     * @return 返回模板列表
     */
    public List<Wxtemplate> getTemplateList(MsgTriggerEnum trigger, String projectid) {
        WxTemplateCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.WXTEMPLATE);
        List<Wxtemplate> list = cache.getDataList(projectid);
        List<Wxtemplate> templateList = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (Wxtemplate template : list) {
                if (template.getTrigger().equals(trigger.name()) && template.getStatus()) {
                    templateList.add(template);
                }
            }
        }
        return templateList;
    }

    @Override
    public String trans(String projectId, String key) {
        WxTemplateCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.WXTEMPLATE);
        Wxtemplate record = cache.getRecord(projectId, key);
        return record == null ? key : record.getContent();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        WxTemplateCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.WXTEMPLATE);
        List<Wxtemplate> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Wxtemplate template : list) {

            SelectDataNode node = new SelectDataNode();
            node.setCode(template.getCode());
            node.setDesc(template.getDescription());
            selectList.add(node);
        }
        return selectList;
    }
}
