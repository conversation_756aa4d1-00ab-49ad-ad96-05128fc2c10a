package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ProdGroupCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.entity.Prodgroup;
import com.cw.entity.Roomtype;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/14 0014
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.prodgroup)
public class ProdgoupDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        ProdGroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODGROUP);
        Prodgroup record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDispsignal();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ProdGroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODGROUP);
        List<Prodgroup> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Prodgroup prodgroup : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(prodgroup.getAreas());
            node.setDesc(prodgroup.getDispsignal());
            selectList.add(node);
        }
        return selectList;
    }

}
