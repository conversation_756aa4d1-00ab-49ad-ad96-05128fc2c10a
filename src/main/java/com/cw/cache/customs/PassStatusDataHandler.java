package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-04-21
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.passrsstatus)
public class PassStatusDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        StatusUtil.PassrsStatusEnum[] values = StatusUtil.PassrsStatusEnum.values();
        for (StatusUtil.PassrsStatusEnum statusEnum : values) {
            if (key.equals(statusEnum.getCode())) {
                return statusEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        StatusUtil.PassrsStatusEnum[] statuses = StatusUtil.PassrsStatusEnum.values();
        List<SelectDataNode> selectList = new ArrayList<>();
        //固定栏目模板
        StatusUtil.PassrsStatusEnum[] values = StatusUtil.PassrsStatusEnum.values();
        for (StatusUtil.PassrsStatusEnum passrsStatus : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(passrsStatus.getCode());
            node.setDesc(passrsStatus.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
