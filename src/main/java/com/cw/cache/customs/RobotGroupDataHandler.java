package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.RobotUtils;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-31
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.robotgroup)
public class RobotGroupDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        RobotUtils.RobotGroup[] values = RobotUtils.RobotGroup.values();
        for (RobotUtils.RobotGroup robotGroup : values) {
            if (robotGroup.name().equalsIgnoreCase(key)) {
                return robotGroup.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        RobotUtils.RobotGroup[] robotGroups = RobotUtils.RobotGroup.values();
        List<SelectDataNode> selectList = new ArrayList<>();
        for (RobotUtils.RobotGroup robotGroup : robotGroups) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(robotGroup.name());
            node.setDesc(robotGroup.getDesc());
            selectList.add(node);
        }
        return selectList;
    }
}
