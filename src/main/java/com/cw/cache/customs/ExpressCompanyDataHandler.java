package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.ExpressCompEnum;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 快递公司下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2023-02-08
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.expresscompany)
public class ExpressCompanyDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        ExpressCompEnum[] values = ExpressCompEnum.values();
        for (ExpressCompEnum company : values) {
            if (company.name().equals(key)) {
                return company.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        ExpressCompEnum[] values = ExpressCompEnum.values();
        for (ExpressCompEnum company : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(company.name());
            node.setDesc(company.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
