package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RulespolicyCache;
import com.cw.entity.Rulespolicy;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-09-19
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.rulespolicy)
public class RulespolicyDataHandler extends BaseCustomHandler {


    @Override
    public String trans(String projectId, String key) {
        RulespolicyCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RULESPOLICY);
        Rulespolicy record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        RulespolicyCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.RULESPOLICY);
        //取分组数据
        List<Rulespolicy> list = cache.getDataList(projectId);
        String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Rulespolicy rule : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(rule.getCode());//策略代码
            node.setDesc(rule.getDescription());//策略描述
            node.setGroup(rule.getControltype() + "");//判断条件
            selectList.add(node);

        }
        return selectList;
    }
}
