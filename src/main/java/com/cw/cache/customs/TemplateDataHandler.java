package com.cw.cache.customs;


import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.TemplateCache;
import com.cw.entity.Template;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/4 13:53
 **/
@CustomDataProp(dataType = SystemUtil.CustomDataKey.template)
public class TemplateDataHandler extends BaseCustomHandler {


    public Template getTemplate(String code, MsgTriggerEnum trigger, String projectid) {
        TemplateCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TEMPLATE);
        if (!code.isEmpty()) {
            return (Template) cache.getRecord(projectid, code);
        }
        List<Template> list = cache.getDataList(projectid);
        if (list != null && !list.isEmpty()) {
            for (Template template : list) {
                if (template.getTrigger().equals(trigger.name()) && template.getStatus()) {
                    return template;
                }
            }
        }
        return null;
    }

    /**
     * @param trigger   触发方式枚举
     * @param projectid 项目ID
     * @return 返回模板列表
     */
    public List<Template> getTemplateList(MsgTriggerEnum trigger, String projectid) {
        TemplateCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TEMPLATE);
        List<Template> list = cache.getDataList(projectid);
        List<Template> templateList = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (Template template : list) {
                if (template.getTrigger().equals(trigger.name()) && template.getStatus()) {
                    templateList.add(template);
                }
            }
        }
        return templateList;
    }

    @Override
    public String trans(String projectId, String key) {
        TemplateCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TEMPLATE);
        Template record = cache.getRecord(projectId, key);
        return record == null ? key : record.getContent();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        TemplateCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.TEMPLATE);
        List<Template> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Template template : list) {

                SelectDataNode node = new SelectDataNode();
            node.setCode(template.getCode());
            node.setDesc(template.getDescription());
            selectList.add(node);
        }
        return selectList;
    }
}
