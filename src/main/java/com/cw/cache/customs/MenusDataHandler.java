package com.cw.cache.customs;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomData;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.MenusCache;
import com.cw.entity.Menus;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.menus.MenuPlatformTypeEnums;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @Describe 一级栏目下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/29 0029
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.menus)
public class MenusDataHandler extends BaseCustomHandler {

    /**
     * 转换栏目名称
     *
     * @param projectId
     * @param key
     * @return
     */
    @Override
    public String trans(String projectId, String key) {
        MenusCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUS);
        Menus record = cache.getRecord(projectId, key);
        return record == null ? key : record.getMname();
    }


    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        MenusCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUS);
        List<Menus> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        //取分组数据
        String type = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        MenuPlatformTypeEnums typeEnums;
        if (type.equalsIgnoreCase(MenuPlatformTypeEnums.PC.getCode())) {
            typeEnums = MenuPlatformTypeEnums.PC;
        } else {
            typeEnums = MenuPlatformTypeEnums.MOBILE;
        }

        //排序List 按照sqlid columntype orgmenuid seq升序
        list.sort(Comparator.comparing(Menus::getColumntype).
                thenComparing(Menus::getOrgmenuid).thenComparing(Menus::getSeq).thenComparing(Menus::getSqlid));
        for (Menus menu : list) {
            SelectDataNode node = new SelectDataNode();
            //过滤值类型不匹配的值
            if (StringUtils.isNotBlank(type) && !type.equalsIgnoreCase(menu.getMtype())) {
                continue;
            }
            node.setCode(menu.getMenuid());
            node.setDesc(menu.getMname());
            //启动状态下拉框 orgmenuid字段为空为一级栏目
            if (StringUtils.isBlank(menu.getOrgmenuid())) {
                //一级栏目以上级分类代码分组
                node.setGroup(CustomData.getDesc(projectId, menu.getColumntype(), SystemUtil.CustomDataKey.column));
                node.setChildren(getChildData(cache, projectId, typeEnums, menu.getMenuid()));
            } else {
                //下级栏目以上级栏目分组
                node.setGroup(CustomData.getDesc(projectId, menu.getOrgmenuid(), SystemUtil.CustomDataKey.menusone));
                node.setChildren(getChildData(cache, projectId, typeEnums, menu.getMenuid()));
            }
            selectList.add(node);
        }
        return selectList;
    }

    /**
     * 递归获取当前menuid子项集合数据
     *
     * @param cache
     * @param projectId
     * @param typeEnums
     * @param menuid
     * @return
     */
    private List<SelectDataNode> getChildData(MenusCache cache, String projectId, MenuPlatformTypeEnums typeEnums, String menuid) {
        List<SelectDataNode> selectDataChildNodes = new ArrayList<>();
        List<Menus> childList = cache.getPageCards(projectId, typeEnums, menuid);
        if (CollectionUtil.isNotEmpty(childList)) {
            for (Menus childMenu : childList) {
                if (childMenu.getShow()) {//如果子项显示则加入子项数据
                    SelectDataNode childNode = new SelectDataNode();
                    childNode.setCode(childMenu.getMenuid());
                    childNode.setDesc(childMenu.getMname());
                    childNode.setGroup(CustomData.getDesc(projectId, childMenu.getOrgmenuid(), SystemUtil.CustomDataKey.menusone));
                    //递归查询子项数据
                    childNode.setChildren(getChildData(cache, projectId, typeEnums, childMenu.getMenuid()));
                    selectDataChildNodes.add(childNode);
                }
            }
        }
        return selectDataChildNodes;
    }




}
