package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.map.TravelTipTagEnum;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 路线标签
 * <AUTHOR> <PERSON>
 * @Create on 2023-07-13
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.tvltags)
public class MapTvlTagsDataHandler extends BaseCustomHandler {


    @Override
    public String trans(String projectId, String key) {
        TravelTipTagEnum[] values = TravelTipTagEnum.values();
        for (TravelTipTagEnum item : values) {
            if (item.name().equals(key)) {
                return item.getDesc();
            }
        }
        return key;

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        //路线规划标签
        TravelTipTagEnum[] values = TravelTipTagEnum.values();
        for (TravelTipTagEnum tagEnum : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(tagEnum.name());
            node.setDesc(tagEnum.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
