package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.EnumUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * @Describe 默认一级资源上级分类枚举下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/25 0025
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.category)
public class CategoryDataHandler extends BaseCustomHandler {


    @Override
    public String trans(String projectId, String key) {
        if (EnumUtils.isValidEnumIgnoreCase(SystemUtil.SourceType.class, key)) {
            SystemUtil.SourceType type = SystemUtil.SourceType.valueOf(key.toUpperCase(Locale.ROOT));
            return type.getDesc();
        } else {
            return key;
        }
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        SystemUtil.SourceType[] values = SystemUtil.SourceType.values();
        List<SelectDataNode> selectList = new ArrayList<>();
        for (SystemUtil.SourceType sourceType : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(sourceType.name());
            node.setDesc(sourceType.getDesc());
            selectList.add(node);
        }
        return selectList;
    }


}
