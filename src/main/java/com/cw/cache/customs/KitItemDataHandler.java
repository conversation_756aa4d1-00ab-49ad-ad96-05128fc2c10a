package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.KititemCache;
import com.cw.entity.Kititem;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/15 0015
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.kititem)
public class KitItemDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        KititemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITITEM);
        Kititem record = cache.getRecord(projectId, key);
        return record == null ? key : record.getProductdesc();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        KititemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.KITITEM);
        List<Kititem> list = cache.getDataList(projectId);
        //String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Kititem kititem : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(kititem.getProductcode());
            node.setDesc(kititem.getProductdesc());
            node.setGroup(kititem.getKitcode());
            selectList.add(node);
        }
        return selectList;
    }
}
