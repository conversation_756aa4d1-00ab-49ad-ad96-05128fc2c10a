package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.MenuContentCache;
import com.cw.entity.Menu_content;
import com.cw.entity.Menus;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/29 0029
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.menucontent)
public class MenuContentDataHandler extends BaseCustomHandler {

    /**
     * 转换名称
     *
     * @param projectId
     * @param key
     * @return
     */
    @Override
    public String trans(String projectId, String key) {
        MenuContentCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        Menu_content record = cache.getRecord(projectId, key);
        return record == null ? key : record.getTitle();
    }


    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        MenuContentCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUCONTENT);
        //取分组数据
        //String menuId = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<Menu_content> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Menu_content menuContent : list) {
            //启动状态下拉框
            SelectDataNode node = new SelectDataNode();
            node.setCode(menuContent.getTitle());
            node.setDesc(menuContent.getSubtitle());
            String menuid = menuContent.getMenuid();
            Menus menus = (Menus) GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUS).getRecord(projectId, menuid);
            if (menus != null) {
                node.setGroup(menus.getMname());
            }
            selectList.add(node);
        }
        return selectList;
    }
}
