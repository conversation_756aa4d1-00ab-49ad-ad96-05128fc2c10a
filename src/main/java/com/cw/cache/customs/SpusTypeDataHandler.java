package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.SpusType;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-03
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.spustype)
public class SpusTypeDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        SpusType[] spusTypes = SpusType.values();
        String record = null;
        for (SpusType spusType : spusTypes) {
            if (key.equalsIgnoreCase(String.valueOf(spusType.getVal()))) {
                record = spusType.getDesc();
                break;
            }
        }
        return record == null ? key : record;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        SpusType[] spusTypes = SpusType.values();
        List<SelectDataNode> selectList = new ArrayList<>();
        //目前产品类型
        for (SpusType spusType : spusTypes) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(spusType.getVal());
            node.setDesc(spusType.getDesc());
            selectList.add(node);

        }
        return selectList;
    }
}
