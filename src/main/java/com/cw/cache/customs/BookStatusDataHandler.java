package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 主订单状态下拉
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/9 0009
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.mainstatus)
public class BookStatusDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        StatusUtil.BookingRSStatusEnum[] values = StatusUtil.BookingRSStatusEnum.values();
        for (StatusUtil.BookingRSStatusEnum statusEnum : values) {
            if (statusEnum.getCode().equals(key)) {
                return statusEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        StatusUtil.BookingRSStatusEnum[] values = StatusUtil.BookingRSStatusEnum.values();
        for (StatusUtil.BookingRSStatusEnum status : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(status.getCode());
            node.setDesc(status.getDesc());
            selectList.add(node);
        }

        return selectList;

    }
}
