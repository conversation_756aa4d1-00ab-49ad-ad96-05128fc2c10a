package com.cw.cache.customs;

import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/9 0009
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.payment)
public class PaymentDataHandler extends FactorDataHandler {
    public PaymentDataHandler() {
        super();
        factoryType = SystemUtil.FactoryType.PAYMENT;
    }

    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    //@Override
    //public String trans(String projectId, String key) {
    //    if (EnumUtils.isValidEnumIgnoreCase(SystemUtil.Payment.class, key)) {
    //        SystemUtil.Payment type = SystemUtil.Payment.valueOf(key.toUpperCase(Locale.ROOT));
    //        return type.getDesc();
    //    } else {
    //        return key;
    //    }
    //
    //}
    //
    //@Override
    //public List<SelectDataNode> getSelectData(String projectId, String... param) {
    //    List<SelectDataNode> selectList = new ArrayList<>();
    //    SystemUtil.Payment[] values = SystemUtil.Payment.values();
    //    for (SystemUtil.Payment wayType : values) {
    //        SelectDataNode node = new SelectDataNode();
    //        node.setCode(wayType.name());
    //        node.setDesc(wayType.getDesc());
    //        selectList.add(node);
    //    }
    //
    //    return selectList;
    //}
}
