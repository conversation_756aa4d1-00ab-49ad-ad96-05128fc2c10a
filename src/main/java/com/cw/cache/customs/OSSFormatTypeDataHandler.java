package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.OSSFormatEnum;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe oss文件格式枚举
 * <AUTHOR> <PERSON>
 * @Create on 2023-11-13
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.ossformat)
public class OSSFormatTypeDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        OSSFormatEnum[] values = OSSFormatEnum.values();

        List<SelectDataNode> selectList = new ArrayList<>();
        String format = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        for (OSSFormatEnum formatEnum : values) {
            SelectDataNode node = new SelectDataNode();
            //匹配类型
            if (StringUtils.isNotBlank(format) && formatEnum.getFormatType().name().equalsIgnoreCase(format)) {
                node.setCode(formatEnum.getDesc());
                node.setDesc(formatEnum.getDesc());
                node.setGroup(formatEnum.getFormatType().name());
                selectList.add(node);
            }

        }
        return selectList;
    }
}
