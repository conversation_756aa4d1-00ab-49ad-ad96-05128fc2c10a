package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.GiftItemCache;
import com.cw.entity.Giftitem;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-12-20
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.giftitem)
public class GiftitemDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        GiftItemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
        Giftitem record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        GiftItemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFTITEM);
        List<Giftitem> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Giftitem giftitem : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(giftitem.getCode());
            node.setDesc(giftitem.getDescription());
            selectList.add(node);
        }
        return selectList;
    }
}
