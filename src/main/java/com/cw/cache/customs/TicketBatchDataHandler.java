package com.cw.cache.customs;

import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;

/**
 * @Describe 票型批次
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/26 0026
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.ticketbatch)
public class TicketBatchDataHandler extends FactorDataHandler {

    public TicketBatchDataHandler() {
        super();
        factoryType = SystemUtil.FactoryType.TICKETBATCH;
    }

    @Override
    public String trans(String projectId,String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

}
