package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ShopsiteCache;
import com.cw.entity.Shopsite;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-10-31
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.shopitem)
public class ShopsiteDataHandler extends BaseCustomHandler {


    @Override
    public String trans(String projectId, String key) {
        ShopsiteCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SHOPSITE);
        Shopsite record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ShopsiteCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SHOPSITE);
        //取所有数据
        List<Shopsite> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Shopsite shopsite : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(shopsite.getCode());
            node.setDesc(shopsite.getDescription());
            selectList.add(node);

        }
        return selectList;
    }
}
