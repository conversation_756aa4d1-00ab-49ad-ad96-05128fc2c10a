package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.ProductKitCache;
import com.cw.entity.Productkit;
import com.cw.utils.CalculateDate;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/14 0014
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.productkit)
public class ProductKitDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        ProductKitCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT);
        Productkit record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        ProductKitCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.PRODUCTKIT);
        List<SelectDataNode> selectList = new ArrayList<>();
        List<Productkit> list = cache.getDataList(projectId);
        list.sort(Comparator.comparing(Productkit::getSqlid));
        String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        for (Productkit productkit : list) {
            if (CalculateDate.compareDates(new Date(), productkit.getEnddate()) > 0L) {//过滤过期套餐
                continue;
            }
            SelectDataNode node = new SelectDataNode();
            //如果过滤字段不为空，则过滤掉groupid不为空的
            if (StringUtils.isBlank(header) && StringUtils.isNotBlank(productkit.getGroupid())) {
                continue;
            }
            node.setCode(productkit.getCode());
            node.setDesc(productkit.getDescription());
            node.setGroup(productkit.getGroupid());
            selectList.add(node);
        }
        return selectList;
    }
}
