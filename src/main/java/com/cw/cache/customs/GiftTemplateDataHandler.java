package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.GiftCache;
import com.cw.entity.Gift;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-12-20
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.gift)
public class GiftTemplateDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        GiftCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT);
        Gift record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        GiftCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.GIFT);
        List<Gift> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        String header = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        for (Gift gift : list) {
            SelectDataNode node = new SelectDataNode();
            if (StringUtils.isNotBlank(header) && !header.equalsIgnoreCase(gift.getCode())) {
                continue;
            } else {
                node.setCode(gift.getCode());
                node.setDesc(gift.getDescription());
                selectList.add(node);
            }
        }
        return selectList;
    }
}
