package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.OfflineProdType;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 线下产品类型数据
 * <AUTHOR> <PERSON>
 * @Create on 2022-04-21
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.olptype)
public class OfflinePtypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        OfflineProdType[] prodTypes = OfflineProdType.values();
        String record = null;
        for (OfflineProdType prodType : prodTypes) {
            if (key.equalsIgnoreCase(prodType.val())) {
                record = prodType.getDesc();
                break;
            }
        }
        return record == null ? key : record;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        OfflineProdType[] prodTypes = OfflineProdType.values();
        List<SelectDataNode> selectList = new ArrayList<>();
        //目前产品类型
        for (OfflineProdType prodType : prodTypes) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(prodType.val());
            node.setDesc(prodType.getDesc());
            selectList.add(node);

        }
        return selectList;
    }
}
