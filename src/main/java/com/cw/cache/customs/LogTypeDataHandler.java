package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.EnumUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 用户操作日志类型
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/27 0027
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.userlogtype)
public class LogTypeDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        if (EnumUtils.isValidEnumIgnoreCase(SystemUtil.UserLogType.class, key)) {
            SystemUtil.UserLogType cardType = SystemUtil.UserLogType.valueOf(key);
            return cardType.getDesc();
        } else {
            return key;
        }

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        //固定栏目模板
        SystemUtil.UserLogType[] values = SystemUtil.UserLogType.values();
        for (SystemUtil.UserLogType userLogType : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(userLogType.name());
            node.setDesc(userLogType.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
