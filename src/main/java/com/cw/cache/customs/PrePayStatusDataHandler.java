package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.EnumUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 付款退款状态下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/13 0013
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.prepaystatus)
public class PrePayStatusDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        if (EnumUtils.isValidEnumIgnoreCase(StatusUtil.PrePayStatusEnum.class, key)) {
            StatusUtil.PrePayStatusEnum cardType = StatusUtil.PrePayStatusEnum.valueOf(key);
            return cardType.getDesc();
        } else {
            return key;
        }

    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        //固定栏目模板
        StatusUtil.PrePayStatusEnum[] values = StatusUtil.PrePayStatusEnum.values();
        for (StatusUtil.PrePayStatusEnum cardType : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(cardType.name());
            node.setDesc(cardType.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
