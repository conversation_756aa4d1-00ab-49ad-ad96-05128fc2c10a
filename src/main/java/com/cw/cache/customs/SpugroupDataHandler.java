package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SpugroupCache;
import com.cw.entity.Spugroup;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-02-22
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.spugroup)
public class SpugroupDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        SpugroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP);
        Spugroup record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        SpugroupCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUGROUP);
        List<Spugroup> list = cache.getDataList(projectId);
        String groupid = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Spugroup spugroup : list) {
            if (StringUtils.isNotBlank(groupid) && !spugroup.getGroupid().equals(groupid)) {
                continue;
            }
            SelectDataNode node = new SelectDataNode();
            node.setCode(spugroup.getCode());
            node.setDesc(spugroup.getDescription());
            node.setGroup(spugroup.getGroupid());
            selectList.add(node);
        }
        return selectList;
    }
}
