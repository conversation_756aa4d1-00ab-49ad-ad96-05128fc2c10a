package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

@CustomDataProp(dataType = SystemUtil.CustomDataKey.actstatus)
public class ActRsStatusDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        StatusUtil.ActRsStatusEnum[] values = StatusUtil.ActRsStatusEnum.values();
        for (StatusUtil.ActRsStatusEnum statusEnum : values) {
            if (statusEnum.getCode().equals(key)) {
                return statusEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        StatusUtil.ActRsStatusEnum[] values = StatusUtil.ActRsStatusEnum.values();
        for (StatusUtil.ActRsStatusEnum status : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(status.getCode());
            node.setDesc(status.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
