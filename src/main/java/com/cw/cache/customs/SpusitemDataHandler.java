package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.SpusitemCache;
import com.cw.entity.Spusitem;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-02-22
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.spusitem)
public class SpusitemDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        SpusitemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM);
        Spusitem record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        SpusitemCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.SPUSITEM);
        List<Spusitem> list = cache.getDataList(projectId);
        String groupid = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Spusitem spusitem : list) {
            if (StringUtils.isNotBlank(groupid) && spusitem.getGroupid().equals(groupid)) {
                continue;
            }
            SelectDataNode node = new SelectDataNode();
            node.setCode(spusitem.getCode());
            node.setDesc(spusitem.getDescription());
            node.setGroup(spusitem.getGroupid());
            selectList.add(node);
        }
        return selectList;
    }
}
