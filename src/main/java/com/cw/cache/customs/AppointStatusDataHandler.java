package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 会场预定状态下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/26 0026
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.mstatus)
public class AppointStatusDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        StatusUtil.MeetingRsStatusEnum[] values = StatusUtil.MeetingRsStatusEnum.values();
        for (StatusUtil.MeetingRsStatusEnum statusEnum : values) {
            if (statusEnum.getCode().equals(key)) {
                return statusEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        StatusUtil.MeetingRsStatusEnum[] values = StatusUtil.MeetingRsStatusEnum.values();
        for (StatusUtil.MeetingRsStatusEnum status : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(status.getCode());
            node.setDesc(status.getDesc());
            selectList.add(node);
        }

        return selectList;

    }
}
