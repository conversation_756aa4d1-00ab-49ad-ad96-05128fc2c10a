package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.meeting.MeetingStyleEnum;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/10 0010
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.meetingstyle)
//public class MeetingStyleDataHandler extends FactorDataHandler {
public class MeetingStyleDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        MeetingStyleEnum[] values = MeetingStyleEnum.values();
        for (MeetingStyleEnum styleEnum : values) {
            if (styleEnum.getVal().equals(key)) {
                return styleEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        MeetingStyleEnum[] values = MeetingStyleEnum.values();
        for (MeetingStyleEnum styleEnum : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(styleEnum.getVal());
            node.setDesc(styleEnum.getDesc());
            selectList.add(node);
        }

        return selectList;
    }

    //public MeetingStyleDataHandler() {
    //    super();
    //    factoryType = SystemUtil.FactoryType.MEETINGSTYLE;
    //}
    //
    //@Override
    //public String trans(String projectId, String key) {
    //    FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
    //    Factor record = cache.getRecord(projectId, key);
    //    return record == null ? key : record.getDescription();
    //}
}
