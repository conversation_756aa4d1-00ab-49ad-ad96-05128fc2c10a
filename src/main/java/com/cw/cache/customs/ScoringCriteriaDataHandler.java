package com.cw.cache.customs;

import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FactorCache;
import com.cw.entity.Factor;
import com.cw.utils.SystemUtil;
import org.checkerframework.checker.units.qual.C;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/10 0010
 */
@C
@CustomDataProp(dataType = SystemUtil.CustomDataKey.scoringcriteria)
public class ScoringCriteriaDataHandler extends FactorDataHandler {

    public ScoringCriteriaDataHandler() {
        super();
        factoryType = SystemUtil.FactoryType.SCORINGCRITERIA;
    }

    @Override
    public String trans(String projectId, String key) {
        FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
        Factor record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }
}
