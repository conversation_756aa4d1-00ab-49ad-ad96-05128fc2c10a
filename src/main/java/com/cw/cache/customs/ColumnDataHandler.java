package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.EnumUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * @Describe 默认一级栏目上级分类
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/27 0027
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.column)
public class ColumnDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        if (EnumUtils.isValidEnumIgnoreCase(SystemUtil.ColumnType.class, key)) {
            SystemUtil.ColumnType type = SystemUtil.ColumnType.valueOf(key.toUpperCase(Locale.ROOT));
            return type.getDesc();
        } else {
            return key;
        }
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        SystemUtil.ColumnType[] values = SystemUtil.ColumnType.values();
        for (SystemUtil.ColumnType sourceType : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(sourceType.name());
            node.setDesc(sourceType.getDesc());
            selectList.add(node);
        }

        return selectList;
    }
}
