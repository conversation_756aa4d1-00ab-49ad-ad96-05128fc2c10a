package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.HotelCache;
import com.cw.entity.Hotel;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/18 0018
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.hotel)
public class HotelDataHandler extends BaseCustomHandler {

    @Override
    public String trans(String projectId, String key) {
        HotelCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL);
        Hotel record = cache.getRecord(projectId, key);
        return record == null ? key : record.getDescription();
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        HotelCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.HOTEL);
        List<Hotel> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        for (Hotel hotel : list) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(hotel.getCode());
            node.setDesc(hotel.getDescription());
            selectList.add(node);
        }
        return selectList;
    }
}
