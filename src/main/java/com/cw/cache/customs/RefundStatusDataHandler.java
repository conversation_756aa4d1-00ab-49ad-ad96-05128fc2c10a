package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.StatusUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe 退款状态下拉框
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/24 0024
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.refundstatus)
public class RefundStatusDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        StatusUtil.RefundStatusEnum[] values = StatusUtil.RefundStatusEnum.values();
        for (StatusUtil.RefundStatusEnum statusEnum : values) {
            if (statusEnum.getCode().equals(key)) {
                return statusEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        List<SelectDataNode> selectList = new ArrayList<>();
        StatusUtil.RefundStatusEnum[] values = StatusUtil.RefundStatusEnum.values();
        for (StatusUtil.RefundStatusEnum status : values) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(status.getCode());
            node.setDesc(status.getDesc());
            selectList.add(node);
        }

        return selectList;

    }
}
