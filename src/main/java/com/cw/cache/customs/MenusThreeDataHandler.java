package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomData;
import com.cw.cache.CustomDataProp;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.MenusCache;
import com.cw.entity.Menus;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/29 0029
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.menusthree)
public class MenusThreeDataHandler extends BaseCustomHandler {

    /**
     * 转换栏目名称
     *
     * @param projectId
     * @param key
     * @return
     */
    @Override
    public String trans(String projectId, String key) {
        MenusCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUS);
        Menus record = cache.getRecord(projectId, key);
        return record == null ? key : record.getMname();
    }


    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        MenusCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.MENUS);
        //取分组数据
        List<Menus> list = cache.getDataList(projectId);
        List<SelectDataNode> selectList = new ArrayList<>();
        String mtype = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        for (Menus menu : list) {
            //启动状态下拉框 orgmenuid不为为空 等级为二级栏目
            if (StringUtils.isNotBlank(menu.getOrgmenuid()) && "3".equals(menu.getMenugrade())) {
                //过滤类型 区分平台类型
                if ((StringUtils.isNotBlank(mtype) && !mtype.equals(menu.getMtype()))) {
                    continue;
                }
                SelectDataNode node = new SelectDataNode();
                node.setCode(menu.getMenuid());
                node.setDesc(menu.getMname());
                //一级栏目名称转换
                node.setGroup(CustomData.getDesc(projectId, menu.getOrgmenuid(), SystemUtil.CustomDataKey.menusone));

                selectList.add(node);
            }
        }
        return selectList;
    }

}
