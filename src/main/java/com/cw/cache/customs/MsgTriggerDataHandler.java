package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.sms.MsgTriggerEnum;
import com.cw.utils.pagedata.SelectDataNode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-31
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.msgtrigger)
public class MsgTriggerDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        MsgTriggerEnum[] values = MsgTriggerEnum.values();
        for (MsgTriggerEnum msgTriggerEnum : values) {
            if (msgTriggerEnum.name().equals(key)) {
                return msgTriggerEnum.getDesc();
            }
        }
        return key;
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {

        MsgTriggerEnum[] msgTriggerEnums = MsgTriggerEnum.values();
        String type = param != null && param.length > 0 && param[0] != null ? param[0] : "";
        List<SelectDataNode> selectList = new ArrayList<>();
        for (MsgTriggerEnum msgTrigger : msgTriggerEnums) {
            SelectDataNode node = new SelectDataNode();
            node.setCode(msgTrigger.name());
            node.setDesc(msgTrigger.getDesc());
            selectList.add(node);
        }
        return selectList;
    }
}
