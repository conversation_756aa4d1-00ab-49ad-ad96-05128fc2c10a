package com.cw.cache.customs;

import com.cw.cache.BaseCustomHandler;
import com.cw.cache.CustomDataProp;
import com.cw.utils.SystemUtil;
import com.cw.utils.pagedata.SelectDataNode;
import com.cw.utils.province.ProvinceUtil;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-12-01
 */
@CustomDataProp(dataType = SystemUtil.CustomDataKey.province)
//public class ProvinceDataHandler extends FactorDataHandler {
public class ProvinceDataHandler extends BaseCustomHandler {
    @Override
    public String trans(String projectId, String key) {
        return ProvinceUtil.transCodeToDesc(key);
    }

    @Override
    public List<SelectDataNode> getSelectData(String projectId, String... param) {
        return ProvinceUtil.getAllDates();
    }

    //public ProvinceDataHandler() {
    //    super();
    //    this.factoryType = SystemUtil.FactoryType.PROVINCE;
    //}
    //
    //@Override
    //public String trans(String projectId, String key) {
    //    FactorCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.FACTOR);
    //    Factor record = cache.getRecord(projectId, key);
    //    return record == null ? key : record.getDescription();
    //}
}
