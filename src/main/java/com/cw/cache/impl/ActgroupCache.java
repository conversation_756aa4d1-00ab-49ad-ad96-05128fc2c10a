package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Actgroup;
import com.cw.entity.Rlang;
import com.cw.mapper.ActgroupMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.ACTGROUP, cacheClass = Actgroup.class)
public class ActgroupCache extends BaseCacheData<Actgroup> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Actgroup>> datas) {
        ActgroupMapper mapper = SpringUtil.getBean(ActgroupMapper.class);
        List<Actgroup> lists = mapper.findAll();
        Map<String, List<Actgroup>> projectdata = lists.stream().collect(Collectors.groupingBy(Actgroup::getProjectid));
        //按照项目分类
        //for (String projectId : projectdata.keySet()) {
        //    datas.put(projectId, Maps.newConcurrentMap());
        //    ConcurrentMap<String, List<Actgroup>> factors = lists.stream().filter(r -> r.getProjectid().equals(projectId))
        //            .collect(Collectors.groupingByConcurrent(Actgroup::getGroupid));
        //    for (Map.Entry<String, List<Actgroup>> entry : factors.entrySet()) {
        //        groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //    }
        //}
        for (Map.Entry<String, List<Actgroup>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Actgroup> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Actgroup::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Actgroup> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Actgroup> actgroups = daoLocal.getProjectObjectList(Actgroup.class, projectId);
        for (Actgroup Actgroup : actgroups) {
            projectdatas.put(Actgroup.getCode(), Actgroup);
        }
        //ConcurrentMap<String, List<Actgroup>> factorMap = actgroups.stream().collect(Collectors.groupingByConcurrent(Actgroup::getGroupid));
        //for (Map.Entry<String, List<Actgroup>> entry : factorMap.entrySet()) {
        //    groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //}
    }

    @Override
    protected void sortList(List<Actgroup> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Actgroup::getSeq));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description,noticetext", "description");
    }
}
