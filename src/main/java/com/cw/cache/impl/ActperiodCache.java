package com.cw.cache.impl;


import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Actperiod;
import com.cw.entity.Rlang;
import com.cw.mapper.ActperiodMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.ACTPERIOD, cacheClass = Actperiod.class)
public class ActperiodCache extends BaseCacheData<Actperiod> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Actperiod>> datas) {
        ActperiodMapper mapper = SpringUtil.getBean(ActperiodMapper.class);
        List<Actperiod> lists = mapper.findAll();
        Map<String, List<Actperiod>> projectdata = lists.stream().collect(Collectors.groupingBy(Actperiod::getProjectid));

        //充填按代码分类
        for (Map.Entry<String, List<Actperiod>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Actperiod> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Actperiod::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Actperiod> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Actperiod> actsites = daoLocal.getProjectObjectList(Actperiod.class, projectId);
        for (Actperiod actsite : actsites) {
            projectdatas.put(actsite.getCode(), actsite);
        }

    }

    @Override
    protected void sortList(List<Actperiod> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Actperiod::getCode));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description", "description");
    }
}
