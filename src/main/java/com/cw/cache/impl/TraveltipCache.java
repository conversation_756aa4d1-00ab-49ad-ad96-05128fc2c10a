package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Traveltip;
import com.cw.mapper.TraveltipMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe 路线规划缓存
 * <AUTHOR> <PERSON>
 * @Create on 2023-07-13
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.TRAVELTIP, cacheClass = Traveltip.class)
public class TraveltipCache extends BaseCacheData<Traveltip> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Traveltip>> datas) {
        TraveltipMapper mapper = SpringUtil.getBean(TraveltipMapper.class);
        List<Traveltip> lists = mapper.findAll();
        Map<String, List<Traveltip>> projectdata = lists.stream().collect(Collectors.groupingBy(Traveltip::getProjectid));
        for (Map.Entry<String, List<Traveltip>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Traveltip> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Traveltip::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Traveltip> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Traveltip> records = daoLocal.getProjectObjectList(Traveltip.class, projectId);
        for (Traveltip record : records) {
            projectdatas.put(record.getCode(), record);
        }
    }

    @Override
    protected void sortList(List<Traveltip> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Traveltip::getSeq));
    }

}
