package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Kitgroup;
import com.cw.entity.Rlang;
import com.cw.mapper.KitgroupMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/1 0001
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.KITGROUP, cacheClass = Kitgroup.class)
public class KitGroupCache extends BaseCacheData<Kitgroup> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Kitgroup>> datas) {
        KitgroupMapper mapper = SpringUtil.getBean(KitgroupMapper.class);
        List<Kitgroup> lists = mapper.findAll();
        Map<String, List<Kitgroup>> projectdata = lists.stream().collect(Collectors.groupingBy(Kitgroup::getProjectid));
        for (Map.Entry<String, List<Kitgroup>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Kitgroup> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Kitgroup::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Kitgroup> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Kitgroup> kitgroupList = daoLocal.getProjectObjectList(Kitgroup.class, projectId);
        for (Kitgroup kitgroup : kitgroupList) {
            projectdatas.put(kitgroup.getCode(), kitgroup);
        }
        //ConcurrentMap<String, List<Kitgroup>> kitgroupMap = kitgroupList.stream().collect(Collectors.groupingByConcurrent(Kitgroup::getCode));
        //for (Map.Entry<String, List<Kitgroup>> entry : kitgroupMap.entrySet()) {
        //    groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //}
    }

    @Override
    protected void sortList(List<Kitgroup> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Kitgroup::getSeq));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description,introduction,detailtext", "description");
    }


}
