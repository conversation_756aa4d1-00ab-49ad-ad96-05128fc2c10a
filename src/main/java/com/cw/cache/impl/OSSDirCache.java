package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Ossdir;
import com.cw.mapper.OSSDirMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-11-23
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.OSSDIR, cacheClass = Ossdir.class)
public class OSSDirCache extends BaseCacheData<Ossdir> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Ossdir>> datas) {
        OSSDirMapper mapper = SpringUtil.getBean(OSSDirMapper.class);
        List<Ossdir> lists = mapper.findAll();
        Map<String, List<Ossdir>> projectdata = lists.stream().collect(Collectors.groupingBy(Ossdir::getProjectid));
        //填充按分组代码数据分类
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Ossdir>> dirList = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Ossdir::getGroup));
            for (Map.Entry<String, List<Ossdir>> entry : dirList.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        //充填按代码分类
        for (Map.Entry<String, List<Ossdir>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Ossdir> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Ossdir::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Ossdir> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Ossdir> dirList = daoLocal.getProjectObjectList(Ossdir.class, projectId);
        for (Ossdir ossdir : dirList) {
            projectdatas.put(ossdir.getCode(), ossdir);
        }
        ConcurrentMap<String, List<Ossdir>> dirMap = dirList.stream().collect(Collectors.groupingByConcurrent(Ossdir::getGroup));
        for (Map.Entry<String, List<Ossdir>> entry : dirMap.entrySet()) {
            //todo 删除通知记录没做删除修改
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Ossdir> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Ossdir::getSqlid));
    }
}
