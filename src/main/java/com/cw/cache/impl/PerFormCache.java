package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Perform;
import com.cw.mapper.PerformMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-07-03
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.PERFORM, cacheClass = Perform.class)
public class PerFormCache extends BaseCacheData<Perform> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Perform>> datas) {
        PerformMapper mapper = SpringUtil.getBean(PerformMapper.class);
        List<Perform> lists = mapper.findAll();
        Map<String, List<Perform>> projectdata = lists.stream().collect(Collectors.groupingBy(Perform::getProjectid));
        for (Map.Entry<String, List<Perform>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Perform> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Perform::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Perform> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Perform> performs = daoLocal.getProjectObjectList(Perform.class, projectId);
        for (Perform perform : performs) {
            projectdatas.put(perform.getCode(), perform);
        }
    }

    @Override
    protected void sortList(List<Perform> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Perform::getSqlid));
    }


}
