package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Rlang;
import com.cw.entity.Ticket;
import com.cw.mapper.TicketMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/18 0018
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.TICKET, cacheClass = Ticket.class)
public class TicketCache extends BaseCacheData<Ticket> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Ticket>> datas) {
        TicketMapper mapper = SpringUtil.getBean(TicketMapper.class);
        List<Ticket> lists = mapper.findAll();
        Map<String, List<Ticket>> projectdata = lists.stream().collect(Collectors.groupingBy(Ticket::getProjectid));
        //填充按分组代码数据分类
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Ticket>> tickets = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Ticket::getGroupid));
            for (Map.Entry<String, List<Ticket>> entry : tickets.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        //充填按代码分类
        for (Map.Entry<String, List<Ticket>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Ticket> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Ticket::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Ticket> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Ticket> tickets = daoLocal.getProjectObjectList(Ticket.class, projectId);
        for (Ticket ticket : tickets) {
            projectdatas.put(ticket.getCode(), ticket);
        }
        ConcurrentMap<String, List<Ticket>> ticketMap = tickets.stream().collect(Collectors.groupingByConcurrent(Ticket::getGroupid));
        for (Map.Entry<String, List<Ticket>> entry : ticketMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Ticket> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Ticket::getGroupid).thenComparing(Ticket::getSeq));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description", "description");
    }
}
