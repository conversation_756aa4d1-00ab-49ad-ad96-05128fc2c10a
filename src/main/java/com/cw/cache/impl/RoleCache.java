package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Op_role;
import com.cw.mapper.Op_roleMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/2 09:47
 **/
@CacheProp(dataType = SystemUtil.GlobalDataType.USER_ROLE, cacheClass = Op_role.class)
public class RoleCache extends BaseCacheData<Op_role> {


    public RoleCache() {
        super();
        this.defaultCodeFiled = "roleid";  //重载.修改默认的下拉取值 key 字段
    }

    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Op_role>> datas) {
        Op_roleMapper mapper = SpringUtil.getBean(Op_roleMapper.class);
        List<Op_role> lists = mapper.findAll();
        Map<String, List<Op_role>> projectdata = lists.stream().collect(Collectors.groupingBy(Op_role::getProjectid));
        for (Map.Entry<String, List<Op_role>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Op_role> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Op_role::getRoleid,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    /**
     * 获取数据.重新填充map.交给子类填充实现
     *
     * @param projectdatas
     * @param projectId
     */
    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Op_role> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Op_role> list = daoLocal.getProjectObjectList(Op_role.class, projectId);
        for (Op_role opRole : list) {
            projectdatas.put(opRole.getRoleid(), opRole);
        }
    }
}
