package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Coupon;
import com.cw.mapper.CouponMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-01-21
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.COUPON, cacheClass = Coupon.class)
public class CouponCache extends BaseCacheData<Coupon> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Coupon>> datas) {
        CouponMapper mapper = SpringUtil.getBean(CouponMapper.class);
        List<Coupon> lists = mapper.findAll();
        Map<String, List<Coupon>> projectdata = lists.stream().collect(Collectors.groupingBy(Coupon::getProjectid));
        //填充按分组代码数据分类
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Coupon>> Coupons = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Coupon::getGroupid));
            for (Map.Entry<String, List<Coupon>> entry : Coupons.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        //充填按代码分类
        for (Map.Entry<String, List<Coupon>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Coupon> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Coupon::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Coupon> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Coupon> Coupons = daoLocal.getProjectObjectList(Coupon.class, projectId);
        for (Coupon Coupon : Coupons) {
            projectdatas.put(Coupon.getCode(), Coupon);
        }
        ConcurrentMap<String, List<Coupon>> CouponMap = Coupons.stream().collect(Collectors.groupingByConcurrent(Coupon::getGroupid));
        for (Map.Entry<String, List<Coupon>> entry : CouponMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Coupon> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Coupon::getGroupid).thenComparing(Coupon::getSeq));
    }
}
