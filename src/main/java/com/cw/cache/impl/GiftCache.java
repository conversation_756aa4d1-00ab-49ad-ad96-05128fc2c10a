package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Gift;
import com.cw.mapper.GiftMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2022-02-24
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.GIFT, cacheClass = Gift.class)
public class GiftCache extends BaseCacheData<Gift> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Gift>> datas) {
        GiftMapper mapper = SpringUtil.getBean(GiftMapper.class);
        List<Gift> lists = mapper.findAll();
        Map<String, List<Gift>> projectdata = lists.stream().collect(Collectors.groupingBy(Gift::getProjectid));
        //for (String projectId : projectdata.keySet()) {
        //    datas.put(projectId, Maps.newConcurrentMap());
        //
        //    ConcurrentMap<String, List<Gift>> gifts = lists.stream().filter(r -> r.getProjectid().equals(projectId))
        //            .collect(Collectors.groupingByConcurrent(Gift::getGroupid));
        //    for (Map.Entry<String, List<Gift>> entry : gifts.entrySet()) {
        //        groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //    }
        //}
        for (Map.Entry<String, List<Gift>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Gift> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Gift::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Gift> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Gift> gifts = daoLocal.getProjectObjectList(Gift.class, projectId);
        for (Gift Gift : gifts) {
            projectdatas.put(Gift.getCode(), Gift);
        }
        //ConcurrentMap<String, List<Gift>> giftMap = gifts.stream().collect(Collectors.groupingByConcurrent(Gift::getGroupid));
        //for (Map.Entry<String, List<Gift>> entry : giftMap.entrySet()) {
        //    groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //}
    }

    @Override
    protected void sortList(List<Gift> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Gift::getSeq));
    }
}
