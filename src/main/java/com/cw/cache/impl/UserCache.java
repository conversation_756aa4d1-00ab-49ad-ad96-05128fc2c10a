package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Op_user;
import com.cw.mapper.Op_userMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/2 09:47
 **/
@CacheProp(dataType = SystemUtil.GlobalDataType.USER, cacheClass = Op_user.class)
public class UserCache extends BaseCacheData<Op_user> {


    public UserCache() {
        super();
        this.defaultCodeFiled = "userid";  //重载.修改默认的下拉取值 key 字段
    }

    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Op_user>> datas) {
        Op_userMapper mapper = SpringUtil.getBean(Op_userMapper.class);
        List<Op_user> lists = mapper.findAll();
        Map<String, List<Op_user>> projectdata = lists.stream().collect(Collectors.groupingBy(Op_user::getProjectid));
        for (Map.Entry<String, List<Op_user>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Op_user> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Op_user::getUserid,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Op_user> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Op_user> records = daoLocal.getProjectObjectList(Op_user.class, projectId);
        for (Op_user record : records) {
            projectdatas.put(record.getUserid(), record);
        }
    }
}
