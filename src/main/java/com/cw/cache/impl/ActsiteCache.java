package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Actsite;
import com.cw.mapper.ActsiteMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.ACTSITE, cacheClass = Actsite.class)
public class ActsiteCache extends BaseCacheData<Actsite> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Actsite>> datas) {
        ActsiteMapper mapper = SpringUtil.getBean(ActsiteMapper.class);
        List<Actsite> lists = mapper.findAll();
        Map<String, List<Actsite>> projectdata = lists.stream().collect(Collectors.groupingBy(Actsite::getProjectid));
        //填充按分组代码数据分类
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Actsite>> actsites = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Actsite::getGroupid));
            for (Map.Entry<String, List<Actsite>> entry : actsites.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        //充填按代码分类
        for (Map.Entry<String, List<Actsite>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Actsite> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Actsite::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Actsite> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Actsite> actsites = daoLocal.getProjectObjectList(Actsite.class, projectId);
        for (Actsite actsite : actsites) {
            projectdatas.put(actsite.getCode(), actsite);
        }
        ConcurrentMap<String, List<Actsite>> actsiteMap = actsites.stream().collect(Collectors.groupingByConcurrent(Actsite::getGroupid));
        for (Map.Entry<String, List<Actsite>> entry : actsiteMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Actsite> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Actsite::getGroupid).thenComparing(Actsite::getSeq));
    }
}
