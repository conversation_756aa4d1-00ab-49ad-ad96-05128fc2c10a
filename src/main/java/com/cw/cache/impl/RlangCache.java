package com.cw.cache.impl;


import com.cw.arithmetic.lang.R;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Rlang;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.RLANG, cacheClass = Rlang.class)
public class RlangCache extends BaseCacheData<Rlang> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Rlang>> datas) {

        List<Rlang> lists = loadAllData();

        Map<String, List<Rlang>> projectdata = lists.stream().collect(Collectors.groupingBy(Rlang::getProjectid));
        //按语言分组
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Rlang>> roomtypes = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Rlang::getLang));
            for (Map.Entry<String, List<Rlang>> entry : roomtypes.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }

        for (Map.Entry<String, List<Rlang>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Rlang> map = new ConcurrentHashMap<>();
            for (Rlang rlang : entry.getValue()) {
                map.put(getUniqueKey(rlang), rlang);
            }
            //entry.getValue().stream().collect(Collectors.toConcurrentMap(getUniqueKey(),
            //Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }


    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Rlang> projectdatas, String projectId) {
        List<Rlang> records = loadProjectData(projectId);

        for (Rlang record : records) {
            projectdatas.put(getUniqueKey(record), record);
        }

        ConcurrentMap<String, List<Rlang>> map = records.stream().collect(Collectors.groupingByConcurrent(Rlang::getLang));
        for (Map.Entry<String, List<Rlang>> entry : map.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
        R.refreshProjectLang(projectId);

    }

    @Override
    public synchronized void refreshOne(String projectId, String refreshKey) {
    }

    @Override
    public String getUniqueKey(Rlang rlang) {
        return rlang.getProjectid() + RedisKey.SPLITSIGNAL + rlang.getLang() + RedisKey.SPLITSIGNAL + rlang.getKeyid(); //项目ID+语言+词条MD5
    }

}
