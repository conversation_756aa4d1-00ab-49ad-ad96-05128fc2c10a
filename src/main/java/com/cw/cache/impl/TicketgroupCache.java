package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Rlang;
import com.cw.entity.Ticketgroup;
import com.cw.mapper.TicketgroupMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/18 0018
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.TICKETGROUP, cacheClass = Ticketgroup.class)
public class TicketgroupCache extends BaseCacheData<Ticketgroup> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Ticketgroup>> datas) {
        TicketgroupMapper mapper = SpringUtil.getBean(TicketgroupMapper.class);
        List<Ticketgroup> lists = mapper.findAll();
        Map<String, List<Ticketgroup>> projectdata = lists.stream().collect(Collectors.groupingBy(Ticketgroup::getProjectid));
        //按照票务分组分类
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Ticketgroup>> factors = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Ticketgroup::getGroupid));
            for (Map.Entry<String, List<Ticketgroup>> entry : factors.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        for (Map.Entry<String, List<Ticketgroup>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Ticketgroup> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Ticketgroup::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Ticketgroup> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Ticketgroup> ticketgroups = daoLocal.getProjectObjectList(Ticketgroup.class, projectId);
        for (Ticketgroup ticketgroup : ticketgroups) {
            projectdatas.put(ticketgroup.getCode(), ticketgroup);
        }
        ConcurrentMap<String, List<Ticketgroup>> factorMap = ticketgroups.stream().collect(Collectors.groupingByConcurrent(Ticketgroup::getGroupid));
        for (Map.Entry<String, List<Ticketgroup>> entry : factorMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Ticketgroup> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Ticketgroup::getSeq));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description,address,richtext,introtext", "description");
    }
}
