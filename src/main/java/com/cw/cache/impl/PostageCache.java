package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Postage;
import com.cw.mapper.PostageMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-12-27
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.POSTAGE, cacheClass = Postage.class)
public class PostageCache extends BaseCacheData<Postage> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Postage>> datas) {
        PostageMapper mapper = SpringUtil.getBean(PostageMapper.class);
        List<Postage> lists = mapper.findAll();
        Map<String, List<Postage>> projectdata = lists.stream().collect(Collectors.groupingBy(Postage::getProjectid));
        for (Map.Entry<String, List<Postage>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Postage> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Postage::getName,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }


    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Postage> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Postage> postageList = daoLocal.getProjectObjectList(Postage.class, projectId);
        for (Postage postage : postageList) {
            projectdatas.put(postage.getName(), postage);
        }
    }

    public Postage getMatchPostage(String projectId, String province) {
        List<Postage> projects = getDataList(projectId);
        for (Postage postage : projects) {
            if (!postage.getName().equals(SystemUtil.DEFAULTUSERID) && postage.getAddress().contains(province)) {
                return postage;
            }
        }
        return null;
    }
}
