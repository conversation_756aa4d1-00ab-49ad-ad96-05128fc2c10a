package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Menus;
import com.cw.entity.Rlang;
import com.cw.mapper.MenusMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.menus.MenuPlatformTypeEnums;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/28 0028
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.MENUS, cacheClass = Menus.class)
public class MenusCache extends BaseCacheData<Menus> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Menus>> datas) {
        MenusMapper mapper = SpringUtil.getBean(MenusMapper.class);
        List<Menus> lists = mapper.findAll();
        Map<String, List<Menus>> projectdata = lists.stream().collect(Collectors.groupingBy(Menus::getProjectid));

        for (String projectId : projectdata.keySet()) {//todo 分组数据有问题
            datas.put(projectId, Maps.newConcurrentMap());
            //按照mtype分组
            ConcurrentMap<String, List<Menus>> menus = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Menus::getMtype));
            for (Map.Entry<String, List<Menus>> entry : menus.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }

        for (Map.Entry<String, List<Menus>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Menus> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Menus::getMenuid,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }



    }

    /**
     * 获取页面下所有子菜单ID
     *
     * @param projectId
     * @param type
     * @param menuid
     * @return
     */
    public List<Menus> getPageCards(String projectId, MenuPlatformTypeEnums type, String menuid) {
        List<Menus> platformlist = groupdatas.get(projectId, type.getCode());//获取所有移动或PC端菜单对象
        if (platformlist != null) {
            List<Menus> cards = platformlist.stream().filter(menus -> (!menus.getOrgmenuid().isEmpty() && menus.getOrgmenuid().equals(menuid)))
                    .collect(Collectors.toList());
            Comparator<Menus> byColumntype = Comparator.comparing(Menus::getColumntype);
            Comparator<Menus> byOrgmenuid = Comparator.comparing(Menus::getOrgmenuid);
            Comparator<Menus> bySeq = Comparator.comparing(Menus::getSeq);
            //根据sql降序
            cards.sort(byColumntype.thenComparing(byOrgmenuid).thenComparing(bySeq).thenComparing(bySeq));
            return cards;
        }
        return Lists.newArrayList();
    }


    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Menus> projectdatas, String projectId) {
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Menus> menusList = daoLocal.getProjectObjectList(Menus.class, projectId);
        for (Menus menus : menusList) {
            projectdatas.put(menus.getMenuid(), menus);
        }
        ConcurrentMap<String, List<Menus>> map = menusList.stream().collect(Collectors.groupingByConcurrent(Menus::getMtype));
        for (Map.Entry<String, List<Menus>> entry : map.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Menus> list) {
        super.sortList(list);
        Comparator<Menus> byColumntype = Comparator.comparing(Menus::getColumntype);
        Comparator<Menus> byOrgmenuid = Comparator.comparing(Menus::getOrgmenuid);
        Comparator<Menus> bySeq = Comparator.comparing(Menus::getSeq);
        list.sort(byColumntype.thenComparing(byOrgmenuid).thenComparing(bySeq).thenComparing(bySeq));
    }


    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "mname", "mname");
    }
}
