package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Ratecode;
import com.cw.mapper.RateCodeMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/15 0015
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.RATECODE, cacheClass = Ratecode.class)
public class RateCodeCache extends BaseCacheData<Ratecode> {

    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Ratecode>> datas) {
        RateCodeMapper mapper = SpringUtil.getBean(RateCodeMapper.class);
        List<Ratecode> lists = mapper.findAll();
        Map<String, List<Ratecode>> projectdata = lists.stream().collect(Collectors.groupingBy(Ratecode::getProjectid));

        for (Map.Entry<String, List<Ratecode>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Ratecode> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Ratecode::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Ratecode> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Ratecode> rateCodes = daoLocal.getProjectObjectList(Ratecode.class, projectId);
        for (Ratecode ratecode : rateCodes) {
            projectdatas.put(ratecode.getCode(), ratecode);
        }
        //ConcurrentMap<String, List<Ratecode>> rateCodeMap = rateCodes.stream().collect(Collectors.groupingByConcurrent(Ratecode::getCode));
        //for (Map.Entry<String, List<Ratecode>> entry : rateCodeMap.entrySet()) {
        //    groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //}
    }
}
