package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Parksite;
import com.cw.entity.Rlang;
import com.cw.mapper.ParksiteMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.PARKSITE, cacheClass = Parksite.class)
public class ParksiteCache extends BaseCacheData<Parksite> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Parksite>> datas) {
        ParksiteMapper mapper = SpringUtil.getBean(ParksiteMapper.class);
        List<Parksite> lists = mapper.findAll();
        Map<String, List<Parksite>> projectdata = lists.stream().collect(Collectors.groupingBy(Parksite::getProjectid));
        for (Map.Entry<String, List<Parksite>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Parksite> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Parksite::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Parksite> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Parksite> parksites = daoLocal.getProjectObjectList(Parksite.class, projectId);
        for (Parksite Parksite : parksites) {
            projectdatas.put(Parksite.getCode(), Parksite);
        }
    }

    @Override
    protected void sortList(List<Parksite> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Parksite::getSeq));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description,address,richtext", "description");
    }
}
