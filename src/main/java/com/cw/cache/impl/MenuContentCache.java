package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Menu_content;
import com.cw.entity.Rlang;
import com.cw.mapper.MenucontentMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/29 0029
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.MENUCONTENT, cacheClass = Menu_content.class)
public class MenuContentCache extends BaseCacheData<Menu_content> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Menu_content>> datas) {
        MenucontentMapper mapper = SpringUtil.getBean(MenucontentMapper.class);
        List<Menu_content> lists = mapper.findAll();
        Map<String, List<Menu_content>> projectdata = lists.stream().collect(Collectors.groupingBy(Menu_content::getProjectid));
        //分组数据
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Menu_content>> menuContents = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Menu_content::getMenuid));
            for (Map.Entry<String, List<Menu_content>> entry : menuContents.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue()); //按menuid分组
            }
        }
        for (Map.Entry<String, List<Menu_content>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Menu_content> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Menu_content::getContentid,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Menu_content> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Menu_content> menu_contents = daoLocal.getProjectObjectList(Menu_content.class, projectId);
        for (Menu_content menu_content : menu_contents) {
            projectdatas.put(String.valueOf(menu_content.getContentid()), menu_content);
        }
        ConcurrentMap<String, List<Menu_content>> contentMap = menu_contents.stream().collect(Collectors.groupingByConcurrent(Menu_content::getMenuid));
        for (Map.Entry<String, List<Menu_content>> entry : contentMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Menu_content> list) {
        super.sortList(list);
        Comparator<Menu_content> byMenuid = Comparator.comparing(Menu_content::getMenuid);
        Comparator<Menu_content> byTop = Comparator.comparing(Menu_content::getTop).reversed();
        Comparator<Menu_content> bySeq = Comparator.comparing(Menu_content::getSeq);
        Comparator<Menu_content> byCreatetime = Comparator.comparing(Menu_content::getCreatetime).reversed();
        Comparator<Menu_content> bySql = Comparator.comparing(Menu_content::getSqlid).reversed();
        list.sort(byMenuid.thenComparing(byTop).thenComparing(bySeq).thenComparing(byCreatetime).thenComparing(bySql));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "title,subtitle,subtitle3,subtitle4,contentinfo,richtext",
                "title");
    }
}
