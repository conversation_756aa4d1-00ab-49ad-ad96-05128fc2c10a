package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Prodgroup;
import com.cw.mapper.ProdgroupMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/14 0014
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.PRODGROUP, cacheClass = Prodgroup.class)
public class ProdGroupCache extends BaseCacheData<Prodgroup> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Prodgroup>> datas) {
        ProdgroupMapper mapper = SpringUtil.getBean(ProdgroupMapper.class);
        List<Prodgroup> lists = mapper.findAll();
        Map<String, List<Prodgroup>> projectdata = lists.stream().collect(Collectors.groupingBy(Prodgroup::getProjectid));
        for (Map.Entry<String, List<Prodgroup>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Prodgroup> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Prodgroup::getAreas,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }


    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Prodgroup> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Prodgroup> prodgroups = daoLocal.getProjectObjectList(Prodgroup.class, projectId);
        for (Prodgroup prodgroup : prodgroups) {
            projectdatas.put(prodgroup.getAreas(), prodgroup);
        }
    }
}
