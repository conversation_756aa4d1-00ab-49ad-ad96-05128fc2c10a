package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Factor;
import com.cw.entity.Rlang;
import com.cw.mapper.FactorMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/23 0023
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.FACTOR, cacheClass = Factor.class)
public class FactorCache extends BaseCacheData<Factor> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Factor>> datas) {
        FactorMapper mapper = SpringUtil.getBean(FactorMapper.class);
        List<Factor> lists = mapper.findAll();
        Map<String, List<Factor>> projectdata = lists.stream().collect(Collectors.groupingBy(Factor::getProjectid));
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            //ConcurrentMap<String, List<Factor>> factors = lists.stream().collect(Collectors.groupingByConcurrent(Factor::getType));
            //按照header分组
            ConcurrentMap<String, List<Factor>> factors = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Factor::getHeader));
            for (Map.Entry<String, List<Factor>> entry : factors.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        for (Map.Entry<String, List<Factor>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Factor> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Factor::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Factor> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Factor> factors = daoLocal.getProjectObjectList(Factor.class, projectId);
        for (Factor factor : factors) {
            projectdatas.put(factor.getCode(), factor);
        }
        ConcurrentMap<String, List<Factor>> factorMap = factors.stream().collect(Collectors.groupingByConcurrent(Factor::getHeader));
        for (Map.Entry<String, List<Factor>> entry : factorMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Factor> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Factor::getType).thenComparing(Factor::getHeader).thenComparing(Factor::getSeq));
    }


    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description", "description");
    }
}
