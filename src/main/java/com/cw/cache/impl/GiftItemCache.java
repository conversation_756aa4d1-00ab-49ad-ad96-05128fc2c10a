package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Giftitem;
import com.cw.mapper.GiftitemMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2022-02-24
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.GIFTITEM, cacheClass = Giftitem.class)
public class GiftItemCache extends BaseCacheData<Giftitem> {
    private ConcurrentMap<String, String> transMatrix = Maps.newConcurrentMap();//代码转商品描述MAP

    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Giftitem>> datas) {
        GiftitemMapper mapper = SpringUtil.getBean(GiftitemMapper.class);
        List<Giftitem> lists = mapper.findAll();
        Map<String, List<Giftitem>> projectdata = lists.stream().collect(Collectors.groupingBy(Giftitem::getProjectid));
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Giftitem>> giftitems = lists.stream().collect(Collectors.groupingByConcurrent(Giftitem::getGroupid));
            for (Map.Entry<String, List<Giftitem>> entry : giftitems.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        for (Map.Entry<String, List<Giftitem>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Giftitem> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Giftitem::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
            updTransMatrix(entry.getValue());
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Giftitem> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Giftitem> giftitems = daoLocal.getProjectObjectList(Giftitem.class, projectId);
        for (Giftitem giftitem : giftitems) {
            projectdatas.put(giftitem.getCode(), giftitem);
        }
        ConcurrentMap<String, List<Giftitem>> giftitemMap = giftitems.stream().collect(Collectors.groupingByConcurrent(Giftitem::getGroupid));
        for (Map.Entry<String, List<Giftitem>> entry : giftitemMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
        updTransMatrix(giftitems);
    }

    private void updTransMatrix(List<Giftitem> giftitemList) {
        for (Giftitem giftitem : giftitemList) {
            transMatrix.put(giftitem.getCode(), giftitem.getDescription());
            List<String> specs1s = Arrays.asList(giftitem.getSpecs1().split(",")); //规格名称
            List<String> specs1codes = Arrays.asList(giftitem.getSpecs1code().split(","));//规格代码
            if (specs1s.size() == specs1codes.size()) {
                for (int i = 0; i < specs1s.size(); i++) {
                    transMatrix.put(specs1codes.get(i), specs1s.get(i));
                }
            } else {
                System.out.println("发现异常伴手礼描述数据!" + giftitem.getCode() + ":" + giftitem.getDescription());
            }
            if (!giftitem.getSpecs2code().isEmpty()) {
                List<String> specs2s = Arrays.asList(giftitem.getSpecs2().split(",")); //规格名称
                List<String> specs2codes = Arrays.asList(giftitem.getSpecs2code().split(","));//规格代码
                if (specs2s.size() == specs2codes.size()) {
                    for (int i = 0; i < specs2s.size(); i++) {
                        transMatrix.put(specs2codes.get(i), specs2s.get(i));
                    }
                } else {
                    System.out.println("发现异常伴手礼描述数据!" + giftitem.getCode() + ":" + giftitem.getDescription());
                }
            }
        }
    }

    /**
     * @param specStr      G0001:ABC:DEF 商品精确编码
     * @param concatSignal 描述拼接符号
     * @param needItemDesc 是否需要商品头描述.传true 返回的是 大衣:红:XL false 返回的是 红:XL
     * @return
     */
    public String quickTrans(String specStr, String concatSignal, boolean needItemDesc) {
        String[] specStrs = specStr.split(":");
        String formatStr = "";
        for (int i = needItemDesc ? 0 : 1; i < specStrs.length; i++) {
            String tobeTrans = specStrs[i];//准备翻译成描述的代码
            formatStr += formatStr.isEmpty() ? transMatrix.getOrDefault(tobeTrans, tobeTrans) :
                    concatSignal + transMatrix.getOrDefault(tobeTrans, tobeTrans);
        }
        return formatStr;
    }


    @Override
    protected void sortList(List<Giftitem> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Giftitem::getGroupid).thenComparing(Giftitem::getSeq));
    }
}
