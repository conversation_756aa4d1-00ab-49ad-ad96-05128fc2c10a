package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Rlang;
import com.cw.entity.Roomtype;
import com.cw.mapper.RoomtypeMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/2 09:47
 **/
@CacheProp(dataType = SystemUtil.GlobalDataType.ROOMTYPE, cacheClass = Roomtype.class)
public class RoomTypeCache extends BaseCacheData<Roomtype> {



    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Roomtype>> datas) {
        RoomtypeMapper mapper = SpringUtil.getBean(RoomtypeMapper.class);
        List<Roomtype> lists = mapper.findAll();
        Map<String, List<Roomtype>> projectdata = lists.stream().collect(Collectors.groupingBy(Roomtype::getProjectid));
        //按照酒店分组
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Roomtype>> roomtypes = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Roomtype::getHotelcode));
            for (Map.Entry<String, List<Roomtype>> entry : roomtypes.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }

        for (Map.Entry<String, List<Roomtype>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Roomtype> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Roomtype::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }


    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Roomtype> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Roomtype> roomtypes = daoLocal.getProjectObjectList(Roomtype.class, projectId);
        for (Roomtype roomtype : roomtypes) {
            projectdatas.put(roomtype.getCode(), roomtype);
        }
        ConcurrentMap<String, List<Roomtype>> roomtypeMap = roomtypes.stream().collect(Collectors.groupingByConcurrent(Roomtype::getHotelcode));
        for (Map.Entry<String, List<Roomtype>> entry : roomtypeMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Roomtype> list) {
        list.sort(Comparator.comparing(Roomtype::getHotelcode).thenComparing(Roomtype::getSeq));
    }


    public List<Roomtype> getRoomtypeList(String projectId, String hotelcode) {
        if (hotelcode.isEmpty()) {
            return getDataList(projectId);
        } else {
            return getDataList(projectId).stream().filter(t -> t.getHotelcode().equals(hotelcode)).collect(Collectors.toList());
        }
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description", "description");
    }
}
