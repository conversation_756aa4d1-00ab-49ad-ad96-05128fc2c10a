package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Restaurant;
import com.cw.mapper.RestaurantMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-05-05
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.RESTAURANT, cacheClass = Restaurant.class)
public class RestaurantCache extends BaseCacheData<Restaurant> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Restaurant>> datas) {
        RestaurantMapper mapper = SpringUtil.getBean(RestaurantMapper.class);
        List<Restaurant> lists = mapper.findAll();
        Map<String, List<Restaurant>> projectdata = lists.stream().collect(Collectors.groupingBy(Restaurant::getProjectid));
        for (Map.Entry<String, List<Restaurant>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Restaurant> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Restaurant::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Restaurant> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Restaurant> restaurants = daoLocal.getProjectObjectList(Restaurant.class, projectId);
        for (Restaurant restaurant : restaurants) {
            projectdatas.put(restaurant.getCode(), restaurant);
        }
    }

    @Override
    protected void sortList(List<Restaurant> list) {
        super.sortList(list);
    }
}
