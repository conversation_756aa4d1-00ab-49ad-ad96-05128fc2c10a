package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Shopsite;
import com.cw.mapper.ShopsiteMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe 店铺站点缓存
 * <AUTHOR> <PERSON>
 * @Create on 2024-10-31
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.SHOPSITE, cacheClass = Shopsite.class)
public class ShopsiteCache extends BaseCacheData<Shopsite> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Shopsite>> datas) {
        ShopsiteMapper mapper = SpringUtil.getBean(ShopsiteMapper.class);
        List<Shopsite> lists = mapper.findAll();
        Map<String, List<Shopsite>> projectdata = lists.stream().collect(Collectors.groupingBy(Shopsite::getProjectid));

        //按照代码
        for (Map.Entry<String, List<Shopsite>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Shopsite> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Shopsite::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Shopsite> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Shopsite> shopsites = daoLocal.getProjectObjectList(Shopsite.class, projectId);
        for (Shopsite shopsite : shopsites) {
            projectdatas.put(shopsite.getCode(), shopsite);
        }

    }

    @Override
    protected void sortList(List<Shopsite> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Shopsite::getCode));
    }
}
