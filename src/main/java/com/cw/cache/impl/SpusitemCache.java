package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Spusitem;
import com.cw.mapper.SpusitemMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-02-22
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.SPUSITEM, cacheClass = Spusitem.class)
public class SpusitemCache extends BaseCacheData<Spusitem> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Spusitem>> datas) {
        SpusitemMapper mapper = SpringUtil.getBean(SpusitemMapper.class);
        List<Spusitem> lists = mapper.findAll();
        Map<String, List<Spusitem>> projectdata = lists.stream().collect(Collectors.groupingBy(Spusitem::getProjectid));
        //按照产品大类代码分组
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Spusitem>> factors = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Spusitem::getGroupid));
            for (Map.Entry<String, List<Spusitem>> entry : factors.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        //产品小类代码填充数据
        for (Map.Entry<String, List<Spusitem>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Spusitem> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Spusitem::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Spusitem> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Spusitem> spusitems = daoLocal.getProjectObjectList(Spusitem.class, projectId);
        for (Spusitem rule : spusitems) {
            projectdatas.put(rule.getCode(), rule);
        }
        ConcurrentMap<String, List<Spusitem>> factorMap = spusitems.stream().collect(Collectors.groupingByConcurrent(Spusitem::getGroupid));
        for (Map.Entry<String, List<Spusitem>> entry : factorMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Spusitem> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Spusitem::getGroupid).thenComparing(Spusitem::getSeq));
    }
}
