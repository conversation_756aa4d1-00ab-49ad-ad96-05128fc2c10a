package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Spugroup;
import com.cw.mapper.SpugroupMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-02-22
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.SPUGROUP, cacheClass = Spugroup.class)
public class SpugroupCache extends BaseCacheData<Spugroup> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Spugroup>> datas) {
        SpugroupMapper mapper = SpringUtil.getBean(SpugroupMapper.class);
        List<Spugroup> lists = mapper.findAll();
        Map<String, List<Spugroup>> projectdata = lists.stream().collect(Collectors.groupingBy(Spugroup::getProjectid));

        //按照产品分组分类
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Spugroup>> factors = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Spugroup::getGroupid));
            for (Map.Entry<String, List<Spugroup>> entry : factors.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        //按照产品大类代码
        for (Map.Entry<String, List<Spugroup>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Spugroup> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Spugroup::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Spugroup> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Spugroup> spugroups = daoLocal.getProjectObjectList(Spugroup.class, projectId);
        for (Spugroup rule : spugroups) {
            projectdatas.put(rule.getCode(), rule);
        }
        ConcurrentMap<String, List<Spugroup>> factorMap = spugroups.stream().collect(Collectors.groupingByConcurrent(Spugroup::getGroupid));
        for (Map.Entry<String, List<Spugroup>> entry : factorMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Spugroup> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Spugroup::getGroupid).thenComparing(Spugroup::getSeq));
    }
}
