package com.cw.cache.impl;


import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Actqr;
import com.cw.mapper.ActqrMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.ACTQR, cacheClass = Actqr.class)
public class ActqrCache extends BaseCacheData<Actqr> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Actqr>> datas) {
        ActqrMapper mapper = SpringUtil.getBean(ActqrMapper.class);
        List<Actqr> lists = mapper.findAll();
        Map<String, List<Actqr>> projectdata = lists.stream().collect(Collectors.groupingBy(Actqr::getProjectid));

        //充填按代码分类
        for (Map.Entry<String, List<Actqr>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Actqr> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Actqr::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Actqr> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Actqr> actsites = daoLocal.getProjectObjectList(Actqr.class, projectId);
        for (Actqr actsite : actsites) {
            projectdatas.put(actsite.getCode(), actsite);
        }

    }

    @Override
    protected void sortList(List<Actqr> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Actqr::getCode));
    }
}
