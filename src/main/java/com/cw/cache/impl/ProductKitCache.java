package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Productkit;
import com.cw.mapper.ProductkitMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/18 0018
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.PRODUCTKIT, cacheClass = Productkit.class)
public class ProductKitCache extends BaseCacheData<Productkit> {



    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Productkit>> datas) {
        ProductkitMapper mapper = SpringUtil.getBean(ProductkitMapper.class);
        List<Productkit> lists = mapper.findAll();
        Map<String, List<Productkit>> projectdata = lists.stream().collect(Collectors.groupingBy(Productkit::getProjectid));
        //先分组再加数据
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Productkit>> productKits = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Productkit::getGroupid));
            for (Map.Entry<String, List<Productkit>> entry : productKits.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        for (Map.Entry<String, List<Productkit>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Productkit> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Productkit::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Productkit> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Productkit> productkits = daoLocal.getProjectObjectList(Productkit.class, projectId);
        for (Productkit productkit : productkits) {
            projectdatas.put(productkit.getCode(), productkit);
        }
        ConcurrentMap<String, List<Productkit>> productkitMap = productkits.stream().collect(Collectors.groupingByConcurrent(Productkit::getGroupid));
        for (Map.Entry<String, List<Productkit>> entry : productkitMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Productkit> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Productkit::getGroupid));
    }
}
