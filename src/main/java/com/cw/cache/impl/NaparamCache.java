package com.cw.cache.impl;


import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Naparam;
import com.cw.mapper.NaparamMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.NA_PARAM, cacheClass = Naparam.class)
public class NaparamCache extends BaseCacheData<Naparam> {


    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Naparam>> datas) {
        NaparamMapper mapper = SpringUtil.getBean(NaparamMapper.class);
        List<Naparam> lists = mapper.findAll();
        Map<String, List<Naparam>> projectdata = lists.stream().collect(Collectors.groupingBy(Naparam::getProjectid));
        for (Map.Entry<String, List<Naparam>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Naparam> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Naparam::getParamname,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Naparam> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Naparam> naparamList = daoLocal.getProjectObjectList(Naparam.class, projectId);
        for (Naparam naparam : naparamList) {
            projectdatas.put(naparam.getParamname(), naparam);
        }
    }

    @Override
    protected void sortList(List<Naparam> list) {
        Collections.sort(list, (o1, o2) -> o1.getParamname().compareTo(o2.getParamname()));
    }
}
