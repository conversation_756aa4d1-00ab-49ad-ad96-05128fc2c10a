package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Parkbusiness;
import com.cw.mapper.ParkbusinessMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.PARKBUSS, cacheClass = Parkbusiness.class)
public class ParkBussinessCache extends BaseCacheData<Parkbusiness> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Parkbusiness>> datas) {
        ParkbusinessMapper mapper = SpringUtil.getBean(ParkbusinessMapper.class);
        List<Parkbusiness> lists = mapper.findAll();
        Map<String, List<Parkbusiness>> projectdata = lists.stream().collect(Collectors.groupingBy(Parkbusiness::getProjectid));
        for (Map.Entry<String, List<Parkbusiness>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Parkbusiness> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Parkbusiness::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Parkbusiness> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Parkbusiness> parkbuss = daoLocal.getProjectObjectList(Parkbusiness.class, projectId);
        for (Parkbusiness parkbusiness : parkbuss) {
            projectdatas.put(parkbusiness.getCode(), parkbusiness);
        }
    }

    @Override
    protected void sortList(List<Parkbusiness> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Parkbusiness::getSeq));
    }
}
