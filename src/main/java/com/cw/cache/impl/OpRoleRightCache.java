package com.cw.cache.impl;


import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Op_role_right;
import com.cw.mapper.Op_role_rightMapper;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.rights.RightType;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/2 09:47
 **/
@CacheProp(dataType = SystemUtil.GlobalDataType.OP_ROLE_RIGHT, cacheClass = Op_role_right.class)
public class OpRoleRightCache extends BaseCacheData<Op_role_right> {


    public boolean hasRight(String projectId, String roles, int rightid) {
        if (!roles.contains(SystemUtil.RESERVE_ROLE)) {
            if (datas.containsKey(projectId)) {
                String[] rolearray = roles.split(",");
                for (String role : rolearray) {
                    //如果改用户关联的其中一个角色有权限调用方法.就返回 true
                    if (datas.getOrDefault(projectId, new ConcurrentHashMap<>(0)).containsKey(role + SystemUtil.DEFAULT_SPLITCHAR + rightid)) {
                        return true;
                    }
                }
                return false;
            } else {
                return false;
            }
        }
        return true;//超级管理员权限组有全部权限
    }

    public List<String> getRolesRight(String userRoles, String projectId) {
        String[] roles = userRoles.split(",");
        Set<String> sets = Sets.newHashSet();
        for (String role : roles) {
            sets.addAll(groupdatas.get(projectId, role).stream().map(Op_role_right::getRightid).collect(Collectors.toList()));
        }
        return new ArrayList<>(sets);
    }


    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Op_role_right>> datas) {
        Op_role_rightMapper mapper = SpringUtil.getBean(Op_role_rightMapper.class);
        List<Op_role_right> lists = mapper.getAllByType(RightType.OP.name());
        Map<String, List<Op_role_right>> projectdata = lists.stream().collect(Collectors.groupingBy(Op_role_right::getProjectid));
        for (String projectId : projectdata.keySet()) {

            datas.put(projectId, Maps.newConcurrentMap());

            List<Op_role_right> projectDataList = projectdata.get(projectId);
            ConcurrentMap<String, List<Op_role_right>> roleRights = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Op_role_right::getRoleid));
            for (Map.Entry<String, List<Op_role_right>> entry : roleRights.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
            projectDataList.forEach(t -> {
                datas.get(t.getProjectid()).put(t.getRoleid() + SystemUtil.DEFAULT_SPLITCHAR + t.getRightid(), t);
            });
//            groupdatas = list.stream().collect(Collectors.groupingByConcurrent(Op_role_right::getRoleid));
        }
//        for (Map.Entry<String, List<Op_role_right>> entry : projectdata.entrySet()) {
//            ConcurrentMap<String, Op_role_right> map=entry.getValue().stream().collect(Collectors.toConcurrentMap(Roomtype::getCode,
//                    Function.identity(), (t1, t2) -> t2));
//            datas.put(entry.getKey(),map);
//        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Op_role_right> projectdatas, String projectId) {
        Op_role_rightMapper mapper = SpringUtil.getBean(Op_role_rightMapper.class);
        List<Op_role_right> lists = mapper.getProjectRoleOpRights(projectId);
        ConcurrentMap<String, List<Op_role_right>> roleRights = lists.stream().collect(Collectors.groupingByConcurrent(Op_role_right::getRoleid));
        for (Map.Entry<String, List<Op_role_right>> entry : roleRights.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
        lists.forEach(t -> {
            projectdatas.put(t.getRoleid() + SystemUtil.DEFAULT_SPLITCHAR + t.getRightid(), t);
        });

    }

    @Override
    protected void sortList(List<Op_role_right> list) {
        list.sort(Comparator.comparing(Op_role_right::getRoleid).thenComparing(Op_role_right::getRightid));
    }
}
