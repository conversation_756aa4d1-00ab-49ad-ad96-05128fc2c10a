package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Notifyrobot;
import com.cw.mapper.NotifyrobotMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/21 0021
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.NOTIFYROBOT, cacheClass = Notifyrobot.class)
public class NotifyrobotCache extends BaseCacheData<Notifyrobot> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Notifyrobot>> datas) {
        NotifyrobotMapper mapper = SpringUtil.getBean(NotifyrobotMapper.class);
        List<Notifyrobot> lists = mapper.findAll();
        Map<String, List<Notifyrobot>> projectdata = lists.stream().collect(Collectors.groupingBy(Notifyrobot::getProjectid));
        //机器人分组数据
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Notifyrobot>> factors = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Notifyrobot::getGroup));
            for (Map.Entry<String, List<Notifyrobot>> entry : factors.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }

        for (Map.Entry<String, List<Notifyrobot>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Notifyrobot> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Notifyrobot::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Notifyrobot> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Notifyrobot> notifyrobots = daoLocal.getProjectObjectList(Notifyrobot.class, projectId);
        for (Notifyrobot notifyrobot : notifyrobots) {
            projectdatas.put(String.valueOf(notifyrobot.getCode()), notifyrobot);
        }
        ConcurrentMap<String, List<Notifyrobot>> contentMap = notifyrobots.stream().collect(Collectors.groupingByConcurrent(Notifyrobot::getGroup));
        for (Map.Entry<String, List<Notifyrobot>> entry : contentMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Notifyrobot> list) {
        Collections.sort(list, (o1, o2) -> {
            int compareValue = o1.getGroup().compareTo(o2.getGroup());
            if (compareValue == 0) {
                compareValue = o1.getType().compareTo(o2.getType());
            }
            if (compareValue == 0) {
                compareValue = o1.getCode().compareTo(o2.getCode());
            }
            return compareValue;
        });
    }
}
