package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Webconf;
import com.cw.mapper.WebconfMapper;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * @Describe 网站页脚配置数据
 * <AUTHOR> <PERSON>
 * @Create on 2022-02-23
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.WEBCONF, cacheClass = Webconf.class)
public class WebconfCache extends BaseCacheData<Webconf> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Webconf>> datas) {
        WebconfMapper mapper = SpringUtil.getBean(WebconfMapper.class);
        List<Webconf> lists = mapper.findAll();
        for (Webconf webconf : lists) {
            oneRecordMap.put(webconf.getProjectid(), webconf);
        }
    }

    public List<Webconf> getAllWebConf() {
        return oneRecordMap.values().stream().collect(Collectors.toList());
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Webconf> projectdatas, String projectId) {
        WebconfMapper mapper = SpringUtil.getBean(WebconfMapper.class);
        Webconf webconf = mapper.findFirstByProjectid(projectId);
        oneRecordMap.put(webconf.getProjectid(), webconf);
    }

    @Override
    protected void sortList(List<Webconf> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Webconf::getProjectid));
    }
}
