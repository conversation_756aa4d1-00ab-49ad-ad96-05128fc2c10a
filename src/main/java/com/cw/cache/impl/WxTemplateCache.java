package com.cw.cache.impl;


import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Wxtemplate;
import com.cw.mapper.WxtemplateMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/2 09:47
 **/
@CacheProp(dataType = SystemUtil.GlobalDataType.WXTEMPLATE, cacheClass = Wxtemplate.class)
public class WxTemplateCache extends BaseCacheData<Wxtemplate> {


    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Wxtemplate>> datas) {
        List<Wxtemplate> lists = SpringUtil.getBean(WxtemplateMapper.class).findAllByStatus(true);
        Map<String, List<Wxtemplate>> projectdata = lists.stream().collect(Collectors.groupingBy(Wxtemplate::getProjectid));
        //填充按分组代码数据分类
        //for (String projectId : projectdata.keySet()) {
        //    datas.put(projectId, Maps.newConcurrentMap());
        //    ConcurrentMap<String, List<Template>> tickets = lists.stream().filter(r -> r.getProjectid().equals(projectId))
        //            .collect(Collectors.groupingByConcurrent(Template::getGroup));
        //    for (Map.Entry<String, List<Template>> entry : tickets.entrySet()) {
        //        groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //    }
        //}
        //充填按代码分类
        for (Map.Entry<String, List<Wxtemplate>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Wxtemplate> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Wxtemplate::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Wxtemplate> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Wxtemplate> templates = daoLocal.getProjectObjectList(Wxtemplate.class, projectId);
        for (Wxtemplate template : templates) {
            projectdatas.put(template.getCode(), template);
        }
        //ConcurrentMap<String, List<Template>> ticketMap = templates.stream().collect(Collectors.groupingByConcurrent(Template::getGroup));
        //for (Map.Entry<String, List<Template>> entry : ticketMap.entrySet()) {
        //    groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //}
    }

    @Override
    protected void sortList(List<Wxtemplate> list) {
        list.sort(Comparator.comparing(Wxtemplate::getCode));
    }
}
