package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Park;
import com.cw.entity.Rlang;
import com.cw.mapper.ParkMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.PARK, cacheClass = Park.class)
public class ParkCache extends BaseCacheData<Park> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Park>> datas) {
        ParkMapper mapper = SpringUtil.getBean(ParkMapper.class);
        List<Park> lists = mapper.findAll();
        Map<String, List<Park>> projectdata = lists.stream().collect(Collectors.groupingBy(Park::getProjectid));
        for (Map.Entry<String, List<Park>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Park> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Park::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Park> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Park> parks = daoLocal.getProjectObjectList(Park.class, projectId);
        for (Park Park : parks) {
            projectdatas.put(Park.getCode(), Park);
        }
    }

    @Override
    protected void sortList(List<Park> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Park::getSeq));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description", "description");
    }
}
