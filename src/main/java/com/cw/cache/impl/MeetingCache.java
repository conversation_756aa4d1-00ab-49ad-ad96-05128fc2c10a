package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Meeting;
import com.cw.entity.Rlang;
import com.cw.mapper.MeetingMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/25 0025
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.MEETING, cacheClass = Meeting.class)
public class MeetingCache extends BaseCacheData<Meeting> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Meeting>> datas) {
        MeetingMapper mapper = SpringUtil.getBean(MeetingMapper.class);
        List<Meeting> lists = mapper.findAll();
        Map<String, List<Meeting>> projectdata = lists.stream().collect(Collectors.groupingBy(Meeting::getProjectid));
        //填充按分组代码数据分类
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Meeting>> meetings = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Meeting::getGroupid));
            for (Map.Entry<String, List<Meeting>> entry : meetings.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }

        for (Map.Entry<String, List<Meeting>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Meeting> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Meeting::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Meeting> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Meeting> meetings = daoLocal.getProjectObjectList(Meeting.class, projectId);
        for (Meeting meeting : meetings) {
            projectdatas.put(meeting.getCode(), meeting);
        }
        ConcurrentMap<String, List<Meeting>> meetingMap = meetings.stream().collect(Collectors.groupingByConcurrent(Meeting::getGroupid));
        for (Map.Entry<String, List<Meeting>> entry : meetingMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Meeting> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Meeting::getGroupid).thenComparing(Meeting::getSeq));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description", "description");
    }


}
