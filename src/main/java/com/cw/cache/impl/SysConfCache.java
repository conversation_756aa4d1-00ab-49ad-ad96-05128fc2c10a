package com.cw.cache.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.arithmetic.sku.YamlTool;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.config.confyaml.ConfYaml;
import com.cw.entity.Rlang;
import com.cw.entity.Sysconf;
import com.cw.mapper.SysconfMapper;
import com.cw.pojo.dto.app.res.LinkData;
import com.cw.pojo.dto.conf.res.cms.BusinessBanner;
import com.cw.pojo.dto.conf.res.cms.PageFoot;
import com.cw.pojo.dto.conf.res.cms.PageHeader;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/2 09:47
 **/
@CacheProp(dataType = SystemUtil.GlobalDataType.SYSCONF, cacheClass = Sysconf.class)
public class SysConfCache extends BaseCacheData<Sysconf> {

    //    private ConcurrentMap<String, Sysconf> appidMap = Maps.newConcurrentMap();
    private ConcurrentMap<String, ConfYaml> confYamlMap = new ConcurrentHashMap<>();


    public SysConfCache() {
        super();
        this.defaultCodeFiled = "projectid";  //重载.修改默认的下拉取值 key 字段
    }

    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Sysconf>> datas) {
        SysconfMapper mapper = SpringUtil.getBean(SysconfMapper.class);
        List<Sysconf> lists = mapper.findAll();
        for (Sysconf sysconf : lists) {
            oneRecordMap.put(sysconf.getProjectid(), sysconf);
            updConfYaml(sysconf);
        }
        //充填按代码分类
        Map<String, List<Sysconf>> projectdata = lists.stream().collect(Collectors.groupingBy(Sysconf::getProjectid));
        for (Map.Entry<String, List<Sysconf>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Sysconf> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Sysconf::getProjectid,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    public List<Sysconf> getAllSysConf() {
        return oneRecordMap.values().stream().collect(Collectors.toList());
    }


    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Sysconf> projectdatas, String projectId) {
        SysconfMapper mapper = SpringUtil.getBean(SysconfMapper.class);
        Sysconf sysconf = mapper.findFirstByProjectid(projectId);
        oneRecordMap.put(sysconf.getProjectid(), sysconf);
        updConfYaml(sysconf);

        List<Sysconf> list = new ArrayList<>();
        list.add(sysconf);
        for (Sysconf sysconf1 : list) {
            projectdatas.put(sysconf1.getProjectid(), sysconf1);
        }
    }

    private void updConfYaml(Sysconf sysconf) {
        if (StrUtil.isNotBlank(sysconf.getYaml())) {
            ConfYaml confYaml = YamlTool.readValue(sysconf.getYaml(), ConfYaml.class);
            confYamlMap.put(sysconf.getProjectid(), confYaml);
        }
    }

    public ConfYaml getConfYaml(String projectId) {
        return confYamlMap.getOrDefault(projectId, new ConfYaml());
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {

        Sysconf sysconf = oneRecordMap.get(projectId);
        Set<String> textSet = Sets.newHashSet();
        if (StringUtils.isNotBlank(sysconf.getPcheader())) {  //解析页头中的文字
            List<PageHeader> pageHeaders = JSON.parseObject(sysconf.getPcheader(), new TypeReference<List<PageHeader>>() {
            });
            if (CollectionUtil.isNotEmpty(pageHeaders)) {
                textSet.addAll(new PageHeader().getTitleList(pageHeaders));
            }

            //List<Rlang> headerRlangs = RecordTranslateSplitor.collectTransTexts(pcHeaderList);
            //list.addAll(headerRlangs);
        }

        if (StringUtils.isNotBlank(sysconf.getPcfoot())) {//解析页尾中的文字
            PageFoot pageFoot = JSON.parseObject(sysconf.getPcfoot(), new TypeReference<PageFoot>() {
            });

            textSet.add(pageFoot.getCopyright());
            if (CollectionUtil.isNotEmpty(pageFoot.getFriendlinks())) {
                for (LinkData linkData : pageFoot.getFriendlinks()) {
                    textSet.add(linkData.getTile());
                }
            }
            if (CollectionUtil.isNotEmpty(pageFoot.getImagelinks())) {
                for (LinkData linkData : pageFoot.getImagelinks()) {
                    textSet.add(linkData.getTile());
                }
            }
            if (CollectionUtil.isNotEmpty(pageFoot.getRecords())) {
                for (LinkData linkData : pageFoot.getRecords()) {
                    textSet.add(linkData.getTile());
                }
            }
            if (CollectionUtil.isNotEmpty(pageFoot.getTextlinks())) {
                for (LinkData linkData : pageFoot.getTextlinks()) {
                    textSet.add(linkData.getTile());
                }
            }

            if (ObjectUtil.isNotEmpty(pageFoot.getBizBanners())) {
                for (BusinessBanner businessBanner : pageFoot.getBizBanners().getBanners()) {
                    //pcFootList.add(businessBanner.getBusiness());
                   textSet.add(businessBanner.getBusiness());
                }
            }
        }
        //添加字段
        textSet.add(sysconf.getWebtitle());
        textSet.add(sysconf.getWxappname());

        List<Rlang> list = RecordTranslateSplitor.collectTransTexts(textSet);
        return list;
    }

    @Data
    private class PageNode {
        long sqlid;
        String title;
        String type;
    }
}
