package com.cw.cache.impl;


import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Template;
import com.cw.mapper.TemplateMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/2 09:47
 **/
@CacheProp(dataType = SystemUtil.GlobalDataType.TEMPLATE, cacheClass = Template.class)
public class TemplateCache extends BaseCacheData<Template> {


    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Template>> datas) {
        List<Template> lists = SpringUtil.getBean(TemplateMapper.class).findAllByStatus(true);
        Map<String, List<Template>> projectdata = lists.stream().collect(Collectors.groupingBy(Template::getProjectid));
        //填充按分组代码数据分类
        //for (String projectId : projectdata.keySet()) {
        //    datas.put(projectId, Maps.newConcurrentMap());
        //    ConcurrentMap<String, List<Template>> tickets = lists.stream().filter(r -> r.getProjectid().equals(projectId))
        //            .collect(Collectors.groupingByConcurrent(Template::getGroup));
        //    for (Map.Entry<String, List<Template>> entry : tickets.entrySet()) {
        //        groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //    }
        //}
        //充填按代码分类
        for (Map.Entry<String, List<Template>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Template> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Template::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Template> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Template> templates = daoLocal.getProjectObjectList(Template.class, projectId);
        for (Template template : templates) {
            projectdatas.put(template.getCode(), template);
        }
        //ConcurrentMap<String, List<Template>> ticketMap = templates.stream().collect(Collectors.groupingByConcurrent(Template::getGroup));
        //for (Map.Entry<String, List<Template>> entry : ticketMap.entrySet()) {
        //    groupdatas.put(projectId, entry.getKey(), entry.getValue());
        //}
    }

    @Override
    protected void sortList(List<Template> list) {
        list.sort(Comparator.comparing(Template::getCode));
    }
}
