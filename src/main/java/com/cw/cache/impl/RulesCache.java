package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Rules;
import com.cw.mapper.RulesMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/10/8 0008
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.RULES, cacheClass = Rules.class)
public class RulesCache extends BaseCacheData<Rules> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Rules>> datas) {
        RulesMapper mapper = SpringUtil.getBean(RulesMapper.class);
        List<Rules> lists = mapper.findAll();
        Map<String, List<Rules>> projectdata = lists.stream().collect(Collectors.groupingBy(Rules::getProjectid));

        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Rules>> factors = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Rules::getType));
            for (Map.Entry<String, List<Rules>> entry : factors.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }
        for (Map.Entry<String, List<Rules>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Rules> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Rules::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Rules> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Rules> rules = daoLocal.getProjectObjectList(Rules.class, projectId);
        for (Rules rule : rules) {
            projectdatas.put(rule.getCode(), rule);
        }
        ConcurrentMap<String, List<Rules>> factorMap = rules.stream().collect(Collectors.groupingByConcurrent(Rules::getType));
        for (Map.Entry<String, List<Rules>> entry : factorMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Rules> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Rules::getType).thenComparing(Rules::getSeq));
    }
}
