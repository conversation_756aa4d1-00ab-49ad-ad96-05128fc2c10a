package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Rulespolicy;
import com.cw.mapper.RulespolicyMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-09-19
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.RULESPOLICY, cacheClass = Rulespolicy.class)
public class RulespolicyCache extends BaseCacheData<Rulespolicy> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Rulespolicy>> datas) {
        RulespolicyMapper mapper = SpringUtil.getBean(RulespolicyMapper.class);
        List<Rulespolicy> lists = mapper.findAll();
        Map<String, List<Rulespolicy>> projectdata = lists.stream().collect(Collectors.groupingBy(Rulespolicy::getProjectid));

        for (Map.Entry<String, List<Rulespolicy>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Rulespolicy> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Rulespolicy::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Rulespolicy> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Rulespolicy> rules = daoLocal.getProjectObjectList(Rulespolicy.class, projectId);
        for (Rulespolicy rule : rules) {
            projectdatas.put(rule.getCode(), rule);
        }

    }

    @Override
    protected void sortList(List<Rulespolicy> list) {
        super.sortList(list);
    }
}
