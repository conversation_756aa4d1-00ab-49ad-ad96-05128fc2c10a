package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Kititem;
import com.cw.entity.Rlang;
import com.cw.mapper.KititemMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/15 0015
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.KITITEM, cacheClass = Kititem.class)
public class KititemCache extends BaseCacheData<Kititem> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Kititem>> datas) {
        KititemMapper mapper = SpringUtil.getBean(KititemMapper.class);
        List<Kititem> lists = mapper.findAll();
        Map<String, List<Kititem>> projectdata = lists.stream().collect(Collectors.groupingBy(Kititem::getProjectid));
        //先分组再加数据
        for (String projectId : projectdata.keySet()) {
            datas.put(projectId, Maps.newConcurrentMap());
            ConcurrentMap<String, List<Kititem>> productKits = lists.stream().filter(r -> r.getProjectid().equals(projectId))
                    .collect(Collectors.groupingByConcurrent(Kititem::getKitcode));
            for (Map.Entry<String, List<Kititem>> entry : productKits.entrySet()) {
                groupdatas.put(projectId, entry.getKey(), entry.getValue());
            }
        }


        for (Map.Entry<String, List<Kititem>> entry : projectdata.entrySet()) {
            //kitcode+productcode做标识 餐饮代码可能为空则取kitcode+productgroup
            ConcurrentMap<String, Kititem> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(r ->
                            r.getProductcode().isEmpty() ? r.getKitcode() + r.getProductgroup() : r.getKitcode() + r.getProductcode(),
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Kititem> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Kititem> kititemList = daoLocal.getProjectObjectList(Kititem.class, projectId);
        for (Kititem kititem : kititemList) {
            projectdatas.put(kititem.getProductcode().isEmpty() ?
                    kititem.getKitcode() + kititem.getProductgroup() : kititem.getKitcode() + kititem.getProductcode(), kititem);
        }
        ConcurrentMap<String, List<Kititem>> kitgroupMap = kititemList.stream().collect(Collectors.groupingByConcurrent(Kititem::getKitcode));
        for (Map.Entry<String, List<Kititem>> entry : kitgroupMap.entrySet()) {
            groupdatas.put(projectId, entry.getKey(), entry.getValue());
        }
    }

    @Override
    protected void sortList(List<Kititem> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Kititem::getProducttype));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "productdesc", "productdesc");
    }
}
