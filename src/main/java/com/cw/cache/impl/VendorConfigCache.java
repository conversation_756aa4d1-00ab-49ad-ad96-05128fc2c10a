package com.cw.cache.impl;

import cn.hutool.core.util.StrUtil;
import com.cw.arithmetic.sku.YamlTool;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.config.confyaml.VendorYaml;
import com.cw.entity.Vendorconfig;
import com.cw.mapper.VendorconfigMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.VendorType;
import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 项目接口供应商配置缓存
 *
 * <AUTHOR>
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.VENDORCONFIG, cacheClass = Vendorconfig.class)
public class VendorConfigCache extends BaseCacheData<Vendorconfig> {
    private Map<String, Vendorconfig> appidMap = Maps.newHashMap();

    private ConcurrentMap<String, VendorYaml> vendorYamlMap = new ConcurrentHashMap<>();


    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Vendorconfig>> datas) {
        VendorconfigMapper mapper = SpringUtil.getBean(VendorconfigMapper.class);
        List<Vendorconfig> lists = mapper.findAll();
        Map<String, List<Vendorconfig>> projectdata = lists.stream().collect(Collectors.groupingBy(Vendorconfig::getProjectid));
        for (Map.Entry<String, List<Vendorconfig>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Vendorconfig> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Vendorconfig::getVtype,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
            for (Vendorconfig vendorconfig : map.values()) {
                appidMap.put(vendorconfig.getAppid(), vendorconfig);
                updVendorYaml(vendorconfig);
            }
        }
    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Vendorconfig> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Vendorconfig> vendors = daoLocal.getProjectObjectList(Vendorconfig.class, projectId);
        for (Vendorconfig vendorconfig : vendors) {
            projectdatas.put(vendorconfig.getVtype(), vendorconfig);
            appidMap.put(vendorconfig.getAppid(), vendorconfig);
            updVendorYaml(vendorconfig);
        }
    }

    private void updVendorYaml(Vendorconfig vendorconfig) {
        if (StrUtil.isNotBlank(vendorconfig.getYaml())) {
            VendorYaml vendorYaml = YamlTool.readValue(vendorconfig.getYaml(), VendorYaml.class);
            vendorYamlMap.put(vendorconfig.getVtype(), vendorYaml);
        }
    }

    public VendorYaml getVendorYaml(VendorType vendorType) {
        return vendorYamlMap.getOrDefault(vendorType.name(), new VendorYaml());
    }

    public Vendorconfig getVendorConfigByAppId(String appid) {
        return appidMap.getOrDefault(appid, null);
    }

    public Vendorconfig getVendorConfig(String projectId, VendorType vendorType) {
        ConcurrentMap<String, Vendorconfig> map = datas.get(projectId);
        if (map == null) {
            return null;
        }
        return map.get(vendorType.name());
    }

    /**
     * 根据 适配器.获取该类型的所有配置
     *
     * @param type
     * @return
     */
    public List<Vendorconfig> getVendorTypeConfig(VendorType type) {
        List<Vendorconfig> result = new ArrayList<>();
        for (ConcurrentMap<String, Vendorconfig> row : datas.values()) {
            for (Map.Entry<String, Vendorconfig> entry : row.entrySet()) {
                if (entry.getKey().equals(type.name())) {
                    result.add(entry.getValue());
                }
            }
        }
        return result;
    }

    public Vendorconfig getVendorConfigFunction(VendorType type, Predicate<Vendorconfig> predicate) {
        List<Vendorconfig> result = new ArrayList<>();
        for (ConcurrentMap<String, Vendorconfig> row : datas.values()) {
            for (Map.Entry<String, Vendorconfig> entry : row.entrySet()) {
                if (entry.getKey().equals(type.name())) {
                    if (predicate.test(entry.getValue())) {
                        return entry.getValue();
                    }
                }
            }
        }
        return null;
    }


//    public Vendorconfig getVendorConfigByOutId(VendorType type, String outId) {
//        List<Vendorconfig> result = new ArrayList<>();
//        for (ConcurrentMap<String, Vendorconfig> row : datas.values()) {
//            for (Map.Entry<String, Vendorconfig> entry : row.entrySet()) {
//                if (entry.getKey().equals(type.name())&&entry.getValue().getOutid().equals(outId)) {
//                    return entry.getValue();
//                }
//            }
//        }
//        return null;
//    }

}
