package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Meetinggroup;
import com.cw.entity.Rlang;
import com.cw.mapper.MeetingGroupMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2021/10/25 0025
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.MEETINGGROUP, cacheClass = Meetinggroup.class)
public class MeetingGroupCache extends BaseCacheData<Meetinggroup> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Meetinggroup>> datas) {
        MeetingGroupMapper mapper = SpringUtil.getBean(MeetingGroupMapper.class);
        List<Meetinggroup> lists = mapper.findAll();
        Map<String, List<Meetinggroup>> projectdata = lists.stream().collect(Collectors.groupingBy(Meetinggroup::getProjectid));
        for (Map.Entry<String, List<Meetinggroup>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Meetinggroup> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Meetinggroup::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Meetinggroup> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Meetinggroup> meetingGroups = daoLocal.getProjectObjectList(Meetinggroup.class, projectId);
        for (Meetinggroup meetingGroup : meetingGroups) {
            projectdatas.put(meetingGroup.getCode(), meetingGroup);
        }
    }

    @Override
    protected void sortList(List<Meetinggroup> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Meetinggroup::getSeq));
    }

    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description,introduction", "description");
    }
}
