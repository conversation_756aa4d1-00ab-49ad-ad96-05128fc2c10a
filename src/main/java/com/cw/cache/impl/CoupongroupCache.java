package com.cw.cache.impl;

import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Coupongroup;
import com.cw.mapper.CoupongroupMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @<PERSON> on 2024-01-22
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.COUPONGROUP, cacheClass = Coupongroup.class)
public class CoupongroupCache extends BaseCacheData<Coupongroup> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Coupongroup>> datas) {
        CoupongroupMapper mapper = SpringUtil.getBean(CoupongroupMapper.class);
        List<Coupongroup> lists = mapper.findAll();
        Map<String, List<Coupongroup>> projectdata = lists.stream().collect(Collectors.groupingBy(Coupongroup::getProjectid));
        //充填按代码分类
        for (Map.Entry<String, List<Coupongroup>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Coupongroup> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Coupongroup::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Coupongroup> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Coupongroup> coupongroups = daoLocal.getProjectObjectList(Coupongroup.class, projectId);
        for (Coupongroup coupongroup : coupongroups) {
            projectdatas.put(coupongroup.getCode(), coupongroup);
        }

    }

    @Override
    protected void sortList(List<Coupongroup> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Coupongroup::getSeq));
    }
}
