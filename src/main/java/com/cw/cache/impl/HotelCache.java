package com.cw.cache.impl;

import com.cw.arithmetic.lang.collector.RecordTranslateSplitor;
import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Hotel;
import com.cw.entity.Rlang;
import com.cw.mapper.HotelMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @C<PERSON> on 2021/9/18 0018
 */
@CacheProp(dataType = SystemUtil.GlobalDataType.HOTEL, cacheClass = Hotel.class)
public class HotelCache extends BaseCacheData<Hotel> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Hotel>> datas) {
        HotelMapper mapper = SpringUtil.getBean(HotelMapper.class);
        List<Hotel> lists = mapper.findAll();
        Map<String, List<Hotel>> projectdata = lists.stream().collect(Collectors.groupingBy(Hotel::getProjectid));
        for (Map.Entry<String, List<Hotel>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Hotel> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Hotel::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Hotel> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Hotel> hotels = daoLocal.getProjectObjectList(Hotel.class, projectId);
        for (Hotel hotel : hotels) {
            projectdatas.put(hotel.getCode(), hotel);
        }
    }

    @Override
    protected void sortList(List<Hotel> list) {
        list.sort(Comparator.comparing(Hotel::getSeq));
    }


    @Override
    public List<Rlang> collectSourceLang(String projectId) {
        return RecordTranslateSplitor.splitRecord(getDataList(projectId), "description,address,introduction,noticetext", "description");
    }
}
