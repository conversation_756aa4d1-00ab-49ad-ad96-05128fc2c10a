package com.cw.cache.impl;


import com.cw.cache.BaseCacheData;
import com.cw.cache.CacheProp;
import com.cw.entity.Spuqr;
import com.cw.mapper.SpuqrMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@CacheProp(dataType = SystemUtil.GlobalDataType.SPUQR, cacheClass = Spuqr.class)
public class SpuqrCache extends BaseCacheData<Spuqr> {
    @Override
    protected void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, Spuqr>> datas) {
        SpuqrMapper mapper = SpringUtil.getBean(SpuqrMapper.class);
        List<Spuqr> lists = mapper.findAll();
        Map<String, List<Spuqr>> projectdata = lists.stream().collect(Collectors.groupingBy(Spuqr::getProjectid));

        //充填按代码分类
        for (Map.Entry<String, List<Spuqr>> entry : projectdata.entrySet()) {
            ConcurrentMap<String, Spuqr> map = entry.getValue().stream().collect(Collectors.toConcurrentMap(Spuqr::getCode,
                    Function.identity(), (t1, t2) -> t2));
            datas.put(entry.getKey(), map);
        }

    }

    @Override
    protected void reFreshProjectData(ConcurrentMap<String, Spuqr> projectdatas, String projectId) {
        DaoLocal daoLocal = SpringUtil.getBean(DaoLocal.class);
        List<Spuqr> actsites = daoLocal.getProjectObjectList(Spuqr.class, projectId);
        for (Spuqr actsite : actsites) {
            projectdatas.put(actsite.getCode(), actsite);
        }

    }

    @Override
    protected void sortList(List<Spuqr> list) {
        super.sortList(list);
        list.sort(Comparator.comparing(Spuqr::getCode));
    }
}
