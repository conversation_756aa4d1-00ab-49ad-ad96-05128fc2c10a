package com.cw.cache;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.redisimpl.RedisGlobalDataCache;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.mq.MsgNotifyer;
import com.cw.service.mq.msgmodel.ConfigUpdEventModel;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/5/25 14:52
 **/
@Slf4j
@Service
public class GlobalCache extends BaseGlobalCache {

    //    @Cacheable、@CachePut、@CacheEvict 注释介绍
//    @Cacheable 的作用 主要针对方法配置，能够根据方法的请求参数对其结果进行缓存
//    @CachePut 的作用 主要针对方法配置，能够根据方法的请求参数对其结果进行缓存，和
//               @Cacheable 不同的是，它每次都会触发真实方法的调用
//    @CachEvict 的作用 主要针对方法配置，能够根据一定的条件对缓存进行清空

//    @Autowired
//    DaoLocal<?> daoLocal;

//    private static final String KEY__DATASTRUTURE = "DataAttribute";
//
//    public static ConcurrentHashMap<String, Object> systemHT = new ConcurrentHashMap<String, Object>();
//
//    private DataStructure dataStructure;

    @Autowired
    public GlobalCache(DataStructure dataStructure) {
        init(dataStructure);
//        this.dataStructure = dataStructure;
//        systemHT.put(KEY__DATASTRUTURE, dataStructure);
    }

    public static DataStructure getDataStructure() {
        return (DataStructure) systemHT.get(KEY__DATASTRUTURE);
    }

    public synchronized void initLocalCache() {
        dataStructure.refreshDataStructure(SystemUtil.GlobalDataType.ALL, "", "");
    }

    @SafeVarargs
    public final synchronized <T> void refreshAndNotify(SystemUtil.GlobalDataType type, String projectId, T... t) {
        MsgNotifyer notifyer = SpringUtil.getBean(MsgNotifyer.class);
        //发送通知者负责先刷新内存值
        if (!type.equals(SystemUtil.GlobalDataType.ALL) && type.equals(SystemUtil.GlobalDataType.RLANG)) {
            writeNewValToRedis(type, projectId, t);
        }
        String refreshKey = null;
        if (t != null && t.length > 0) {
            BaseCacheData<T> cacheData = dataStructure.getCache(type);//.getUniqueKey(t);
            for (T r : t) {
                refreshKey = cacheData.getUniqueKey(r);
                notifyer.sendGlobalNotify(new ConfigUpdEventModel(type, projectId, refreshKey));
            }
        } else {
            notifyer.sendGlobalNotify(new ConfigUpdEventModel(type, projectId, null));
        }
    }

    /**
     * 删除记录.刷新内存
     *
     * @param type
     * @param projectId
     * @param t
     * @param <T>
     */
    @SafeVarargs
    public final synchronized <T> void deleteAndNotify(SystemUtil.GlobalDataType type, String projectId, T... t) {//TODO  后台删除数据的地方要加上
        MsgNotifyer notifyer = SpringUtil.getBean(MsgNotifyer.class);
        //发送通知者负责先刷新内存值
        if (t != null && t.length == 1) { //删除时传了指定对象
            removeOldValToRedis(type, projectId, t[0]);
        }
        String refreshKey = null;
        if (t != null) {
            BaseCacheData<T> cacheData = dataStructure.getCache(type);//.getUniqueKey(t);
            for (T r : t) {
                refreshKey = cacheData.getUniqueKey(r);
                notifyer.sendGlobalNotify(new ConfigUpdEventModel(type, projectId, refreshKey));
            }
        } else {
            notifyer.sendGlobalNotify(new ConfigUpdEventModel(type, projectId, null));
        }
    }


    @SafeVarargs
    public final <T> void writeNewValToRedis(SystemUtil.GlobalDataType type, String projectId, T... t) {
//        Class c=GlobalCache.getDataStructure().getCache(type).getCacheTableClass();
        if (t != null && t.length == 1) {//刷新单条记录
            RedisGlobalDataCache.updRecords(type, t);
            BaseCacheData<T> baseCacheData = GlobalCache.getDataStructure().getCache(type);
            //log.info("刷新redis 单条记录 {} ID: {}", type, baseCacheData.getUniqueKey(t[0]));
        } else {
            //NULL 更新对象为NULL 先将内存中的跟DB做比较.DB中没有的就做删除
            BaseCacheData cacheData = dataStructure.getCache(type);
            List<?> dbdata = null;
            DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
            if (StrUtil.isNotBlank(projectId)) {
                dbdata = daoLocal.getProjectObjectList(cacheData.getCacheTableClass(), projectId);
                RedisGlobalDataCache.cleanBeforeUpd(projectId, type, dbdata);
            } else {
                dbdata = daoLocal.getObjectList(StrUtil.format("from {}", cacheData.getCacheTableClass().getSimpleName()));
            }
            if (CollectionUtil.isNotEmpty(dbdata)) {
                RedisGlobalDataCache.updRecords(type, dbdata.toArray());
            }
            //log.info("刷新redis 批量记录 {} projectid: {}", type, projectId);
        }

    }

    public <T> void removeOldValToRedis(SystemUtil.GlobalDataType type, String projectId, T... t) {
        RedisGlobalDataCache.removeRecords(projectId, type, t);
    }
}