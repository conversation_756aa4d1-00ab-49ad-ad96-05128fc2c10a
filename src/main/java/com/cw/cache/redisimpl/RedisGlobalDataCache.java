package com.cw.cache.redisimpl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.BaseCacheData;
import com.cw.cache.GlobalCache;
import com.cw.cache.RedisTool;
import com.cw.utils.RedisKey;
import com.cw.utils.SystemUtil;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/2/13 14:06
 **/
public class RedisGlobalDataCache {

    /**
     * @param globalDataType 对应FactorCache 中的数据
     * @return
     */
    public static String getRedisTableMapKey(SystemUtil.GlobalDataType globalDataType) {
        return RedisKey.MallGlobalTableCacheKey + globalDataType.name();
    }

    public static <T> void cleanBeforeUpd(String projectId, SystemUtil.GlobalDataType globalDataType, List<T> dbrecords) {
        BaseCacheData<T> baseCacheData = GlobalCache.getDataStructure().getCache(globalDataType);
        Set<String> dbKeys = Sets.newHashSet();
        for (T dbrecord : dbrecords) {
            String dbKey = baseCacheData.getUniqueKey(dbrecord);
            if (StrUtil.isNotBlank(dbKey)) {
                dbKeys.add(dbKey);
            }
        }
        String patternKey = projectId + ":*";
        ScanOptions options = ScanOptions.scanOptions()
                .count(10000) //这里指定每次扫描key的数量
                .match(patternKey).build();
        Set<String> redisExKeys = Sets.newHashSet();
        Cursor<Map.Entry<String, String>> cursor = RedisTool.getInstance().opsForHash().
                scan(RedisGlobalDataCache.getRedisTableMapKey(globalDataType), options);
        while (cursor.hasNext()) {
            redisExKeys.add(cursor.next().getKey());
        }
        try {
            cursor.close();
        } catch (IOException e) {
        }

        int cleancount = 0;
        for (String redisExKey : redisExKeys) { //REIDS 中缓存的配置按db 中的为准
            if (!dbKeys.contains(redisExKey)) {
                RedisTool.getInstance().opsForHash().delete(RedisGlobalDataCache.getRedisTableMapKey(globalDataType), redisExKey);
                cleancount++;
            }
        }
        for (String dbKey : dbKeys) {
            if (!redisExKeys.contains(dbKey)) {
                LoggerFactory.getLogger(RedisGlobalDataCache.class).warn("{}缓存待添加数据-->{}", globalDataType, dbKey);
            }
        }
        if (cleancount > 0) {
            LoggerFactory.getLogger(RedisGlobalDataCache.class).info("清理非同步数据{} -{} 条", globalDataType, cleancount);
        }

    }


    @SafeVarargs
    public static <T> void updRecords(SystemUtil.GlobalDataType globalDataType, T... t) {
//        RedisTool.getInstance().opsForHash().put();
        if (t != null && t.length > 0) {
            Map<String, String> maps = Maps.newHashMap();
            BaseCacheData<T> baseCacheData = GlobalCache.getDataStructure().getCache(globalDataType);
            for (T r : t) {
                String key = baseCacheData.getUniqueKey(r);
                if (key.isEmpty()) {
                    continue;
                }
                if (maps.containsKey(key)) {
                    LoggerFactory.getLogger(RedisGlobalDataCache.class).warn("重复ID {}  KEY{}", globalDataType, key);
                }
                maps.put(baseCacheData.getUniqueKey(r), JSON.toJSONString(r));
            }
            RedisTool.getInstance().opsForHash().putAll(getRedisTableMapKey(globalDataType), maps);
        }

    }

    @SafeVarargs
    public static <T> void removeRecords(String projectId, SystemUtil.GlobalDataType globalDataType, T... t) {
        if (t != null && t.length > 0) {
            BaseCacheData baseCacheData = GlobalCache.getDataStructure().getCache(globalDataType);
            T r = t[0];
            String uniqueKey = baseCacheData.getUniqueKey(r);
            RedisTool.getInstance().opsForHash().delete(getRedisTableMapKey(globalDataType), uniqueKey);
        }
    }


}
