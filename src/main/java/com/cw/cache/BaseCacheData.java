package com.cw.cache;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.lang.collector.LangTranslateCollector;
import com.cw.cache.redisimpl.RedisGlobalDataCache;
import com.cw.utils.SystemUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/1 10:17
 **/
public abstract class BaseCacheData<T> implements LangTranslateCollector {
    protected ConcurrentMap<String, ConcurrentMap<String, T>> datas = new ConcurrentHashMap<>();  //提供  <项目ID, 代码,表记录的存储结构>
    protected String defaultCodeFiled = "code";
    protected String defaultDescFiled = "desc";
    protected ConcurrentMap<String, T> oneRecordMap = new ConcurrentHashMap<>(); //孤儿.类似 Section,Interfaceconf 这种只有一条的记录.可以用这个.
    protected Table<String, String, List<T>> groupdatas = HashBasedTable.create();// ConcurrentMap<String, List<T>> groupdatas = new ConcurrentHashMap<>();//分组的数据.提供给 subattr 这种表用的

    protected String rediskey;

    public T getOne(String projectId) {
        return oneRecordMap.get(projectId);
    }

    public T getRecord(String projectId, String key) {
        return datas.getOrDefault(projectId, new ConcurrentHashMap<>()).getOrDefault(key == null ? "" : key, null);
//        return datas.getOrDefault(key, null);
    }


    protected List<T> loadAllData() {
//        List l=RedisTool.getInstance().opsForHash().values()
        List<String> list = RedisTool.getInstance().opsForHash().values(RedisGlobalDataCache.getRedisTableMapKey(getMyType()));
        return (List<T>) JSON.parseArray(StrUtil.wrap(StrUtil.join(",", list), "[", "]"), getCacheTableClass());
    }

    protected List<T> loadProjectData(String projectId) {
        String patternKey = projectId + ":*";
        ScanOptions options = ScanOptions.scanOptions()
                .count(10000) //这里指定每次扫描key的数量 暂定50000
                .match(patternKey).build();
        List<String> projectData = Lists.newArrayList();
        Cursor<Map.Entry<String, String>> cursor = RedisTool.getInstance().opsForHash().scan(RedisGlobalDataCache.getRedisTableMapKey(getMyType()), options);
        while (cursor.hasNext()) {
            projectData.add(cursor.next().getValue());
        }
        try {
            cursor.close();
        } catch (IOException e) {
        }
        return (List<T>) JSON.parseArray(StrUtil.wrap(StrUtil.join(",", projectData), "[", "]"), getCacheTableClass());
    }

    /**
     * 返回分组 list 数据
     *
     * @param key
     * @return
     */
    public List<T> getGroupList(String projectId, String key) {
        List<T> list = null;
        if (groupdatas != null) {
            list = new ArrayList<>();
            if (groupdatas.get(projectId, key) != null) {
                list.addAll(groupdatas.get(projectId, key));
            }
//            groupdatas.get(projectId, key);//  getOrDefault(key, new ArrayList<>());  每次请求都新建一个.避免
        } else {
            list = new ArrayList<>();
        }
        if (list == null) {
            list = Lists.newArrayList();
        }
        if (!list.isEmpty()) {
            sortList(list);
        }
        return list;
    }

    public Map<String, T> getDataMap(String projectId) {
        return datas.getOrDefault(projectId, new ConcurrentHashMap<>());
    }


//    public synchronized void refreshData() {
//        ConcurrentHashMap<String, T> newdata = new ConcurrentHashMap<>();
//        queryAndFillData(newdata);
//        this.datas = newdata;
//    }

//    protected abstract void queryAndFillData(ConcurrentHashMap<String, T> newdata);

    /**
     * @param projectId 传空或 NULL 就是刷新全部.
     */
    public synchronized void refreshData(String projectId) {
        if (StringUtils.isBlank(projectId)) {
            ConcurrentHashMap<String, ConcurrentMap<String, T>> newdatas = new ConcurrentHashMap<>();
            reFreshAllData(newdatas);
            this.datas = newdatas;
        } else {
            ConcurrentHashMap<String, T> projectdatas = new ConcurrentHashMap<>();
            reFreshProjectData(projectdatas, projectId);
            this.datas.put(projectId, projectdatas);
        }

    }

    /**
     * 根据修改通知.删除或者编辑单条记录.单独刷新
     *
     * @param projectId
     * @param refreshKey
     */
    public synchronized void refreshOne(String projectId, String refreshKey) {
        //暂不实现.从 redis 里刷新全部.  groupdata,datas 都要刷新.
//        String mapKey=RedisGlobalDataCache.getRedisTableMapKey(getMyType());
//
//        Object json=RedisTool.getInstance().opsForHash().get(mapKey,refreshKey);
//        if(json==null){ //从 redis 中删除了记录
//            refreshData(projectId);  //读取全表数据.赋值
//        }else {
//            T t=(T)JSON.parseObject(json.toString(),getCacheTableClass());//内存值
//        }
    }

    protected abstract void reFreshAllData(ConcurrentHashMap<String, ConcurrentMap<String, T>> datas);

    /**
     * 获取数据.重新填充map.交给子类填充实现
     */
    protected abstract void reFreshProjectData(ConcurrentMap<String, T> projectdatas, String projectId);

    public String getUniqueKey(T t) {
        return StrUtil.EMPTY;
    }

    /**
     * 返回当前缓存类缓存的表
     *
     * @return
     */
    public Class<?> getCacheTableClass() {
        CacheProp cacheProp = this.getClass().getAnnotation(CacheProp.class);
        return cacheProp == null ? null : cacheProp.cacheClass();
    }

    protected SystemUtil.GlobalDataType getMyType() {
        CacheProp cacheProp = this.getClass().getAnnotation(CacheProp.class);
        return cacheProp == null ? null : cacheProp.dataType();
    }


    /**
     * 提供给子类重载自定义排序方法
     */
    protected void sortList(List<T> list) {

    }

    /**
     * 获取 全部 list 的方法
     *
     * @return
     */
    public List<T> getDataList(String projectId) {
        List<T> list = new ArrayList<>(datas.getOrDefault(projectId, new ConcurrentHashMap<>()).values());
        sortList(list);
        return list;
    }

    /**
     * 条件获取
     *
     * @param projectId
     * @param condition
     * @return
     */
    public List<T> getDataListWithCondition(String projectId, Predicate<T> condition) {
        List<T> list = getDataList(projectId).stream().filter(condition).collect(Collectors.toList());
        return list;
    }


}
