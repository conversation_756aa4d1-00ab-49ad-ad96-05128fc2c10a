package com.cw.cache;

import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/6 02:39
 **/
@Component
public class RedissonTool {

    private static RedissonClient redissonClient;

    @Autowired
    public RedissonTool(RedissonClient redissonClient) {
        RedissonTool.redissonClient = redissonClient;
    }

    public static RedissonClient getInstance() {
        return redissonClient;
    }

}
