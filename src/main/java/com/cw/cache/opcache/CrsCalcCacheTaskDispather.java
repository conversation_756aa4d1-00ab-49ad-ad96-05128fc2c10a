package com.cw.cache.opcache;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.cw.cache.GlobalCache;
import com.cw.core.CoreCache;
import com.cw.entity.Naparam;
import com.cw.entity.Roomtype;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.schedualtask.ElectionService;
import com.cw.utils.*;
import com.cw.utils.enums.LoggerType;
import com.cw.utils.enums.ProdType;
import com.cw.utils.na.NaParams;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/8/7 15:35
 **/
@Service
public class CrsCalcCacheTaskDispather {
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    RabbitTemplate rabbitTemplate;
    @Autowired
    ElectionService electionService;
    private Logger nalogger = LoggerUtil.getLogger(LoggerUtil.LoggerType.nalog);
    private Logger logger = LoggerFactory.getLogger(LoggerType.sys.name());
    @Autowired
    private Environment env;
    @Autowired
    private GlobalCache globalCache;
    @Autowired
    private DaoLocal<?> daoLocal;
    private Integer calcrateInternal = 20;
    private Integer calcgridInternal = 20;
    private Integer calcworker = 2;
    private Integer calcgridlenth = 180;
    private Integer calcratelenth = 180;

//    private int calclenth = 180; //计算的天数.后面改成配置的.包括消费者数量等
//
//    private int workders = 2 * 2;// 总的并发消费者数量, 可同时处理4条消息,这里假设的是2台双核服务器集群
//
//    private int internelMinute = 10; //每次发布任务的时间间隔.

    /**
     * 增加一个动态读取配置的选择. 暂时不做了.等更新再处理吧
     *
     * @param key
     * @return
     */
    private Integer getConfigFromRedis(String key, Integer defaultvalue) {
        Object o = redisTemplate.opsForValue().get(key);
        if (o == null) {
            return defaultvalue;
        } else {
            try {
                Integer num = Integer.parseInt(o.toString()); //如果有正确的INT 值.就返回
                return num;
            } catch (NumberFormatException e) {
            }
        }
        return defaultvalue;
    }

    private Integer get_Rate_Calclenth() {
        calcratelenth = getConfigFromRedis(RedisKey.calc_rate_daylenth, calcratelenth);
        return calcratelenth == null ? 180 : calcratelenth;
    }

    private Integer get_Grid_Calclenth() {
        calcgridlenth = getConfigFromRedis(RedisKey.calc_grid_daylenth, calcgridlenth);
        return calcgridlenth == null ? 180 : calcgridlenth;
    }

    private Integer getWorkders() {
        return calcworker == null ? 2 : calcworker;
    }

    private Integer get_Rate_InternelMinute() {
//        Sysconf interfaceConf = GlobalCache.getDataStructure().getConf();
//        if (!interfaceConf.getRateinternal().isEmpty()) {
//            calcrateInternal = NumberUtil.parseInt(interfaceConf.getRateinternal());
//        }
        return CalculateNumber.max(20, calcrateInternal); //尽量按高.设置过低也不行
    }

    private Integer get_Grid_InternalMinute() {
//        Sysconf interfaceConf = GlobalCache.getDataStructure().getConf();
//        if (!interfaceConf.getGridinternal().isEmpty()) {
//            calcgridInternal = NumberUtil.parseInt(interfaceConf.getGridinternal());
//        }
        return CalculateNumber.max(20, calcgridInternal);//尽量按高.设置过低也不行
    }


    public void publishJob() {  //心跳执行.每5分钟会执行一次
        if (!canExcutePub()) {
            return;
        }
        pubRateTask();
        pubGridTask();
    }

    public void publishRateJob() {  //心跳执行.每5分钟会执行一次
        if (!canExcutePub()) {
            return;
        }
        pubRateTask();
    }

    public void publishGridJob() {  //心跳执行.每5分钟会执行一次
        if (!canExcutePub()) {
            return;
        }
        pubGridTask();
    }

    private boolean canExcutePub() {
        boolean lcanop = electionService.isMasterNode();
        if (lcanop) {//存活时间8分钟. 8分钟没有重新发布任务.就交给集群的其他机器去发
//            logger.info("我是定时缓存同步任务发布器");
        } else {
//            logger.debug("我不是任务发布器.跳过.等待机会");
            return false;
        }
        return true;
    }

    private HashSet<String> calcPubRateTaskUnit() {
        HashSet<String> set = new HashSet<>();
        return set;
    }

    private HashSet<String> calcPubRoomGridTaskUnit() {
        HashSet<String> set = new HashSet<>();
        return set;
    }


    //redis历史价格清理 目前只有房的
    public void clearRateCache(Date naDate, String projectId) {
        //缓存配置一个指定天数
        Integer day = 7;
        Naparam naparam = (Naparam) GlobalCache.getDataStructure()
                .getCache(SystemUtil.GlobalDataType.NA_PARAM).getRecord(projectId, NaParams.CL_CACHE_DAY.name());
        if (naparam != null) {
            day = Integer.valueOf(naparam.getParamvalue());
        }
        Date fromDateObj = CalculateDate.reckonDay(naDate, 5, day * -1);
        String fromDate = DateUtil.format(fromDateObj, DatePattern.NORM_DATE_PATTERN);
        nalogger.info("★Redis清理{}天前价格缓存 {},开始", day, fromDate);

        //List<Channeldet> channelList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.CHANNELDET).getDataList();
        //HashSet<String> rateSet = new HashSet<>();
        //for (Channeldet row : channelList) {
        //    String[] rates = row.getRatecodes().split(",");
        //    Collections.addAll(rateSet, rates);
        //}
        List<String> rateCodeList = daoLocal.getObjectList("select code from Ratecode where projectid=?1", projectId);
        for (String rate : rateCodeList) {
            List<Roomtype> roomTypeList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).getDataList(projectId);
            for (Roomtype roomType : roomTypeList) {
                String key = CoreCache.getRateCacheKey(projectId, rate, ProdType.ROOM.val(), roomType.getCode());
                Set<String> rateKey = redisTemplate.opsForHash().keys(key);
                List<String> rateList = rateKey.stream().filter(s -> CalculateDate.isBefore(CalculateDate.stringToDate(s), fromDateObj)).collect(Collectors.toList());
                String[] strs = rateList.toArray(new String[rateList.size()]);
                if (strs.length > 0) {
                    redisTemplate.opsForHash().delete(key, strs);
                }
            }
        }
        nalogger.info("★Redis清理价格缓存,结束,清理{}个价格代码缓存 {}", rateCodeList.size(), StringUtils.join(rateCodeList.toArray(), ","));
    }

    /**
     * 清理redis历史库存
     *
     * @param naDate
     * @param projectId
     */
    public void clearGridCache(Date naDate, String projectId) {
        //缓存配置一个指定天数
        Integer day = 7;
        Naparam naparam = (Naparam) GlobalCache.getDataStructure()
                .getCache(SystemUtil.GlobalDataType.NA_PARAM).getRecord(projectId, NaParams.CL_CACHE_DAY.name());
        if (naparam != null) {
            day = Integer.valueOf(naparam.getParamvalue());
        }
        Date fromDateObj = CalculateDate.reckonDay(naDate, 5, day * -1);
        String fromDate = DateUtil.format(fromDateObj, DatePattern.NORM_DATE_PATTERN);
        nalogger.info("★Redis清理{}天前库存缓存 {},开始", day, fromDate);

        //Set<String> channelList = ((ChannelDataHandler)SpringUtil.getBean(CustomData.class).getHandler(SystemUtil.CustomDataKey.channel)).getNotifyChannels();

        //for (String channel : channelList) {
        //List<String> blockList = daoLocal.getObjectList("select code from Block where channel=?1", channel);
        List<String> blockList = Arrays.asList("");  //daoLocal.getObjectList("select code from Ratecode where projectid=?1", projectId);
        for (String block : blockList) {
            List<Roomtype> roomTypeList = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.ROOMTYPE).getDataList(projectId);
            for (Roomtype roomType : roomTypeList) {
                String key = CoreCache.getResourceCacheKey(projectId, block, ProdType.ROOM.val(), roomType.getCode());
                Set<String> gridKey = redisTemplate.opsForHash().keys(key);
                List<String> gridList = gridKey.stream().filter(s -> CalculateDate.isBefore(CalculateDate.stringToDate(s), fromDateObj)).collect(Collectors.toList());
                String[] strs = gridList.toArray(new String[gridList.size()]);
                if (strs.length > 0) {
                    redisTemplate.opsForHash().delete(key, strs);
                }
            }
        }
        //}
        //    nalogger.info("★Redis清理库存缓存,结束,清理{}个渠道库存 {}",channelList.size(),StringUtils.join(channelList.toArray(),","));
        nalogger.info("★Redis清理库存缓存,结束,清理{}个渠道库存 {}", blockList.size(), StringUtils.join(blockList.toArray(), ","));

    }


    private void pubRateTask() {

    }

    private void pubGridTask() {

    }


}
