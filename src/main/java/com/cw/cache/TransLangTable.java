package com.cw.cache;

import com.cw.entity.Rlang;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 有需要快速获取翻译内容的接口
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/10/25 16:52
 **/
public interface TransLangTable {
    /*
     *
     * 收集这个表内当前所有需要翻译的原始字段信息
     *
     * @param projectId
     * @return
     */
    default List<Rlang> collectLangInfo(String projectId) {
        return Lists.newArrayList();
    }
}
