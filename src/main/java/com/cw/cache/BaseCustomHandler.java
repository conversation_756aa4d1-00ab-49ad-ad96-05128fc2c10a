package com.cw.cache;


import com.cw.utils.pagedata.SelectDataNode;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/4 11:03
 **/
public abstract class BaseCustomHandler {

    public abstract String trans(String projectId, String key); //翻译

    public String getTabletrans(String projectId, String key) {
        return trans(projectId, key); //需要的自己重载定义表格显示吧.默认都是一样的
    }

    /**
     * 默认要求实现下拉数据方法
     *
     * @return
     */
    public abstract List<SelectDataNode> getSelectData(String projectId, String... param);

    /**
     * 给 factor,template 这种数据用的.全局 Map 里 key 是组+"_"+code
     *
     * @param group
     * @param key
     * @return
     */
    public String transByGroup(String projectId, String group, String key) {
        return trans(projectId, group + key);
    }
}
