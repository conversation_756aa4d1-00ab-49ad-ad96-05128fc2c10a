package com.cw.inject.rights;

import com.cw.cache.GlobalCache;
import com.cw.cache.impl.OpRoleRightCache;
import com.cw.cache.impl.UserCache;
import com.cw.entity.Op_user;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.service.context.GlobalContext;
import com.cw.utils.SystemUtil;
import com.cw.utils.rights.OpRightCodes;
import com.cw.utils.rights.RequireOpRight;
import com.cw.utils.rights.RightCode;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by flyhigh on 2017/6/9.
 */
@Aspect
@Component
@Order(2)
public class ApiRightInjecter {

//    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisTemplate redisTemplate;


    //pointcut 表达式写法
    //  "execution(* com.just.demo.service.UserShoppingService.buy(..))&&args(bookname)"
//    任意公共方法的执行：
//    execution(public * *(..))
//    任何一个以“set”开始的方法的执行：
//    execution(* set*(..))
//    AccountService 接口的任意方法的执行：
//    execution(* com.xyz.service.AccountService.*(..))
//    定义在service包里的任意方法的执行：
//    execution(* com.xyz.service.*.*(..))
//    定义在service包和所有子包里的任意类的任意方法的执行：
//    execution(* com.xyz.service..*.*(..))
//    定义在pointcutexp包和所有子包里的JoinPointObjP2类的任意方法的执行：
//    execution(* com.test.spring.aop.pointcutexp..JoinPointObjP2.*(..))")
//            ***> 最靠近(..)的为方法名,靠近.*(..))的为类名或者接口名,如上例的JoinPointObjP2.*(..))
//
//     pointcutexp包里的任意类.
//      within(com.test.spring.aop.pointcutexp.*)
//      pointcutexp包和所有子包里的任意类.
//      within(com.test.spring.aop.pointcutexp..*)
//    实现了MyInterface接口的所有类,如果MyInterface不是接口,限定MyInterface单个类.
//this(com.test.spring.aop.pointcutexp.MyInterface)
//            ***> 当一个实现了接口的类被AOP的时候,用getBean方法必须cast为接口类型,不能为该类的类型.
//
//            带有@MyTypeAnnotation标注的所有类的任意方法.
//    @within(com.elong.annotation.MyTypeAnnotation)
//    @target(com.elong.annotation.MyTypeAnnotation)
//            带有@MyTypeAnnotation标注的任意方法.
//    @annotation(com.elong.annotation.MyTypeAnnotation)
//***> @within和@target针对类的注解,@annotation是针对方法的注解
//
//    参数带有@MyMethodAnnotation标注的方法.
//    @args(com.elong.annotation.MyMethodAnnotation)
//    参数为String类型(运行是决定)的方法.
//    args(String)


    //切点:拦截加了加锁注解的方法
    @Pointcut("within(com.cw.controller.config..*)&&@annotation(com.cw.utils.rights.RequireOpRight)")
    //主要是为了给下单.支付.取消增加
    public void apiRightCut() {

    }

    @Around("apiRightCut()&&@annotation(requireOpRight)")
    public Object checkRight(ProceedingJoinPoint joinPoint, RequireOpRight requireOpRight) throws Throwable {//注意. around拦截要返回拦截方法的返回值.否则会出现没结果返回的情况
//        logger.info("当前方法要求的权限:"+ requireOpRight.opRight()+SpringUtil.getCurrentUser().getUserid());
        OpRoleRightCache opRoleRightCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.OP_ROLE_RIGHT);
        UserCache userCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.USER);
        Op_user opUser = userCache.getRecord(GlobalContext.getCurrentProjectId(), GlobalContext.getCurrentUserId());
        boolean lhasright = false;
        if (opUser != null) {
            lhasright = opRoleRightCache.hasRight(opUser.getProjectid(), opUser.getRoleid(), requireOpRight.opRight());
        }
        if (!lhasright) {
//            throw new AccessDeniedException("方法权限不足");
            //  return ResultJson.failure(ResultCode.FORBIDDEN).msg("没有权限" + requireOpRight.opRight());
            //返回权限不足的名字
            int opRight = requireOpRight.opRight();
            List<RightCode> opRights = OpRightCodes.getAllOpRights();
            for (RightCode right : opRights) {
                if (Integer.parseInt(right.getCode()) == opRight) {
                    return ResultJson.failure(ResultCode.FORBIDDEN).msg("没有权限[" + right.getCodename() + "]代码:" + right.getCode());
                }
            }

        }
        Object object = null;
        try {
            object = joinPoint.proceed();
        } catch (Throwable throwable) {
            throw throwable;
        }
        return object;
    }


}
