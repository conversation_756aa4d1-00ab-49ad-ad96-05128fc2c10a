package com.cw.inject;

import cn.dev33.satoken.exception.NotLoginException;
import com.alibaba.fastjson.JSON;
import com.cw.config.exception.CustomException;
import com.cw.exception.DefinedException;
import com.cw.service.log.impl.SystemLogTool;
import com.cw.utils.LoggerUtil;
import com.cw.utils.RobotUtils;
import com.cw.utils.datetime.DateStyle;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Created by flyhigh on 2017/6/9.
 */
@Slf4j
@Aspect
@Component
@Order(3)
public class ExceptionLogInjecter {


    //pointcut 表达式写法
    //  "execution(* com.just.demo.service.UserShoppingService.buy(..))&&args(bookname)"
//    任意公共方法的执行：
//    execution(public * *(..))
//    任何一个以“set”开始的方法的执行：
//    execution(* set*(..))
//    AccountService 接口的任意方法的执行：
//    execution(* com.xyz.service.AccountService.*(..))
//    定义在service包里的任意方法的执行：
//    execution(* com.xyz.service.*.*(..))
//    定义在service包和所有子包里的任意类的任意方法的执行：
//    execution(* com.xyz.service..*.*(..))
//    定义在pointcutexp包和所有子包里的JoinPointObjP2类的任意方法的执行：
//    execution(* com.test.spring.aop.pointcutexp..JoinPointObjP2.*(..))")
//            ***> 最靠近(..)的为方法名,靠近.*(..))的为类名或者接口名,如上例的JoinPointObjP2.*(..))
//
//     pointcutexp包里的任意类.
//      within(com.test.spring.aop.pointcutexp.*)
//      pointcutexp包和所有子包里的任意类.
//      within(com.test.spring.aop.pointcutexp..*)
//    实现了MyInterface接口的所有类,如果MyInterface不是接口,限定MyInterface单个类.
//this(com.test.spring.aop.pointcutexp.MyInterface)
//            ***> 当一个实现了接口的类被AOP的时候,用getBean方法必须cast为接口类型,不能为该类的类型.
//
//            带有@MyTypeAnnotation标注的所有类的任意方法.
//    @within(com.elong.annotation.MyTypeAnnotation)
//    @target(com.elong.annotation.MyTypeAnnotation)
//            带有@MyTypeAnnotation标注的任意方法.
//    @annotation(com.elong.annotation.MyTypeAnnotation)
//***> @within和@target针对类的注解,@annotation是针对方法的注解
//
//    参数带有@MyMethodAnnotation标注的方法.
//    @args(com.elong.annotation.MyMethodAnnotation)
//    参数为String类型(运行是决定)的方法.
//    args(String)


    //切点:拦截加了加锁注解的方法
    @Pointcut("within(com.cw.controller..*)&&@annotation(org.springframework.web.bind.annotation.RequestMapping)")
    //主要是为了给下单.支付.取消增加
    public void controllerCut() {

    }

    @Around(value = "controllerCut()")
    public Object controlException(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        try {
            result = joinPoint.proceed();
        } catch (Exception e) {
            logErr(joinPoint, e);//拦截错误.打印错误参数
            throw e;
        }
        return result;
    }

    private void logErr(ProceedingJoinPoint joinPoint, Exception e) {
        HttpServletRequest request = ((ServletRequestAttributes)
                RequestContextHolder.getRequestAttributes()).getRequest();

        // 请求路径
        String requestUrl = request.getRequestURL().toString();

        // 获取请求参数进行打印
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        List<Object> params = Lists.newArrayList();
        for (Object arg : joinPoint.getArgs()) {
            boolean lneedlog = arg.getClass().getName().startsWith(LoggerUtil.pkgpreifix);
            if (arg instanceof String) {
                params.add(arg);
            } else if (lneedlog) {
                params.add(arg);
            }
        }

        String strexinfo = LoggerUtil.getExceptionDesc(e, true);
        boolean lsendrobot = true;
        if (e instanceof DefinedException) {
            DefinedException df = (DefinedException) e;
            if (df.getErrorcode() > 100 && df.getErrorcode() < 200) { //校验表单的异常就.不发到机器人了
                lsendrobot = false;
            }
        }
        if (e instanceof CustomException) {
            CustomException customEx = (CustomException) e;
            if (customEx.getResultJson() != null) {
                if (customEx.getResultJson().getCode() < 400) {//一般是用户输入问题.密码错的之类.不做警告处理
                    lsendrobot = false;
                }
            }
        }
        if (e instanceof NotLoginException) {
            //System.out.println(request.getRequestURI());
            log.error("没带TOKEN的请求来源:{}", requestUrl);

            lsendrobot = false;
        }
        //机器人报错通知
        if (lsendrobot) {
            String robotMsg = RobotUtils.transRobotExceptionParamsMsg(strexinfo,
                    JSON.toJSONStringWithDateFormat(params, DateStyle.YYYY_MM_DD.getValue()),
                    requestUrl, request);
            SystemLogTool.getInstance().sendOfficeMsg(RobotUtils.RobotGroup.DSMALLERROR, robotMsg);

//            System.out.println(robotMsg);
        }
//        log.error("哟哟.错误来了--->请求URL {} 参数{}",requestUrl,params);
    }

}
