package com.cw.inject.log;

import cn.hutool.extra.servlet.ServletUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.NamedThreadLocal;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/11/29 09:52
 **/
@Slf4j
@Component
public class SlowQueryRequestInterceptor implements HandlerInterceptor {

    private static final long THRESHOLD = TimeUnit.SECONDS.toMillis(2);//超时监控. 2秒以上的都记录下 暂时不记录body 参数.后面加上traceid 请求再做记录

    private Logger slowLogger = LoggerFactory.getLogger("slowlog");

    private NamedThreadLocal<Long> threadLocal = new NamedThreadLocal("StopWatch_StartTime");


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        long startTime = System.currentTimeMillis();

   /*     String url=request.getRequestURL().toString();

        // 执行请求
        handler.getClass().getMethod("handleRequest", HttpServletRequest.class, HttpServletResponse.class).invoke(handler, request, response);
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        if (duration > THRESHOLD) {
            slowLogger.info("请求url:{} body:{} 耗时:{}ms ", url, request.getQueryString(), duration);
        }*/
        threadLocal.set(startTime);
        return true;
    }

    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        long startTime = threadLocal.get();
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        if (!ServletUtil.isMultipart(request) && duration > THRESHOLD) {
            slowLogger.info("请求url:{} 参数:{} 耗时:{}ms ", request.getRequestURI(), request.getQueryString(), duration);
        }
        threadLocal.remove();
    }

    @Data
    private static class TempRecord {
        private Long startTime;
        private String body;

        public TempRecord(Long startTime, String body) {
            this.startTime = startTime;
            this.body = body;
        }
    }
}
