package com.cw.inject.cache;

import com.cw.core.CorePrice;
import com.cw.utils.enums.ProdType;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/1/4 02:12
 **/
@Aspect
@Component
public class RateInjecter {
    @Autowired
    CorePrice corePrice;

    @Pointcut(value = "execution(* com.cw.mapper.common.bean.RratedetDao.batchUpdateRratedet(..))")
    public void updRoomPricePointCut() {

    }

    //  String roomType, Date startDate, Date endDate,String rateCode, String projectId, BigDecimal price, List<Integer> week
    @AfterReturning("updRoomPricePointCut()" +
            "&&args(roomType,startDate,endDate,rateCode,projectId,price,week)")
    public void updRoomCache(String roomType, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price, List<Integer> week) {
        corePrice.calc2Cache(projectId, rateCode, ProdType.ROOM, roomType, startDate, endDate);
    }


    @Pointcut(value = "execution(* com.cw.mapper.common.bean.TratedetDao.batchupdateTratedet(..))")
    public void updTicketPricePointCut() {

    }

    //  String tCode, Date startDate, Date endDate, String projectId, BigDecimal price, List<Integer> week
    @AfterReturning(value = "updTicketPricePointCut()" +
            "&&args(tCode,startDate,endDate,rateCode,projectId,price,week)", argNames = "tCode,startDate,endDate,rateCode,projectId,price,week")
    public void updTicketCache(String tCode, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price, List<Integer> week) {
        corePrice.calc2Cache(projectId, rateCode, ProdType.TICKET, tCode, startDate, endDate);

        System.out.println("updTicketCache 票务价格缓存更新");

    }

    @Pointcut(value = "execution(* com.cw.mapper.common.bean.SpuratedetDao.batchUpdateSpuratedet(..))")
    public void updSpusitemPricePointCut() {

    }

    @AfterReturning(value = "updSpusitemPricePointCut()" +
            "&&args(spusitem,startDate,endDate,rateCode,projectId,price,week)", argNames = "spusitem,startDate,endDate,rateCode,projectId,price,week")
    public void updSpusitemCache(String spusitem, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price, List<Integer> week) {
        corePrice.calc2Cache(projectId, rateCode, ProdType.WARES, spusitem, startDate, endDate);

        System.out.println("updSpusitemCache 景区产品价格缓存更新");

    }


    @Pointcut(value = "execution(* com.cw.mapper.common.bean.ActratedetDao.batchUpdateActratedet(..))")
    public void updActsitePricePointCut() {

    }

    @AfterReturning(value = "updActsitePricePointCut()" +
            "&&args(sitecode,period,startDate,endDate,rateCode,projectId,price,week)", argNames = "sitecode,period,startDate,endDate,rateCode,projectId,price,week")
    public void updActsiteCache(String sitecode, String period, Date startDate, Date endDate, String rateCode, String projectId, BigDecimal price, List<Integer> week) {
        //corePrice.calc2Cache(projectId, rateCode, ProdType.ACTGROUP, sitecode, startDate, endDate);
        //
        //System.out.println("updActsiteCache 预约场所价格缓存更新");

    }


    @Pointcut(value = "execution(* com.cw.mapper.common.bean.GiftratedetDao.updateGiftratedet(..))")
    public void updGiftitemPricePointCut() {

    }

    @AfterReturning(value = "updGiftitemPricePointCut()" +
            "&&args(giftitem,rateCode,projectId,price)", argNames = "giftitem,rateCode,projectId,price")
    public void updGiftitemPriceCache(String giftitem, String rateCode, String projectId, BigDecimal price) {
        //corePrice.calc2Cache(projectId, rateCode, ProdType.ITEMS, giftitem, new Date(), new Date());
        //System.out.println("updGiftitemPriceCache 伴手礼商品价格缓存更新");

    }

}
