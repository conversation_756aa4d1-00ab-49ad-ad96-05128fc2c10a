package com.cw.inject.cache;

import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-03-31
 */
@Aspect
@Component
public class SmsInjecter {
    @Pointcut(value = "execution(* com.cw.service.log.impl.MsgServiceImpl.sendMessage(..))")
    public void sendMsgPointCut() {  //拦截发送短信

    }
}
