package com.cw.inject.cache;

import com.alibaba.fastjson.JSONObject;
import com.cw.core.CoreAvl;
import com.cw.core.func.order.StdOrderData;
import com.cw.entity.Booking_rs;
import com.cw.pojo.dto.app.req.node.CombineInfo;
import com.cw.utils.enums.ProdType;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/1/4 02:12
 **/
@Aspect
@Component
public class GridResourceInjecter {
    @Autowired
    CoreAvl coreAvl;

    @Pointcut(value = "execution(* com.cw.core.CoreSync3.diapatchCancelOrderAction(..))")
    public void cancelOrderPointCut() {

    }

    @AfterReturning("cancelOrderPointCut()&&args(orderData)")
    public void updCancelCache(StdOrderData orderData) {
        if (orderData.getBookingRs() != null) {
            Booking_rs rs = orderData.getBookingRs();
            if (rs != null) {

                if (rs.getPtype().equals(ProdType.ITEMS.val())) {
                    CombineInfo combineInfo = JSONObject.parseObject(rs.getCombineinfo(), CombineInfo.class);
                    for (CombineInfo.CombineNode node : combineInfo.getNodes()) {
                        coreAvl.calcGiftItemCache(rs.getProjectid(), node.getGroupid(), Arrays.asList(node.getProductcode()));//重算所有合单购买的伴手礼缓存
                    }

                } else {//普通的日期型产品
                    coreAvl.calc2Cache(rs.getProjectid(), rs.getProduct(), rs.getArrdate(), rs.getDeptdate(), ProdType.getProdType(rs.getPtype()), "");//TODO 最后一个参数是伴手礼的规格
                }
//                LoggerFactory.getLogger(this.getClass()).info("取消订单更新缓存 {} {} {}", rs.getProduct(), rs.getArrdate(), rs.getDeptdate());

            }

        }
    }


    @Pointcut(value = "execution(* com.cw.mapper.common.bean.RroomsDao.batchUpdateRrooms(..))")
    public void updRoomResourcePointCut() {

    }

    //  String roomType, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week
    @AfterReturning("updRoomResourcePointCut()&&args(roomType,startDate,endDate,projectId,avl,week)")
    public void updRoomCache(String roomType, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week) {
        coreAvl.calc2Cache(projectId, roomType, startDate, endDate, ProdType.ROOM, "");

    }

    @Pointcut(value = "execution(* com.cw.mapper.common.bean.RticketsDao.batchUpdateRticket(..))")
    public void updTicketResourcePointCut() {

    }

    //  String tCode, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week
    @AfterReturning("updTicketResourcePointCut()&&args(tCode,startDate,endDate,projectId,avl,week)")
    public void updTicketCache(String tCode, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week) {
        coreAvl.calc2Cache(projectId, tCode, startDate, endDate, ProdType.TICKET, "");

    }


    @Pointcut(value = "execution(* com.cw.mapper.common.bean.RspuitemsDao.updateRspuitems(..))")
    public void updSpusitemResourcePointCut() {

    }

    @AfterReturning("updSpusitemResourcePointCut()&&args( spusitem,  projectId,  avl,  pickup, startDate, endDate)")
    public void updSpusitemCache(String spusitem, String projectId, Integer avl, Integer pickup, Date startDate, Date endDate) {
        coreAvl.calc2Cache(projectId, spusitem, startDate, endDate, ProdType.WARES, "");

    }

    @Pointcut(value = "execution(* com.cw.mapper.common.bean.RacttimeDao.batchUpdateRacttime(..))")
    public void updActsiteResourcePointCut() {

    }

    @AfterReturning("updActsiteResourcePointCut()&&args(sitecode,period,startDate,endDate,projectId,avl,week)")
    public void updSitecodeCache(String sitecode, String period, Date startDate, Date endDate, String projectId, Integer avl, List<Integer> week) {
        //coreAvl.calc2Cache(projectId, sitecode, startDate, endDate, ProdType.ACTGROUP, period);
        //System.out.println("updSitecodeCache 预约场所库存缓存更新");

    }


    @Pointcut(value = "execution(* com.cw.mapper.common.bean.RgiftitemsDao.updateGiftitems(..))")
    public void updGiftitemResourcePointCut() {

    }

    @AfterReturning("updGiftitemResourcePointCut()&&args(giftitem,projectId,avl)")
    public void updGiftitemResourceCache(String giftitem, String projectId, Integer avl) {
        //coreAvl.calc2Cache(projectId, giftitem, new Date(), new Date(), ProdType.ITEMS, "");
        //System.out.println("updGiftitemCache 伴手礼商品库存缓存更新");

    }

//    @AfterReturning("updRoomResourcePointCut()")
//    public void updRoomCache2() {
//        LoggerFactory.getLogger(this.getClass()).error("更新房型缓存GRID2222");
//    }

}
