package com.cw.inject.cache;

import com.cw.core.func.order.StdOrderData;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2022/1/4 02:12
 **/
@Aspect
@Component
@Order(99)
@Slf4j
public class OrderInjecter {

    @Pointcut(value = "execution(* com.cw.core.CoreSync3.saveOrder_Now(..))")
    public void createOrderPointCut() {  //拦截创建订单

    }

    @Pointcut(value = "execution(* com.cw.core.CoreSync3.diapatchCancelOrderAction(..))")
    public void cancelOrderPointCut() {//拦截取消订单

    }


    @AfterReturning("cancelOrderPointCut()&&args(orderData)")
    public void updCancelSumCalc(StdOrderData orderData) {
        sum(orderData, true);
    }

    @AfterReturning("createOrderPointCut()&&args(orderData)")
    public void updCreateSumCalc(StdOrderData orderData) {
        sum(orderData, false);
    }

    private void sum(StdOrderData orderData, boolean lcanel) {
//        log.info("执行订单统计");
        //将统计数据丢到统计的 redis 队列.定时取出.更新到内存统计表
    }


}
