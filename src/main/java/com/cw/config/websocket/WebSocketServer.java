package com.cw.config.websocket;

import com.cw.utils.RedisKey;
import com.cw.utils.SpringUtil;
import com.cw.utils.WebSocketUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.net.SocketException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022/1/18 0018
 */
@ServerEndpoint("/ws/asset/{projectid}")
@Component
@Slf4j
public class WebSocketServer {
    /* 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
     */
    private static AtomicInteger online = new AtomicInteger();

    /**
     * concurrent包的线程安全Map，用来存放每个客户端对应的WebSocketServer对象。
     * key: IP:PORT  eg：127.0.0.1:8081
     * value: 连接session对象
     */
    private static Map<String, Session> sessionPools = Maps.newConcurrentMap();
    /**
     * map<ip,userid>
     */
    private static Map<Object, Object> userMap = new HashMap<>();
    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 发送消息方法
     *
     * @param session 客户端与socket建立的会话
     * @param message 消息
     * @throws IOException
     */
    public void sendMessage(Session session, String message) throws IOException {
        if (session != null && session.isOpen() && StringUtils.isNotBlank(message)) {
            session.getBasicRemote().sendText(message);
        }
    }


    /**
     * 连接建立成功调用
     *
     * @param session 客户端与socket建立的会话
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("projectid") String projectid) throws SocketException {
        String remoteIP = WebSocketUtil.getRemoteIP(session);
        String localIp = WebSocketUtil.getServerIp();
        String redisKey = remoteIP + "-" + localIp;
        redisTemplate = (RedisTemplate) SpringUtil.getBean("redisTemplate");
        userMap = redisTemplate.opsForHash().entries(RedisKey.webUserIdMaP);
        if (sessionPools.containsKey(remoteIP)) {
            sessionPools.remove(remoteIP);
            sessionPools.put(remoteIP, session);
        } else {
            sessionPools.put(remoteIP, session);
            online.incrementAndGet();
        }
        //redis刷新
        if (userMap.containsKey(redisKey)) {
            redisTemplate.opsForHash().delete(RedisKey.webUserIdMaP, redisKey);
            redisTemplate.opsForHash().put(RedisKey.webUserIdMaP, redisKey, projectid);
        } else {
            redisTemplate.opsForHash().put(RedisKey.webUserIdMaP, redisKey, projectid);
        }

    }

    /**
     * 关闭连接时调用
     *
     * @param session 客户端与socket建立的会话
     */
    @OnClose
    public void onClose(Session session) throws SocketException {
        String localIp = WebSocketUtil.getServerIp();//获取本地服务器ip
        if (session != null && session.isOpen()) {
            redisTemplate = (RedisTemplate) SpringUtil.getBean("redisTemplate");
            userMap = redisTemplate.opsForHash().entries(RedisKey.webUserIdMaP);
            String remoteIP = WebSocketUtil.getRemoteIP(session);
            String redisKey = remoteIP + "-" + localIp;
            if (sessionPools.containsKey(remoteIP)) {
                sessionPools.remove(remoteIP);
                online.decrementAndGet();
            }
            //redis去除对应IP信息 刷新
            if (userMap.containsKey(redisKey)) {
                redisTemplate.opsForHash().delete(RedisKey.webUserIdMaP, redisKey);
            }
            log.info("远程客户端断开连接,客户端ip:{},服务器IP:{}", remoteIP, localIp);
        } else {

            //遍历删除session为空的数据
            checkLocalSession(localIp);
        }


    }


    /**
     * 检查本地  删除断开链接的session  ，前端负责心跳
     *
     * @param localIp
     */
    private void checkLocalSession(String localIp) {
        if (sessionPools.size() > 0) {
            redisTemplate = (RedisTemplate) SpringUtil.getBean("redisTemplate");
            for (Map.Entry<String, Session> entry : sessionPools.entrySet()) {
                if (entry.getValue() == null || !entry.getValue().isOpen()) { //session为null
                    sessionPools.remove(entry.getKey());
                    online.decrementAndGet();
                    String redisKey = entry.getKey() + "-" + localIp;
                    redisTemplate.opsForHash().delete(RedisKey.webUserIdMaP, redisKey);
                    log.info("后台客户端断开连接获取数据统计,客户端ip:{},服务器IP:{}", entry.getKey(), localIp);
                }
            }
        }
    }

    /**
     * 收到客户端消息时触发（群发）
     *
     * @param message 消息
     * @throws IOException
     */
    @OnMessage
    public void onMessage(String message) throws IOException {
        for (Session session : sessionPools.values()) {
            try {

            } catch (Exception e) {
                log.error("群发消息失败", e);
            }
        }
    }


    /**
     * 发生错误时候
     *
     * @param session
     * @param throwable
     */
    @OnError
    public void onError(Session session, Throwable throwable) throws SocketException {
        log.info("后台客户端【{}】发生错误", WebSocketUtil.getRemoteAddress(session));
        checkLocalSession(WebSocketUtil.getServerIp());
        throwable.printStackTrace(); //打印错误信息 可以不打印
    }

    /**
     * 给指定用户发送消息
     *
     * @param remoteIP 远程客户端标识 IP:PORT  eg:127.0.0.1:8081
     * @param message  消息
     * @throws IOException
     */
    public void sendInfo(String remoteIP, String message) throws SocketException {
        Session session = sessionPools.get(remoteIP);
        if (session == null) {
            if (sessionPools.containsKey(remoteIP)) {
                sessionPools.remove(remoteIP);
                online.decrementAndGet();
            }
            String localIp = WebSocketUtil.getServerIp();
            String redisKey = remoteIP + "-" + localIp;
            //负载均衡 需要删除对应本机
            redisTemplate = (RedisTemplate) SpringUtil.getBean("redisTemplate");
            redisTemplate.opsForHash().delete(RedisKey.webUserIdMaP, redisKey);
            log.info("服务器IP:{}不存在后台客户端IP:{}的session链接，清除本地和redis记录", localIp, remoteIP);
        } else if (!session.isOpen()) {
            onClose(session);
        } else {
            try {
                sendMessage(session, message);
            } catch (Exception e) {
                log.error("给后台客户端【{}】发送消息失败", remoteIP, e);
            }
        }
    }
}
