package com.cw.config.filter;

import cn.hutool.core.util.StrUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 全局跨域处理.
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/23 14:21
 **/
//已经暂时用 Mycors 中的过滤器代替
@Deprecated
@ConditionalOnExpression(" false")
@Component
public class HttpRequestFilter implements Filter {
    static final String OPTIONS = "OPTIONS";
    static final String MAXAGE = "3600";
    static final String ALLOWMETHODS = "POST, GET, OPTIONS, DELETE,HEAD";
    static final String ALLOWSIGNAL = "*";
    static final String AGENT = "Agent";//对应 AGENTTYPE 枚举
    static final String APPID = "Appid";  // 客户端对应的项目 ID

    @PostConstruct
    public void bbb() {
        System.out.println("filter 初始化来咯");
    }


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        // 允许指定域访问跨域资源
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, ALLOWSIGNAL);

        // 允许所有请求方式
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, ALLOWMETHODS);
        // 有效时间
        response.setHeader(HttpHeaders.ACCESS_CONTROL_MAX_AGE, MAXAGE);
        // 允许的header参数
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, ALLOWSIGNAL);

        if (OPTIONS.equals(((HttpServletRequest) servletRequest).getMethod())) {      //放行 OPTIONS 请求
            response.getWriter().print(StrUtil.EMPTY);
            return;
        }
        request.setAttribute(AGENT, request.getHeader(AGENT));  //APP 端的访问都缓存起来
        request.setAttribute(APPID, request.getHeader(APPID));//APP 端的访问都缓存起来

        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void destroy() {

    }
}
