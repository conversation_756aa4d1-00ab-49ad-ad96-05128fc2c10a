package com.cw.config.filter;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/17 17:05
 **/
@Slf4j
//@WebFilter(filterName = "traceLogFilter",urlPatterns = "/webapi/*")
public class SpeedFilter {//extends AbstractRequestLoggingFilter
//    private final String REQID="REQID";
//    private final String REQSTARTMILL="REQSTARTMILL";
//
//    private long workid=RandomUtil.randomLong(1,99999)+RandomUtil.randomLong(1,99999);
//
//    private long dataid=RandomUtil.randomLong(1,99999)+RandomUtil.randomLong(1,99999);
//
//    @Override
//    protected void beforeRequest(HttpServletRequest httpServletRequest, String s) {
//        MDC.put(REQID, IdUtil.getSnowflake(workid,dataid).nextIdStr());
//        MDC.put(REQSTARTMILL,String.valueOf(System.currentTimeMillis()));
//        Stopwatch.createStarted();
//    }
//
//    @Override
//    protected void afterRequest(HttpServletRequest httpServletRequest, String s) {
//        log.info("接口请求: {}  请求内容{} 耗时 {} ms",httpServletRequest.getRequestURI(),s,(System.currentTimeMillis()-Long.valueOf(MDC.get(REQSTARTMILL))));
//    }
//
//    @Override
//    public void destroy() {
//        MDC.remove(REQID);
//    }
//
//
//    @Override
//    protected boolean isIncludePayload() {
//        return true;
//    }
//
//    @Override
//    protected boolean isIncludeQueryString() {
//        return true;
//    }
}
