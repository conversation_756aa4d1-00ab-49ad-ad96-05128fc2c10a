package com.cw.config.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.hutool.core.util.StrUtil;
import com.cw.exception.DefinedException;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.utils.LoggerUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * controller层异常无法捕获处理，需要自己处理
 * Created at 2018/8/27.
 */
@RestControllerAdvice
@Slf4j
public class DefaultExceptionHandler {

    /**
     * 处理所有自定义异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(CustomException.class)
    public ResultJson handleCustomException(CustomException e) {
        if (e.getResultJson().getCode() != ResultCode.FORBIDDEN.code()) {
            log.error(e.getResultJson().getCode() + "" + e.getResultJson().getMsg());
        }
        return e.getResultJson();
    }

    /**
     * 处理参数校验异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultJson handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
//        log.error(e.getBindingResult().getFieldError().getField() + e.getBindingResult().getFieldError().getDefaultMessage());
        return ResultJson.failure(ResultCode.BAD_REQUEST).msg("invalidparam: " + e.getBindingResult().getFieldError().getField()
                + e.getBindingResult().getFieldError().getDefaultMessage());
    }

    @ExceptionHandler(Exception.class)
    public ResultJson handleDefaultException(Exception e) {
//        log.error("ERROR:>>{} {} {}", e.getMessage(), e.getStackTrace()[0].getFileName(), e.getStackTrace()[0].getLineNumber());
//        e.printStackTrace();
        String strexinfo = LoggerUtil.getExceptionDesc(e, true);  //ExceptionUtil.stacktraceToString(e);
        String trackifnfo = StrUtil.EMPTY;
        if (e instanceof DefinedException) {
            DefinedException df = (DefinedException) e;
            if (df.getTrackid() != null) {
                trackifnfo = df.getTrackid();
            }
        }
        String errMsg = "";
        boolean lsendrobot = true;
        if (e instanceof DefinedException) {
            DefinedException df = (DefinedException) e;
            if (df.getErrorcode() > 100 && df.getErrorcode() < 200) { //校验表单的异常就.不发到机器人了
                errMsg = df.getMessage();
                lsendrobot = false;
                return ResultJson.failure(ResultCode.SERVER_ERROR).msg(errMsg);
            }
        }
        if (lsendrobot) {
            log.error("ERRORDETAIL:{}>>{}", trackifnfo, strexinfo);
        }
        //机器人报错通知
        return ResultJson.failure(ResultCode.SERVER_ERROR).msg(StrUtil.isBlank(e.getMessage()) ? ResultCode.SERVER_ERROR.getMsg() : "服务器开小差啦，请稍后再试");
    }

    @ExceptionHandler(NotLoginException.class)
    public ResultJson handleNotLoginException(NotLoginException e, HttpServletRequest request, HttpServletResponse response) {
        //log.error("未登录申请访问:{}", request.getRequestURI());
//        System.out.println("未登录尝试访问----->" + request.getRequestURI());
        return ResultJson.failure(ResultCode.UNAUTHORIZED).msg(e.getMessage());
    }

    @ExceptionHandler(NotRoleException.class)
    public ResultJson handleNoAccessExceptionException(NotRoleException e, HttpServletRequest request, HttpServletResponse response) {
        return ResultJson.failure(ResultCode.FORBIDDEN);//403 禁止访问
    }

}
