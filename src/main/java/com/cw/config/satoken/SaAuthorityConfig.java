package com.cw.config.satoken;

import cn.dev33.satoken.stp.StpInterface;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.OpRoleRightCache;
import com.cw.cache.impl.UserCache;
import com.cw.entity.Op_user;
import com.cw.utils.SystemUtil;
import com.cw.utils.rights.JwtUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * 权限框架获取角色权限,角色配置
 * 暂时没有用.权限校验用的是自己写的拦截器
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/5 22:45
 **/
@Configuration
@ConditionalOnExpression("false") //暂时不用
public class SaAuthorityConfig implements StpInterface {

    /**
     * 返回 loginId 用户包含的权限代码集合
     *
     * @param loginId
     * @param loginType
     * @return
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        String userid = JwtUtils.getUserIdFromSaLoginId(loginId);
        String projectid = JwtUtils.getProjectIdFromSaLoginId(loginId);
        UserCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.USER);
        Op_user user = cache.getRecord(projectid, userid);
        OpRoleRightCache rightCache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.OP_ROLE_RIGHT);

        List<String> myrights = rightCache.getRolesRight(user.getRoleid(), projectid);
//        System.out.println(userid + "right" + JSON.toJSONString(myrights));


        return myrights;
    }

    /**
     * 返回loginid 所拥有的角色标识集合
     *
     * @param loginId
     * @param loginType
     * @return
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        String userid = JwtUtils.getUserIdFromSaLoginId(loginId);
        String projectid = JwtUtils.getProjectIdFromSaLoginId(loginId);
        UserCache cache = GlobalCache.getDataStructure().getCache(SystemUtil.GlobalDataType.USER);
        Op_user user = cache.getRecord(projectid, userid);
        return Arrays.asList(user.getRoleid().split(","));
    }


}
