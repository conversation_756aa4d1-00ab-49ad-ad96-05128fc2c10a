package com.cw.config.satoken;

import cn.dev33.satoken.fun.SaFunction;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpLogic;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/10/3 20:11
 **/
public class WebAppStpUtil {
    public static final String TYPE = "webapp";
    public static StpLogic stpLogic = new StpLogic(TYPE);

    public WebAppStpUtil() {
    }

    public static String getLoginType() {
        return stpLogic.getLoginType();
    }

    public static String getTokenName() {
        return stpLogic.getTokenName();
    }

    public static void setTokenValue(String tokenValue, int cookieTimeout) {
        stpLogic.setTokenValue(tokenValue, cookieTimeout);
    }

    public static String getTokenValue() {
        return stpLogic.getTokenValue();
    }

    public static SaTokenInfo getTokenInfo() {
        return stpLogic.getTokenInfo();
    }

    public static void login(Object id) {
        stpLogic.login(id);
    }

    public static void login(Object id, String device) {
        stpLogic.login(id, device);
    }

    public static void login(Object id, boolean isLastingCookie) {
        stpLogic.login(id, isLastingCookie);
    }

    public static void login(Object id, SaLoginModel loginModel) {
        stpLogic.login(id, loginModel);
    }

    public static void logout() {
        stpLogic.logout();
    }

    public static void logoutByTokenValue(String tokenValue) {
        stpLogic.logoutByTokenValue(tokenValue);
    }

    public static void logoutByLoginId(Object loginId) {
        stpLogic.logoutByLoginId(loginId);
    }

    public static void logoutByLoginId(Object loginId, String device) {
        stpLogic.logoutByLoginId(loginId, device);
    }

    public static boolean isLogin() {
        return stpLogic.isLogin();
    }

    public static void checkLogin() {
        stpLogic.checkLogin();
    }

    public static Object getLoginId() {
        return stpLogic.getLoginId();
    }

    /**
     * @deprecated
     */
    @Deprecated
    public static void setLoginId(Object loginId) {
        stpLogic.login(loginId);
    }

    public static <T> T getLoginId(T defaultValue) {
        return stpLogic.getLoginId(defaultValue);
    }

    public static Object getLoginIdDefaultNull() {
        return stpLogic.getLoginIdDefaultNull();
    }

    public static String getLoginIdAsString() {
        return stpLogic.getLoginIdAsString();
    }

    public static int getLoginIdAsInt() {
        return stpLogic.getLoginIdAsInt();
    }

    public static long getLoginIdAsLong() {
        return stpLogic.getLoginIdAsLong();
    }

    public static Object getLoginIdByToken(String tokenValue) {
        return stpLogic.getLoginIdByToken(tokenValue);
    }

    public static SaSession getSessionByLoginId(Object loginId, boolean isCreate) {
        return stpLogic.getSessionByLoginId(loginId, isCreate);
    }

    public static SaSession getSessionBySessionId(String sessionId) {
        return stpLogic.getSessionBySessionId(sessionId);
    }

    public static SaSession getSessionByLoginId(Object loginId) {
        return stpLogic.getSessionByLoginId(loginId);
    }

    public static SaSession getSession(boolean isCreate) {
        return stpLogic.getSession(isCreate);
    }

    public static SaSession getSession() {
        return stpLogic.getSession();
    }

    public static SaSession getTokenSessionByToken(String tokenValue) {
        return stpLogic.getTokenSessionByToken(tokenValue);
    }

    public static SaSession getTokenSession() {
        return stpLogic.getTokenSession();
    }

    public static void checkActivityTimeout() {
        stpLogic.checkActivityTimeout();
    }

    public static void updateLastActivityToNow() {
        stpLogic.updateLastActivityToNow();
    }

    public static long getTokenTimeout() {
        return stpLogic.getTokenTimeout();
    }

    public static long getSessionTimeout() {
        return stpLogic.getSessionTimeout();
    }

    public static long getTokenSessionTimeout() {
        return stpLogic.getTokenSessionTimeout();
    }

    public static long getTokenActivityTimeout() {
        return stpLogic.getTokenActivityTimeout();
    }

    public static boolean hasRole(Object loginId, String role) {
        return stpLogic.hasRole(loginId, role);
    }

    public static boolean hasRole(String role) {
        return stpLogic.hasRole(role);
    }

    public static void checkRole(String role) {
        stpLogic.checkRole(role);
    }

    public static void checkRoleAnd(String... roleArray) {
        stpLogic.checkRoleAnd(roleArray);
    }

    public static void checkRoleOr(String... roleArray) {
        stpLogic.checkRoleOr(roleArray);
    }

    public static boolean hasPermission(Object loginId, String permission) {
        return stpLogic.hasPermission(loginId, permission);
    }

    public static boolean hasPermission(String permission) {
        return stpLogic.hasPermission(permission);
    }

    public static void checkPermission(String permission) {
        stpLogic.checkPermission(permission);
    }

    public static void checkPermissionAnd(String... permissionArray) {
        stpLogic.checkPermissionAnd(permissionArray);
    }

    public static void checkPermissionOr(String... permissionArray) {
        stpLogic.checkPermissionOr(permissionArray);
    }

    public static String getTokenValueByLoginId(Object loginId) {
        return stpLogic.getTokenValueByLoginId(loginId);
    }

    public static String getTokenValueByLoginId(Object loginId, String device) {
        return stpLogic.getTokenValueByLoginId(loginId, device);
    }

    public static List<String> getTokenValueListByLoginId(Object loginId) {
        return stpLogic.getTokenValueListByLoginId(loginId);
    }

    public static List<String> getTokenValueListByLoginId(Object loginId, String device) {
        return stpLogic.getTokenValueListByLoginId(loginId, device);
    }

    public static String getLoginDevice() {
        return stpLogic.getLoginDevice();
    }

    public static List<String> searchTokenValue(String keyword, int start, int size) {
        return stpLogic.searchTokenValue(keyword, start, size);
    }

    public static List<String> searchSessionId(String keyword, int start, int size) {
        return stpLogic.searchSessionId(keyword, start, size);
    }

    public static List<String> searchTokenSessionId(String keyword, int start, int size) {
        return stpLogic.searchTokenSessionId(keyword, start, size);
    }

    public static void disable(Object loginId, long disableTime) {
        stpLogic.disable(loginId, disableTime);
    }

    public static boolean isDisable(Object loginId) {
        return stpLogic.isDisable(loginId);
    }

    public static long getDisableTime(Object loginId) {
        return stpLogic.getDisableTime(loginId);
    }

    public static void untieDisable(Object loginId) {
        stpLogic.untieDisable(loginId);
    }

    public static void switchTo(Object loginId) {
        stpLogic.switchTo(loginId);
    }

    public static void endSwitch() {
        stpLogic.endSwitch();
    }

    public static boolean isSwitch() {
        return stpLogic.isSwitch();
    }

    public static void switchTo(Object loginId, SaFunction function) {
        stpLogic.switchTo(loginId, function);
    }

    public static void openSafe(long safeTime) {
        stpLogic.openSafe(safeTime);
    }

    public static boolean isSafe() {
        return stpLogic.isSafe();
    }

    public static void checkSafe() {
        stpLogic.checkSafe();
    }

    public static long getSafeTime() {
        return stpLogic.getSafeTime();
    }

    public static void closeSafe() {
        stpLogic.closeSafe();
    }

    /**
     * @deprecated
     */
    @Deprecated
    public static String getLoginKey() {
        return stpLogic.getLoginType();
    }

    /**
     * @deprecated
     */
    @Deprecated
    public static void setLoginId(Object loginId, String device) {
        stpLogic.login(loginId, device);
    }

    /**
     * @deprecated
     */
    @Deprecated
    public static void setLoginId(Object loginId, boolean isLastingCookie) {
        stpLogic.login(loginId, isLastingCookie);
    }

    /**
     * @deprecated
     */
    @Deprecated
    public static void setLoginId(Object loginId, SaLoginModel loginModel) {
        stpLogic.login(loginId, loginModel);
    }

}
