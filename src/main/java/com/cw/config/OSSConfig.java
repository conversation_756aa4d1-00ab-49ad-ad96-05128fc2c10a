package com.cw.config;

import com.cw.config.exception.CustomException;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/9/10 0010
 */
@Data
@Configuration
public class OSSConfig {
    @Value("${oss.endpoint.outside}") //外网地址
    private String endpoint;
    @Value("${oss.endpoint.region}")
    private String region;
    @Value("${oss.bucketName}")
    private String bucketName;
    @Value("${oss.accessKeyId}")
    private String accessKeyId;
    @Value("${oss.accessKeySecret}")
    private String AccessKeySecret;
    @Value("${oss.expiration}")
    private Integer expiration;
    @Value("${oss.url}")
    private String url;
    @Value("${oss.type}")
    private String type;

    /**
     * 拼接OSS网外地址https://bucketName.{endpoint.outside}/fileName
     * 地址为 https://bucketName.oss-cn-hangzhou.aliyuncs.com/fileName
     *
     * @param fileName OSS文件名
     * @return 文件地址
     */
    public String getFileUrl(String fileName) {
        StringBuffer stringBuffer = new StringBuffer();
        String[] s = endpoint.split("https://");
        stringBuffer.append("https://");
        stringBuffer.append(bucketName);
        stringBuffer.append(".");
        stringBuffer.append(s[1]);
        stringBuffer.append("/");
        stringBuffer.append(fileName);
        return stringBuffer.toString();
    }

    /**
     * 自定义域名拼接文件名
     *
     * @param fileName
     * @return url/fileName
     */
    public String getCustomerFileUrl(String fileName) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(url);
        stringBuffer.append("/");
        stringBuffer.append(fileName);
        return stringBuffer.toString();
    }


    /**
     * 检验文件地址是否为正确的OSS地址，返回文件名
     *
     * @param fileUrl
     * @return 替换OSS外网域名 获得文件名
     */
    public String getOssNameUnlessPrefix(String fileUrl) {
        String outsideUrl = getOutsideUrl();
        String customerUrl = getCustomerUrl();
        if (!fileUrl.contains(outsideUrl) && !fileUrl.contains(customerUrl)) {
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("请输入正确的OSS文件地址"));
        } else if (fileUrl.contains(outsideUrl)) {
            //替换OSS域名 获得文件名
            fileUrl = fileUrl.replace(outsideUrl, "");
        } else if (fileUrl.contains(customerUrl)) {
            fileUrl = fileUrl.replace(customerUrl, "");
        }

        return fileUrl;
    }

    /**
     * @return 获取外网OSS地址
     */
    private String getOutsideUrl() {
        StringBuffer outsideStrBuff = new StringBuffer();
        String[] s = endpoint.split("https://");
        outsideStrBuff.append("https://");
        outsideStrBuff.append(bucketName);
        outsideStrBuff.append(".");
        outsideStrBuff.append(endpoint.contains("https://")? s[1]: endpoint);//电信OSS不包含https
        outsideStrBuff.append("/");
        return outsideStrBuff.toString();
    }

    /**
     * 获取自定义域名地址
     *
     * @return
     */
    private String getCustomerUrl() {
        StringBuffer customerStr = new StringBuffer();
        customerStr.append(url);
        customerStr.append("/");
        return customerStr.toString();
    }


}
