package com.cw.config;

import cn.dev33.satoken.interceptor.SaRouteInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.cw.config.satoken.OpenApiStpUtil;
import com.cw.config.satoken.WebAppStpUtil;
import com.cw.inject.log.SlowQueryRequestInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 跨域访问配置文件
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2019-06-20 16:46
 **/
@Configuration
public class HttpConfig implements WebMvcConfigurer {

    SlowQueryRequestInterceptor slowQueryRequestInterceptor;

    public HttpConfig(@Autowired SlowQueryRequestInterceptor slowQueryRequestInterceptor) {
        this.slowQueryRequestInterceptor = slowQueryRequestInterceptor;
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
//                .allowedMethods("*")
                .allowedMethods("POST", "GET", "PUT", "OPTIONS", "DELETE", "HEAD")
                .maxAge(3600)
                .exposedHeaders("access-control-allow-headers",
                        "access-control-allow-methods",
                        "access-control-allow-origin",
                        "access-control-max-age")
                .allowedHeaders("*")
                .allowCredentials(true);

    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(getDefinedRouterInterceptor()).addPathPatterns("/**");

        registry.addInterceptor(slowQueryRequestInterceptor).addPathPatterns("/api/**", "/report/**", "/webapi/**");//记录一下慢查询
    }

    /**
     * 要做登陆拦截校验的路径
     *
     * @return
     */
    private SaRouteInterceptor getDefinedRouterInterceptor() {
        return new SaRouteInterceptor((req, res, handler) -> {

            //第一个参数是要拦截的通配路径,第二个参数是排除的白名单路径
            SaRouter.match(Arrays.asList("/webapi/**"),
                    Arrays.asList("/webapi/user/**", "/webapi/resource/**", "/webapi/content/**", "/webapi/wxop/**", "/mp/**",
                            "/webapi/pub/**","/webapi/userAction/public/upload_oss_open"), WebAppStpUtil::checkLogin);

            //SaRouter.match("/api/**", "/api/users/login", StpUtil::checkLogin);

            SaRouter.match(Arrays.asList("/api/**"),
                    Arrays.asList("/api/sysconf/loadconf", "/api/users/login"), StpUtil::checkLogin);

            SaRouter.match(Arrays.asList("/v1/openapi/**"),
                    Arrays.asList("/v1/openapi/getToken"), OpenApiStpUtil::checkLogin);


        });
    }


    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        JSON.DEFFAULT_DATE_FORMAT = "yyyy-MM-dd";
        FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setDateFormat("yyyy-MM-dd");
        fastJsonConfig.setSerializerFeatures(SerializerFeature.WriteDateUseDateFormat);
        //3处理中文乱码问题
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        //4.在convert中添加配置信息.
        fastJsonHttpMessageConverter.setSupportedMediaTypes(fastMediaTypes);
        fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
//        HttpMessageConverter<?> converter = fastJsonHttpMessageConverter;

        //fastJsonHttpmessageConverter必须放在StringHttpMessageConverter之后 不然转出来的json会是字符串
        StringHttpMessageConverter stringHttpMessageConverter = null;
        for(HttpMessageConverter converter: converters){
            if(converter instanceof StringHttpMessageConverter){
                stringHttpMessageConverter = (StringHttpMessageConverter)converter;
                break;
            }
        }
        if(stringHttpMessageConverter==null){
            stringHttpMessageConverter = new StringHttpMessageConverter(Charset.forName("UTF-8"));
        }
        stringHttpMessageConverter.setSupportedMediaTypes(Arrays.asList(MediaType.TEXT_PLAIN, MediaType.APPLICATION_JSON_UTF8));
        converters.set(0, stringHttpMessageConverter);
        converters.set(1, fastJsonHttpMessageConverter);
//        converters.add(0,fastJsonHttpMessageConverter); //优先使用fastjson做相应跟输出

    }


    /**
     * @param converters
     */
    @Override  //响应统一 UTF-8
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        for (HttpMessageConverter<?> converter : converters) {
            if (converter instanceof StringHttpMessageConverter) {
                //覆盖默认设置
                ((StringHttpMessageConverter) converter).setDefaultCharset(Charset.forName("UTF-8"));
            }
        }
    }




}
