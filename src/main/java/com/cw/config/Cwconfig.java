package com.cw.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/12/3 17:23
 **/
@Component
@Data
@RefreshScope
@ConfigurationProperties(prefix = "cwconfig", ignoreInvalidFields = false)
public class Cwconfig {

    Boolean checksign = false;  //开启接口校验模式
    Boolean ticketmode = true;
    String domain = ""; // 回调通知地址 https://wxapp.citybaytech.com  #环境域名 对应生产环境.测试环境的地址前缀
    String pmsdomain ="";


}
