package com.cw.config.confyaml;

import com.cw.config.confyaml.node.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/4/24 17:36
 **/
@Data
public class ConfYaml {
    private Conf_Sysnode sys = new Conf_Sysnode();
    private Conf_Pcnode pc = new Conf_Pcnode();
    private Conf_Appnode app = new Conf_Appnode();
    private Conf_Invoicenode invoice = new Conf_Invoicenode();
    private Conf_Consolenode console = new Conf_Consolenode();
    private Conf_Map map = new Conf_Map();


   /* public static Integer SYSLEVEL=1;
    public static Integer PCLEVEL=2;
    public static Integer APPLEVEL=3;
    public static Integer INVOICELEVEL=4;
    public static Integer CONSOLELEVEL =5;


    public  <T> T getConfYaml(Integer level){
        switch (level){
            case 1:
               return (T) sys;
            case 2:
                return (T) pc;
            case 3:
                return (T) app;
            case 4:
                return (T) invoice;
            case 5:
                return (T) console;
        }
        return null;
    }*/

}
