package com.cw.config.confyaml;

import com.cw.config.confyaml.node.Conf_DsParking;
import com.cw.config.confyaml.node.Conf_Gsms;
import com.cw.config.confyaml.node.Conf_Xms;
import lombok.Data;

/**
 * @Describe vendor 配置文件
 * <AUTHOR> <PERSON>
 * @Create on 2024-11-13
 */
@Data
public class VendorYaml {
    private Conf_Xms xms = new Conf_Xms();

    private Conf_Gsms gsms = new Conf_Gsms();

    private Conf_DsParking parking = new Conf_DsParking();
}
