package com.cw.config.confyaml.node;

import com.cw.entity.Sysconf;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/4/24 17:37
 **/
@Data
public class Conf_Sysnode extends AbstractConfNode {
    private Integer autocancel = 30;/* 自动取消订单时间 */
    private Integer autoreceive = 7;/* 自动确认收货时间 */
    private String ratecode = "";/*  默认售卖价格码 */
    private String address = "";/* 景区地址 */
    private String tel = "";/* 景区联系电话 */
    private String longitude = "";/* 景区入口经度 */
    private String latitude = "";/* 景区入口纬度 */
    private String pab_pid = "";/* 线下支付订单目标ID */
    private String pab_prefix = "";/* 线下支付订单前缀 */
    private Integer show_alerttime = 5;/* 演出通知提前时间 默认提前5分钟通知*/
    private String parkingsys_type = "";/* 停车系统类型 */
    private boolean lcheckmaprange = false;//是否限定范围内才能预订
    private boolean lcheckactcross = false;//是否检查活动交叉


    @Override
    public void fillConfInfo(Sysconf sysconf) {
        this.autocancel = sysconf.getAutocancel();
        this.ratecode = sysconf.getRatecode();
        this.address = sysconf.getAddress();
        this.tel = sysconf.getTel();
        this.longitude = sysconf.getLongitude();
        this.latitude = sysconf.getLatitude();
    }


}
