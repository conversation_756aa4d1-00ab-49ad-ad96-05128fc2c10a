package com.cw.config.confyaml.node;

import com.cw.entity.Sysconf;
import lombok.Data;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-08-03
 */
@Data
public class Conf_Map extends AbstractConfNode {

    private String lefttop;//左上角坐标   106.899783, 29.483928
    private String rightbottom;//右下角坐标  107.436079, 28.784006
    private String key;//地图key  高德地图KEY
    private String secret;//地图secret 高德地图secret
    private String style;//地图风格 3b99270e138492212da0239dffd1a606
    private String bgpath;//地图瓦片图前缀地址
    private String h5webviewUrl;//H5URL   https://wap.citybaytech.com/map/map
    private String zoomrange;//地图缩放范围  8,13
    private String zoom;//地图缩放级别 8
    private String title;//地图标题  178旅游地图

    @Override
    public void fillConfInfo(Sysconf sysconf) {

    }
}
