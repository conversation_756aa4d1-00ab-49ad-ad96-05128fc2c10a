package com.cw.config.confyaml.node;

import com.cw.entity.Sysconf;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/4/24 17:54
 **/
@Data
public class Conf_Consolenode extends AbstractConfNode {
    private String icon;/* 后台网站图标,BASE64 */
    private String loginpicurl = "";/* 后台登录背景图 url  */
    private String logo = "";  //后台企业 LOGO 图片地址
    private String title = "";  //后台企业 系统主标题
    private String subtitle = "";  //后台企业 系统副标题
    private String dymaticcode = ""; //动态领券二维码

    @Override
    public void fillConfInfo(Sysconf sysconf) {
        this.setIcon(sysconf.getWebicon());
        this.setLoginpicurl(sysconf.getLoginpicurl());
        this.setLogo(sysconf.getWeblogo());
        this.setTitle(sysconf.getTitle());
        this.setSubtitle(sysconf.getSubtitle());
    }
}
