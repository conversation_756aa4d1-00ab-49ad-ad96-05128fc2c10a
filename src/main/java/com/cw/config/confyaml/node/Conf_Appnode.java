package com.cw.config.confyaml.node;

import com.cw.entity.Sysconf;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/4/24 17:37
 **/
@Data
public class Conf_Appnode extends AbstractConfNode {
    private String loginpicurl = "";/* 小程序登录封面背景图 */
    private String personalurl = "";/* 小程序个人中心背景图 */
    private String color = "";/* 移动端主题颜色 */
    private String bgcolor = "";/* 移动端背景颜色 */
    private String textcolor = "";/* 移动端框外文本颜色 */
    private String sharepicurl = "";/* 移动端分享图片 url  */
    private String appname = "";//微信小程序名称
    private int apptype = 1; //小程序类型 1:旅游小程序 2:全域旅游小程序
    private int regmode = 1;//注册模式 1:小程序手机号注册 2:使用昵称+头像授权注册
    private int enlang = 0;//是否开启多语言 0:关闭 1:开启
    private int btnnum = 5; //小程序按钮区按钮数量
    private String startpage_picurl;//小程序开屏图片
    private String startpage_videourl;//小程序开屏视频
    private boolean showlang = false;
    private boolean showroomprice = true;
    private Integer[] funs = new Integer[]{}; //功能开关   [1,2,3,4]
    private Integer[] self_funs = new Integer[]{}; //自助服务功能开关   [1,2,3,4]
    private Integer funlayout = 0;// 个人中心功能开关 0:竖向布局 1: 横向布局
    private String sphid = "";

    @Override
    public void fillConfInfo(Sysconf sysconf) {//修复并填充到yaml
        this.loginpicurl = sysconf.getLoginpicurl();
        this.personalurl = sysconf.getApppersonalurl();
        this.color = sysconf.getAppcolor();
        this.bgcolor = sysconf.getAppgbcolor();
        this.textcolor = sysconf.getApptextcolor();
        this.sharepicurl = sysconf.getAppsharepicurl();
    }
}
