package com.cw.config.confyaml.node;

import com.cw.outsys.pojo.xms.PayMapping;
import com.cw.outsys.pojo.xms.RateMapping;
import lombok.Data;

import java.util.List;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024-11-13
 */
@Data
public class Conf_Xms {
    private String blockcode;
    private String ratecode;
    private String source;
    private String market;
    private String channel;
    private Integer rstype;

    private List<PayMapping> paymapping;

    private List<RateMapping> rpmapping;

}
