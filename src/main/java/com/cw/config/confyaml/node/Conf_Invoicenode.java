package com.cw.config.confyaml.node;

import com.cw.entity.Sysconf;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/4/24 17:37
 **/
@Data
public class Conf_Invoicenode extends AbstractConfNode {
    String auditor;/* 审核人 */
    @ApiModelProperty(value = "支持开票的订单类型,下拉框ptype", required = true)
    String types = "";  //支持开票的订单类型  R,T,C

    @ApiModelProperty(value = "限制红冲重开次数", required = true)
    Integer limit;

    @ApiModelProperty(value = "诺诺发票升级固定值token", required = true)
    String token;

    @ApiModelProperty(value = "诺诺发票升级 分机号", required = true)
    String extensionNumber;

    @ApiModelProperty(value = "销方名称", required = true)
    String salerName;
    @ApiModelProperty(value = "销方税号", required = true)
    String salerTaxNum;
    @ApiModelProperty(value = "销方电话", required = true)
    String salerTel;
    @ApiModelProperty(value = "销方地址", required = true)
    String salerAddress;
    @ApiModelProperty(value = "销方银行账号和开户行地址")
    String salerAccount;

    @ApiModelProperty(value = "开票员", required = true)
    String clerk;
    @ApiModelProperty(value = "收款人", required = true)
    String payee;
    @ApiModelProperty(value = "复核人", required = true)
    String checker;

    @ApiModelProperty(value = "店铺号，百望云参数", required = true)
    String shopNo;
    @ApiModelProperty(value = "收银台号，百望云参数", required = true)
    String cashNo;
    @ApiModelProperty(value = "允许红冲次数", required = true)
    Integer lred;
    @ApiModelProperty(value = "发票明细税率配置")
    private List<TaxDetailBean> taxDetail;

    @Data
    public static class TaxDetailBean implements Serializable {
        @ApiModelProperty(value = "支持开票的订单类型，下拉框ptype", example = "T", required = true)
        private String type;
        @ApiModelProperty(value = "税率", example = "0.06", required = true)
        private String taxrate;
        @ApiModelProperty(value = "商品编码（商品税收分类编码开发者自行填写）")
        private String goodsCode;
        @ApiModelProperty(value = "自行编码（可不填）")
        private String selfCode;
        @ApiModelProperty(value = "单价含税标记，0-不含税，1-含税", example = "0")
        private String withTaxFlag;
    }

    @Override
    public void fillConfInfo(Sysconf sysconf) {

    }
}
