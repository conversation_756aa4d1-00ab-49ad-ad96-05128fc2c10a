package com.cw.config.confyaml.node;

import com.cw.entity.Sysconf;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2023/4/24 17:37
 **/
@Data
public class Conf_Pcnode extends AbstractConfNode {
    private String loginurl = "";/* 官网登录背景图 */
    private String color = "";/* 官网主题颜色 */
    private String bgcolor = "";/* 官网背景颜色 */
    private String btextcolor = "";/* 官网框外文本颜色 */
    private String title = "";/* 官网网站登录欢迎语 */
    private String logo = "";/* 官网网站logo图片地址 */
    private String icon = "";/* 官网网站页签icon图片地址 */
    private String copyright = null;/* 版权所有 */


    @Override
    public void fillConfInfo(Sysconf sysconf) {
        this.setLoginurl(sysconf.getWebloginurl());
        this.setColor(sysconf.getWebcolor());
        this.setBgcolor(sysconf.getWebgbcolor());
        this.setBtextcolor(sysconf.getWebtextcolor());
        this.setTitle(sysconf.getWebtitle());
        this.setLogo(sysconf.getPclogo());
        this.setIcon(sysconf.getPcicon());
    }
}
