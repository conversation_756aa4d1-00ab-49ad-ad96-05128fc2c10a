package com.cw.outsys.stdop.common;

import com.cw.outsys.pojo.zjplatform.common.OtaOrder_guestNode;
import com.cw.pojo.dto.app.req.node.OrderGuestInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by flyhigh on 2016/8/1.
 */
@Data
public class StdOrder_ticketNode {
    String ticketcode = "";
    String outticketcode = "";
    Integer num = 0;
    BigDecimal amount = BigDecimal.ZERO;
    Date usedate = null;//
    OtaOrder_guestNode guestinfo = null;
    List<OrderGuestInfo> idinfos = null;  //实名制信息
    BigDecimal price = BigDecimal.ZERO;//单价
    String regno = "";
}
