package com.cw.outsys.stdop.request;


import com.alibaba.fastjson.annotation.JSONField;
import com.cw.core.func.order.StdOrderData;
import com.cw.outsys.base.SysStdRequest;
import com.cw.outsys.stdop.common.StdOrder_crmNode;
import com.cw.outsys.stdop.common.StdOrder_discountNode;
import com.cw.outsys.stdop.common.StdOrder_guestNode;
import com.cw.outsys.stdop.common.StdOrder_ordersNode;
import com.cw.outsys.stdop.response.StdOrderResponse;
import com.cw.utils.enums.ProdType;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/5/23 13:48
 **/
@Data
public class StdOrderRequest extends SysStdRequest<StdOrderResponse> {
    @NotBlank
    String otaorderid = "";
    @NotBlank
    String sceniccode = "";

    String packagecode;
    Integer packagenum;

    ProdType prodType;

    StdOrder_guestNode guestinfo = null;
    StdOrder_ordersNode orderlist = null;
    StdOrder_crmNode crminfo = null;
    List<StdOrder_discountNode> discounts = null;
    BigDecimal totalamount = BigDecimal.ZERO;
    BigDecimal payment = BigDecimal.ZERO;//已付款金额
    String remark;//客人备注
    String memo;//折扣及发票信息备注
    String paytype;//支付方式
    String ratecode;//价格代码..方便以后对接时能对应多套
    String actcode;//活动价格代码  如果传了这个.优先级比价格代码更高..对套餐无效
    String paymenttype;//付款方式
    Boolean ratevisible = false;//是否房价保密

    @JSONField(serialize = false)
    StdOrderData orderDataContext; //注入上下文相关数据 有的取消接口需要获取到订单记录对象

}
