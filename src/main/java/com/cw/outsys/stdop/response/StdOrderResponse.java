package com.cw.outsys.stdop.response;

import com.cw.core.orderhandler.pojo.StdIdResult;
import com.cw.outsys.base.SysStdResponse;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/5/24 16:39
 **/
@Data
public class StdOrderResponse extends SysStdResponse {
    String outid;//外部系统订单号
    String bookingid; //本地订单号
    BigDecimal totalamount;
    String qrcodeurl;
    BigDecimal actualamount;//实际支付金额
    String pmsorderid;//酒店确认号
    String tcheckNo; //门票辅助码
    StdIdResult stdIdResult;  //子订单的其他订单号

}
