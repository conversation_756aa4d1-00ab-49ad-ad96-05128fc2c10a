package com.cw.outsys.stdop.response;

import com.cw.outsys.base.SysStdResponse;
import com.cw.outsys.stdop.common.array.StdTicketSubOrderNode;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/5/24 17:02
 **/
@Data
public class StdTicketQueryResponse extends SysStdResponse {
    Integer quantity;
    String name;
    String mobile;
    String colno;
    String assistCheckNo;
    String status;//TODO  最后转一个定义好的

    String qrcodeurl;//第三方票务的二维码链接地址
    List<StdTicketSubOrderNode> orders;
}
