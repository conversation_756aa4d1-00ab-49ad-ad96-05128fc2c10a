package com.cw.outsys.pojo.mtticket.request;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cw.entity.Booking_rs;
import com.cw.entity.Ticket_rs;
import com.cw.entity.Vendorconfig;
import com.cw.outsys.api.BaseOutSysRequest;
import com.cw.outsys.api.OutSysApi;
import com.cw.outsys.pojo.mtticket.pojo.request.MTOrderCancelReqData;
import com.cw.outsys.pojo.mtticket.response.MTOrderCancelResponse;
import com.cw.outsys.stdop.request.StdCancelOrderRequest;
import com.cw.pojo.dto.app.req.node.OrderGuestInfo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.ArrayList;
import java.util.List;

@Data
@OutSysApi(
        methodName = "CancelOrder", description = "微票退款接口", reqMethod = HttpMethod.POST)
public class MTOrderCancelRequest extends BaseOutSysRequest<MTOrderCancelResponse, StdCancelOrderRequest> {


    @Override
    public MTOrderCancelRequest setVendor(Vendorconfig vendorconfig) {
        return (MTOrderCancelRequest) super.setVendor(vendorconfig);
    }

    @Override
    public MTOrderCancelRequest transfer(StdCancelOrderRequest cancelRequest) {
        //填充对象
        MTOrderCancelReqData requestData = new MTOrderCancelReqData();
        requestData.setOrderSerialId(cancelRequest.getOtaorderid());

        Booking_rs rs = cancelRequest.getOrderDataContext().getBookingRs();
        if (StringUtils.isNotBlank(cancelRequest.getRefundreason())) {
            requestData.setReason(cancelRequest.getRefundreason());
        }
        if (rs != null) {
            requestData.setPartnerOrderId(rs.getOutid());//平台预定号
            requestData.setTickets(rs.getAnz());//票数
        }
        List<String> codes = new ArrayList<>();
        List<Ticket_rs> rsList = cancelRequest.getOrderDataContext().getTickets();
        for (Ticket_rs node : rsList) {
            if (StringUtils.isNotBlank(node.getIdinfo())) {
                List<OrderGuestInfo> idinfos = JSON.parseArray(node.getIdinfo(), OrderGuestInfo.class);
                for (OrderGuestInfo idinfo : idinfos) {
                    if (StringUtils.isNotBlank(idinfo.getIdno())) {
                        codes.add(idinfo.getIdno());//身份证号保存
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(codes)) {
            //如果身份证和门票数量对的上 传身份证，一证多买的问题则不传
            if (codes.size() == requestData.getTickets()) {
                requestData.setCodes(codes.toArray(new String[0]));
            }
        }

        this.setData(JSON.toJSONString(requestData));
        return this;
    }

    @Override
    public Class<MTOrderCancelResponse> getResponseClass() {
        return MTOrderCancelResponse.class;
    }


}
