package com.cw.outsys.client.impl.crm;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.TypeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cw.exception.DefinedException;
import com.cw.outsys.api.OutSysRequest;
import com.cw.outsys.api.OutSysResponse;
import com.cw.outsys.client.BaseSysClient;
import com.cw.outsys.pojo.crm.zhijian.ZhijianResultDataJson;
import com.cw.outsys.pojo.zjplatform.ZjErrorData;
import com.cw.outsys.pojo.zjplatform.ZjResult;
import com.cw.pojo.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.http.*;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/20 21:16
 **/
@Slf4j
public class ZhijianCrmClient extends BaseSysClient {
//    @Autowired
//    RestTemplate restTemplate;
//
//    @Autowired
//    RedissonClient redissonClient;

    public static String zhjian_topic = "XMS_TOPIC";  //订阅更新TOKEN 事件
    long expire = 3600;
    String crmdata = "data";
    String crmcode = "success";
    String crmdesc = "message";
    String crmneedlogin = "needlogin";
    String tokenLock = "ZHIJIANCRMTOKENLOCK";
    String tokenurl = "action/apimanager/gettoken";
    String zhijian_TokenMap = "ZHIJIANCRM_TOKENMAP";
    private ConcurrentHashMap<String, String> tokenHashMap = new ConcurrentHashMap<>();//本地缓存的TOKEN 内容


    private MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");


    private String getCrmToken() throws DefinedException {
        String token = "";
        String url = config.getUrl();
        String key = config.getUserpwd();
        String secret = config.getAppsecrect();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(type);
        headers.add("accept", "*/*");

        String crmurl = url + tokenurl + "?appid=" + key + "&appsecret=" + secret;
        ResponseEntity<String> entity = restTemplate.getForEntity(crmurl, String.class);
        JSONObject crmresult = JSONObject.parseObject(entity.getBody());
        Boolean code = crmresult.getBoolean(crmcode);
        if (code) {
            token = crmresult.getString("access_token");
            getRedissonClient().getMapCache(zhijian_TokenMap).put(config.getProjectid(), token, expire, TimeUnit.SECONDS);
        }
        return token;
    }

  /*  public JSON postMsgToCrm(CrmUtil.CrmMsgTypeV2 msgType, String data) throws Exception {
        ChannelProperty appuser = ApiContextUtil.getCurrentUser();
        CrmProperties ifcProperties = crmConfig.getProperties(appuser.getSection());
        String url = ifcProperties.getV2url();
        String crmurl = "";
        String tokenInfo = getCacheToken();
        JSONObject crmresult = new JSONObject();
        if (StringUtils.isNotBlank(url)) {
            for (int i = 0; i < 2; i++) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(type);
                HttpEntity<String> formEntity = new HttpEntity<>(data, headers);
                crmurl = url + msgType.val() + "&access_token=" + tokenInfo;
                ResponseEntity entity = restTemplate.exchange(crmurl, HttpMethod.POST, formEntity, String.class);
                crmresult = JSONObject.parseObject(entity.getBody().toString());
                if (crmresult!=null) {
                    Boolean code = crmresult.getBoolean(crmcode);
                    Boolean needlogin = crmresult.getBoolean(crmneedlogin);
                    if (needlogin!=null && needlogin==true) {
                        tokenInfo = getCrmToken(); //对方服务器重启异常时, token丢失了,重新获取
                        continue;
                    }
                    if (code!=null && !code) {
                        throw new DefinedException(crmresult.getString(crmdesc));
                    } else {
                        break;
                    }
                }
            }
            return crmresult;
        } else {
            logger.error("CRM配置文件中没有指定地址");
            throw new DefinedException("CRM配置文件中没有指定地址");
        }
    }
*/

    @Override
    public <T extends OutSysResponse> T execute(OutSysRequest<T> paramOutSysRequest) throws DefinedException {
        if (!tokenHashMap.containsKey(getTokenSignal())) {
            refreshTokenAndLogin();
        }
        String url = config.getUrl() + paramOutSysRequest.getOutApiAnotion().path(); //拼接地址.得到URL

        log.info(paramOutSysRequest.getOutApiAnotion().reqMethod() + "请求url:{}", url);
        String body = JSON.toJSONString(paramOutSysRequest);
        HttpEntity<String> httpEntity = new HttpEntity<>(body, packPostHeader(body));
        String tokenInfo = tokenHashMap.get(getTokenSignal());
        url = url + "&access_token=" + tokenInfo;
        ResponseEntity<String> responseEntity = null;


        for (int i = 0; i < 2; i++) {
            responseEntity = getRestTemplate().exchange(url, paramOutSysRequest.getOutApiAnotion().reqMethod(),
                    httpEntity, String.class, paramOutSysRequest.getGetRequestParams()); //根据类的注解.执行POST或者GET请求

            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                log.info("\n-->智简CRM接口请求{} \n------>响应{}", body, responseEntity.getBody());
                String bodyStr = responseEntity.getBody();

                Type superClass = paramOutSysRequest.getClass().getGenericSuperclass();

                Type type = ((ParameterizedType) superClass).getActualTypeArguments()[0];//推断返回的类型

                ZhijianResultDataJson<T> zjResult = JSON.parseObject(bodyStr,
                        new TypeReference<ZhijianResultDataJson<T>>(TypeUtil.getClass(type)) {
                        });
                if (zjResult.getNeedlogin()) {//TOKEN 失效.再跑一次
                    excuteTokenLogin();
                    continue;
                }
                if (zjResult.getSuccess()) {
                    return zjResult.getData();
                } else {
                    ZjResult<ZjErrorData> error = JSON.parseObject(bodyStr == null ? "{}" : bodyStr,
                            new TypeReference<ZjResult<ZjErrorData>>() {
                            });
                    if (error.getData() != null) {
                        System.out.println(error.getData().getCode() + "错误原因" + error.getData().getDesc());
                        throw new DefinedException(error.getData().getDesc(),
                                ResultCode.PMSERR.code(), NumberUtil.parseInt(error.getData().getCode()));
                    } else {//防止接口返回没返回 DATA 描述错误.给个默认的
                        throw new DefinedException(ResultCode.PMSERR.getMsg(),
                                ResultCode.PMSERR.code(), ResultCode.PMSERR.code());
                    }
                }
            }
        }
        return null;
    }

    @Override
    public <T extends OutSysResponse> List<T> executeArray(OutSysRequest<T> paramOutSysRequest) throws DefinedException {
        return super.executeArray(paramOutSysRequest);
    }

    @Override
    public void refreshTokenAndLogin() throws DefinedException {
        RLock lock = getRedissonClient().getLock(tokenLock + config.getProjectid());
        try {
            boolean lok = lock.tryLock(3, 5, TimeUnit.SECONDS);//等待5秒.   //一般来说一次refresh toekn 就200ms内吧.后面如果抖动大.锁超时的时间可以减少
            if (lok) {//获取到锁后.刷新token
                excuteTokenLogin();
                lock.unlock();
            } else { //加锁失败.这个线程就不用去发起刷新了.直接从redis 缓存里拿出来用就是了

            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void excuteTokenLogin() throws DefinedException {
        String token = "";
        String url = config.getUrl();
        String key = config.getUserpwd();
        String secret = config.getAppsecrect();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(type);
        headers.add("accept", "*/*");

        String crmurl = url + tokenurl + "?appid=" + key + "&appsecret=" + secret;
        ResponseEntity<String> entity = restTemplate.getForEntity(crmurl, String.class);

        if (entity.getStatusCode() != HttpStatus.OK) {
            throw new DefinedException("智简CRM 获取TOKEN 失败");
        }

        JSONObject crmresult = JSONObject.parseObject(entity.getBody());
        Boolean code = crmresult.getBoolean(crmcode);
        if (code) {
            token = crmresult.getString("access_token");
            log.info("登陆成功,返回CRM token 凭证: " + token);

            tokenHashMap.put(getTokenSignal(), token);//先更新本地token 缓存.再广播给其他服务器更新

            getRedissonClient().getMapCache(zhijian_TokenMap).
                    put(config.getProjectid(), token, expire, TimeUnit.SECONDS);

            RTopic topic = getRedissonClient().getTopic(zhjian_topic);
            long clientsReceivedMessage = topic.publish("UPDATE TOKEN CREDENTIAL"); //发布更新通知  给其他集群应用做更新

        }


    }

    private String getTokenSignal() {
        return config.getProjectid() + config.getAppid() + config.getUrl();
    }

    @Override
    public void init() {
        RTopic topic = getRedissonClient().getTopic(zhjian_topic);
        topic.addListener(String.class, new MessageListener<String>() {
            @Override
            public void onMessage(CharSequence charSequence, String s) {
                RMap<String, String> map = getRedissonClient().getMap(zhijian_TokenMap);  //将redis map 里的值同步到本地
                tokenHashMap.putAll(map);
                log.info("收到 zhijian CRM session 更新本地token缓存通知");
            }
        });

    }

    private HttpHeaders packPostHeader(String body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }
}
