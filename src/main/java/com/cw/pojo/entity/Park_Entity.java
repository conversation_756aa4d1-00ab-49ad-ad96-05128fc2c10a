package com.cw.pojo.entity;

import com.cw.utils.annotion.PropertyMsg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2023-04-04
 */
@Data
@ApiModel(description = "园区实体对象")
public class Park_Entity {
    @ApiModelProperty(value = "公共数据对象唯一id,新建为0", example = "0", required = true)
    private Long sqlid = 0L;
    @PropertyMsg("园区代码")
    @ApiModelProperty(value = "园区代码", required = true)
    private String code = "";
    @PropertyMsg("园区名称")
    @ApiModelProperty(value = "园区名称", required = true)
    private String description = "";
    @ApiModelProperty(value = "园区坐标", required = true)
    @PropertyMsg("园区坐标")
    private String coordinate = "";
    @ApiModelProperty(value = "是否展示")
    @PropertyMsg("是否展示")
    private Boolean lshow = true;
    @ApiModelProperty(value = "园区业态类型，下拉框-mapbusinesstype,用逗号分割")
    private String businesstype;
    @ApiModelProperty(value = "排序")
    @PropertyMsg("排序")
    private Integer seq;
}
