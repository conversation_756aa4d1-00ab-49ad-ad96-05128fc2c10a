package com.cw.pojo.dto.app.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("栏目内容查询请求")
public class MenuContentQueryReq {
//    @ApiModelProperty(value = "小程序appid")
//    private String appid = "";

    @ApiModelProperty(value = "栏目 id", example = "index or 1", required = true)
    private String menuid = "";

    @ApiModelProperty(value = "包含下层栏目内容", example = "true ,false ", required = false)
    private Boolean includeSub = false;
}
