
package com.cw.pojo.dto.app.res;

import com.cw.pojo.common.spuitem.SpuPackCheckInfo;
import com.cw.pojo.dto.app.req.node.CombineInfo;
import com.cw.pojo.dto.app.req.node.OrderGuestInfo;
import com.cw.pojo.dto.app.req.node.RefundInfo;
import com.cw.pojo.dto.order.res.OrderKitItemItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019-07-13 23:17
 **/
@Data
@ApiModel(description = "订单详情")
public class AppOrderDetailResult {
    @ApiModelProperty(value = "订单号")
    String orderid = "";
    @ApiModelProperty(value = "总金额", example = "0.00")
    BigDecimal totalAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "支付截止时间unix时间戳.精确到秒")
    String expireTime = "";

    @ApiModelProperty(value = "购买数量")
    Integer num = 1;

    @ApiModelProperty(value = "产品归属的大组代码.")
    String groupCode = "";

    @ApiModelProperty(value = "产品大组描述.通常是酒店,或者票务大组描述")
    String groupDesc = "";

    @ApiModelProperty(value = "园区开放时间", example = "8:00-18:00")
    private String t_opentime = "";

    @ApiModelProperty(value = "坐标,景区坐标OR酒店坐标,逗号分割")
    private String singals = "";

    @ApiModelProperty(value = "景区联系电话")
    private String t_telno = "";

    @ApiModelProperty(value = "景区地址")
    private String t_address = "";


    @ApiModelProperty(value = "酒店地址")
    private String r_address = "";

    @ApiModelProperty(value = "酒店联系电话")
    private String r_telno = "";

    @ApiModelProperty(value = "是否为实名制身份证,默认false", example = "是否为实名制身份证")
    private boolean t_lneedcard = false;

    @ApiModelProperty(value = "购买产品描述")
    String productDesc = "";

    @ApiModelProperty(value = "购买产品特性标签描述", example = "有窗.WIFI")
    String productTagDesc = "";


    @ApiModelProperty(value = " 使用日期OR 入住时间")
    String startdate = "";

    @ApiModelProperty(value = "离店时间(针对房票等其他商品用不上)")
    String enddate = "";

    @ApiModelProperty(value = "订单评分")
    Integer point = 0;


    @ApiModelProperty(value = "预订人")
    String booker = "";

    @ApiModelProperty(value = "实名制购票人姓名,逗号分割")
    String ticketIdInfos = "";

    @ApiModelProperty(value = " 预订联系电话")
    String bookerphone = "";

    @ApiModelProperty(value = "下单时间 yyyy-MM-dd HH:mm")
    String createdatetime = "";

    @ApiModelProperty(value = "免费取消订单时间 yyyy-MM-dd HH:mm")
    String cancelfreetime = "";

    @ApiModelProperty(value = "产品图片")
    String productpic = "";

    @ApiModelProperty(value = "产品类型")
    String productType = "";

    @ApiModelProperty(value = "套餐子项是否含产品类型", notes = "R-客房，T-门票，C-餐饮,K-自定义，M-会议，I-伴手礼")
    String includeType = "";

    @ApiModelProperty(value = "订单状态 待支付C,待出行P/(伴手礼物流运输中-确认收货),待审核U/伴手礼退货处理中-联系客服,取消X，完成F")
    String status = "";

    @ApiModelProperty(value = "产品类型为I,退款信息为空显示当前收货状态信息，伴手礼收货流程，0-待备货，1-待发货，2-待收货，3-已收货")
    Integer goodsStatus;

    @ApiModelProperty(value = "产品类型为I,退款信息不为空状态显示，优先级最高,伴手礼退货流程,0-审核中，1-审核通过，2-退货接收中（显示快递信息），3-退货已接收，4-已退款，5-已取消")
    Integer refundStatus;


    @ApiModelProperty(value = "门票订单二维码.待出行状态下才有")
    String qrcode = "";

    @ApiModelProperty(value = "门票辅助码")
    String assistCode = "";

    @ApiModelProperty(value = "音频URL")
    String audiourl = "";

    @ApiModelProperty(value = "音频有效期")
    Integer audioExpireHour;

    @ApiModelProperty(value = "备注")
    String remark = "";

    @ApiModelProperty(value = "含房产品入住须知")
    String notification = "";

    @ApiModelProperty(value = "实名制购票信息", notes = "实名制购票信息")
    List<OrderGuestInfo> ticketGuestInfo;

    @ApiModelProperty(value = "退款信息", notes = "退款信息")
    RefundInfo refundInfo;

    @ApiModelProperty(value = "套餐产品明细", notes = "套餐产品明细")
    List<OrderKitItemItem> orderProductInfo;

    @ApiModelProperty(value = "景区套餐券核销信息", notes = "景区套餐券核销信息")
    List<SpuPackCheckInfo> packCheckInfo;

    @ApiModelProperty(value = "伴手礼订单信息", notes = "伴手礼订单信息")
    CombineInfo combineInfo;

    @ApiModelProperty(value = "使用提示")
    String usetips = "";


    @ApiModelProperty(value = "附加证件描述", example = "员工号")
    String extranodesc = "";

    @ApiModelProperty(value = "附加证件号", example = "x123456")
    String extrano = "";


}
