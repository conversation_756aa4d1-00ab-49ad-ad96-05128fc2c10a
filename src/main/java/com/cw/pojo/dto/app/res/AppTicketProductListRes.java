package com.cw.pojo.dto.app.res;

import com.cw.pojo.dto.common.res.PageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("H5应用端票型产品列表元素")
public class AppTicketProductListRes extends PageResponse<AppTicketProductListRes.TicketProductDetailListData, AppTicketProductListRes.TicketProductDetailListData> {

    @ApiModelProperty(value = "产品大组代码", required = true, example = "001")
    private String groupid = "";

    @ApiModelProperty(value = "主标题", required = true, example = "001")
    private String title = "";

    @ApiModelProperty(value = "景点地址", required = true, example = "001")
    private String address = "";

    @ApiModelProperty(value = "对应的大组产品轮播图地址数组")
    private List<String> picurls = new ArrayList<>();

    @ApiModelProperty(value = " 对应的大组产品标签")
    private List<String> tags = new ArrayList<>();

    @ApiModelProperty(value = "坐标,景区坐标OR酒店坐标,逗号分割")
    private String singals = "";

    @ApiModelProperty(value = "单张列表图地址 也是分享图")
    private String listpicurl = "";

    @ApiModelProperty(value = "园区开放时间", example = "8:00-18:00")
    private String opentime = "8:00-18:00";

    @ApiModelProperty(value = "景区联系电话", example = "222222")
    private String telno = "";

    @ApiModelProperty(value = "营业状态", notes = "0:当前关闭,1:开放", example = "1")
    private String opstatus = "1";

    @ApiModelProperty(value = "评分", notes = "point", example = "2018")
    private Double point;

    @ApiModelProperty(value = "入园信息")
    private String infotext;

    @ApiModelProperty(value = "景点介绍-已经修改为对应预订须知")
    private String introtext;

    @ApiModelProperty(value = "游玩指南")
    private String guidetext;

    @ApiModelProperty(value = "订票预订须知")
    private String ordernote;

    @ApiModelProperty(value = "订票弹窗-针对疫情")
    private String alertnote;

    @ApiModelProperty(value = "弹窗类型，0-不弹窗，1-弹窗，2-弹窗并阅读所有内容")
    private Integer alertops;


    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "预订开始日期", notes = "预订开始日期", required = true, example = "2021-10-01")
    Date startdate = null;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "预订结束日期", notes = "预订开始日期", required = true, example = "2021-10-01")
    Date enddate = null;



    @ApiModelProperty(value = "更多推荐")
    private List<AppProductGroupRes.ProductGroupListData> recommend;


    @Data
    public static class TicketProductDetailListData {
        @ApiModelProperty(value = " 产品代码", required = true, example = "001")
        private String productCode = "";

        @ApiModelProperty(value = "主标题", required = true, example = "001")
        private String title = "";

        @ApiModelProperty(value = "产品封面图url", required = true, example = "001")
        private String picurl = "";

        @ApiModelProperty(value = "剩余或销量数字", required = true, example = "80")
        private Integer dispNum = 0;

        @ApiModelProperty(value = "0 不显示销量数字,1代表销量,2代表剩余", required = true, example = "80")
        private Integer showmode = 0;

        @ApiModelProperty(value = "是否需要实名制购票", required = true, example = "true")
        private Boolean lneedIdcard = false;

        @ApiModelProperty(value = "是否实名制一证多票", required = true, example = "true")
        private Boolean loneToMany = false;

        @ApiModelProperty(value = "是否需要选择购票时段", required = true, example = "true")
        private Boolean lshowtime = false;

        @ApiModelProperty(value = "当前起步最低价", required = true, example = "80")
        private String priceInfo = "";

        @ApiModelProperty(value = "原价", required = true, example = "80")
        private String orgpriceInfo = "";

        @ApiModelProperty(value = "是否售空", notes = "库存状态售空时,前端不可购买")
        private boolean loverbook = false;

        @ApiModelProperty(value = " 关闭原因", required = false, example = "库存关闭的原因")
        private String overreason = "";

        @ApiModelProperty(value = " 标签", notes = "标签数组")
        private List<String> tags = new ArrayList<>();

        @ApiModelProperty(value = "简短购买说明")
        private String shortinfo;

        @ApiModelProperty(value = "是否预订须知单独弹窗")
        private boolean lsingleNotice = false;

        @ApiModelProperty(value = "是否需要输入额外证件信息,额外输入的证件名称", example = "员工证")
        private String extranodesc;

        @ApiModelProperty(value = "最大购买数量", example = "99")
        Integer limitmax = 99;
    }


}
