spring:
  application:
    name: dsmall
  cloud:
    nacos:
      config:
        file-extension: yaml
        group: BHDEV
        namespace: d297e791-c3a4-4de4-957b-2c34b7eb4b10
        server-addr: 47.98.165.42:8848
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        server-addr: ${spring.cloud.nacos.config.server-addr}
        cluster-name: ${spring.cloud.nacos.config.group}
        group: ${spring.cloud.nacos.config.group}
      username: nacos
      password: smtgbk123
    config:
      #这项要写在配置中心才有用. allow true,overr one false override sys properties 表示配置文件以配置中心为准.但允许启动的命令行参数进行优先覆盖
      #允许本地覆盖远程配置
      allow-override: true
      #overrideNone设置为true，外部的配置优先级更低，而且不能覆盖任何存在的属性源。默认为false
      override-none: false
      #用来表示外部配置能否覆盖系统属性
      override-system-properties: false
ribbon:
  ServerListRefreshInterval: 5000
management:
  endpoint:
    info:
      enabled: false