server:
  port: 9500
spring:
  rabbitmq:
    host: ************
    port: 5672
    username: just
    password: 123
    virtual-host: DSMALL
  redis:
    password: chikan654321
    port: 6379
    host: ************
    database: 0
  datasource:
    username: root
    password: cwkjbh123
    url: ********************************************************
  data:
    redis:
      repositories:
        enabled: false
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    open-in-view: false
    data:
      redis:
        repositories:
          enabled: false
sms:
  mode: default
oss:
  type: oss #存储类型 oss-阿里云，cos-腾讯云
  expiration: 12 #临时访问地址有效时间 小时
  bucketName: datascenic
  accessKeyId: LTAI5t76VakTYWgPmCoUWTBC
  accessKeySecret: ******************************
  url: https://ossfile.citybaytech.com #自定义域名访问
  endpoint:
    outside: https://oss-cn-hangzhou.aliyuncs.com
    ecs: https://oss-cn-hangzhou-internal.aliyuncs.com
    vpc: https://oss-cn-hangzhou-internal.aliyuncs.com
    region: ap-guangzhou #cos的区域地址
logging:
  file:
    path: d:/otaifc
knife4j:
  enable: true
  markdowns: classpath:markdown/*
  documents:
    - group: 后台说明
      name: 后台设置接口调用说明
      locations: classpath:markdown/admin.md
cwconfig:
  paymode: false            #是否真实支付     false:模拟支付 true:真实支付  真实环境不要配置
  ordermode: false        #订单是否产生推送   false:不产生推送 true:产生推送  真实环境不要配置
  ticketmode: false        #门票是否产生推送   false:不产生推送 true:产生推送  真实环境不要配置
  domain: https://wxapp.citybaytech.com  #环境域名 对应生产环境.测试环境的地址前缀




