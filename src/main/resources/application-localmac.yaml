server:
  port: 9500
  shutdown: graceful
spring:
  rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: just
    password: 123
    virtual-host: JUSTTEST
  redis:
    password: chikan654321
    port: 6379
    host: 127.0.0.1
    database: 0
  datasource:
    username: root
    password: wjc<PERSON>@ssw0rd
    url: jdbc:mysql://**************:3306/litowntest?serverTimezone=GMT%2B8
    #    url: ****************************************************************
    hikari:
      max-lifetime: 30000
      connection-test-query: select 1
      minimum-idle: 3
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
  data:
    redis:
      repositories:
        enabled: false
  lifecycle:
    timeout-per-shutdown-phase: 10s
sms:
  mode: default
oss:
  expiration: 12 #临时访问地址有效时间 小时
  bucketName: datascenic
  accessKeyId: LTAI5t76VakTYWgPmCoUWTBC
  accessKeySecret: ******************************
  url: https://ossfile.citybaytech.com #自定义域名访问
  endpoint:
    outside: https://oss-cn-hangzhou.aliyuncs.com
    ecs: https://oss-cn-hangzhou-internal.aliyuncs.com
    vpc: https://oss-cn-hangzhou-internal.aliyuncs.com
    region: ap-guangzhou
logging:
  file:
    path: d:/otaifc
knife4j:
  enable: true
  markdowns: classpath:markdown/*
  documents:
    - group: 后台说明
      name: 后台设置接口调用说明
      locations: classpath:markdown/admin.md
cwconfig:
  checksign: false
  debugmode: true
  domain: https://apptest.citybaytech.com  #环境域名 对应生产环境.测试环境的地址前缀


