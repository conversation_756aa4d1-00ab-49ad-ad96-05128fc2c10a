package com.cw.testrun;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.ClassScanner;
import com.cw.arithmetic.SysFuncLibTool;
import com.cw.entity.Booking_rs;
import com.cw.pojo.dto.order.res.OrderListRes;
import com.cw.utils.CalculateDate;
import com.cw.utils.datetime.DateStyle;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/7 01:41
 **/
@Slf4j
public class TCollect {

    @Test
    public void b64() {
        //System.out.println(SysFuncLibTool.generateCouponEncodeQr("170726932889092b7300", "001"));

    }

    @Test
    public void testExec() {
        // 根据服务器的核心数创建线程池
        int numProcessors = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(numProcessors);
        log.info("核心任务数 {}", numProcessors);


        Stopwatch stopwatch = Stopwatch.createStarted();
        // 定义要执行的任务
        for (int i = 1; i <= 30; i++) {
            int i1 = i;
            //Runnable task = () -> System.out.println("Task " + i1 + " is running on thread: " + Thread.currentThread().getName());
            Runnable task = new Runnable() {
                @Override
                public void run() {
                    Stopwatch stopwatch = Stopwatch.createStarted();
                    try {
                        Thread.sleep(3000L);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    log.info("Task {} runing.{} consume:{}", i1, Thread.currentThread().getName(), stopwatch.stop());
                }
            };
            // 将任务提交给线程池执行
            executor.execute(task);
        }

        // 关闭线程池
        executor.shutdown();

        try {
            executor.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        log.info("总耗时:{}", stopwatch.stop());
    }

    @Test
    public void testTime() {
        String menuid = "PCmainCarousels";
        String checkcol = "18-";
//        yyyy-MM-dd HH:mm
        Date d = CalculateDate.stringToDate("2022-02-10 0:00", DateStyle.YYYY_MM_DD_HH_MM);
        int hour = CalculateDate.getDateProperty(d, Calendar.HOUR_OF_DAY);
        boolean lcheck = SysFuncLibTool.lcontentCanShow(menuid, checkcol, CalculateDate.getDateProperty(d, Calendar.HOUR_OF_DAY));
        log.info(hour + "--lcheck:  " + (lcheck ? "通过可显示" : "不可显示"));


    }

    @Test
    public void tttb() {
//        System.out.println(EnumUtil.fromString(AgentType.class, "", AgentType.WXAPP));
        List<Booking_rs> bookingRsList = new ArrayList<>();
        for (int i = 0; i < 2000; i++) {
            Booking_rs bookingRs = new Booking_rs();
            bookingRs.setBookingid("111222");
            bookingRsList.add(bookingRs);
        }
//        for (int i = 0; i < 2000 ; i++) {
//            OrderListRes.OrderListData node = BeanUtil.toBean();
//        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        for (Booking_rs bookingRs : bookingRsList) {
//            new OrderListRes.OrderListData();
            OrderListRes.OrderListData node = BeanUtil.toBean(bookingRs, OrderListRes.OrderListData.class);
        }
        log.info("{}ms", stopwatch.stop());

        log.info("{}", bookingRsList.size());


    }


    @Test
    public void bbb() {
        String path = "com.cw.controller.config";
        ClassScanner.scanPackageByAnnotation(path, RestController.class).forEach(clazz -> {
            RequestMapping mapping = clazz.getAnnotation(RequestMapping.class);
            log.info(mapping.value()[0]);
        });
    }

    @Test
    public void aaa() {
        Date d = new Date();
        log.info("{} ----{}", DateUtil.beginOfDay(d), DateUtil.endOfDay(d));

    }

//    @Test
//    public  void ddd(){
//        String jpql="from {0} where projectid=?1 ";
//        jpql = MessageFormat.format(jpql, Roomtype.class.getSimpleName());
//        System.out.println(jpql,"00");
//    }


}
