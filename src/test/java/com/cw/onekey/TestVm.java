package com.cw.onekey;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.entity.Booking_rs;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/7/29 22:01
 **/
public class TestVm {

    @Test
    public void xxxx() {
        Booking_rs booking_rs = new Booking_rs();
        booking_rs.setGuestname("AAA");
        booking_rs.setAmount(BigDecimal.valueOf(2L));
        booking_rs.setArrdate(new Date());
        booking_rs.setLhide(true);

        Booking_rs b=new Booking_rs();
        ObjectUtil.defaultIfNull("x","");

        BeanUtil.copyProperties(booking_rs,b);
        System.out.println(StrUtil.format("New Name:{}  {}   {}  {}", b.getGuestname(), b.getAmount(),b.getArrdate(),b.getLhide()));
    }
}
