package com.cw.unittest;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.cw.DsmallApplication;
import com.cw.arithmetic.SpelFomulaFactory;
import com.cw.arithmetic.others.Sp;
import com.cw.core.func.order.StdOrderData;
import com.cw.core.orderhandler.OrderVendorSwitcher;
import com.cw.core.vendor.order.ticket.MiniTicketVendor;
import com.cw.entity.Booking_rs;
import com.cw.entity.Ticket_rs;
import com.cw.outsys.base.SysStdResponse;
import com.cw.outsys.stdop.request.*;
import com.cw.outsys.stdop.response.*;
import com.cw.unitrule.APITestRule;
import com.cw.utils.CalculateDate;
import com.cw.utils.SpringUtil;
import com.cw.utils.enums.ProdType;
import com.cw.utils.enums.VendorType;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/9/15 17:27
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = DsmallApplication.class,
        properties = {"spring.cloud.nacos.config.enabled=false", "spring.profiles.active=ljh"},  //指定测试的配置文件
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestMiniTicketVendor extends APITestRule {

    String token;
    MiniTicketVendor miniTicketVendor;
    OrderVendorSwitcher switcher;
    String projectId = "001";
    Date queryStartDate = new Date();
    Date queryEndDate = new Date();
    String bookingid = "";
    String regno = "";
    private MockMvc mvc;
    @Autowired
    private WebApplicationContext context;

    @Before
    public void init() throws Exception {
        mvc = MockMvcBuilders.webAppContextSetup(context).build();
        switcher = SpringUtil.getBean(OrderVendorSwitcher.class);
        miniTicketVendor = (MiniTicketVendor) switcher.getVendorHandler(VendorType.MINI_TICKET);
        queryStartDate = new Date();
        queryEndDate = DateUtil.offsetDay(queryStartDate, 30);
        bookingid = RandomUtil.randomString("1234567890", 10);
        regno = RandomUtil.randomString("1234567890", 10);

    }

    public void output(SysStdResponse response, Object data) {
        log.info("请求成功{} --->结果:{}", response.getStd_flag() ? "成功" : "失败", JSON.toJSONString(data));
    }

    public void output_withOutJsonFormat(SysStdResponse response, Object data) {
//        log.info("请求成功{} --->结果:{}", response.getStd_flag() ? "成功" : "失败", data);
    }

    public StdOrderData getAtestData(int passlen, int passportlen, int forlen) {
        String baseStr = RandomUtil.BASE_NUMBER.toUpperCase();
        String passport = RandomUtil.randomString(baseStr, passlen);
        String pass = RandomUtil.randomString(baseStr, passportlen);
        String forno = RandomUtil.randomString(baseStr, forlen);

        String testidinfo = "[{\"guestname\":\"测试\",\"idno\":\"450521199402270059\",\"idtype\":\"1\",\"mobile\":\"15278917894\"}]";

        StdOrderData stdOrderData = new StdOrderData();
        Booking_rs rs = new Booking_rs();
        rs.setBookingid("20240924430400005");
        rs.setGuestname("测试");
        rs.setTel("15278917894");
        rs.setAmount(0.01);
        rs.setPtype(ProdType.TICKET.val());
        rs.setProjectid(projectId);
        stdOrderData.setBookingRs(rs);

        Ticket_rs ticket_rs = new Ticket_rs();
        ticket_rs.setBookingid(bookingid);
        ticket_rs.setProjectid(projectId);
        ticket_rs.setUsedate(CalculateDate.reckonDay(new Date(), 5, 5));
        ticket_rs.setTcode("18017");
        ticket_rs.setAmount(0.01);
        ticket_rs.setPrice(0.01);
        ticket_rs.setAnz(1);
        ticket_rs.setIdinfo(testidinfo);
        ticket_rs.setRegno(regno);
        stdOrderData.getTickets().add(ticket_rs);
        return stdOrderData;
    }

    @Test
    public void createOrder() throws Exception {
//        StdOrderData stdOrderData = getAtestData(8,8,14);
//        StdOrderResponse stdOrderResponse = shendaTicketVendor.createOrder(stdOrderData);
//        log.info("订单号:{}", JSON.toJSONString(stdOrderResponse.getStdIdResult()));


//        StdOrderData stdOrderData2 = getAtestData(7,7,13);
//        StdOrderResponse stdOrderResponse2 = shendaTicketVendor.createOrder(stdOrderData2);
//        log.info("订单号:{}", JSON.toJSONString(stdOrderResponse2.getStdIdResult()));

        StdOrderData stdOrderData3 = getAtestData(5, 5, 5);
        StdOrderResponse stdOrderResponse3 = miniTicketVendor.createOrder(stdOrderData3);
        log.info("订单号:{}", JSON.toJSONString(stdOrderResponse3.getStdIdResult()));
    }


    @Test
    public void complie() {
        Booking_rs bookingRs = new Booking_rs();
        bookingRs.setArrdate(new Date());
        bookingRs.setDeptdate(CalculateDate.reckonDay(new Date(), 5, 1));
        bookingRs.setBookingid("66666");
        bookingRs.setTel("5555");
        bookingRs.setProduct("MS12");
        bookingRs.setPtype(ProdType.ROOM.val());
        bookingRs.setAmount(1000);
        bookingRs.setGuestname("陈某人");
        String el = "'你好!'+#booking_rs.guestname+'欢迎入住'+#gdesc('R',#booking_rs.product,'001',false)+'酒店'+#pdesc('R',#booking_rs.product,'001')+' ! 入住日期 :'+#d2s(#booking_rs.arrdate)";
        String smscontent = SpelFomulaFactory.getStringFomulaResult(el, bookingRs);
        log.info("测试短信发送内容:{}", smscontent);


        Sp sp = Sp.builder().build();
        sp.setSmscode("666666");
        String spel = "'验证码:'+#sp.smscode ";
        smscontent = SpelFomulaFactory.getStringFomulaResult(spel, sp);
        log.info("测试验证码发送内容:{}", smscontent);
    }

    @Test
    public void cancelOrder() throws Exception {
        StdCancelOrderRequest request = new StdCancelOrderRequest();
        request.setOtaorderid("8864178310");
        request.setOutid("30105418");
        request.setProjectId(projectId);

        String testidinfo = "[{\"guestname\":\"测试\",\"idno\":\"450521199402270059\",\"idtype\":\"1\",\"mobile\":\"15278917894\"}]";

        StdOrderData stdOrderData = new StdOrderData();
        Booking_rs rs = new Booking_rs();
        rs.setBookingid(bookingid);
        rs.setGuestname("测试");
        rs.setTel("15278917894");
        rs.setAmount(0.01);
        rs.setPtype(ProdType.TICKET.val());
        rs.setProjectid(projectId);
        rs.setOutid("30105418");
        rs.setAnz(1);
        stdOrderData.setBookingRs(rs);

        Ticket_rs ticket_rs = new Ticket_rs();
        ticket_rs.setBookingid("8864178310");
        ticket_rs.setProjectid(projectId);
        ticket_rs.setUsedate(CalculateDate.reckonDay(new Date(), 5, 5));
        ticket_rs.setTcode("18017");
        ticket_rs.setAmount(1);
        ticket_rs.setAnz(1);
        ticket_rs.setIdinfo(testidinfo);
        ticket_rs.setRegno("8864178310");
        stdOrderData.getTickets().add(ticket_rs);

        request.setOrderDataContext(stdOrderData);

        StdCancelOrderResponse response = miniTicketVendor.cancelOrder(request);
        output_withOutJsonFormat(response, response.getStd_data());
    }

    @Test
    public void testQueryCancel() throws Exception {
        StdTicketQueryCanelStatusRequest request = new StdTicketQueryCanelStatusRequest();
        request.setCancelno("20240925175600002");
        request.setOutId("30261614");

        request.setProjectId(projectId);
        StdTicketQueryCancelStatusResponse response = miniTicketVendor.queryTicketCacnelStatus(request);
        output_withOutJsonFormat(response, response.getStd_data());
//        StdTicketQueryCanelStatusResponse response = miniTicketVendor.query(request);

    }

    @Test
    public void taocan() throws Exception {
//        cancelOrder();
        queryTicket();
        queryTicketStatus();
        queryTicketQrCodeUrl();
        queryTicketQrPic();
    }

    @Test
    public void queryTicketQrCodeUrl() throws Exception {
        StdTicketQrCodeUrlRequest request = new StdTicketQrCodeUrlRequest();
        request.setColno(bookingid);
        request.setProjectId(projectId);
        StdTicketQrCodeUrlResponse response = miniTicketVendor.queryTicketQrCodeUrl(request);
        output_withOutJsonFormat(response, response.getStd_data());
    }

    @Test
    public void queryTicket() throws Exception {
        StdTicketQueryRequest request = new StdTicketQueryRequest();
        request.setColno("20240925175600002");
        request.setOutId("30261614");
        request.setProjectId(projectId);
        StdTicketQueryResponse response = miniTicketVendor.queryTicket(request);
        output_withOutJsonFormat(response, response.getStd_data());
    }

    @Test
    public void queryTicketStatus() throws Exception {
        StdTicketQueryStatusRequest request = new StdTicketQueryStatusRequest();
        request.setColno(bookingid);
        request.setProjectId(projectId);
        StdTicketQueryStatusResponse response = miniTicketVendor.queryTicketStatus(request);
        output_withOutJsonFormat(response, response.getStd_data());
    }

    @Test
    public void queryTicketQrPic() throws Exception {
        StdTicketQueryQrPicRequest request = new StdTicketQueryQrPicRequest();
        request.setColno(bookingid);
        request.setProjectId(projectId);
        request.setAssistCode("4708856019");
        StdTicketQueryQrPicResponse response = miniTicketVendor.queryTicketQrPic(request);
        System.out.println(response.getImg());
        output_withOutJsonFormat(response, response.getStd_data());
    }

    @Test
    public void sendTicketMsg() throws Exception {
        StdTicketSendMsgRequest request = new StdTicketSendMsgRequest();
        request.setColno(bookingid);
        request.setProjectId(projectId);
        request.setColno("20240925175600002");
        request.setTemplate("30227954");
        request.setOutid("30261614");
        request.setMobileno("15278917894");
        StdTicketSendMsgResponse response = miniTicketVendor.sendTicketMsg(request);
        output_withOutJsonFormat(response, response.getStd_data());
    }

    @Test
    public void changeTicketDate() throws Exception {
        StdTicketChangeDateRequest request = new StdTicketChangeDateRequest();
        request.setBookingid(bookingid);
        request.setProjectId(projectId);
        StdTicketChangeDateResponse response = miniTicketVendor.changeTicketDate(request);
        output_withOutJsonFormat(response, response.getStd_data());
    }

    @Test
    public void changeTicketRoom() throws Exception {
        StdTicketChangeNumRequest request = new StdTicketChangeNumRequest();
        request.setBookingid(bookingid);
        request.setProjectId(projectId);
        StdTicketChangeNumResponse response = miniTicketVendor.changeTicketRoom(request);
        output_withOutJsonFormat(response, response.getStd_data());
    }


}
